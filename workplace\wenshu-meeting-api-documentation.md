# Wenshu-Meeting 智能会议管理模块 - 详细API接口文档

## 📋 模块概述

**服务名称**: wenshu-meeting  
**服务端口**: 1021  
**版本**: v1.0.0  
**最后更新**: 2024-12-28  

**核心功能**: 智能会议管理服务，提供视频分析、音频识别、实时音视频通信、会议纪要生成等功能

**技术栈**:
- Spring Boot 3.x
- Spring Cloud 2023.x
- WebSocket + STOMP (实时通信)
- JavaCV + FFmpeg (视频处理)
- 阿里云DashScope (AI分析)
- Spring Data JPA (数据持久化)
- Redis (会议室状态管理)
- Thymeleaf (页面模板)

---

## 🏗️ 服务架构

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                Wenshu-Meeting 智能会议管理服务                │
│                        (Port: 1021)                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│   视频分析     │ │实时通信  │ │ 页面路由   │
│ (Video AI)    │ │(WebSocket)│ │(Page Route)│
└───────────────┘ └────────┘ └───────────┘
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│ JavaCV+FFmpeg │ │Redis房间│ │Thymeleaf  │
│   视频处理     │ │状态管理  │ │ 模板引擎   │
└───────────────┘ └────────┘ └───────────┘
```

### 服务依赖关系
- **wenshu-chat**: AI智能对话服务
- **wenshu-voice**: 语音识别服务
- **wenshu-api**: 通用API服务和文件管理
- **阿里云DashScope**: 视频内容分析
- **Redis**: 会议室状态和用户管理

---

## 🌐 REST API接口

### 基础信息
- **Base URL**: `http://localhost:1021`
- **Content-Type**: `application/x-www-form-urlencoded` / `application/json`
- **鉴权方式**: JWT Token (Header: `Authorization: Bearer <token>`)

### 文件处理限制
- **单文件最大**: 10GB (支持大型视频文件)
- **支持格式**: MP4, AVI, MOV, WMV等主流视频格式
- **连接超时**: 1小时
- **处理超时**: 1小时

---

## 📚 视频分析API

### 1. 智能视频分析

#### 1.1 开始视频分析
**接口路径**: `POST /wenshu/meeting/analysis`

**功能描述**: 对上传的视频文件进行全面智能分析，包括视频内容分析、音频转录、会议纪要生成、日程信息提取

**请求参数**:
```http
POST /wenshu/meeting/analysis
Content-Type: application/x-www-form-urlencoded

analysisId: [分析任务ID] (必填)
userId: [用户ID] (必填)
```

**请求示例**:
```bash
curl -X POST http://localhost:1021/wenshu/meeting/analysis \
  -H "Authorization: Bearer <your-token>" \
  -d "analysisId=12345" \
  -d "userId=1001"
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "开始分析，请耐心等待",
  "data": null,
  "timestamp": 1640995200000
}
```

**分析流程**:
1. 🎬 **视频内容分析**: 使用JavaCV将视频切分为10秒片段，逐段进行AI分析
2. 🎵 **音频提取识别**: 从视频中提取音频，调用语音识别服务转换为文字
3. 📝 **会议纪要生成**: 基于音频转录文本，使用AI生成结构化会议纪要
4. 📅 **日程信息提取**: 从会议内容中智能提取后续日程安排
5. 💾 **结果存储**: 将所有分析结果存储到数据库

**分析状态**:
- `0`: 未开始
- `1`: 分析中
- `2`: 分析成功
- `3`: 分析失败

**错误响应**:
```json
{
  "code": 400,
  "msg": "查询id不存在",
  "data": null,
  "timestamp": 1640995200000
}
```

---

## 📄 页面路由API

### 2. 会议页面导航

#### 2.1 跳转会议页面
**接口路径**: `GET /toMeeting`

**功能描述**: 跳转到会议主页面，提供会议功能入口

**请求示例**:
```bash
curl -X GET http://localhost:1021/toMeeting
```

**响应**: 返回会议页面模板 `/meet/Meeting`

#### 2.2 跳转直播主播页面
**接口路径**: `GET /pubLive`

**功能描述**: 跳转到直播主播控制页面，用于主播端音视频控制

**请求示例**:
```bash
curl -X GET http://localhost:1021/pubLive
```

**响应**: 返回主播页面模板 `/live/PubLive`

#### 2.3 跳转直播观众页面
**接口路径**: `GET /subLive`

**功能描述**: 跳转到直播观众观看页面，用于观众端音视频接收

**请求示例**:
```bash
curl -X GET http://localhost:1021/subLive
```

**响应**: 返回观众页面模板 `/live/SubLive`

---

## 🔌 WebSocket实时通信API

### WebSocket连接配置

#### 连接信息
- **WebSocket端点**: `ws://localhost:1021/signal/{roomId}/{userId}/{pub}`
- **协议**: 原生WebSocket + 自定义信令
- **编解码器**: 自定义EncoderUtil/DecoderUtil

#### 路径参数说明
- `roomId`: 会议室ID，标识用户要加入的会议房间
- `userId`: 用户ID，唯一标识用户身份
- `pub`: 用户身份标识，0=普通参会者，1=主播/主持人

#### 连接示例
```javascript
// WebSocket连接示例
const roomId = "room_001";
const userId = "user_1001";
const pub = 0; // 0=普通用户, 1=主播

const websocket = new WebSocket(`ws://localhost:1021/signal/${roomId}/${userId}/${pub}`);

websocket.onopen = function(event) {
  console.log("WebSocket连接已建立");
  
  // 请求房间用户列表
  const message = {
    type: "roomUserList",
    roomId: roomId
  };
  websocket.send(JSON.stringify(message));
};

websocket.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log("收到消息:", data);
  
  // 处理不同类型的消息
  switch(data.type) {
    case "join":
      console.log("用户加入:", data.data);
      break;
    case "leave":
      console.log("用户离开:", data.data);
      break;
    case "roomUserList":
      console.log("房间用户列表:", data.data);
      break;
    // ... 其他消息类型
  }
};
```

---

### 3. WebSocket信令消息类型

#### 3.1 WebRTC信令消息

##### Offer信令 (发起连接)
**发送地址**: `/signal/{roomId}/{userId}/{pub}`  
**消息类型**: `offer`

```json
{
  "type": "offer",
  "targetUid": "target_user_id",
  "sdp": "WebRTC SDP信息",
  "sessionId": "session_12345"
}
```

##### Answer信令 (应答连接)
**发送地址**: `/signal/{roomId}/{userId}/{pub}`  
**消息类型**: `answer`

```json
{
  "type": "answer",
  "targetUid": "target_user_id",
  "sdp": "WebRTC SDP信息",
  "sessionId": "session_12345"
}
```

##### ICE Candidate信令 (网络候选)
**发送地址**: `/signal/{roomId}/{userId}/{pub}`  
**消息类型**: `candidate`

```json
{
  "type": "candidate",
  "targetUid": "target_user_id",
  "candidate": "ICE候选信息",
  "sdpMid": "audio",
  "sdpMLineIndex": 0
}
```

#### 3.2 会议控制信令

##### 呼叫信令
**发送地址**: `/signal/{roomId}/{userId}/{pub}`  
**消息类型**: `call`

```json
{
  "type": "call",
  "targetUid": "target_user_id",
  "callType": "video", // video/audio
  "callerName": "发起者姓名"
}
```

##### 房间用户列表请求
**发送地址**: `/signal/{roomId}/{userId}/{pub}`  
**消息类型**: `roomUserList`

```json
{
  "type": "roomUserList",
  "roomId": "room_001"
}
```

#### 3.3 系统通知消息 (订阅接收)

##### 用户加入通知
**订阅地址**: 自动推送到房间内所有用户  
**消息类型**: `join`

```json
{
  "type": "join",
  "message": "user_1001 join then room",
  "code": 200,
  "data": {
    "userId": "user_1001",
    "roomId": "room_001",
    "nickName": "user_1001",
    "pub": 0
  }
}
```

##### 用户离开通知
**订阅地址**: 自动推送到房间内所有用户  
**消息类型**: `leave`

```json
{
  "type": "leave",
  "message": "user_1001 leave then room",
  "code": 200,
  "data": {
    "userId": "user_1001",
    "roomId": "room_001",
    "nickName": "user_1001",
    "pub": 0
  }
}
```

##### 房间用户列表响应
**订阅地址**: 发送给请求用户  
**消息类型**: `roomUserList`

```json
{
  "type": "roomUserList",
  "message": "房间人数",
  "code": 200,
  "data": [
    {
      "userId": "user_1001",
      "roomId": "room_001",
      "nickName": "user_1001",
      "pub": 0
    },
    {
      "userId": "user_1002",
      "roomId": "room_001",
      "nickName": "user_1002",
      "pub": 1
    }
  ]
}
```

---

## 🔧 技术实现细节

### 视频处理引擎
- **视频切分**: JavaCV + FFmpeg，每10秒切分一段
- **格式支持**: MP4, AVI, MOV, WMV等主流格式
- **AI分析**: 阿里云DashScope qwen-max模型
- **并发处理**: 支持多视频同时分析
- **错误恢复**: 自动跳过损坏片段，继续处理

### 音频处理能力
- **音频提取**: 从视频中提取高质量音频
- **格式转换**: 自动转换为语音识别支持的格式
- **语音识别**: 集成wenshu-voice服务
- **文本处理**: 智能去重和错误纠正

### WebSocket通信
- **连接管理**: 线程安全的连接池管理
- **房间隔离**: Redis存储房间状态，支持多房间并发
- **消息路由**: 支持点对点和房间广播
- **自动清理**: 连接断开时自动清理资源

---

---

## 📊 性能指标

### 处理性能
| 指标 | 数值 | 说明 |
|------|------|------|
| 视频分析延迟 | < 2倍视频时长 | 1小时视频约2小时处理 |
| 音频识别延迟 | < 30秒 | 1分钟音频 |
| WebSocket延迟 | < 50ms | 实时信令传输 |
| 并发会议室 | 100+ | 同时支持的会议房间 |
| 单房间用户 | 50+ | 每个房间最大用户数 |
| 文件大小限制 | 10GB | 单个视频文件 |

### 资源消耗
- **内存使用**: 平均 2GB (视频处理时)
- **CPU使用**: 平均 < 60% (分析时)
- **磁盘空间**: 临时文件自动清理
- **网络带宽**: 依赖视频文件大小

---

## 🛠️ 配置说明

### 服务配置
```yaml
server:
  port: 1021
  tomcat:
    max-http-form-post-size: 10000MB
    max-swallow-size: 10000MB
    connection-timeout: 3600000    # 1小时
    max-connections: 8192
    max-threads: 200

spring:
  servlet:
    multipart:
      max-file-size: 10GB
      max-request-size: 10GB
      file-size-threshold: 2KB
      location: ${java.io.tmpdir}
```

### WebSocket配置
```java
@Configuration
public class WebSocketConfig {
    @Bean
    public ServerEndpointExporter serverEndpointExporter(){
        return new ServerEndpointExporter();
    }
}
```

### 视频处理配置
```yaml
wenshu:
  meeting:
    video:
      # 视频处理配置
      segment-duration: 10          # 视频切分时长(秒)
      max-file-size: 10GB          # 最大文件大小
      temp-dir: ${java.io.tmpdir}  # 临时目录
      cleanup-delay: 3600000       # 清理延迟(1小时)

    analysis:
      # AI分析配置
      max-retry: 3                 # 最大重试次数
      timeout: 30000              # 分析超时(30秒)
      batch-size: 5               # 批处理大小
```

---

## 🔍 错误处理

### 常见错误码
| 错误码 | 错误信息 | 解决方案 |
|-------|---------|----------|
| 400 | 查询id不存在 | 确认analysisId正确 |
| 400 | 参数错误 | 检查请求参数格式 |
| 401 | 未授权 | 提供有效的JWT Token |
| 500 | 视频处理失败 | 检查视频格式和完整性 |
| 500 | AI分析失败 | 检查网络和AI服务状态 |
| 500 | 音频识别失败 | 检查音频质量 |
| 500 | WebSocket连接失败 | 检查网络和服务状态 |

### 错误处理策略
1. **视频处理失败**: 自动跳过损坏片段，继续处理
2. **AI服务不可用**: 使用预设模板生成基础分析结果
3. **WebSocket断开**: 客户端自动重连机制
4. **文件上传失败**: 支持断点续传
5. **内存不足**: 自动清理临时文件

---

## 🔒 安全措施

### 文件安全
- **格式验证**: 严格验证视频文件格式
- **大小限制**: 限制单文件最大10GB
- **病毒扫描**: 上传文件安全检查
- **临时存储**: 处理完成后自动删除

### WebSocket安全
- **路径验证**: 验证房间ID和用户ID
- **连接限制**: 防止恶意连接攻击
- **消息验证**: 验证消息格式和内容
- **房间隔离**: 确保房间间数据隔离

### 数据保护
- **传输加密**: WebSocket支持WSS加密
- **会议隐私**: 会议内容端到端保护
- **访问控制**: 基于用户权限的房间访问
- **审计日志**: 完整的会议操作记录

---

## 💻 开发集成指南

### 客户端集成示例

#### JavaScript/Web集成
```javascript
// 会议管理客户端
class WenshuMeetingClient {
  constructor(baseUrl, token) {
    this.baseUrl = baseUrl;
    this.token = token;
    this.websocket = null;
    this.roomId = null;
    this.userId = null;
  }

  // 开始视频分析
  async startVideoAnalysis(analysisId, userId) {
    const formData = new FormData();
    formData.append('analysisId', analysisId);
    formData.append('userId', userId);

    try {
      const response = await fetch(`${this.baseUrl}/wenshu/meeting/analysis`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`
        },
        body: formData
      });

      const result = await response.json();
      if (result.code === 200) {
        console.log('视频分析已开始:', result.msg);
        return result;
      } else {
        throw new Error(result.msg);
      }
    } catch (error) {
      console.error('视频分析启动失败:', error);
      throw error;
    }
  }

  // 连接WebSocket会议室
  connectToRoom(roomId, userId, isPub = false) {
    this.roomId = roomId;
    this.userId = userId;

    const pub = isPub ? 1 : 0;
    const wsUrl = `ws://localhost:1021/signal/${roomId}/${userId}/${pub}`;

    this.websocket = new WebSocket(wsUrl);

    this.websocket.onopen = (event) => {
      console.log('WebSocket连接已建立');
      this.onConnected(event);

      // 请求房间用户列表
      this.requestRoomUserList();
    };

    this.websocket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMessage(data);
    };

    this.websocket.onclose = (event) => {
      console.log('WebSocket连接已关闭');
      this.onDisconnected(event);
    };

    this.websocket.onerror = (error) => {
      console.error('WebSocket错误:', error);
      this.onError(error);
    };
  }

  // 发送WebRTC Offer
  sendOffer(targetUid, sdp, sessionId) {
    const message = {
      type: 'offer',
      targetUid: targetUid,
      sdp: sdp,
      sessionId: sessionId
    };
    this.sendMessage(message);
  }

  // 发送WebRTC Answer
  sendAnswer(targetUid, sdp, sessionId) {
    const message = {
      type: 'answer',
      targetUid: targetUid,
      sdp: sdp,
      sessionId: sessionId
    };
    this.sendMessage(message);
  }

  // 发送ICE Candidate
  sendCandidate(targetUid, candidate, sdpMid, sdpMLineIndex) {
    const message = {
      type: 'candidate',
      targetUid: targetUid,
      candidate: candidate,
      sdpMid: sdpMid,
      sdpMLineIndex: sdpMLineIndex
    };
    this.sendMessage(message);
  }

  // 发起呼叫
  makeCall(targetUid, callType = 'video', callerName) {
    const message = {
      type: 'call',
      targetUid: targetUid,
      callType: callType,
      callerName: callerName
    };
    this.sendMessage(message);
  }

  // 请求房间用户列表
  requestRoomUserList() {
    const message = {
      type: 'roomUserList',
      roomId: this.roomId
    };
    this.sendMessage(message);
  }

  // 发送消息
  sendMessage(message) {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket未连接，无法发送消息');
    }
  }

  // 处理接收到的消息
  handleMessage(data) {
    console.log('收到消息:', data);

    switch(data.type) {
      case 'join':
        this.onUserJoined(data.data);
        break;
      case 'leave':
        this.onUserLeft(data.data);
        break;
      case 'roomUserList':
        this.onRoomUserList(data.data);
        break;
      case 'offer':
        this.onOfferReceived(data);
        break;
      case 'answer':
        this.onAnswerReceived(data);
        break;
      case 'candidate':
        this.onCandidateReceived(data);
        break;
      case 'call':
        this.onCallReceived(data);
        break;
      default:
        console.log('未知消息类型:', data.type);
    }
  }

  // 断开连接
  disconnect() {
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
  }

  // 事件回调方法（可重写）
  onConnected(event) {
    console.log('已连接到会议室:', this.roomId);
  }

  onDisconnected(event) {
    console.log('已断开会议室连接');
  }

  onError(error) {
    console.error('连接错误:', error);
  }

  onUserJoined(userData) {
    console.log('用户加入:', userData);
  }

  onUserLeft(userData) {
    console.log('用户离开:', userData);
  }

  onRoomUserList(userList) {
    console.log('房间用户列表:', userList);
  }

  onOfferReceived(data) {
    console.log('收到Offer:', data);
  }

  onAnswerReceived(data) {
    console.log('收到Answer:', data);
  }

  onCandidateReceived(data) {
    console.log('收到ICE Candidate:', data);
  }

  onCallReceived(data) {
    console.log('收到呼叫:', data);
  }
}

// 使用示例
const meetingClient = new WenshuMeetingClient('http://localhost:1021', 'your-jwt-token');

// 开始视频分析
meetingClient.startVideoAnalysis('analysis_12345', '1001')
  .then(result => {
    console.log('分析已开始:', result);
  })
  .catch(error => {
    console.error('分析失败:', error);
  });

// 连接到会议室
meetingClient.connectToRoom('room_001', 'user_1001', false);

// 自定义事件处理
meetingClient.onUserJoined = function(userData) {
  // 在页面上显示新用户
  const userElement = document.createElement('div');
  userElement.id = `user_${userData.userId}`;
  userElement.textContent = `${userData.nickName} (${userData.pub ? '主播' : '参会者'})`;
  document.getElementById('userList').appendChild(userElement);
};

meetingClient.onUserLeft = function(userData) {
  // 从页面移除用户
  const userElement = document.getElementById(`user_${userData.userId}`);
  if (userElement) {
    userElement.remove();
  }
};
```

#### Python集成示例
```python
import requests
import json
import websocket
import threading
from typing import Optional, Dict, Any, Callable

class WenshuMeetingClient:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {token}'
        }
        self.ws = None
        self.room_id = None
        self.user_id = None
        self.connected = False

        # 事件回调
        self.on_connected: Optional[Callable] = None
        self.on_disconnected: Optional[Callable] = None
        self.on_user_joined: Optional[Callable] = None
        self.on_user_left: Optional[Callable] = None
        self.on_message: Optional[Callable] = None

    def start_video_analysis(self, analysis_id: str, user_id: str) -> Dict[str, Any]:
        """开始视频分析"""
        url = f"{self.base_url}/wenshu/meeting/analysis"
        data = {
            'analysisId': analysis_id,
            'userId': user_id
        }

        response = requests.post(url, headers=self.headers, data=data)
        result = response.json()

        if result['code'] == 200:
            return result
        else:
            raise Exception(f"视频分析启动失败: {result['msg']}")

    def connect_to_room(self, room_id: str, user_id: str, is_pub: bool = False):
        """连接到会议室"""
        self.room_id = room_id
        self.user_id = user_id

        pub = 1 if is_pub else 0
        ws_url = f"ws://localhost:1021/signal/{room_id}/{user_id}/{pub}"

        self.ws = websocket.WebSocketApp(
            ws_url,
            on_open=self._on_open,
            on_message=self._on_message,
            on_error=self._on_error,
            on_close=self._on_close
        )

        # 在新线程中运行WebSocket
        ws_thread = threading.Thread(target=self.ws.run_forever)
        ws_thread.daemon = True
        ws_thread.start()

    def _on_open(self, ws):
        """WebSocket连接打开"""
        print(f"已连接到会议室: {self.room_id}")
        self.connected = True

        if self.on_connected:
            self.on_connected()

        # 请求房间用户列表
        self.request_room_user_list()

    def _on_message(self, ws, message):
        """处理接收到的消息"""
        try:
            data = json.loads(message)
            print(f"收到消息: {data}")

            if self.on_message:
                self.on_message(data)

            # 处理特定消息类型
            if data['type'] == 'join' and self.on_user_joined:
                self.on_user_joined(data['data'])
            elif data['type'] == 'leave' and self.on_user_left:
                self.on_user_left(data['data'])

        except Exception as e:
            print(f"消息处理错误: {e}")

    def _on_error(self, ws, error):
        """WebSocket错误处理"""
        print(f"WebSocket错误: {error}")
        self.connected = False

    def _on_close(self, ws, close_status_code, close_msg):
        """WebSocket连接关闭"""
        print("WebSocket连接已关闭")
        self.connected = False

        if self.on_disconnected:
            self.on_disconnected()

    def send_message(self, message: Dict[str, Any]):
        """发送消息"""
        if self.connected and self.ws:
            self.ws.send(json.dumps(message))
        else:
            print("WebSocket未连接，无法发送消息")

    def send_offer(self, target_uid: str, sdp: str, session_id: str):
        """发送WebRTC Offer"""
        message = {
            'type': 'offer',
            'targetUid': target_uid,
            'sdp': sdp,
            'sessionId': session_id
        }
        self.send_message(message)

    def send_answer(self, target_uid: str, sdp: str, session_id: str):
        """发送WebRTC Answer"""
        message = {
            'type': 'answer',
            'targetUid': target_uid,
            'sdp': sdp,
            'sessionId': session_id
        }
        self.send_message(message)

    def make_call(self, target_uid: str, call_type: str = 'video', caller_name: str = None):
        """发起呼叫"""
        message = {
            'type': 'call',
            'targetUid': target_uid,
            'callType': call_type,
            'callerName': caller_name or self.user_id
        }
        self.send_message(message)

    def request_room_user_list(self):
        """请求房间用户列表"""
        message = {
            'type': 'roomUserList',
            'roomId': self.room_id
        }
        self.send_message(message)

    def disconnect(self):
        """断开连接"""
        if self.ws:
            self.ws.close()

# 使用示例
if __name__ == "__main__":
    client = WenshuMeetingClient('http://localhost:1021', 'your-jwt-token')

    # 开始视频分析
    try:
        result = client.start_video_analysis('analysis_12345', '1001')
        print(f"视频分析已开始: {result}")
    except Exception as e:
        print(f"视频分析失败: {e}")

    # 设置事件回调
    def on_user_joined(user_data):
        print(f"新用户加入: {user_data['nickName']} ({'主播' if user_data['pub'] else '参会者'})")

    def on_user_left(user_data):
        print(f"用户离开: {user_data['nickName']}")

    client.on_user_joined = on_user_joined
    client.on_user_left = on_user_left

    # 连接到会议室
    client.connect_to_room('room_001', 'user_1001', False)

    # 保持程序运行
    try:
        while True:
            import time
            time.sleep(1)
    except KeyboardInterrupt:
        client.disconnect()
        print("程序退出")
```

---

---

## 🎯 使用场景示例

### 场景1: 智能会议录制分析系统
```javascript
// 会议录制分析管理器
class MeetingRecordingAnalyzer {
  constructor(meetingClient) {
    this.client = meetingClient;
    this.analysisQueue = [];
    this.analysisResults = new Map();
  }

  // 批量分析会议录像
  async batchAnalyzeRecordings(recordings, userId) {
    const results = [];

    for (let i = 0; i < recordings.length; i++) {
      const recording = recordings[i];
      console.log(`分析第${i + 1}/${recordings.length}个录像: ${recording.title}`);

      try {
        // 开始视频分析
        const analysisResult = await this.client.startVideoAnalysis(
          recording.analysisId,
          userId
        );

        // 轮询分析状态
        const finalResult = await this.pollAnalysisStatus(recording.analysisId);

        const analysisRecord = {
          recordingId: recording.id,
          title: recording.title,
          analysisId: recording.analysisId,
          status: 'completed',
          result: finalResult,
          analyzedAt: new Date()
        };

        results.push(analysisRecord);
        this.analysisResults.set(recording.id, analysisRecord);

        // 生成会议纪要
        await this.generateMeetingMinutes(analysisRecord);

      } catch (error) {
        console.error(`录像${recording.title}分析失败:`, error);
        results.push({
          recordingId: recording.id,
          title: recording.title,
          status: 'failed',
          error: error.message,
          analyzedAt: new Date()
        });
      }
    }

    return results;
  }

  // 轮询分析状态
  async pollAnalysisStatus(analysisId, maxAttempts = 60) {
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        const status = await this.checkAnalysisStatus(analysisId);

        if (status.completed) {
          return status.result;
        } else if (status.failed) {
          throw new Error(status.error);
        }

        // 等待30秒后重试
        await new Promise(resolve => setTimeout(resolve, 30000));

      } catch (error) {
        if (attempt === maxAttempts - 1) {
          throw error;
        }
        await new Promise(resolve => setTimeout(resolve, 30000));
      }
    }

    throw new Error('分析超时');
  }

  // 检查分析状态（需要实现具体的状态查询接口）
  async checkAnalysisStatus(analysisId) {
    // 这里应该调用实际的状态查询接口
    // 返回格式: { completed: boolean, failed: boolean, result?: any, error?: string }
    return { completed: false, failed: false };
  }

  // 生成会议纪要
  async generateMeetingMinutes(analysisRecord) {
    const minutes = {
      meetingTitle: analysisRecord.title,
      analysisDate: analysisRecord.analyzedAt,
      videoAnalysis: analysisRecord.result.videoAnalysis,
      audioTranscript: analysisRecord.result.audioTranscript,
      meetingAnalysis: analysisRecord.result.meetingAnalysis,
      scheduleExtraction: analysisRecord.result.scheduleExtraction,
      keyPoints: this.extractKeyPoints(analysisRecord.result),
      actionItems: this.extractActionItems(analysisRecord.result),
      nextSteps: this.extractNextSteps(analysisRecord.result)
    };

    // 保存会议纪要
    await this.saveMeetingMinutes(analysisRecord.recordingId, minutes);

    return minutes;
  }

  extractKeyPoints(analysisResult) {
    // 从分析结果中提取关键要点
    const keyPoints = [];

    if (analysisResult.meetingAnalysis) {
      const analysis = analysisResult.meetingAnalysis;
      // 简单的关键词提取逻辑
      const keywords = ['决定', '同意', '计划', '目标', '问题', '解决方案'];

      keywords.forEach(keyword => {
        if (analysis.includes(keyword)) {
          const sentences = analysis.split('。');
          sentences.forEach(sentence => {
            if (sentence.includes(keyword)) {
              keyPoints.push(sentence.trim());
            }
          });
        }
      });
    }

    return keyPoints;
  }

  extractActionItems(analysisResult) {
    // 提取行动项
    const actionItems = [];

    if (analysisResult.scheduleExtraction) {
      // 从日程提取中获取行动项
      actionItems.push(...analysisResult.scheduleExtraction);
    }

    return actionItems;
  }

  extractNextSteps(analysisResult) {
    // 提取下一步计划
    const nextSteps = [];

    if (analysisResult.meetingAnalysis) {
      const analysis = analysisResult.meetingAnalysis;
      const stepKeywords = ['下一步', '接下来', '后续', '计划'];

      stepKeywords.forEach(keyword => {
        if (analysis.includes(keyword)) {
          const sentences = analysis.split('。');
          sentences.forEach(sentence => {
            if (sentence.includes(keyword)) {
              nextSteps.push(sentence.trim());
            }
          });
        }
      });
    }

    return nextSteps;
  }

  async saveMeetingMinutes(recordingId, minutes) {
    // 保存会议纪要到数据库或文件系统
    console.log(`保存会议纪要: ${recordingId}`, minutes);
  }
}
```

### 场景2: 实时视频会议系统
```javascript
// 实时视频会议管理器
class RealTimeMeetingManager {
  constructor(meetingClient) {
    this.client = meetingClient;
    this.localStream = null;
    this.remoteStreams = new Map();
    this.peerConnections = new Map();
    this.isHost = false;
  }

  // 创建会议室
  async createMeetingRoom(roomId, userId, userName) {
    try {
      // 获取本地媒体流
      this.localStream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      });

      // 显示本地视频
      this.displayLocalVideo(this.localStream);

      // 以主播身份连接到会议室
      this.isHost = true;
      this.client.connectToRoom(roomId, userId, true);

      // 设置事件处理
      this.setupEventHandlers(userName);

      console.log(`会议室 ${roomId} 创建成功`);

    } catch (error) {
      console.error('创建会议室失败:', error);
      throw error;
    }
  }

  // 加入会议室
  async joinMeetingRoom(roomId, userId, userName) {
    try {
      // 获取本地媒体流
      this.localStream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      });

      // 显示本地视频
      this.displayLocalVideo(this.localStream);

      // 以参会者身份连接到会议室
      this.isHost = false;
      this.client.connectToRoom(roomId, userId, false);

      // 设置事件处理
      this.setupEventHandlers(userName);

      console.log(`已加入会议室 ${roomId}`);

    } catch (error) {
      console.error('加入会议室失败:', error);
      throw error;
    }
  }

  // 设置事件处理器
  setupEventHandlers(userName) {
    // 用户加入事件
    this.client.onUserJoined = (userData) => {
      console.log('新用户加入:', userData);
      this.onUserJoined(userData);
    };

    // 用户离开事件
    this.client.onUserLeft = (userData) => {
      console.log('用户离开:', userData);
      this.onUserLeft(userData);
    };

    // 收到Offer
    this.client.onOfferReceived = (data) => {
      this.handleOffer(data);
    };

    // 收到Answer
    this.client.onAnswerReceived = (data) => {
      this.handleAnswer(data);
    };

    // 收到ICE Candidate
    this.client.onCandidateReceived = (data) => {
      this.handleCandidate(data);
    };

    // 收到呼叫
    this.client.onCallReceived = (data) => {
      this.handleIncomingCall(data);
    };

    // 房间用户列表
    this.client.onRoomUserList = (userList) => {
      this.handleRoomUserList(userList);
    };
  }

  // 处理用户加入
  onUserJoined(userData) {
    // 如果是主播，主动发起呼叫
    if (this.isHost && userData.userId !== this.client.userId) {
      setTimeout(() => {
        this.initiateCall(userData.userId);
      }, 1000);
    }

    // 在UI上显示新用户
    this.addUserToUI(userData);
  }

  // 处理用户离开
  onUserLeft(userData) {
    // 清理对应的连接
    if (this.peerConnections.has(userData.userId)) {
      this.peerConnections.get(userData.userId).close();
      this.peerConnections.delete(userData.userId);
    }

    // 清理远程流
    if (this.remoteStreams.has(userData.userId)) {
      this.remoteStreams.delete(userData.userId);
    }

    // 从UI移除用户
    this.removeUserFromUI(userData.userId);
  }

  // 发起呼叫
  async initiateCall(targetUserId) {
    try {
      // 创建RTCPeerConnection
      const peerConnection = this.createPeerConnection(targetUserId);
      this.peerConnections.set(targetUserId, peerConnection);

      // 添加本地流
      this.localStream.getTracks().forEach(track => {
        peerConnection.addTrack(track, this.localStream);
      });

      // 创建Offer
      const offer = await peerConnection.createOffer();
      await peerConnection.setLocalDescription(offer);

      // 发送Offer
      this.client.sendOffer(targetUserId, offer.sdp, `session_${Date.now()}`);

    } catch (error) {
      console.error('发起呼叫失败:', error);
    }
  }

  // 处理收到的Offer
  async handleOffer(data) {
    try {
      const peerConnection = this.createPeerConnection(data.targetUid);
      this.peerConnections.set(data.targetUid, peerConnection);

      // 添加本地流
      this.localStream.getTracks().forEach(track => {
        peerConnection.addTrack(track, this.localStream);
      });

      // 设置远程描述
      await peerConnection.setRemoteDescription({
        type: 'offer',
        sdp: data.sdp
      });

      // 创建Answer
      const answer = await peerConnection.createAnswer();
      await peerConnection.setLocalDescription(answer);

      // 发送Answer
      this.client.sendAnswer(data.targetUid, answer.sdp, data.sessionId);

    } catch (error) {
      console.error('处理Offer失败:', error);
    }
  }

  // 处理收到的Answer
  async handleAnswer(data) {
    try {
      const peerConnection = this.peerConnections.get(data.targetUid);
      if (peerConnection) {
        await peerConnection.setRemoteDescription({
          type: 'answer',
          sdp: data.sdp
        });
      }
    } catch (error) {
      console.error('处理Answer失败:', error);
    }
  }

  // 处理ICE Candidate
  async handleCandidate(data) {
    try {
      const peerConnection = this.peerConnections.get(data.targetUid);
      if (peerConnection) {
        await peerConnection.addIceCandidate({
          candidate: data.candidate,
          sdpMid: data.sdpMid,
          sdpMLineIndex: data.sdpMLineIndex
        });
      }
    } catch (error) {
      console.error('处理ICE Candidate失败:', error);
    }
  }

  // 创建RTCPeerConnection
  createPeerConnection(userId) {
    const configuration = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
      ]
    };

    const peerConnection = new RTCPeerConnection(configuration);

    // ICE候选事件
    peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        this.client.sendCandidate(
          userId,
          event.candidate.candidate,
          event.candidate.sdpMid,
          event.candidate.sdpMLineIndex
        );
      }
    };

    // 远程流事件
    peerConnection.ontrack = (event) => {
      console.log('收到远程流:', event.streams[0]);
      this.remoteStreams.set(userId, event.streams[0]);
      this.displayRemoteVideo(userId, event.streams[0]);
    };

    return peerConnection;
  }

  // 显示本地视频
  displayLocalVideo(stream) {
    const localVideo = document.getElementById('localVideo');
    if (localVideo) {
      localVideo.srcObject = stream;
    }
  }

  // 显示远程视频
  displayRemoteVideo(userId, stream) {
    let remoteVideo = document.getElementById(`remoteVideo_${userId}`);
    if (!remoteVideo) {
      remoteVideo = document.createElement('video');
      remoteVideo.id = `remoteVideo_${userId}`;
      remoteVideo.autoplay = true;
      remoteVideo.playsInline = true;
      document.getElementById('remoteVideos').appendChild(remoteVideo);
    }
    remoteVideo.srcObject = stream;
  }

  // 添加用户到UI
  addUserToUI(userData) {
    const userList = document.getElementById('userList');
    const userElement = document.createElement('div');
    userElement.id = `user_${userData.userId}`;
    userElement.className = 'user-item';
    userElement.innerHTML = `
      <span>${userData.nickName}</span>
      <span class="user-role">${userData.pub ? '主播' : '参会者'}</span>
    `;
    userList.appendChild(userElement);
  }

  // 从UI移除用户
  removeUserFromUI(userId) {
    const userElement = document.getElementById(`user_${userId}`);
    if (userElement) {
      userElement.remove();
    }

    const remoteVideo = document.getElementById(`remoteVideo_${userId}`);
    if (remoteVideo) {
      remoteVideo.remove();
    }
  }

  // 离开会议
  leaveMeeting() {
    // 停止本地流
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
    }

    // 关闭所有连接
    this.peerConnections.forEach(pc => pc.close());
    this.peerConnections.clear();

    // 清理远程流
    this.remoteStreams.clear();

    // 断开WebSocket连接
    this.client.disconnect();

    console.log('已离开会议');
  }
}
```

---

## 🔧 高级配置

### 视频处理高级配置
```yaml
# application.yml
wenshu:
  meeting:
    video:
      # 视频处理配置
      processing:
        segment-duration: 10          # 视频切分时长(秒)
        max-segments: 360            # 最大片段数(1小时视频)
        parallel-processing: true     # 并行处理
        thread-pool-size: 4          # 处理线程池大小

      # 视频格式配置
      formats:
        supported: ["mp4", "avi", "mov", "wmv", "mkv"]
        preferred: "mp4"

      # 临时文件配置
      temp:
        base-dir: ${java.io.tmpdir}/wenshu-meeting
        cleanup-enabled: true
        cleanup-delay: 3600000       # 1小时后清理
        max-disk-usage: 50GB         # 最大磁盘使用

    # AI分析配置
    analysis:
      dashscope:
        model: qwen-max
        max-tokens: 2000
        temperature: 0.3
        timeout: 30000

      retry:
        max-attempts: 3
        delay: 5000
        backoff-multiplier: 2

    # WebSocket配置
    websocket:
      # 连接配置
      max-connections: 1000
      max-rooms: 100
      max-users-per-room: 50

      # 消息配置
      max-message-size: 64KB
      heartbeat-interval: 30000
      connection-timeout: 300000

      # Redis配置
      redis:
        key-prefix: "wenshu:meeting:"
        room-ttl: 86400              # 房间TTL(24小时)
        user-ttl: 3600               # 用户TTL(1小时)
```

### 性能优化配置
```java
@Configuration
public class MeetingPerformanceConfig {

    @Bean
    @ConfigurationProperties(prefix = "wenshu.meeting.thread-pool")
    public ThreadPoolTaskExecutor videoProcessingExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(4);
        executor.setMaxPoolSize(16);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("video-processing-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean
    public RedisTemplate<String, Object> meetingRedisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);

        // 设置序列化器
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());

        template.afterPropertiesSet();
        return template;
    }

    @Bean
    public CacheManager meetingCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(Duration.ofMinutes(30))
            .recordStats());
        return cacheManager;
    }
}
```

---

## 📊 监控和运维

### 健康检查配置
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,websocket
  endpoint:
    health:
      show-details: always
      show-components: always
  health:
    diskspace:
      enabled: true
      threshold: 5GB
  metrics:
    export:
      prometheus:
        enabled: true
```

### 自定义健康检查
```java
@Component
public class MeetingHealthIndicator implements HealthIndicator {

    private final VideoAnalysisService videoAnalysisService;
    private final WebSocketServer webSocketServer;
    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public Health health() {
        try {
            // 检查视频处理服务
            boolean videoHealthy = checkVideoProcessing();

            // 检查WebSocket服务
            boolean wsHealthy = checkWebSocketService();

            // 检查Redis连接
            boolean redisHealthy = checkRedisConnection();

            // 检查磁盘空间
            boolean diskHealthy = checkDiskSpace();

            if (videoHealthy && wsHealthy && redisHealthy && diskHealthy) {
                return Health.up()
                    .withDetail("video", "正常")
                    .withDetail("websocket", "正常")
                    .withDetail("redis", "正常")
                    .withDetail("disk", "正常")
                    .withDetail("activeRooms", getActiveRoomCount())
                    .withDetail("activeConnections", getActiveConnectionCount())
                    .build();
            } else {
                return Health.down()
                    .withDetail("video", videoHealthy ? "正常" : "异常")
                    .withDetail("websocket", wsHealthy ? "正常" : "异常")
                    .withDetail("redis", redisHealthy ? "正常" : "异常")
                    .withDetail("disk", diskHealthy ? "正常" : "异常")
                    .build();
            }
        } catch (Exception e) {
            return Health.down()
                .withDetail("error", e.getMessage())
                .build();
        }
    }

    private boolean checkVideoProcessing() {
        // 检查视频处理服务状态
        return true;
    }

    private boolean checkWebSocketService() {
        // 检查WebSocket服务状态
        return webSocketServer != null;
    }

    private boolean checkRedisConnection() {
        try {
            redisTemplate.opsForValue().get("health_check");
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private boolean checkDiskSpace() {
        File tempDir = new File(System.getProperty("java.io.tmpdir"));
        long freeSpace = tempDir.getFreeSpace();
        long threshold = 5L * 1024 * 1024 * 1024; // 5GB
        return freeSpace > threshold;
    }

    private int getActiveRoomCount() {
        // 获取活跃房间数
        return 0;
    }

    private int getActiveConnectionCount() {
        // 获取活跃连接数
        return 0;
    }
}
```

---

---

## 🚀 部署指南

### Docker部署
```dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app

# 安装FFmpeg和相关依赖
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libavcodec-extra \
    fonts-dejavu-core \
    fontconfig \
    && rm -rf /var/lib/apt/lists/*

# 复制应用程序
COPY target/wenshu-meeting-0.0.1-SNAPSHOT.jar app.jar

# 创建必要的目录
RUN mkdir -p /app/logs/wenshu-meeting
RUN mkdir -p /app/temp/video-processing
RUN mkdir -p /app/uploads

# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' > /etc/timezone

# 暴露端口
EXPOSE 1021

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:1021/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", "-jar", "-Xmx4g", "-Xms2g", "app.jar"]
```

### Docker Compose部署
```yaml
version: '3.8'

services:
  wenshu-meeting:
    build: .
    container_name: wenshu-meeting
    ports:
      - "1021:1021"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - NACOS_SERVER_ADDR=nacos:8848
      - DASHSCOPE_API_KEY=${DASHSCOPE_API_KEY}
      - MYSQL_URL=********************************
      - MYSQL_USERNAME=root
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./logs:/app/logs
      - ./temp:/app/temp
      - ./uploads:/app/uploads
      - /dev/shm:/dev/shm  # 共享内存，提高视频处理性能
    depends_on:
      - nacos
      - mysql
      - redis
    networks:
      - wenshu-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:1021/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  redis:
    image: redis:7-alpine
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - wenshu-network
    command: redis-server --appendonly yes

  nacos:
    image: nacos/nacos-server:v2.2.3
    container_name: nacos
    ports:
      - "8848:8848"
    environment:
      - MODE=standalone
    networks:
      - wenshu-network

  mysql:
    image: mysql:8.0
    container_name: mysql
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_DATABASE=ry-cloud
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - wenshu-network

volumes:
  mysql_data:
  redis_data:

networks:
  wenshu-network:
    driver: bridge
```

### Kubernetes部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wenshu-meeting
  labels:
    app: wenshu-meeting
spec:
  replicas: 2
  selector:
    matchLabels:
      app: wenshu-meeting
  template:
    metadata:
      labels:
        app: wenshu-meeting
    spec:
      containers:
      - name: wenshu-meeting
        image: wenshu/meeting:1.0.0
        ports:
        - containerPort: 1021
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: NACOS_SERVER_ADDR
          value: "nacos-service:8848"
        - name: DASHSCOPE_API_KEY
          valueFrom:
            secretKeyRef:
              name: dashscope-secret
              key: api-key
        - name: REDIS_HOST
          value: "redis-service"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 1021
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 1021
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: temp
          mountPath: /app/temp
        - name: shm
          mountPath: /dev/shm
      volumes:
      - name: logs
        emptyDir: {}
      - name: temp
        emptyDir:
          sizeLimit: 20Gi
      - name: shm
        emptyDir:
          medium: Memory
          sizeLimit: 2Gi

---
apiVersion: v1
kind: Service
metadata:
  name: wenshu-meeting-service
spec:
  selector:
    app: wenshu-meeting
  ports:
  - port: 1021
    targetPort: 1021
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: wenshu-meeting-ingress
  annotations:
    nginx.ingress.kubernetes.io/websocket-services: "wenshu-meeting-service"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
spec:
  rules:
  - host: meeting.wenshu.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: wenshu-meeting-service
            port:
              number: 1021
```

---

## 📞 技术支持

### 常见问题解答

**Q1: 视频分析失败怎么办？**
A: 检查视频格式是否支持，确认文件完整性，查看日志获取详细错误信息。

**Q2: WebSocket连接失败怎么处理？**
A: 检查网络连接，确认服务端口开放，验证房间ID和用户ID格式。

**Q3: 支持哪些视频格式？**
A: 支持MP4、AVI、MOV、WMV、MKV等主流格式，推荐使用MP4格式。

**Q4: 如何提高视频处理速度？**
A: 增加处理线程数，使用SSD存储，确保充足的内存和CPU资源。

**Q5: 会议室最多支持多少人？**
A: 单个会议室最多支持50人同时在线，可根据服务器性能调整。

**Q6: WebSocket消息大小限制？**
A: 单条消息最大64KB，大型数据建议分片传输。

### 性能优化建议

1. **视频处理优化**
   - 使用SSD存储提高I/O性能
   - 增加处理线程池大小
   - 启用并行处理模式
   - 合理设置视频切分时长

2. **WebSocket优化**
   - 启用消息压缩
   - 设置合理的心跳间隔
   - 使用连接池管理
   - 实现客户端重连机制

3. **系统优化**
   - 配置充足的内存(推荐4GB+)
   - 使用Redis集群提高性能
   - 启用缓存减少数据库访问
   - 监控磁盘空间使用

4. **网络优化**
   - 使用CDN加速静态资源
   - 启用Gzip压缩
   - 配置负载均衡
   - 优化网络带宽

### 故障排查指南

#### 视频分析问题
```bash
# 检查FFmpeg是否正确安装
ffmpeg -version

# 检查视频文件信息
ffprobe -v quiet -print_format json -show_format -show_streams video.mp4

# 查看处理日志
tail -f logs/wenshu-meeting/meeting.log | grep "video"
```

#### WebSocket连接问题
```bash
# 测试WebSocket连接
wscat -c ws://localhost:1021/signal/test_room/test_user/0

# 检查Redis连接
redis-cli ping

# 查看连接日志
tail -f logs/wenshu-meeting/meeting.log | grep "WebSocket"
```

#### 性能监控
```bash
# 查看JVM内存使用
jstat -gc <pid>

# 监控磁盘使用
df -h

# 查看网络连接
netstat -an | grep 1021
```

### 联系方式
- **技术支持**: <EMAIL>
- **API文档**: https://docs.wenshu.com/meeting
- **问题反馈**: https://github.com/wenshu/meeting/issues
- **在线帮助**: https://help.wenshu.com/meeting

### 版本更新日志

#### v1.0.0 (2024-12-28)
- ✨ 新增智能视频分析功能
- ✨ 新增实时WebSocket通信
- ✨ 新增会议纪要自动生成
- ✨ 新增日程信息智能提取
- ✨ 新增多格式视频支持
- 🔧 优化视频处理性能
- 🔧 优化WebSocket连接管理
- 🐛 修复内存泄漏问题
- 🐛 修复并发处理异常

#### 即将发布
- 🚀 支持实时语音转文字
- 🚀 支持会议录制功能
- 🚀 支持屏幕共享
- 🚀 支持会议白板
- 🚀 支持移动端适配

---

*文档版本: v1.0.0 | 最后更新: 2024-12-28 | Wenshu-Meeting智能会议管理服务*
