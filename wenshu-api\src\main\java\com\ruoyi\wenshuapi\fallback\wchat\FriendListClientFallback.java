package com.ruoyi.wenshuapi.fallback.wchat;

import com.ruoyi.wenshuapi.client.wchat.FriendListClient;
import com.ruoyi.wenshuapi.pojo.wchat.FriendList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 好友关系服务降级处理类
 *
 * <p><strong>功能说明：</strong>
 * 当好友关系服务(wenshu-wchat)不可用或调用超时时，触发熔断降级机制，
 * 避免服务雪崩，同时提供可读性强的错误提示和详细的日志记录。</p>
 *
 * <p><strong>降级策略：</strong>
 * 1. 返回友好的错误响应（HTTP 503状态）
 * 2. 记录完整的异常堆栈信息
 * 3. 空操作保护（如添加/删除操作不执行实际动作）
 * 4. 查询操作返回空数据集
 * </p>
 */
@Slf4j
@Component
public class FriendListClientFallback implements FriendListClient {

    private static final String SERVICE_UNAVAILABLE_MSG = "好友关系服务暂时不可用，请稍后重试";

    /**
     * 创建标准的降级响应
     */
    private ResponseEntity<Map<String, Object>> createFallbackResponse(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        response.put("data", data);
        response.put("fallback", true);
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
    }

    /**
     * 创建成功的降级响应（用于查询操作）
     */
    private ResponseEntity<Map<String, Object>> createSuccessFallbackResponse(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "（降级数据）" + message);
        response.put("data", data);
        response.put("fallback", true);
        return ResponseEntity.ok(response);
    }

    @Override
    public ResponseEntity<Map<String, Object>> addFriend(FriendList friendList) {
        if (friendList != null) {
            log.warn("好友关系服务降级触发 >> 添加好友失败 - 用户ID: {}, 好友ID: {}",
                    friendList.getUserId(), friendList.getFriendId());
        }
        return createFallbackResponse("好友添加失败，" + SERVICE_UNAVAILABLE_MSG, null);
    }

    @Override
    public ResponseEntity<Map<String, Object>> deleteFriend(Long friendId) {
        log.warn("好友关系服务降级触发 >> 删除好友失败 - 好友关系ID: {}", friendId);
        return createFallbackResponse("好友删除失败，" + SERVICE_UNAVAILABLE_MSG, null);
    }

    @Override
    public ResponseEntity<Map<String, Object>> deleteFriendByUserIds(Long userId, Long friendUserId) {
        log.warn("好友关系服务降级触发 >> 根据用户ID删除好友失败 - 用户ID: {}, 好友用户ID: {}", userId, friendUserId);
        return createFallbackResponse("好友删除失败，" + SERVICE_UNAVAILABLE_MSG, null);
    }

    @Override
    public ResponseEntity<Map<String, Object>> updateFriendStatus(Long friendId, String status) {
        log.warn("好友关系服务降级触发 >> 更新好友状态失败 - 好友关系ID: {}, 新状态: {}", friendId, status);
        return createFallbackResponse("好友状态更新失败，" + SERVICE_UNAVAILABLE_MSG, null);
    }

    @Override
    public ResponseEntity<Map<String, Object>> getFriendById(Long friendId) {
        log.warn("好友关系服务降级触发 >> 根据ID获取好友关系失败 - 好友关系ID: {}", friendId);
        return createSuccessFallbackResponse(SERVICE_UNAVAILABLE_MSG, null);
    }

    @Override
    public ResponseEntity<Map<String, Object>> getFriendsByUserId(Long userId) {
        log.warn("好友关系服务降级触发 >> 根据用户ID获取好友列表失败 - 用户ID: {}", userId);
        return createSuccessFallbackResponse(SERVICE_UNAVAILABLE_MSG, new ArrayList<>());
    }

    @Override
    public ResponseEntity<Map<String, Object>> getFriendsByUserIdAndStatus(Long userId, String status) {
        log.warn("好友关系服务降级触发 >> 根据用户ID和状态获取好友列表失败 - 用户ID: {}, 状态: {}", userId, status);
        return createSuccessFallbackResponse(SERVICE_UNAVAILABLE_MSG, new ArrayList<>());
    }

    @Override
    public ResponseEntity<Map<String, Object>> checkFriendship(Long userId1, Long userId2) {
        log.warn("好友关系服务降级触发 >> 检查好友关系失败 - 用户1: {}, 用户2: {}", userId1, userId2);
        Map<String, Object> data = new HashMap<>();
        data.put("isFriend", false);
        data.put("status", "unknown");
        return createSuccessFallbackResponse(SERVICE_UNAVAILABLE_MSG, data);
    }

    @Override
    public ResponseEntity<Map<String, Object>> getAllFriends() {
        log.warn("好友关系服务降级触发 >> 获取所有好友关系失败");
        return createSuccessFallbackResponse(SERVICE_UNAVAILABLE_MSG, new ArrayList<>());
    }

    @Override
    public ResponseEntity<Map<String, Object>> getFriendCountByUserId(Long userId) {
        log.warn("好友关系服务降级触发 >> 获取用户好友数量失败 - 用户ID: {}", userId);
        return createSuccessFallbackResponse(SERVICE_UNAVAILABLE_MSG, 0);
    }

    @Override
    public ResponseEntity<Map<String, Object>> getFriendCountByUserIdAndStatus(Long userId, String status) {
        log.warn("好友关系服务降级触发 >> 根据状态获取用户好友数量失败 - 用户ID: {}, 状态: {}", userId, status);
        return createSuccessFallbackResponse(SERVICE_UNAVAILABLE_MSG, 0);
    }

    @Override
    public ResponseEntity<Map<String, Object>> searchFriends(Long userId, String keyword) {
        log.warn("好友关系服务降级触发 >> 搜索好友失败 - 用户ID: {}, 关键词: {}", userId, keyword);
        return createSuccessFallbackResponse(SERVICE_UNAVAILABLE_MSG, new ArrayList<>());
    }

    @Override
    public ResponseEntity<Map<String, Object>> batchAddFriends(List<FriendList> friendLists) {
        log.warn("好友关系服务降级触发 >> 批量添加好友失败 - 好友数量: {}",
                friendLists != null ? friendLists.size() : 0);
        return createFallbackResponse("批量添加好友失败，" + SERVICE_UNAVAILABLE_MSG, null);
    }

    @Override
    public ResponseEntity<Map<String, Object>> batchDeleteFriends(List<Long> friendIds) {
        log.warn("好友关系服务降级触发 >> 批量删除好友失败 - 好友关系数量: {}",
                friendIds != null ? friendIds.size() : 0);
        return createFallbackResponse("批量删除好友失败，" + SERVICE_UNAVAILABLE_MSG, null);
    }

    @Override
    public ResponseEntity<Map<String, Object>> batchUpdateFriendStatus(List<Long> friendIds, String status) {
        log.warn("好友关系服务降级触发 >> 批量更新好友状态失败 - 好友关系数量: {}, 新状态: {}",
                friendIds != null ? friendIds.size() : 0, status);
        return createFallbackResponse("批量更新好友状态失败，" + SERVICE_UNAVAILABLE_MSG, null);
    }
}
