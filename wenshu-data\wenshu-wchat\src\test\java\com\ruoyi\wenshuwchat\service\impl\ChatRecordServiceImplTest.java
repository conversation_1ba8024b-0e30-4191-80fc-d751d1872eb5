package com.ruoyi.wenshuwchat.service.impl;

import com.ruoyi.wenshuapi.pojo.wchat.ChatRecord;
import com.ruoyi.wenshuwchat.dao.ChatRecordDao;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ChatRecordServiceImpl 测试类
 */
@ExtendWith(MockitoExtension.class)
class ChatRecordServiceImplTest {

    @Mock
    private ChatRecordDao chatRecordDao;

    @InjectMocks
    private ChatRecordServiceImpl chatRecordService;

    private ChatRecord testChatRecord;

    @BeforeEach
    void setUp() {
        testChatRecord = new ChatRecord();
        testChatRecord.setId(1L);
        testChatRecord.setSenderId(100L);
        testChatRecord.setReceiverId(200L);
        testChatRecord.setContent("测试消息");
        testChatRecord.setContentType("text");
        testChatRecord.setSendTime(LocalDateTime.now());
        testChatRecord.setIsRead(false);
    }

    @Test
    void testSendMessage_Success() {
        // 准备测试数据
        when(chatRecordDao.insert(any(ChatRecord.class))).thenReturn(1);
        
        // 执行测试
        Long messageId = chatRecordService.sendMessage(testChatRecord);
        
        // 验证结果
        assertNotNull(messageId);
        verify(chatRecordDao, times(1)).insert(any(ChatRecord.class));
    }

    @Test
    void testSendMessage_InvalidSender() {
        // 准备测试数据
        testChatRecord.setSenderId(null);
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> chatRecordService.sendMessage(testChatRecord)
        );
        
        assertEquals("发送者ID不能为空或无效", exception.getMessage());
    }

    @Test
    void testSendMessage_SameSenderAndReceiver() {
        // 准备测试数据
        testChatRecord.setReceiverId(100L); // 与发送者ID相同
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> chatRecordService.sendMessage(testChatRecord)
        );
        
        assertEquals("发送者和接收者不能是同一人", exception.getMessage());
    }

    @Test
    void testSendMessage_EmptyContent() {
        // 准备测试数据
        testChatRecord.setContent("");
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> chatRecordService.sendMessage(testChatRecord)
        );
        
        assertEquals("消息内容不能为空", exception.getMessage());
    }

    @Test
    void testSendMessage_UnsupportedContentType() {
        // 准备测试数据
        testChatRecord.setContentType("audio");
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> chatRecordService.sendMessage(testChatRecord)
        );
        
        assertTrue(exception.getMessage().contains("不支持的消息类型"));
    }

    @Test
    void testGenerateSessionId() {
        // 执行测试
        String sessionId1 = chatRecordService.generateSessionId(100L, 200L);
        String sessionId2 = chatRecordService.generateSessionId(200L, 100L);
        
        // 验证结果
        assertNotNull(sessionId1);
        assertNotNull(sessionId2);
        assertEquals(sessionId1, sessionId2); // 顺序不同但结果相同
    }

    @Test
    void testGenerateSessionId_SameUser() {
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> chatRecordService.generateSessionId(100L, 100L)
        );
        
        assertEquals("不能与自己聊天", exception.getMessage());
    }

    @Test
    void testDeleteMessage() {
        // 准备测试数据
        when(chatRecordDao.deleteById(1L)).thenReturn(1);
        
        // 执行测试
        int result = chatRecordService.deleteMessage(1L);
        
        // 验证结果
        assertEquals(1, result);
        verify(chatRecordDao, times(1)).deleteById(1L);
    }

    @Test
    void testMarkMessageAsRead() {
        // 准备测试数据
        when(chatRecordDao.updateReadStatus(1L, true)).thenReturn(1);
        
        // 执行测试
        int result = chatRecordService.markMessageAsRead(1L);
        
        // 验证结果
        assertEquals(1, result);
        verify(chatRecordDao, times(1)).updateReadStatus(1L, true);
    }

    @Test
    void testGetMessagesBySessionId() {
        // 准备测试数据
        String sessionId = "test-session-id";
        List<ChatRecord> expectedMessages = Arrays.asList(testChatRecord);
        when(chatRecordDao.selectBySessionId(sessionId)).thenReturn(expectedMessages);
        
        // 执行测试
        List<ChatRecord> result = chatRecordService.getMessagesBySessionId(sessionId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testChatRecord, result.get(0));
        verify(chatRecordDao, times(1)).selectBySessionId(sessionId);
    }

    @Test
    void testGetMessagesBySessionIdWithPaging() {
        // 准备测试数据
        String sessionId = "test-session-id";
        List<ChatRecord> expectedMessages = Arrays.asList(testChatRecord);
        when(chatRecordDao.selectBySessionIdWithPaging(sessionId, 0, 10)).thenReturn(expectedMessages);
        
        // 执行测试
        List<ChatRecord> result = chatRecordService.getMessagesBySessionIdWithPaging(sessionId, 1, 10);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(chatRecordDao, times(1)).selectBySessionIdWithPaging(sessionId, 0, 10);
    }

    @Test
    void testGetMessagesBySessionIdWithPaging_InvalidPage() {
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> chatRecordService.getMessagesBySessionIdWithPaging("session-id", 0, 10)
        );
        
        assertEquals("页码必须大于0", exception.getMessage());
    }

    @Test
    void testGetMessagesBySessionIdWithPaging_InvalidSize() {
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> chatRecordService.getMessagesBySessionIdWithPaging("session-id", 1, 101)
        );
        
        assertTrue(exception.getMessage().contains("每页大小必须在1-100之间"));
    }

    @Test
    void testGetUnreadMessageCount() {
        // 准备测试数据
        when(chatRecordDao.countUnreadMessages(200L)).thenReturn(5);
        
        // 执行测试
        int result = chatRecordService.getUnreadMessageCount(200L);
        
        // 验证结果
        assertEquals(5, result);
        verify(chatRecordDao, times(1)).countUnreadMessages(200L);
    }

    @Test
    void testHasSessionPermission() {
        // 准备测试数据
        String sessionId = "session_100_200";
        
        // 执行测试
        boolean hasPermission1 = chatRecordService.hasSessionPermission(sessionId, 100L);
        boolean hasPermission2 = chatRecordService.hasSessionPermission(sessionId, 200L);
        boolean hasPermission3 = chatRecordService.hasSessionPermission(sessionId, 300L);
        
        // 验证结果
        assertTrue(hasPermission1);
        assertTrue(hasPermission2);
        assertFalse(hasPermission3);
    }

    @Test
    void testMessageExists() {
        // 准备测试数据
        when(chatRecordDao.selectById(1L)).thenReturn(testChatRecord);
        when(chatRecordDao.selectById(999L)).thenReturn(null);
        
        // 执行测试
        boolean exists1 = chatRecordService.messageExists(1L);
        boolean exists2 = chatRecordService.messageExists(999L);
        
        // 验证结果
        assertTrue(exists1);
        assertFalse(exists2);
    }
}
