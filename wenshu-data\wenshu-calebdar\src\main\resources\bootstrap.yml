# Spring
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 2313147023
    url: *******************************************************************************************************************************************************************************************************

  application:
    # 应用名称
    name: wenshu-calebdar
  profiles:
    # 环境配置
    active: dev
  autoconfigure:
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 8.155.31.217:8848
      config:
        # 配置中心地址
        server-addr: 8.155.31.217:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

server:
  port: 8705