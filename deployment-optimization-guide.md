# 🚀 Docker Compose 部署空间优化指南

## 📊 优化总结

### 🔧 主要优化措施

#### 1. **配置模板化** - 减少重复配置
- **公共服务配置模板** (`x-common-service`)
- **环境变量模板** (`x-common-environment`, `x-database-environment`, `x-redis-environment`)
- **资源限制模板** (3个不同级别的资源限制)

#### 2. **资源限制分级** - 优化内存和CPU使用
```yaml
# 核心服务 (gateway, auth)
memory: 768M, cpu: 0.8 cores

# 标准服务 (chat, voice, meeting, multimodal)  
memory: 512M, cpu: 0.5 cores

# 轻量级服务 (其他数据服务)
memory: 256M, cpu: 0.3 cores
```

#### 3. **服务分组和配置文件** - 按需启动
- **核心服务**: 始终运行
- **监控服务**: 仅在 `monitoring` profile 下运行
- **开发服务**: 仅在 `development` profile 下运行

#### 4. **移除不必要的配置**
- 移除了 `livechat`, `wenshu-wchat`, `editfile` 服务
- 优化了日志卷挂载（仅必要服务保留）
- 统一了网络和安全配置

### 💾 空间节省估算

| 优化项目 | 节省空间 | 说明 |
|---------|---------|------|
| 移除3个服务 | ~1.5GB | 每个服务约500MB镜像 |
| 资源限制优化 | ~2GB RAM | 限制内存使用，避免过度分配 |
| 配置模板化 | ~60% 配置行数 | 减少重复配置 |
| 按需启动服务 | ~500MB | monitor和gen服务按需启动 |

**总计预估节省**: ~4GB 部署空间 + 显著减少内存使用

## 🚀 使用方法

### 基础部署（推荐）
```bash
# 启动核心服务
docker-compose -f docker-compose.app.optimized.yml up -d
```

### 包含监控的部署
```bash
# 启动核心服务 + 监控
docker-compose -f docker-compose.app.optimized.yml --profile monitoring up -d
```

### 开发环境部署
```bash
# 启动所有服务（包括代码生成）
docker-compose -f docker-compose.app.optimized.yml --profile development --profile monitoring up -d
```

### 选择性启动服务
```bash
# 只启动特定服务
docker-compose -f docker-compose.app.optimized.yml up -d gateway auth system chat
```

## 📈 性能优化建议

### 1. **JVM 优化**
所有服务已配置基础JVM参数：
```
-Xmx512m -Xms256m (标准服务)
-Xmx256m -Xms128m (轻量级服务)
```

### 2. **数据库连接优化**
建议在应用配置中设置：
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
```

### 3. **Redis 连接优化**
```yaml
spring:
  data:
    redis:
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
```

## 🔍 监控和调优

### 资源使用监控
```bash
# 查看容器资源使用情况
docker stats

# 查看特定服务的资源使用
docker stats wenshu-gateway wenshu-auth wenshu-system
```

### 日志管理
```bash
# 限制日志大小（在docker-compose中添加）
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

## 🛠️ 进一步优化建议

### 1. **镜像优化**
- 使用多阶段构建减少镜像大小
- 使用 Alpine Linux 基础镜像
- 清理不必要的依赖和缓存

### 2. **服务合并**
考虑将以下轻量级服务合并：
- `wenshu-base` + `wenshu-team` → `wenshu-data-service`
- `wenshu-audit` + `wenshu-file` → `wenshu-file-service`

### 3. **缓存优化**
- 实现应用级缓存减少数据库访问
- 使用 Redis 集群提高缓存性能

### 4. **数据库优化**
- 考虑使用读写分离
- 实现数据库连接池优化
- 定期清理日志和临时数据

## 📋 迁移检查清单

- [ ] 备份原始 docker-compose.app.yml
- [ ] 测试优化版本在开发环境
- [ ] 验证所有服务正常启动
- [ ] 检查服务间通信正常
- [ ] 监控资源使用情况
- [ ] 验证日志输出正常
- [ ] 测试应用功能完整性

## 🚨 注意事项

1. **首次部署**: 建议先在测试环境验证
2. **资源监控**: 部署后密切监控资源使用情况
3. **服务依赖**: 确保服务启动顺序正确
4. **数据持久化**: 确认重要数据的卷挂载配置
5. **网络配置**: 验证服务间网络通信正常

## 📞 故障排除

### 常见问题
1. **内存不足**: 调整资源限制或增加服务器内存
2. **服务启动失败**: 检查依赖服务是否正常运行
3. **网络连接问题**: 验证网络配置和防火墙设置
4. **配置错误**: 检查环境变量和配置文件

### 回滚方案
```bash
# 如需回滚到原始配置
docker-compose -f docker-compose.app.yml up -d
```
