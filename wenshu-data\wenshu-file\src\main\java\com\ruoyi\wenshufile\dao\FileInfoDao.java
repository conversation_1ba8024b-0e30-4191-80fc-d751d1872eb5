package com.ruoyi.wenshufile.dao;

import com.ruoyi.wenshuapi.pojo.file.FileInfoPojo;
import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface FileInfoDao {

    // 插入文件信息（自增主键回填）
    @Insert("INSERT INTO wenshu_file_storage (file_name, file_path, upload_time, uploader_id, file_size, file_status, owner_type) " +
            "VALUES (#{fileName}, #{filePath}, #{uploadTime}, #{uploaderId}, #{fileSize}, #{fileStatus}, #{ownerType})")
    @Options(useGeneratedKeys = true, keyProperty = "fileId", keyColumn = "file_id")
    int insertFile(FileInfoPojo file);

    // 根据ID删除文件
    @Delete("DELETE FROM wenshu_file_storage WHERE file_id = #{fileId}")
    int deleteById(@Param("fileId") int fileId);

    // 更新文件信息 - 使用IF函数处理动态字段
    @Update("UPDATE wenshu_file_storage SET " +
            "file_name = IF(#{fileName} IS NOT NULL, #{fileName}, file_name), " +
            "file_path = IF(#{filePath} IS NOT NULL, #{filePath}, file_path), " +
            "upload_time = IF(#{uploadTime} IS NOT NULL, #{uploadTime}, upload_time), " +
            "uploader_id = IF(#{uploaderId} IS NOT NULL, #{uploaderId}, uploader_id), " +
            "file_size = IF(#{fileSize} IS NOT NULL, #{fileSize}, file_size), " +
            "file_status = IF(#{fileStatus} IS NOT NULL, #{fileStatus}, file_status), " +
            "owner_type = IF(#{ownerType} IS NOT NULL, #{ownerType}, owner_type) " +
            "WHERE file_id = #{fileId}")
    int updateFile(FileInfoPojo file);

    // 根据ID查询文件
    @Select("SELECT * FROM wenshu_file_storage WHERE file_id = #{fileId}")
    @Results(id = "fileResultMap", value = {
            @Result(property = "fileId", column = "file_id", id = true),
            @Result(property = "fileName", column = "file_name"),
            @Result(property = "filePath", column = "file_path"),
            @Result(property = "uploadTime", column = "upload_time"),
            @Result(property = "uploaderId", column = "uploader_id"),
            @Result(property = "fileSize", column = "file_size"),
            @Result(property = "fileStatus", column = "file_status"),
            @Result(property = "ownerType", column = "owner_type")
    })
    FileInfoPojo selectById(@Param("fileId") int fileId);

    // 条件查询文件列表 - 使用OR逻辑处理可选参数
    @Select("SELECT * FROM wenshu_file_storage " +
            "WHERE ( #{fileName} IS NULL OR file_name LIKE CONCAT('%', #{fileName}, '%') ) " +
            "AND ( #{uploaderId} IS NULL OR uploader_id = #{uploaderId} ) " +
            "AND ( #{fileStatus} IS NULL OR file_status = #{fileStatus} ) " +
            "AND ( #{ownerType} IS NULL OR owner_type = #{ownerType} ) " +
            "ORDER BY upload_time DESC")
    @ResultMap("fileResultMap")
    List<FileInfoPojo> selectFilesByCondition(FileInfoPojo condition);

    // 根据文件路径精确查询
    @Select("SELECT * FROM wenshu_file_storage WHERE file_path = #{filePath}")
    @ResultMap("fileResultMap")
    FileInfoPojo selectByPath(@Param("filePath") String filePath);

    // 更新文件状态
    @Update("UPDATE wenshu_file_storage SET file_status = #{status} WHERE file_id = #{fileId}")
    int updateFileStatus(@Param("fileId") int fileId, @Param("status") String status);

    // 统计用户上传的文件数量
    @Select("SELECT COUNT(*) FROM wenshu_file_storage WHERE uploader_id = #{uploaderId}")
    int countByUploader(@Param("uploaderId") int uploaderId);
}