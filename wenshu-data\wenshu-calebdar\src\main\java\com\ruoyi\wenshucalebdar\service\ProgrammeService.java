package com.ruoyi.wenshucalebdar.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.wenshuapi.pojo.programme.Programme;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 日程服务接口
 */
public interface ProgrammeService extends IService<Programme> {
    /**
     * 根据时间范围查询日程
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 日程列表
     */
    List<Programme> getProgrammesByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Long userId);
    
    /**
     * 根据用户ID查询日程
     * @param userId 用户ID
     * @return 日程列表
     */
    List<Programme> getProgrammesByUserId(Long userId);
    
    /**
     * 根据用户ID和时间范围查询日程
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 日程列表
     */
    List<Programme> getProgrammesByUserIdAndTimeRange(Long userId, LocalDateTime startTime, LocalDateTime endTime);
}
