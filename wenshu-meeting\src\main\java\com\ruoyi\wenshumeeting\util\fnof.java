//package com.ruoyi.wenshumeeting.util;
//
//import com.ruoyi.wenshumeeting.service.VideoSplitter;
//import com.ruoyi.wenshumeeting.service.AudioExtractor;
//
//import java.io.IOException;
//import java.util.List;
//
//
//
///**
// * 视频处理工具测试类
// * 用于测试VideoSplitter类的功能
// *
// * <AUTHOR>
// * @date 2025-07-04
// */
//public class fnof {
//
//    /**
//     * 主方法 - 测试VideoSplitter和AudioExtractor类的功能
//     *
//     * @param args 命令行参数
//     */
//    public static void main(String[] args) {
//        // 指定要处理的视频文件路径
//        String videoPath = "D:/桌面杂项/abc/QQ2025530-2125-HD.mp4";
//
//        System.out.println("=== 开始测试视频处理功能 ===");
//        System.out.println("输入视频路径: " + videoPath);
//        System.out.println();
//
//        // 测试音频提取功能
//        testAudioExtractor(videoPath);
//
//        System.out.println();
//        System.out.println("==========================================");
//        System.out.println();
//
//        // 测试视频切分功能
//        //testVideoSplitter(videoPath);
//    }
//
//    /**
//     * 测试AudioExtractor类的音频提取功能
//     *
//     * @param videoPath 视频文件路径
//     */
//    private static void testAudioExtractor(String videoPath) {
//        System.out.println("=== 开始测试AudioExtractor类 ===");
//        System.out.println("输入视频: " + videoPath);
//        System.out.println();
//
//        try {
//            // 调用音频提取方法
//            System.out.println("正在从视频中提取音频...");
//            long startTime = System.currentTimeMillis();
//
//            String audioPath = extractAudio(videoPath);
//
//            long endTime = System.currentTimeMillis();
//            long duration = (endTime - startTime) / 1000;
//
//            // 输出提取结果
//            System.out.println();
//            System.out.println("音频提取完成！");
//            System.out.println("提取的音频文件: " + audioPath);
//            System.out.println("处理耗时: " + duration + " 秒");
//            System.out.println("=== AudioExtractor测试完成 ===");
//
//        } catch (Exception e) {
//            System.err.println();
//            System.err.println("音频提取过程中发生错误:");
//            System.err.println("错误类型: " + e.getClass().getSimpleName());
//            System.err.println("错误信息: " + e.getMessage());
//            System.err.println();
//            System.err.println("详细错误堆栈:");
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 测试VideoSplitter类的视频切分功能
//     *
//     * @param videoPath 视频文件路径
//     */
//    private static void testVideoSplitter(String videoPath) {
//        System.out.println("=== 开始测试VideoSplitter类 ===");
//
//        try {
//            // 创建VideoSplitter实例
//            VideoSplitter videoSplitter = new VideoSplitter();
//
//            // 调用视频切分方法
//            System.out.println("正在切分视频...");
//            List<String> segmentPaths = videoSplitter.splitVideo(videoPath);
//
//            // 输出切分结果
//            System.out.println("视频切分完成！");
//            System.out.println("总共生成 " + segmentPaths.size() + " 个视频片段:");
//            System.out.println();
//
//            // 逐个显示切分后的视频片段路径
//            for (int i = 0; i < segmentPaths.size(); i++) {
//                System.out.println("片段 " + (i + 1) + ": " + segmentPaths.get(i));
//            }
//
//            System.out.println();
//            System.out.println("=== VideoSplitter测试完成 ===");
//
//        } catch (Exception e) {
//            System.err.println("视频切分过程中发生错误:");
//            System.err.println("错误信息: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//}