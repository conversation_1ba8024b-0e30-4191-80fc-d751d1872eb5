package com.ruoyi.wenshumultimodal.controller;

import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.dashscope.exception.UploadFileException;
import com.ruoyi.wenshuapi.client.file.FileStorageClient;
import com.ruoyi.wenshumultimodal.service.MultiModalService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.wenshuapi.util.file.ApiResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

@RestController
@RequestMapping("/api/ocr")
@RequiredArgsConstructor
public class OcrController {
    private final MultiModalService multiModalService;
    private final FileStorageClient fileStorageClient;
    @PostMapping("/parse/imag")
    public ApiResponse<String> parseImage(@RequestParam("file") MultipartFile file,
                                          @RequestParam(required = false, value = "prompt")String  prompt,
                                          @RequestParam(value = "userId") int  userId) throws NoApiKeyException, UploadFileException, IOException {
        String accessUrl=getfilePath(file, userId);

        String content = "图片解析出来的内容为:"+multiModalService.imageMultiModalConversationCall(accessUrl,prompt);
        return ApiResponse.success(content);
    }
    public String getfilePath(MultipartFile file,int userId){
        ApiResponse<Map<String, Object>> mapApiResponse = fileStorageClient.uploadFile(file, userId, null, null);
        // 将Windows路径分隔符统一转换为Unix风格的正斜杠，以避免URI解析错误
        String filePath = mapApiResponse.getData().get("filePath").toString();
        String normalizedPath = filePath.replace("\\", "/");
        String accessUrl = "file:///D:/wenshu/file-storage/" + normalizedPath;
        return accessUrl;
    }
}
