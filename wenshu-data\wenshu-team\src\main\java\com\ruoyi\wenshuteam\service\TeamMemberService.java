package com.ruoyi.wenshuteam.service;

import com.ruoyi.wenshuapi.pojo.team.TeamUserRelation;

import java.util.List;

/**
 * 团队-成员关系服务层接口
 * 提供团队与成员关联关系的业务逻辑操作
 */
public interface TeamMemberService {

    /**
     * 添加成员到团队
     *
     * @param relation 团队-用户关系对象
     * @return 添加成功返回 true，失败返回 false
     * @throws IllegalArgumentException 如果参数无效（如ID为负值）
     */
    boolean addMemberToTeam(TeamUserRelation relation);

    /**
     * 从团队中移除成员
     *
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 移除成功返回 true，成员不存在返回 false
     * @throws IllegalArgumentException 如果参数无效
     */
    boolean removeMemberFromTeam(int teamId, int userId);

    /**
     * 解散团队（移除团队所有成员）
     *
     * @param teamId 要解散的团队ID
     * @return 移除的成员数量
     * @throws IllegalArgumentException 如果团队ID无效
     */
    int disbandTeam(int teamId);

    /**
     * 移除用户的所有团队关系（用户退出所有团队）
     *
     * @param userId 用户ID
     * @return 移除的团队关系数量
     * @throws IllegalArgumentException 如果用户ID无效
     */
    int removeUserFromAllTeams(int userId);

    /**
     * 获取团队所有成员ID列表
     *
     * @param teamId 团队ID
     * @return 成员ID列表，无成员返回空列表
     * @throws IllegalArgumentException 如果团队ID无效
     */
    List<Integer> getTeamMembers(int teamId);

    /**
     * 获取用户加入的所有团队ID列表
     *
     * @param userId 用户ID
     * @return 团队ID列表，未加入任何团队返回空列表
     * @throws IllegalArgumentException 如果用户ID无效
     */
    List<Integer> getUserTeams(int userId);

    /**
     * 检查用户是否在指定团队中
     *
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 存在返回 true，不存在返回 false
     * @throws IllegalArgumentException 如果参数无效
     */
    boolean isUserInTeam(int teamId, int userId);

    /**
     * 将用户转移到新团队
     *
     * @param userId    用户ID
     * @param oldTeamId 原团队ID
     * @param newTeamId 新团队ID
     * @return 转移成功返回 true，用户不在原团队中返回 false
     * @throws IllegalArgumentException 如果参数无效
     */
    boolean transferUserToNewTeam(int userId, int oldTeamId, int newTeamId);

    /**
     * 获取团队成员数量
     *
     * @param teamId 团队ID
     * @return 团队成员数量
     * @throws IllegalArgumentException 如果团队ID无效
     */
    int countTeamMembers(int teamId);

    /**
     * 获取用户加入的团队数量
     *
     * @param userId 用户ID
     * @return 用户加入的团队数量
     * @throws IllegalArgumentException 如果用户ID无效
     */
    int countUserTeams(int userId);

    /**
     * 批量添加成员到团队
     *
     * @param teamId    团队ID
     * @param userIds   用户ID列表
     * @return 成功添加的成员数量
     * @throws IllegalArgumentException 如果参数无效
     */
    int batchAddMembersToTeam(int teamId, List<Integer> userIds);

    /**
     * 批量从团队中移除成员
     *
     * @param teamId    团队ID
     * @param userIds   用户ID列表
     * @return 成功移除的成员数量
     * @throws IllegalArgumentException 如果参数无效
     */
    int batchRemoveMembersFromTeam(int teamId, List<Integer> userIds);
}