package com.ruoyi.wenshumeeting.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 视频分析任务实体类
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Entity
@Table(name = "video_analysis")
public class VideoAnalysis {
    
    /**
     * 任务唯一标识
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    
    /**
     * 提交任务的用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Integer userId;
    
    /**
     * 视频文件的唯一标识
     */
    @Column(name = "file_id", nullable = false, length = 64)
    private String fileId;
    
    /**
     * 初始识别生成的文本内容
     */
    @Column(name = "initial_text", columnDefinition = "TEXT")
    private String initialText;
    
    /**
     * 最终分析生成的文本内容
     */
    @Column(name = "analysis_text", columnDefinition = "TEXT")
    private String analysisText;
    
    /**
     * 任务失败的具体原因
     */
    @Column(name = "failure_reason", columnDefinition = "TEXT")
    private String failureReason;
    
    /**
     * 任务状态: 0未开始 1分析中 2成功 3失败
     */
    @Column(name = "status", nullable = false)
    private Integer status = 0;
    
    /**
     * 视频提交的时间戳
     */
    @Column(name = "upload_time", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime uploadTime;

    /**
     * 分析开始处理的时间
     */
    @Column(name = "start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 任务结束的时间(成功/失败)
     */
    @Column(name = "finish_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime finishTime;
    
    // 无参构造函数
    public VideoAnalysis() {}
    
    // 全参构造函数
    public VideoAnalysis(Long id, Integer userId, String fileId, String initialText, 
                        String analysisText, String failureReason, Integer status, 
                        LocalDateTime uploadTime, LocalDateTime startTime, LocalDateTime finishTime) {
        this.id = id;
        this.userId = userId;
        this.fileId = fileId;
        this.initialText = initialText;
        this.analysisText = analysisText;
        this.failureReason = failureReason;
        this.status = status;
        this.uploadTime = uploadTime;
        this.startTime = startTime;
        this.finishTime = finishTime;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Integer getUserId() {
        return userId;
    }
    
    public void setUserId(Integer userId) {
        this.userId = userId;
    }
    
    public String getFileId() {
        return fileId;
    }
    
    public void setFileId(String fileId) {
        this.fileId = fileId;
    }
    
    public String getInitialText() {
        return initialText;
    }
    
    public void setInitialText(String initialText) {
        this.initialText = initialText;
    }
    
    public String getAnalysisText() {
        return analysisText;
    }
    
    public void setAnalysisText(String analysisText) {
        this.analysisText = analysisText;
    }
    
    public String getFailureReason() {
        return failureReason;
    }
    
    public void setFailureReason(String failureReason) {
        this.failureReason = failureReason;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public LocalDateTime getUploadTime() {
        return uploadTime;
    }
    
    public void setUploadTime(LocalDateTime uploadTime) {
        this.uploadTime = uploadTime;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalDateTime getFinishTime() {
        return finishTime;
    }
    
    public void setFinishTime(LocalDateTime finishTime) {
        this.finishTime = finishTime;
    }
    
    @Override
    public String toString() {
        return "VideoAnalysis{" +
                "id=" + id +
                ", userId=" + userId +
                ", fileId='" + fileId + '\'' +
                ", initialText='" + initialText + '\'' +
                ", analysisText='" + analysisText + '\'' +
                ", failureReason='" + failureReason + '\'' +
                ", status=" + status +
                ", uploadTime=" + uploadTime +
                ", startTime=" + startTime +
                ", finishTime=" + finishTime +
                '}';
    }
}
