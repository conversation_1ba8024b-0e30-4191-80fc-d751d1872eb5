package com.ruoyi.wenshucommon.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 对话基础实体类
 */
@TableName(value = "base_conversation", autoResultMap = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BaseConversation {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String conversationId;

    private String title;

    private String content;

    @TableField(fill = FieldFill.INSERT)
    private Date createdTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updatedTime;
} 