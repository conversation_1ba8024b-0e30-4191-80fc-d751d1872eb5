package com.ruoyi.wenshumeeting.service.impl;

import com.ruoyi.wenshumeeting.dao.VideoAnalysisDao;
import com.ruoyi.wenshumeeting.pojo.VideoAnalysis;
import com.ruoyi.wenshumeeting.service.VideoAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 视频分析任务服务层实现类
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
@Transactional
public class VideoAnalysisServiceImpl implements VideoAnalysisService {
    
    @Autowired
    private VideoAnalysisDao videoAnalysisDao;
    
    @Override
    public VideoAnalysis save(VideoAnalysis videoAnalysis) {
        return videoAnalysisDao.save(videoAnalysis);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Optional<VideoAnalysis> findById(Long id) {
        return videoAnalysisDao.findById(id);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<VideoAnalysis> findAll() {
        return videoAnalysisDao.findAll();
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<VideoAnalysis> findByUserId(Integer userId) {
        return videoAnalysisDao.findByUserId(userId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Optional<VideoAnalysis> findByFileId(String fileId) {
        return videoAnalysisDao.findByFileId(fileId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<VideoAnalysis> findByStatus(Integer status) {
        return videoAnalysisDao.findByStatus(status);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<VideoAnalysis> findByUserIdAndStatus(Integer userId, Integer status) {
        return videoAnalysisDao.findByUserIdAndStatus(userId, status);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<VideoAnalysis> findByUploadTimeBetween(LocalDateTime startTime, LocalDateTime endTime) {
        return videoAnalysisDao.findByUploadTimeBetween(startTime, endTime);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<VideoAnalysis> findByUserIdAndUploadTimeBetween(Integer userId, LocalDateTime startTime, LocalDateTime endTime) {
        return videoAnalysisDao.findByUserIdAndUploadTimeBetween(userId, startTime, endTime);
    }
    
    @Override
    public VideoAnalysis update(VideoAnalysis videoAnalysis) {
        return videoAnalysisDao.save(videoAnalysis);
    }
    
    @Override
    public boolean updateStatus(Long id, Integer status) {
        int result = videoAnalysisDao.updateStatus(id, status);
        return result > 0;
    }
    
    @Override
    public boolean startTask(Long id) {
        int result = videoAnalysisDao.updateStatusAndStartTime(id, 1, LocalDateTime.now());
        return result > 0;
    }
    
    @Override
    public boolean completeTask(Long id, String analysisText) {
        int result = videoAnalysisDao.updateTaskCompletion(id, 2, analysisText, LocalDateTime.now());
        return result > 0;
    }
    
    @Override
    public boolean failTask(Long id, String failureReason) {
        int result = videoAnalysisDao.updateTaskFailure(id, 3, failureReason, LocalDateTime.now());
        return result > 0;
    }
    
    @Override
    public boolean deleteById(Long id) {
        if (videoAnalysisDao.existsById(id)) {
            videoAnalysisDao.deleteById(id);
            return true;
        }
        return false;
    }
    
    @Override
    public int deleteByUserId(Integer userId) {
        return videoAnalysisDao.deleteByUserId(userId);
    }
    
    @Override
    public int deleteByFileId(String fileId) {
        return videoAnalysisDao.deleteByFileId(fileId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean existsById(Long id) {
        return videoAnalysisDao.existsById(id);
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean existsByFileId(String fileId) {
        return videoAnalysisDao.findByFileId(fileId).isPresent();
    }
    
    @Override
    @Transactional(readOnly = true)
    public long countByUserId(Integer userId) {
        return videoAnalysisDao.countByUserId(userId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public long countByStatus(Integer status) {
        return videoAnalysisDao.countByStatus(status);
    }
    
    @Override
    @Transactional(readOnly = true)
    public long countByUserIdAndStatus(Integer userId, Integer status) {
        return videoAnalysisDao.countByUserIdAndStatus(userId, status);
    }
    
    @Override
    public VideoAnalysis createTask(Integer userId, String fileId, String initialText) {
        VideoAnalysis videoAnalysis = new VideoAnalysis();
        videoAnalysis.setUserId(userId);
        videoAnalysis.setFileId(fileId);
        videoAnalysis.setInitialText(initialText);
        videoAnalysis.setStatus(0); // 未开始
        videoAnalysis.setUploadTime(LocalDateTime.now());
        
        return videoAnalysisDao.save(videoAnalysis);
    }
}
