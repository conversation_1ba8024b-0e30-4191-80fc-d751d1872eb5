package com.ruoyi.wenshucommon.util.voiceutil;

import javax.sound.sampled.*;
import java.io.*;
import java.nio.file.*;
import java.util.Random;
import java.util.Set;

/**
 * 纯Java音频格式转换工具类
 * 支持将各种音频格式转换为标准的WAV格式
 */
public class PureJavaAudioConverter {

    // 支持的输入格式集合
    private static final Set<String> SUPPORTED_FORMATS = Set.of(
            "mp3", "wav", "ogg", "flac", "aac", "m4a", "wma"
    );

    // 目标格式配置
    private static final float TARGET_SAMPLE_RATE = 44100;
    private static final int TARGET_SAMPLE_SIZE = 16;
    private static final AudioFormat.Encoding TARGET_ENCODING = AudioFormat.Encoding.PCM_SIGNED;

    /**
     * 将音频文件转换为WAV格式
     * 
     * @param sourcePath 源音频文件路径
     * @return 转换后的WAV文件路径
     */
    public static Path convertToWav(Path sourcePath)
            throws UnsupportedAudioFileException, IOException, LineUnavailableException {

        // 验证文件格式
        String fileExt = getFileExtension(sourcePath).toLowerCase();
        if (!SUPPORTED_FORMATS.contains(fileExt)) {
            throw new UnsupportedAudioFileException("不支持的音频格式: " + fileExt);
        }

        Path tempTargetPath = null;
        Path finalPath = null;

        try {
            // 1. 读取原始音频流
            try (AudioInputStream origStream = AudioSystem.getAudioInputStream(sourcePath.toFile())) {
                AudioFormat sourceFormat = origStream.getFormat();

                // 2. 创建目标格式（PCM 44.1kHz 16bit）
                AudioFormat targetFormat = createTargetFormat(sourceFormat);

                // 3. 检查是否需要格式转换
                if(isSameFormat(sourceFormat, targetFormat)){
                    // 3.1 格式相同，直接复制文件
                    tempTargetPath = createTempWavPath(sourcePath);
                    Files.copy(sourcePath, tempTargetPath, StandardCopyOption.REPLACE_EXISTING);
                } else {
                    // 3.2 需要格式转换
                    try (AudioInputStream convertedStream = AudioSystem.getAudioInputStream(targetFormat, origStream)) {
                        // 4. 创建临时文件路径
                        tempTargetPath = createTempWavPath(sourcePath);

                        // 5. 直接写入文件（自动处理未知长度）
                        AudioSystem.write(convertedStream, AudioFileFormat.Type.WAVE, tempTargetPath.toFile());
                    }
                }

                // 6. 生成唯一文件名并移动文件
                finalPath = generateUniqueWavPath(tempTargetPath);
                return finalPath;
            }
        } finally {
            // 7. 清理源文件和临时文件
            Files.deleteIfExists(sourcePath);
            if (tempTargetPath != null && !tempTargetPath.equals(finalPath)) {
                Files.deleteIfExists(tempTargetPath);
            }
        }
    }

    // 获取文件扩展名
    private static String getFileExtension(Path path) {
        String fileName = path.getFileName().toString();
        int dotIndex = fileName.lastIndexOf('.');
        return (dotIndex == -1) ? "" : fileName.substring(dotIndex + 1);
    }

    // 检查两个格式是否相同
    private static boolean isSameFormat(AudioFormat format1, AudioFormat format2) {
        return format1.getEncoding().equals(format2.getEncoding()) &&
                format1.getSampleRate() == format2.getSampleRate() &&
                format1.getSampleSizeInBits() == format2.getSampleSizeInBits() &&
                format1.getChannels() == format2.getChannels() &&
                format1.getFrameSize() == format2.getFrameSize() &&
                format1.getFrameRate() == format2.getFrameRate() &&
                format1.isBigEndian() == format2.isBigEndian();
    }

    // 创建目标格式
    private static AudioFormat createTargetFormat(AudioFormat sourceFormat) {
        return new AudioFormat(
                TARGET_ENCODING,
                TARGET_SAMPLE_RATE,
                TARGET_SAMPLE_SIZE,
                sourceFormat.getChannels(),
                sourceFormat.getChannels() * (TARGET_SAMPLE_SIZE / 8),
                TARGET_SAMPLE_RATE,
                false
        );
    }

    // 创建临时WAV路径
    private static Path createTempWavPath(Path sourcePath) {
        return sourcePath.resolveSibling(
                "temp_" + System.currentTimeMillis() + "_" +
                        Thread.currentThread().getId() + ".wav"
        );
    }

    // 生成唯一WAV文件名
    private static Path generateUniqueWavPath(Path tempPath) throws IOException {
        final int MAX_RETRIES = 100;
        int retryCount = 0;
        Path newPath;

        do {
            String newFilename = generateRandomFilename();
            newPath = tempPath.resolveSibling(newFilename);

            if (Files.exists(newPath)) {
                if (retryCount++ >= MAX_RETRIES) {
                    throw new IOException("无法生成唯一文件名，达到最大重试次数");
                }
            } else {
                Files.move(tempPath, newPath, StandardCopyOption.ATOMIC_MOVE);
                return newPath;
            }
        } while (true);
    }

    // 生成随机文件名
    private static String generateRandomFilename() {
        Random random = new Random();
        StringBuilder sb = new StringBuilder();

        // 生成8位随机数
        for (int i = 0; i < 8; i++) {
            sb.append(random.nextInt(10));
        }

        // 生成6位大写字母
        for (int i = 0; i < 6; i++) {
            sb.append((char) (random.nextInt(26) + 'A'));
        }

        return sb.toString() + ".wav";
    }
} 