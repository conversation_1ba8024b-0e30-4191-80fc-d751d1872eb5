# Wenshu-Team 团队协作模块 - 详细API接口文档

## 📋 模块概述

**服务名称**: wenshu-team  
**服务端口**: 8705  
**版本**: v1.0.0  
**最后更新**: 2024-12-28  

**核心功能**: 团队协作服务，提供团队管理、项目协作、任务分配、进度跟踪、团队沟通等功能

**技术栈**:
- Spring Boot 3.x
- Spring Cloud 2023.x
- WebSocket (实时协作)
- Redis (状态管理)
- MySQL (数据存储)
- Elasticsearch (搜索引擎)
- Spring Data JPA

---

## 🏗️ 服务架构

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                Wenshu-Team 团队协作模块                      │
│                        (Port: 8705)                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│   团队管理     │ │项目协作  │ │ 任务管理   │
│ (Team Mgmt)   │ │(Project)│ │(Task)     │
└───────────────┘ └────────┘ └───────────┘
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│ 成员权限       │ │进度跟踪  │ │ 实时协作   │
│ (Permission)  │ │(Progress)│ │(Realtime) │
└───────────────┘ └────────┘ └───────────┘
```

### 服务依赖关系
- **wenshu-base**: 用户信息和基础权限
- **wenshu-chat**: 团队沟通和AI助手
- **wenshu-file**: 文件共享和协作
- **WebSocket**: 实时状态同步

---

## 🌐 REST API接口

### 基础信息
- **Base URL**: `http://localhost:8705`
- **Content-Type**: `application/json`
- **鉴权方式**: JWT Token (Header: `Authorization: Bearer <token>`)

---

## 👥 团队管理API

### 1. 团队基础操作

#### 1.1 创建团队
**接口路径**: `POST /api/teams/create`

**功能描述**: 创建新的团队

**请求参数**:
```http
POST /api/teams/create
Content-Type: application/json

{
  "teamName": "产品研发团队",
  "description": "负责产品设计和开发工作",
  "teamType": "development",
  "isPublic": false,
  "maxMembers": 50,
  "settings": {
    "allowInvite": true,
    "requireApproval": true,
    "allowGuestAccess": false
  }
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "团队创建成功",
  "data": {
    "teamId": "team_12345",
    "teamName": "产品研发团队",
    "description": "负责产品设计和开发工作",
    "teamType": "development",
    "creator": {
      "userId": "1001",
      "userName": "张三",
      "role": "owner"
    },
    "createTime": "2024-12-28T15:30:00",
    "memberCount": 1,
    "maxMembers": 50,
    "isPublic": false,
    "status": "active",
    "inviteCode": "TEAM2024",
    "settings": {
      "allowInvite": true,
      "requireApproval": true,
      "allowGuestAccess": false
    }
  },
  "timestamp": 1640995200000
}
```

#### 1.2 获取团队列表
**接口路径**: `GET /api/teams`

**功能描述**: 获取用户所属的团队列表

**请求参数**:
```http
GET /api/teams?page=1&size=20&teamType=development&status=active
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "teams": [
      {
        "teamId": "team_12345",
        "teamName": "产品研发团队",
        "description": "负责产品设计和开发工作",
        "teamType": "development",
        "memberCount": 15,
        "maxMembers": 50,
        "userRole": "member",
        "isPublic": false,
        "status": "active",
        "createTime": "2024-12-28T15:30:00",
        "lastActivity": "2024-12-28T16:45:00",
        "avatar": "/avatars/team_12345.jpg"
      }
    ],
    "summary": {
      "totalTeams": 5,
      "ownedTeams": 2,
      "joinedTeams": 3,
      "activeProjects": 8
    },
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 5,
      "totalPages": 1
    }
  },
  "timestamp": 1640995200000
}
```

#### 1.3 获取团队详情
**接口路径**: `GET /api/teams/{teamId}`

**功能描述**: 获取团队的详细信息

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "teamId": "team_12345",
    "teamName": "产品研发团队",
    "description": "负责产品设计和开发工作",
    "teamType": "development",
    "creator": {
      "userId": "1001",
      "userName": "张三",
      "email": "<EMAIL>"
    },
    "createTime": "2024-12-28T15:30:00",
    "updateTime": "2024-12-28T16:00:00",
    "memberCount": 15,
    "maxMembers": 50,
    "isPublic": false,
    "status": "active",
    "inviteCode": "TEAM2024",
    "avatar": "/avatars/team_12345.jpg",
    "settings": {
      "allowInvite": true,
      "requireApproval": true,
      "allowGuestAccess": false,
      "defaultRole": "member"
    },
    "statistics": {
      "totalProjects": 5,
      "activeProjects": 3,
      "completedProjects": 2,
      "totalTasks": 45,
      "completedTasks": 32,
      "totalFiles": 128,
      "totalStorage": "2.5GB"
    },
    "recentActivity": [
      {
        "activityId": "activity_001",
        "type": "task_completed",
        "description": "李四完成了任务：UI设计优化",
        "actor": "李四",
        "timestamp": "2024-12-28T16:45:00"
      }
    ]
  },
  "timestamp": 1640995200000
}
```

#### 1.4 更新团队信息
**接口路径**: `PUT /api/teams/{teamId}`

**功能描述**: 更新团队信息

**请求参数**:
```http
PUT /api/teams/team_12345
Content-Type: application/json

{
  "teamName": "产品研发团队(更新)",
  "description": "负责产品设计、开发和测试工作",
  "maxMembers": 60,
  "settings": {
    "allowInvite": true,
    "requireApproval": false,
    "allowGuestAccess": true
  }
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "团队信息更新成功",
  "data": {
    "teamId": "team_12345",
    "teamName": "产品研发团队(更新)",
    "description": "负责产品设计、开发和测试工作",
    "maxMembers": 60,
    "updateTime": "2024-12-28T17:00:00",
    "settings": {
      "allowInvite": true,
      "requireApproval": false,
      "allowGuestAccess": true
    }
  },
  "timestamp": 1640995200000
}
```

---

## 👤 成员管理API

### 2. 团队成员操作

#### 2.1 邀请成员
**接口路径**: `POST /api/teams/{teamId}/members/invite`

**功能描述**: 邀请用户加入团队

**请求参数**:
```http
POST /api/teams/team_12345/members/invite
Content-Type: application/json

{
  "inviteType": "email",
  "inviteTargets": ["<EMAIL>", "<EMAIL>"],
  "role": "member",
  "message": "欢迎加入我们的产品研发团队！",
  "expireTime": "2024-12-31T23:59:59"
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "邀请发送成功",
  "data": {
    "teamId": "team_12345",
    "invitations": [
      {
        "invitationId": "invite_001",
        "inviteTarget": "<EMAIL>",
        "role": "member",
        "status": "sent",
        "inviteTime": "2024-12-28T17:00:00",
        "expireTime": "2024-12-31T23:59:59",
        "inviteLink": "https://wenshu.com/invite/invite_001"
      },
      {
        "invitationId": "invite_002",
        "inviteTarget": "<EMAIL>",
        "role": "member",
        "status": "sent",
        "inviteTime": "2024-12-28T17:00:00",
        "expireTime": "2024-12-31T23:59:59",
        "inviteLink": "https://wenshu.com/invite/invite_002"
      }
    ],
    "successCount": 2,
    "failedCount": 0
  },
  "timestamp": 1640995200000
}
```

#### 2.2 获取团队成员
**接口路径**: `GET /api/teams/{teamId}/members`

**功能描述**: 获取团队成员列表

**请求参数**:
```http
GET /api/teams/team_12345/members?page=1&size=20&role=member&status=active&keyword=张
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "teamId": "team_12345",
    "members": [
      {
        "userId": "1001",
        "userName": "张三",
        "realName": "张三",
        "email": "<EMAIL>",
        "avatar": "/avatars/1001.jpg",
        "role": "owner",
        "status": "active",
        "joinTime": "2024-12-28T15:30:00",
        "lastActivity": "2024-12-28T16:45:00",
        "permissions": ["manage_team", "manage_members", "manage_projects"],
        "statistics": {
          "completedTasks": 15,
          "activeProjects": 3,
          "contributionScore": 95
        }
      },
      {
        "userId": "1002",
        "userName": "李四",
        "realName": "李四",
        "email": "<EMAIL>",
        "avatar": "/avatars/1002.jpg",
        "role": "member",
        "status": "active",
        "joinTime": "2024-12-25T10:00:00",
        "lastActivity": "2024-12-28T16:30:00",
        "permissions": ["view_projects", "manage_tasks"],
        "statistics": {
          "completedTasks": 8,
          "activeProjects": 2,
          "contributionScore": 78
        }
      }
    ],
    "summary": {
      "totalMembers": 15,
      "activeMembers": 14,
      "inactiveMembers": 1,
      "roleDistribution": {
        "owner": 1,
        "admin": 2,
        "member": 12
      }
    },
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 15,
      "totalPages": 1
    }
  },
  "timestamp": 1640995200000
}
```

#### 2.3 更新成员角色
**接口路径**: `PUT /api/teams/{teamId}/members/{userId}/role`

**功能描述**: 更新团队成员的角色

**请求参数**:
```http
PUT /api/teams/team_12345/members/1002/role
Content-Type: application/json

{
  "role": "admin",
  "reason": "表现优秀，提升为管理员"
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "成员角色更新成功",
  "data": {
    "teamId": "team_12345",
    "userId": "1002",
    "userName": "李四",
    "oldRole": "member",
    "newRole": "admin",
    "updateTime": "2024-12-28T17:15:00",
    "updatedBy": "张三",
    "reason": "表现优秀，提升为管理员",
    "newPermissions": ["manage_members", "manage_projects", "view_analytics"]
  },
  "timestamp": 1640995200000
}
```

---

*文档版本: v1.0.0 | 最后更新: 2024-12-28 | Wenshu-Team团队协作模块*
