package com.ruoyi.wenshucommon.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.wenshucommon.dao.ConversationDao;
import com.ruoyi.wenshucommon.entity.BaseConversation;
import com.ruoyi.wenshucommon.service.ConversationService;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class ConversationServiceImpl extends ServiceImpl<ConversationDao, BaseConversation> implements ConversationService {
    /**
     * 获取单个对话，使用Optional包装
     */
    @Override
    public Optional<BaseConversation> getOneOpt(Wrapper<BaseConversation> queryWrapper) {
        return Optional.ofNullable(getOne(queryWrapper, false));
    }
}
