package com.ruoyi.wenshuapi.dto.file;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文件上传响应DTO
 * 封装文件上传成功后的响应数据
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-28
 */
@Data
public class FileUploadResponseDto {

    /**
     * 文件ID
     */
    private Integer fileId;

    /**
     * 原始文件名
     */
    private String fileName;

    /**
     * 服务器存储路径
     */
    private String filePath;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 格式化的文件大小
     */
    private String fileSizeFormatted;

    /**
     * 文件MIME类型
     */
    private String mimeType;

    /**
     * 文件访问URL
     */
    private String accessUrl;

    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;

    /**
     * 上传者ID
     */
    private Integer uploaderId;

    /**
     * 所有者类型
     */
    private String ownerType;

    /**
     * 文件状态
     */
    private String fileStatus;

    /**
     * 文件MD5哈希值（可选）
     */
    private String md5Hash;

    /**
     * 文件扩展名
     */
    private String fileExtension;

    /**
     * 是否为图片
     */
    private Boolean isImage;

    /**
     * 是否为视频
     */
    private Boolean isVideo;

    /**
     * 是否为音频
     */
    private Boolean isAudio;

    /**
     * 是否为文档
     */
    private Boolean isDocument;
}
