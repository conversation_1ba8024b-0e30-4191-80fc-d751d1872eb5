package com.ruoyi.wenshucommon.util.voiceutil;

import java.nio.file.Files;
import java.nio.file.Path;

/**
 * 文件清理工具类
 * 用于延迟删除临时文件
 */
public class FileCleaner {
    /**
     * 延迟删除文件
     *
     * @param filePath 文件路径
     * @param delayMillis 延迟时间(毫秒)
     */
    public static void scheduleDeletion(Path filePath, long delayMillis) {
        if (filePath != null && Files.exists(filePath)) {
            new Thread(() -> {
                try {
                    Thread.sleep(delayMillis);
                    Files.deleteIfExists(filePath);
                } catch (Exception e) {
                    System.err.println("延迟删除失败: " + filePath);
                }
            }).start();
        }
    }
} 