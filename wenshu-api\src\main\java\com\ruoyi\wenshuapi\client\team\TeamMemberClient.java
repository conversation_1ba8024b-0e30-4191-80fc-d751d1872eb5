package com.ruoyi.wenshuapi.client.team;

import com.ruoyi.wenshuapi.pojo.team.TeamUserRelation;
import com.ruoyi.wenshuapi.util.file.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 团队-成员关系 Feign 客户端接口
 * 提供对团队成员服务的远程调用支持
 *
 * 使用说明：
 * 1. 在需要调用团队成员服务的服务中注入此接口
 * 2. 直接调用接口方法进行远程调用
 *
 * 注意：需要确保服务注册中心中存在名为 "wenshu-team" 的服务
 */
@FeignClient(name = "wenshu-team", contextId = "teamMemberClient")
public interface TeamMemberClient {

    /**
     * 添加成员到团队 (对应 POST /wenshu/teammember/add)
     *
     * @param relation 团队-用户关系对象
     * @return 操作结果
     */
    @PostMapping("/wenshu/teammember/add")
    ApiResponse<Boolean> addMemberToTeam(@RequestBody TeamUserRelation relation);

    /**
     * 从团队中移除成员 (对应 DELETE /wenshu/teammember/remove)
     *
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 操作结果
     */
    @DeleteMapping("/wenshu/teammember/remove")
    ApiResponse<Boolean> removeMemberFromTeam(
            @RequestParam("teamId") int teamId,
            @RequestParam("userId") int userId);

    /**
     * 解散团队 (对应 DELETE /wenshu/teammember/disband/{teamId})
     *
     * @param teamId 团队ID
     * @return 操作结果
     */
    @DeleteMapping("/wenshu/teammember/disband/{teamId}")
    ApiResponse<Integer> disbandTeam(@PathVariable("teamId") int teamId);

    /**
     * 移除用户的所有团队关系 (对应 DELETE /wenshu/teammember/removeuser/{userId})
     *
     * @param userId 用户ID
     * @return 操作结果
     */
    @DeleteMapping("/wenshu/teammember/removeuser/{userId}")
    ApiResponse<Integer> removeUserFromAllTeams(@PathVariable("userId") int userId);

    /**
     * 获取团队所有成员ID列表 (对应 GET /wenshu/teammember/members/{teamId})
     *
     * @param teamId 团队ID
     * @return 成员ID列表
     */
    @GetMapping("/wenshu/teammember/members/{teamId}")
    ApiResponse<List<Integer>> getTeamMembers(@PathVariable("teamId") int teamId);

    /**
     * 获取用户加入的所有团队ID列表 (对应 GET /wenshu/teammember/teams/{userId})
     *
     * @param userId 用户ID
     * @return 团队ID列表
     */
    @GetMapping("/wenshu/teammember/teams/{userId}")
    ApiResponse<List<Integer>> getUserTeams(@PathVariable("userId") int userId);

    /**
     * 检查用户是否在指定团队中 (对应 GET /wenshu/teammember/check)
     *
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 检查结果
     */
    @GetMapping("/wenshu/teammember/check")
    ApiResponse<Boolean> isUserInTeam(
            @RequestParam("teamId") int teamId,
            @RequestParam("userId") int userId);

    /**
     * 将用户转移到新团队 (对应 PUT /wenshu/teammember/transfer)
     *
     * @param userId    用户ID
     * @param oldTeamId 原团队ID
     * @param newTeamId 新团队ID
     * @return 操作结果
     */
    @PutMapping("/wenshu/teammember/transfer")
    ApiResponse<Boolean> transferUserToNewTeam(
            @RequestParam("userId") int userId,
            @RequestParam("oldTeamId") int oldTeamId,
            @RequestParam("newTeamId") int newTeamId);

    /**
     * 获取团队成员数量 (对应 GET /wenshu/teammember/count/members/{teamId})
     *
     * @param teamId 团队ID
     * @return 成员数量
     */
    @GetMapping("/wenshu/teammember/count/members/{teamId}")
    ApiResponse<Integer> countTeamMembers(@PathVariable("teamId") int teamId);

    /**
     * 获取用户加入的团队数量 (对应 GET /wenshu/teammember/count/teams/{userId})
     *
     * @param userId 用户ID
     * @return 团队数量
     */
    @GetMapping("/wenshu/teammember/count/teams/{userId}")
    ApiResponse<Integer> countUserTeams(@PathVariable("userId") int userId);

    /**
     * 批量添加成员到团队 (对应 POST /wenshu/teammember/batch/add)
     *
     * @param teamId  团队ID
     * @param userIds 用户ID列表
     * @return 成功添加的数量
     */
    @PostMapping("/wenshu/teammember/batch/add")
    ApiResponse<Integer> batchAddMembersToTeam(
            @RequestParam("teamId") int teamId,
            @RequestBody List<Integer> userIds);

    /**
     * 批量从团队中移除成员 (对应 DELETE /wenshu/teammember/batch/remove)
     *
     * @param teamId  团队ID
     * @param userIds 用户ID列表
     * @return 成功移除的数量
     */
    @DeleteMapping("/wenshu/teammember/batch/remove")
    ApiResponse<Integer> batchRemoveMembersFromTeam(
            @RequestParam("teamId") int teamId,
            @RequestBody List<Integer> userIds);
}