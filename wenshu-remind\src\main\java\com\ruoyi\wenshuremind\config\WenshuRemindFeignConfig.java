package com.ruoyi.wenshuremind.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import feign.codec.Decoder;
import feign.codec.Encoder;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

/**
 * Feign客户端配置类
 * 配置Jackson以支持Java 8时间类型的序列化和反序列化
 */
@Configuration
public class WenshuRemindFeignConfig {

    /**
     * 配置支持Java 8时间类型的ObjectMapper
     */
    @Bean(name = "wenshuRemindObjectMapper")
    public ObjectMapper wenshuRemindObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        // 注册JavaTimeModule以支持Java 8时间类型
        objectMapper.registerModule(new JavaTimeModule());

        // 禁用将日期写成时间戳
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        return objectMapper;
    }

    /**
     * 配置Jackson HTTP消息转换器
     */
    @Bean(name = "wenshuRemindMappingJackson2HttpMessageConverter")
    public MappingJackson2HttpMessageConverter wenshuRemindMappingJackson2HttpMessageConverter() {
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        converter.setObjectMapper(wenshuRemindObjectMapper());
        return converter;
    }

    /**
     * 配置Feign解码器
     */
    @Bean(name = "wenshuRemindFeignDecoder")
    public Decoder wenshuRemindFeignDecoder() {
        HttpMessageConverter<?> jacksonConverter = wenshuRemindMappingJackson2HttpMessageConverter();
        ObjectFactory<HttpMessageConverters> objectFactory = () -> new HttpMessageConverters(jacksonConverter);
        return new ResponseEntityDecoder(new SpringDecoder(objectFactory));
    }

    /**
     * 配置Feign编码器
     */
    @Bean(name = "wenshuRemindFeignEncoder")
    public Encoder wenshuRemindFeignEncoder() {
        HttpMessageConverter<?> jacksonConverter = wenshuRemindMappingJackson2HttpMessageConverter();
        ObjectFactory<HttpMessageConverters> objectFactory = () -> new HttpMessageConverters(jacksonConverter);
        return new SpringEncoder(objectFactory);
    }
}
