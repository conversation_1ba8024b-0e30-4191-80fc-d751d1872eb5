package com.ruoyi.wenshucommon.util.voiceutil;

import com.alibaba.dashscope.audio.asr.translation.TranslationRecognizerParam;
import com.alibaba.dashscope.audio.asr.translation.TranslationRecognizerRealtime;
import com.alibaba.dashscope.audio.asr.translation.results.TranslationRecognizerResult;
import com.alibaba.dashscope.common.ResultCallback;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.file.Path;
import java.util.concurrent.*;

/**
 * 实时语音翻译任务类
 * 实现 Callable 接口以支持返回值
 * 传入文件路径即可接收返回内容
 */
public class RealtimeTranslateTask implements Callable<String[]> {
    private static final Logger logger = LoggerFactory.getLogger(RealtimeTranslateTask.class);

    private final Path filepath;
    private final String targetLanguage = "en";
    private final StringBuilder transcription = new StringBuilder();
    private final StringBuilder translation = new StringBuilder();
    private final CountDownLatch completionLatch = new CountDownLatch(1);
    private String form = null;

    /**
     * 构造函数
     * 
     * @param filepath 音频文件路径
     * @param form 音频格式
     */
    public RealtimeTranslateTask(Path filepath, String form) {
        this.filepath = filepath;
        this.form = form;
    }

    @Override
    public String[] call() throws Exception {
        Path processedFilePath = filepath;
        boolean needsCleanup = false;

        try {
            String audioFormat = form; // 默认使用原始格式

            // 检查音频声道数，如果是双声道或采样率不是16kHz则转换
            try {
                int channelCount = AudioConverter.getChannelCount(filepath);
                int originalSampleRate = AudioConverter.getSampleRate(filepath);
                logger.info("音频文件声道数: {}, 采样率: {} Hz", channelCount, originalSampleRate);

                // 如果是双声道或采样率不是16kHz，需要转换
                if (channelCount > 1 || originalSampleRate != 16000) {
                    logger.info("检测到需要转换的音频格式，开始转换为单声道16kHz WAV");
                    processedFilePath = AudioConverter.convertToMono(filepath);
                    needsCleanup = true;
                    audioFormat = "wav"; // 转换后的格式是WAV
                    logger.info("音频转换完成，使用转换后的文件: {}，格式: {}", processedFilePath, audioFormat);
                }
            } catch (Exception e) {
                logger.warn("音频格式检查失败，尝试直接使用原文件: {}", e.getMessage());
                // 如果检查失败，继续使用原文件，让API返回具体错误
            }

            // 阿里云API要求16kHz采样率，我们已经在本地转换了音频
            TranslationRecognizerParam param = TranslationRecognizerParam.builder()
                    .apiKey("sk-574f46304e5c4405aa5bbe26af6489b0")
                    .model("gummy-realtime-v1")
                    .format(audioFormat)  // 使用正确的音频格式
                    .sampleRate(16000)    // 固定使用16kHz，因为我们已经转换了音频
                    .transcriptionEnabled(true)
                    .sourceLanguage("auto")
                    .translationEnabled(true)
                    .translationLanguages(new String[]{targetLanguage})
                    .build();

            TranslationRecognizerRealtime translator = new TranslationRecognizerRealtime();

        ResultCallback<TranslationRecognizerResult> callback = new ResultCallback<>() {
            @Override
            public void onEvent(TranslationRecognizerResult result) {
                // 处理转写结果
                if (result.getTranscriptionResult() != null) {
                    String currentTranscription = result.getTranscriptionResult().getText();
                    if (result.isSentenceEnd()) {
                        transcription.append(currentTranscription).append("\n");
                    } else {
                        updateLastLine(transcription, currentTranscription);
                    }
                }

                // 处理翻译结果
                if (result.getTranslationResult() != null) {
                    String currentTranslation = result.getTranslationResult()
                            .getTranslation(targetLanguage)
                            .getText();

                    if (result.isSentenceEnd()) {
                        translation.append(currentTranslation).append("\n");
                    } else {
                        updateLastLine(translation, currentTranslation);
                    }
                }
            }

            @Override
            public void onComplete() {
                completionLatch.countDown();
            }

            @Override
            public void onError(Exception e) {
                e.printStackTrace();
                completionLatch.countDown();
            }
        };

            translator.call(param, callback);

            try (FileInputStream fis = new FileInputStream(processedFilePath.toFile())) {
                byte[] buffer = new byte[3200];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    ByteBuffer byteBuffer = ByteBuffer.wrap(buffer, 0, bytesRead);
                    translator.sendAudioFrame(byteBuffer);
                    Thread.sleep(100);
                }
            } catch (Exception e) {
                logger.error("音频数据发送失败: {}", e.getMessage(), e);
                throw e;
            } finally {
                translator.stop();
            }

            completionLatch.await();
            return new String[]{
                    transcription.toString(),
                    translation.toString()
            };

        } finally {
            // 清理临时转换文件
            if (needsCleanup) {
                AudioConverter.cleanupConvertedFile(processedFilePath);
            }
        }
    }

    // 更新最后一行内容的工具方法
    private void updateLastLine(StringBuilder buffer, String newContent) {
        int lastNewline = buffer.lastIndexOf("\n");
        if (lastNewline != -1) {
            buffer.replace(lastNewline + 1, buffer.length(), newContent);
        } else {
            buffer.setLength(0);
            buffer.append(newContent);
        }
    }
}
