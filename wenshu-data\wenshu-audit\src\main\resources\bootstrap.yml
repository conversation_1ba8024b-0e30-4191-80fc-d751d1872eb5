# Spring
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************
    username: root
    password: 2313147023
  application:
    # 应用名称
    name: wenshu-audit
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 8.155.31.217:8848
      config:
        # 配置中心地址
        server-addr: 8.155.31.217:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
server:
  port: 8603
# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.ruoyi.wenshuaudit.entity
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false

# 日志配置
logging:
  level:
    root: INFO
    com.ruoyi.wenshuaudit: DEBUG

# 日程服务配置
programme:
  service:
    url: http://localhost:8080
