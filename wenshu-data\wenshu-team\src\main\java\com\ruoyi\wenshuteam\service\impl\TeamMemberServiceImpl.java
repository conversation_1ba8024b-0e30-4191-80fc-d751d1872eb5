package com.ruoyi.wenshuteam.service.impl;

import com.ruoyi.wenshuapi.pojo.team.TeamUserRelation;
import com.ruoyi.wenshuteam.dao.TeamMemberDao;
import com.ruoyi.wenshuteam.service.TeamMemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 团队-成员关系服务实现类
 * 提供团队与成员关联关系的业务逻辑实现
 */
@Service
public class TeamMemberServiceImpl implements TeamMemberService {

    private final TeamMemberDao teamMemberDao;

    @Autowired
    public TeamMemberServiceImpl(TeamMemberDao teamMemberDao) {
        this.teamMemberDao = teamMemberDao;
    }

    /**
     * 添加成员到团队
     *
     * 实现逻辑：
     * 1. 参数校验：团队ID和用户ID必须大于0
     * 2. 检查成员是否已在团队中
     * 3. 添加成员关系
     * 4. 处理唯一约束冲突（成员已在团队中）
     *
     * @param relation 团队-用户关系对象
     * @return 添加成功返回 true，成员已存在返回 false
     * @throws IllegalArgumentException 如果参数无效
     */
    @Override
    @Transactional
    public boolean addMemberToTeam(TeamUserRelation relation) {
        // 参数校验
        if (relation == null || relation.getTeamId() <= 0 || relation.getUserId() <= 0) {
            throw new IllegalArgumentException("无效的团队或用户ID");
        }

        try {
            // 尝试添加成员关系
            int affectedRows = teamMemberDao.insert(relation);
            return affectedRows > 0;
        } catch (DuplicateKeyException e) {
            // 处理唯一约束冲突（成员已在团队中）
            return false;
        }
    }

    /**
     * 从团队中移除成员
     *
     * 实现逻辑：
     * 1. 参数校验：团队ID和用户ID必须大于0
     * 2. 检查成员是否在团队中
     * 3. 执行移除操作
     *
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 移除成功返回 true，成员不存在返回 false
     * @throws IllegalArgumentException 如果参数无效
     */
    @Override
    @Transactional
    public boolean removeMemberFromTeam(int teamId, int userId) {
        // 参数校验
        if (teamId <= 0 || userId <= 0) {
            throw new IllegalArgumentException("无效的团队或用户ID");
        }

        // 检查成员是否存在
        if (!isUserInTeam(teamId, userId)) {
            return false;
        }

        // 执行移除操作
        int affectedRows = teamMemberDao.deleteByTeamAndUser(teamId, userId);
        return affectedRows > 0;
    }

    /**
     * 解散团队（移除团队所有成员）
     *
     * 实现逻辑：
     * 1. 参数校验：团队ID必须大于0
     * 2. 获取团队当前成员数量（用于返回）
     * 3. 删除所有成员关系
     *
     * @param teamId 要解散的团队ID
     * @return 移除的成员数量
     * @throws IllegalArgumentException 如果团队ID无效
     */
    @Override
    @Transactional
    public int disbandTeam(int teamId) {
        // 参数校验
        if (teamId <= 0) {
            throw new IllegalArgumentException("无效的团队ID");
        }

        // 获取当前成员数量
        int memberCount = countTeamMembers(teamId);

        // 删除所有成员关系
        teamMemberDao.deleteByTeamId(teamId);

        return memberCount;
    }

    /**
     * 移除用户的所有团队关系（用户退出所有团队）
     *
     * 实现逻辑：
     * 1. 参数校验：用户ID必须大于0
     * 2. 获取用户当前加入的团队数量（用于返回）
     * 3. 删除所有团队关系
     *
     * @param userId 用户ID
     * @return 移除的团队关系数量
     * @throws IllegalArgumentException 如果用户ID无效
     */
    @Override
    @Transactional
    public int removeUserFromAllTeams(int userId) {
        // 参数校验
        if (userId <= 0) {
            throw new IllegalArgumentException("无效的用户ID");
        }

        // 获取当前加入的团队数量
        int teamCount = countUserTeams(userId);

        // 删除所有团队关系
        teamMemberDao.deleteByUserId(userId);

        return teamCount;
    }

    /**
     * 获取团队所有成员ID列表
     *
     * 实现逻辑：
     * 1. 参数校验：团队ID必须大于0
     * 2. 查询数据库获取成员列表
     *
     * @param teamId 团队ID
     * @return 成员ID列表，无成员返回空列表
     * @throws IllegalArgumentException 如果团队ID无效
     */
    @Override
    public List<Integer> getTeamMembers(int teamId) {
        // 参数校验
        if (teamId <= 0) {
            throw new IllegalArgumentException("无效的团队ID");
        }

        return teamMemberDao.selectUserIdsByTeamId(teamId);
    }

    /**
     * 获取用户加入的所有团队ID列表
     *
     * 实现逻辑：
     * 1. 参数校验：用户ID必须大于0
     * 2. 查询数据库获取团队列表
     *
     * @param userId 用户ID
     * @return 团队ID列表，未加入任何团队返回空列表
     * @throws IllegalArgumentException 如果用户ID无效
     */
    @Override
    public List<Integer> getUserTeams(int userId) {
        // 参数校验
        if (userId <= 0) {
            throw new IllegalArgumentException("无效的用户ID");
        }

        return teamMemberDao.selectTeamIdsByUserId(userId);
    }

    /**
     * 检查用户是否在指定团队中
     *
     * 实现逻辑：
     * 1. 参数校验：团队ID和用户ID必须大于0
     * 2. 查询数据库检查关系存在性
     *
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 存在返回 true，不存在返回 false
     * @throws IllegalArgumentException 如果参数无效
     */
    @Override
    public boolean isUserInTeam(int teamId, int userId) {
        // 参数校验
        if (teamId <= 0 || userId <= 0) {
            throw new IllegalArgumentException("无效的团队或用户ID");
        }

        return teamMemberDao.existsRelation(teamId, userId) > 0;
    }

    /**
     * 将用户转移到新团队
     *
     * 实现逻辑：
     * 1. 参数校验：所有ID必须大于0
     * 2. 检查用户是否在原团队中
     * 3. 执行转移操作（原子操作）
     *
     * @param userId    用户ID
     * @param oldTeamId 原团队ID
     * @param newTeamId 新团队ID
     * @return 转移成功返回 true，用户不在原团队中返回 false
     * @throws IllegalArgumentException 如果参数无效
     */
    @Override
    @Transactional
    public boolean transferUserToNewTeam(int userId, int oldTeamId, int newTeamId) {
        // 参数校验
        if (userId <= 0 || oldTeamId <= 0 || newTeamId <= 0) {
            throw new IllegalArgumentException("无效的用户或团队ID");
        }

        // 检查用户是否在原团队中
        if (!isUserInTeam(oldTeamId, userId)) {
            return false;
        }

        // 执行转移操作
        int affectedRows = teamMemberDao.updateUserTeam(oldTeamId, newTeamId, userId);
        return affectedRows > 0;
    }

    /**
     * 获取团队成员数量
     *
     * 实现逻辑：
     * 1. 参数校验：团队ID必须大于0
     * 2. 查询数据库获取成员数量
     *
     * @param teamId 团队ID
     * @return 团队成员数量
     * @throws IllegalArgumentException 如果团队ID无效
     */
    @Override
    public int countTeamMembers(int teamId) {
        // 参数校验
        if (teamId <= 0) {
            throw new IllegalArgumentException("无效的团队ID");
        }

        return teamMemberDao.countMembersByTeamId(teamId);
    }

    /**
     * 获取用户加入的团队数量
     *
     * 实现逻辑：
     * 1. 参数校验：用户ID必须大于0
     * 2. 查询数据库获取团队数量
     *
     * @param userId 用户ID
     * @return 用户加入的团队数量
     * @throws IllegalArgumentException 如果用户ID无效
     */
    @Override
    public int countUserTeams(int userId) {
        // 参数校验
        if (userId <= 0) {
            throw new IllegalArgumentException("无效的用户ID");
        }

        return teamMemberDao.countTeamsByUserId(userId);
    }

    /**
     * 批量添加成员到团队
     *
     * 实现逻辑：
     * 1. 参数校验：团队ID必须大于0，用户ID列表不能为空
     * 2. 过滤已存在的成员
     * 3. 批量添加新成员
     *
     * @param teamId    团队ID
     * @param userIds   用户ID列表
     * @return 成功添加的成员数量
     * @throws IllegalArgumentException 如果参数无效
     */
    @Override
    @Transactional
    public int batchAddMembersToTeam(int teamId, List<Integer> userIds) {
        // 参数校验
        if (teamId <= 0) {
            throw new IllegalArgumentException("无效的团队ID");
        }
        if (CollectionUtils.isEmpty(userIds)) {
            throw new IllegalArgumentException("用户ID列表不能为空");
        }

        int successCount = 0;
        for (Integer userId : userIds) {
            if (userId == null || userId <= 0) {
                continue; // 跳过无效ID
            }

            // 检查是否已存在
            if (isUserInTeam(teamId, userId)) {
                continue;
            }

            // 添加成员
            TeamUserRelation relation = new TeamUserRelation(teamId, userId);
            if (addMemberToTeam(relation)) {
                successCount++;
            }
        }
        return successCount;
    }

    /**
     * 批量从团队中移除成员
     *
     * 实现逻辑：
     * 1. 参数校验：团队ID必须大于0，用户ID列表不能为空
     * 2. 批量移除成员
     *
     * @param teamId    团队ID
     * @param userIds   用户ID列表
     * @return 成功移除的成员数量
     * @throws IllegalArgumentException 如果参数无效
     */
    @Override
    @Transactional
    public int batchRemoveMembersFromTeam(int teamId, List<Integer> userIds) {
        // 参数校验
        if (teamId <= 0) {
            throw new IllegalArgumentException("无效的团队ID");
        }
        if (CollectionUtils.isEmpty(userIds)) {
            throw new IllegalArgumentException("用户ID列表不能为空");
        }

        int successCount = 0;
        for (Integer userId : userIds) {
            if (userId == null || userId <= 0) {
                continue; // 跳过无效ID
            }

            // 移除成员
            if (removeMemberFromTeam(teamId, userId)) {
                successCount++;
            }
        }
        return successCount;
    }
}