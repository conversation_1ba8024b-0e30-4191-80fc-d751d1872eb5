// 包声明：定义当前类所属的包路径，属于工具类包
package com.ruoyi.wenshumeeting.util;

// 导入Spring Bean异常类，用于处理Bean相关的异常
import org.springframework.beans.BeansException;
// 导入Spring Bean定义不存在异常类，当查找的Bean不存在时抛出
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
// 导入Spring Bean工厂后处理器接口，用于在Bean工厂初始化后进行自定义处理
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
// 导入Spring可配置的可列举Bean工厂接口，提供Bean的管理和查询功能
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
// 导入Spring Repository注解，标识这是一个数据访问层组件
import org.springframework.stereotype.Repository;

/**
 * Spring工具类
 * 提供在非Spring管理的类中获取Spring容器中Bean的静态方法
 * 通过实现BeanFactoryPostProcessor接口来获取BeanFactory的引用
 *
 * @Repository 标识这是一个Spring管理的组件，会被自动扫描并注册到容器中
 */
@Repository
public class SpringUtils implements BeanFactoryPostProcessor {

    /**
     * 静态变量：Spring应用上下文环境的Bean工厂
     * 用于存储Spring容器的BeanFactory引用，以便在静态方法中使用
     * ConfigurableListableBeanFactory提供了Bean的创建、查询、管理等功能
     */
    private static ConfigurableListableBeanFactory beanFactory;

    /**
     * Bean工厂后处理器方法
     * 在Spring容器初始化所有Bean定义后，但在实例化Bean之前调用
     * 用于获取并保存BeanFactory的引用，使其可以在静态方法中使用
     *
     * @param beanFactory Spring容器的可配置Bean工厂实例
     * @throws BeansException 如果处理过程中发生Bean相关异常
     */
    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        // 将传入的BeanFactory实例保存到静态变量中，供后续静态方法使用
        SpringUtils.beanFactory = beanFactory;
    }

    /**
     * 获取Bean工厂实例的静态方法
     * 提供对Spring容器BeanFactory的访问入口
     *
     * @return ConfigurableListableBeanFactory Spring容器的Bean工厂实例
     */
    public static ConfigurableListableBeanFactory getBeanFactory() {
        // 返回保存的BeanFactory实例
        return beanFactory;
    }

    /**
     * 根据Bean名称获取Bean实例的泛型方法
     * 通过Bean的注册名称从Spring容器中获取对应的Bean实例
     *
     * @param name Bean在Spring容器中的注册名称
     * @param <T> 返回的Bean类型，支持泛型
     * @return T 指定名称的Bean实例，已转换为目标类型
     * @throws BeansException 如果获取Bean过程中发生异常（如Bean不存在、类型转换失败等）
     */
    @SuppressWarnings("unchecked") // 抑制未检查的类型转换警告
    public static <T> T getBean(String name) throws BeansException {
        // 从BeanFactory中根据名称获取Bean，并强制转换为目标类型T
        return (T) getBeanFactory().getBean(name);
    }

    /**
     * 根据Bean类型获取Bean实例的泛型方法
     * 通过Bean的Class类型从Spring容器中获取对应的Bean实例
     * 适用于容器中只有一个该类型Bean的情况
     *
     * @param clz Bean的Class类型，用于指定要获取的Bean类型
     * @param <T> 返回的Bean类型，与传入的Class类型一致
     * @return T 指定类型的Bean实例
     * @throws BeansException 如果获取Bean过程中发生异常（如Bean不存在、存在多个同类型Bean等）
     */
    public static <T> T getBean(Class<T> clz) throws BeansException {
        // 从BeanFactory中根据类型获取Bean实例
        T result = (T) getBeanFactory().getBean(clz);
        // 返回获取到的Bean实例
        return result;
    }

    /**
     * 检查Spring容器中是否包含指定名称的Bean定义
     * 用于在获取Bean之前验证Bean是否存在，避免抛出异常
     *
     * @param name Bean在Spring容器中的注册名称
     * @return boolean 如果容器中包含指定名称的Bean定义则返回true，否则返回false
     */
    public static boolean containsBean(String name) {
        // 调用BeanFactory的containsBean方法检查Bean是否存在
        return getBeanFactory().containsBean(name);
    }

    /**
     * 判断指定名称的Bean是否为单例模式
     * Spring中Bean的作用域主要有singleton（单例）和prototype（原型）两种
     * 单例模式：容器中只有一个Bean实例，多次获取返回同一个对象
     * 原型模式：每次获取都创建新的Bean实例
     *
     * @param name Bean在Spring容器中的注册名称
     * @return boolean 如果Bean是单例模式返回true，如果是原型模式返回false
     * @throws NoSuchBeanDefinitionException 如果指定名称的Bean定义不存在则抛出此异常
     */
    public static boolean isSingleton(String name) throws NoSuchBeanDefinitionException {
        // 调用BeanFactory的isSingleton方法判断Bean的作用域
        return getBeanFactory().isSingleton(name);
    }

    /**
     * 获取指定名称Bean的Class类型
     * 用于在运行时获取Bean的具体类型信息，便于类型检查和反射操作
     *
     * @param name Bean在Spring容器中的注册名称
     * @return Class<?> Bean的Class类型，使用通配符泛型表示可以是任意类型
     * @throws NoSuchBeanDefinitionException 如果指定名称的Bean定义不存在则抛出此异常
     */
    public static Class<?> getType(String name) throws NoSuchBeanDefinitionException {
        // 调用BeanFactory的getType方法获取Bean的类型信息
        return getBeanFactory().getType(name);
    }

    /**
     * 获取指定Bean名称的所有别名
     * Spring允许为Bean定义多个别名，此方法返回指定Bean的所有别名数组
     * 别名机制提供了Bean命名的灵活性，同一个Bean可以通过多个名称访问
     *
     * @param name Bean在Spring容器中的注册名称（主名称）
     * @return String[] 包含所有别名的字符串数组，如果没有别名则返回空数组
     * @throws NoSuchBeanDefinitionException 如果指定名称的Bean定义不存在则抛出此异常
     */
    public static String[] getAliases(String name) throws NoSuchBeanDefinitionException {
        // 调用BeanFactory的getAliases方法获取Bean的所有别名
        return getBeanFactory().getAliases(name);
    }
}
