package com.ruoyi.wenshuapi.client.audit;

import com.ruoyi.wenshuapi.common.RestResult;
import com.ruoyi.wenshuapi.pojo.audit.WenshuAuditLog;
import com.ruoyi.wenshuapi.pojo.audit.WenshuTeamAudit;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
@FeignClient(
        value = "wenshu-audit",
        path = "/api"
)
public interface AuditFeignClientInter {

    /* 审计日志相关接口 - 与服务端 WenshuAuditLogController 保持一致 */

    /**
     * 添加审计日志
     * 对应 POST /api/add
     */
    @PostMapping("/add")
    RestResult<Void> addLog(@RequestBody WenshuAuditLog log);

    /**
     * 获取审计日志列表
     * 对应 GET /api/list?auditId={auditId}
     */
    @GetMapping("/list")
    RestResult<List<WenshuAuditLog>> getLogList(@RequestParam("auditId") Integer auditId);

    /**
     * 删除审计日志
     * 对应 DELETE /api/delete?logId={logId}
     */
    @DeleteMapping("/delete")
    RestResult<Void> deleteLog(@RequestParam("logId") Integer logId);

    /* 团队审计信息相关接口 - 与服务端 WenshuTeamAuditController 保持一致 */

    /**
     * 获取所有团队审计信息
     * 对应 GET /api/TeamInfogetAll
     */
    @GetMapping("/TeamInfogetAll")
    List<WenshuTeamAudit> TeamInfogetAll();

    /**
     * 获取单个团队审计信息
     * 对应 GET /api/TeamInfogetOne?teamId={teamId}
     */
    @GetMapping("/TeamInfogetOne")
    WenshuTeamAudit TeamInfogetOne(@RequestParam("teamId") int teamId);

    /**
     * 添加团队审计信息
     * 对应 POST /api/TeamInfoadd
     */
    @PostMapping("/TeamInfoadd")
    boolean TeamInfoadd(@RequestBody WenshuTeamAudit wenshuTeamAudit);

    /**
     * 更新团队审计信息
     * 对应 PUT /api/TeamInfoupdate
     */
    @PutMapping("/TeamInfoupdate")
    boolean TeamInfoupdate(@RequestBody WenshuTeamAudit wenshuTeamAudit);

    /**
     * 删除团队审计信息
     * 对应 DELETE /api/TeamInfodelete?teamId={teamId}
     */
    @DeleteMapping("/TeamInfodelete")
    boolean TeamInfodelete(@RequestParam("teamId") int teamId);
}
