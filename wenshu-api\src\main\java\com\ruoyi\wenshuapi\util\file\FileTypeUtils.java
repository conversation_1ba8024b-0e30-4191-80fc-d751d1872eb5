package com.ruoyi.wenshuapi.util.file;

import com.ruoyi.wenshuapi.constants.FileConstants;
import org.apache.commons.io.FilenameUtils;
import org.springframework.util.StringUtils;

import java.text.DecimalFormat;
import java.util.Arrays;

/**
 * 文件类型工具类
 * 提供文件类型判断、大小格式化等通用功能
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-28
 */
public class FileTypeUtils {

    /**
     * 判断文件是否为图片
     * 
     * @param filename 文件名
     * @return 是否为图片
     */
    public static boolean isImage(String filename) {
        if (!StringUtils.hasText(filename)) {
            return false;
        }
        String extension = FilenameUtils.getExtension(filename).toLowerCase();
        return Arrays.asList(FileConstants.FileType.IMAGE_TYPES).contains(extension);
    }

    /**
     * 判断文件是否为视频
     * 
     * @param filename 文件名
     * @return 是否为视频
     */
    public static boolean isVideo(String filename) {
        if (!StringUtils.hasText(filename)) {
            return false;
        }
        String extension = FilenameUtils.getExtension(filename).toLowerCase();
        return Arrays.asList(FileConstants.FileType.VIDEO_TYPES).contains(extension);
    }

    /**
     * 判断文件是否为音频
     * 
     * @param filename 文件名
     * @return 是否为音频
     */
    public static boolean isAudio(String filename) {
        if (!StringUtils.hasText(filename)) {
            return false;
        }
        String extension = FilenameUtils.getExtension(filename).toLowerCase();
        return Arrays.asList(FileConstants.FileType.AUDIO_TYPES).contains(extension);
    }

    /**
     * 判断文件是否为文档
     * 
     * @param filename 文件名
     * @return 是否为文档
     */
    public static boolean isDocument(String filename) {
        if (!StringUtils.hasText(filename)) {
            return false;
        }
        String extension = FilenameUtils.getExtension(filename).toLowerCase();
        return Arrays.asList(FileConstants.FileType.DOCUMENT_TYPES).contains(extension);
    }

    /**
     * 判断文件是否为压缩文件
     * 
     * @param filename 文件名
     * @return 是否为压缩文件
     */
    public static boolean isArchive(String filename) {
        if (!StringUtils.hasText(filename)) {
            return false;
        }
        String extension = FilenameUtils.getExtension(filename).toLowerCase();
        return Arrays.asList(FileConstants.FileType.ARCHIVE_TYPES).contains(extension);
    }

    /**
     * 获取文件类型分类
     * 
     * @param filename 文件名
     * @return 文件类型分类（image/video/audio/document/archive/other）
     */
    public static String getFileTypeCategory(String filename) {
        if (isImage(filename)) {
            return "image";
        } else if (isVideo(filename)) {
            return "video";
        } else if (isAudio(filename)) {
            return "audio";
        } else if (isDocument(filename)) {
            return "document";
        } else if (isArchive(filename)) {
            return "archive";
        } else {
            return "other";
        }
    }

    /**
     * 格式化文件大小
     * 
     * @param size 文件大小（字节）
     * @return 格式化后的文件大小字符串
     */
    public static String formatFileSize(long size) {
        if (size <= 0) {
            return "0 B";
        }
        
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        
        return new DecimalFormat("#,##0.#").format(size / Math.pow(1024, digitGroups)) 
               + " " + units[digitGroups];
    }

    /**
     * 验证文件名是否合法
     * 
     * @param filename 文件名
     * @return 验证结果
     */
    public static FileValidationResult validateFilename(String filename) {
        if (!StringUtils.hasText(filename)) {
            return FileValidationResult.error("文件名不能为空");
        }

        if (filename.length() > FileConstants.FileSize.MAX_FILENAME_LENGTH) {
            return FileValidationResult.error(
                String.format("文件名过长，最大允许 %d 个字符，当前 %d 个字符", 
                             FileConstants.FileSize.MAX_FILENAME_LENGTH, filename.length()));
        }

        // 检查非法字符
        String illegalChars = "<>:\"|?*\\";
        for (char c : illegalChars.toCharArray()) {
            if (filename.indexOf(c) >= 0) {
                return FileValidationResult.error("文件名包含非法字符: " + illegalChars);
            }
        }

        return FileValidationResult.success();
    }

    /**
     * 验证文件大小是否合法
     * 
     * @param fileSize 文件大小（字节）
     * @return 验证结果
     */
    public static FileValidationResult validateFileSize(long fileSize) {
        if (fileSize <= 0) {
            return FileValidationResult.error("文件大小不能为0");
        }

        if (fileSize > FileConstants.FileSize.MAX_FILE_SIZE) {
            return FileValidationResult.error(
                String.format("文件大小超过限制，最大允许 %s，当前文件 %s", 
                             formatFileSize(FileConstants.FileSize.MAX_FILE_SIZE), 
                             formatFileSize(fileSize)));
        }

        return FileValidationResult.success();
    }

    /**
     * 获取文件扩展名
     * 
     * @param filename 文件名
     * @return 文件扩展名（小写）
     */
    public static String getFileExtension(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "";
        }
        return FilenameUtils.getExtension(filename).toLowerCase();
    }

    /**
     * 获取不带扩展名的文件名
     * 
     * @param filename 文件名
     * @return 不带扩展名的文件名
     */
    public static String getBaseName(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "";
        }
        return FilenameUtils.getBaseName(filename);
    }

    /**
     * 清理文件名，移除非法字符
     * 
     * @param filename 原始文件名
     * @return 清理后的文件名
     */
    public static String sanitizeFilename(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "file";
        }
        
        // 移除非法字符，替换为下划线
        String sanitized = filename.replaceAll("[<>:\"|?*\\\\]", "_");
        
        // 限制长度
        if (sanitized.length() > 50) {
            String extension = getFileExtension(sanitized);
            String baseName = getBaseName(sanitized);
            if (baseName.length() > 46) {
                baseName = baseName.substring(0, 46);
            }
            sanitized = StringUtils.hasText(extension) ? baseName + "." + extension : baseName;
        }
        
        return sanitized;
    }

    /**
     * 文件验证结果类
     */
    public static class FileValidationResult {
        private final boolean valid;
        private final String message;

        private FileValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public static FileValidationResult success() {
            return new FileValidationResult(true, null);
        }

        public static FileValidationResult error(String message) {
            return new FileValidationResult(false, message);
        }

        public boolean isValid() {
            return valid;
        }

        public String getMessage() {
            return message;
        }
    }
}
