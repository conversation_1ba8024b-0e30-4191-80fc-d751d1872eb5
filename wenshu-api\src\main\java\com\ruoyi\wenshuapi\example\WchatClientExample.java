package com.ruoyi.wenshuapi.example;

import com.ruoyi.wenshuapi.client.wchat.ChatRecordClient;
import com.ruoyi.wenshuapi.client.wchat.FriendListClient;
import com.ruoyi.wenshuapi.pojo.wchat.FriendList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * wenshu-wchat 客户端使用示例
 * 
 * 演示如何在其他微服务中使用 wenshu-wchat 的 Feign 客户端
 * 进行聊天记录和好友关系的远程调用
 */
@Component
public class WchatClientExample {

    @Autowired
    private ChatRecordClient chatRecordClient;

    @Autowired
    private FriendListClient friendListClient;

    /**
     * 发送文本消息示例
     */
    public void sendTextMessageExample() {
        try {
            ResponseEntity<Map<String, Object>> response = chatRecordClient.sendTextMessage(
                1001L,  // 发送者ID
                1002L,  // 接收者ID
                "Hello, this is a test message!"  // 消息内容
            );
            
            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> body = response.getBody();
                System.out.println("消息发送成功: " + body);
            }
        } catch (Exception e) {
            System.err.println("发送消息失败: " + e.getMessage());
        }
    }

    /**
     * 统一发送消息接口示例
     */
    public void sendUnifiedMessageExample() {
        try {
            ResponseEntity<Map<String, Object>> response = chatRecordClient.sendMessage(
                1001L,      // 发送者ID
                1002L,      // 接收者ID
                "text",     // 内容类型
                "Hello from unified API!",  // 文本内容
                null        // 文件（文本消息时为null）
            );
            
            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> body = response.getBody();
                System.out.println("统一消息发送成功: " + body);
            }
        } catch (Exception e) {
            System.err.println("统一消息发送失败: " + e.getMessage());
        }
    }

    /**
     * 发送文件消息示例
     */
    public void sendFileMessageExample(MultipartFile file) {
        try {
            ResponseEntity<Map<String, Object>> response = chatRecordClient.sendFileMessage(
                1001L,  // 发送者ID
                1002L,  // 接收者ID
                file    // 文件
            );
            
            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> body = response.getBody();
                System.out.println("文件消息发送成功: " + body);
            }
        } catch (Exception e) {
            System.err.println("文件消息发送失败: " + e.getMessage());
        }
    }

    /**
     * 获取聊天记录示例
     */
    public void getChatMessagesExample() {
        try {
            ResponseEntity<Map<String, Object>> response = chatRecordClient.getMessagesBySenderAndReceiver(
                1001L,  // 发送者ID
                1002L   // 接收者ID
            );
            
            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> body = response.getBody();
                System.out.println("获取聊天记录成功: " + body);
            }
        } catch (Exception e) {
            System.err.println("获取聊天记录失败: " + e.getMessage());
        }
    }

    /**
     * 根据会话ID获取聊天记录示例
     */
    public void getMessagesBySessionExample() {
        try {
            String sessionId = "session_1001_1002";  // 会话ID
            ResponseEntity<Map<String, Object>> response = chatRecordClient.getMessagesBySessionId(sessionId);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> body = response.getBody();
                System.out.println("根据会话ID获取聊天记录成功: " + body);
            }
        } catch (Exception e) {
            System.err.println("根据会话ID获取聊天记录失败: " + e.getMessage());
        }
    }

    /**
     * 生成会话ID示例
     */
    public void generateSessionIdExample() {
        try {
            ResponseEntity<Map<String, Object>> response = chatRecordClient.generateSessionId(
                1001L,  // 用户1 ID
                1002L   // 用户2 ID
            );
            
            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> body = response.getBody();
                System.out.println("生成会话ID成功: " + body);
            }
        } catch (Exception e) {
            System.err.println("生成会话ID失败: " + e.getMessage());
        }
    }

    /**
     * 标记消息为已读示例
     */
    public void markMessageAsReadExample() {
        try {
            Long messageId = 12345L;  // 消息ID
            ResponseEntity<Map<String, Object>> response = chatRecordClient.markAsRead(messageId);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> body = response.getBody();
                System.out.println("标记消息已读成功: " + body);
            }
        } catch (Exception e) {
            System.err.println("标记消息已读失败: " + e.getMessage());
        }
    }

    /**
     * 添加好友示例
     */
    public void addFriendExample() {
        try {
            FriendList friendList = new FriendList();
            friendList.setUserId(1001L);
            friendList.setFriendId(1002L);
            friendList.setStatus("active");
            
            ResponseEntity<Map<String, Object>> response = friendListClient.addFriend(friendList);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> body = response.getBody();
                System.out.println("添加好友成功: " + body);
            }
        } catch (Exception e) {
            System.err.println("添加好友失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户好友列表示例
     */
    public void getFriendsListExample() {
        try {
            Long userId = 1001L;
            ResponseEntity<Map<String, Object>> response = friendListClient.getFriendsByUserId(userId);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> body = response.getBody();
                System.out.println("获取好友列表成功: " + body);
            }
        } catch (Exception e) {
            System.err.println("获取好友列表失败: " + e.getMessage());
        }
    }

    /**
     * 检查好友关系示例
     */
    public void checkFriendshipExample() {
        try {
            ResponseEntity<Map<String, Object>> response = friendListClient.checkFriendship(
                1001L,  // 用户1 ID
                1002L   // 用户2 ID
            );
            
            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> body = response.getBody();
                System.out.println("检查好友关系成功: " + body);
            }
        } catch (Exception e) {
            System.err.println("检查好友关系失败: " + e.getMessage());
        }
    }

    /**
     * 删除好友示例
     */
    public void deleteFriendExample() {
        try {
            ResponseEntity<Map<String, Object>> response = friendListClient.deleteFriendByUserIds(
                1001L,  // 用户ID
                1002L   // 好友用户ID
            );
            
            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> body = response.getBody();
                System.out.println("删除好友成功: " + body);
            }
        } catch (Exception e) {
            System.err.println("删除好友失败: " + e.getMessage());
        }
    }

    /**
     * 获取未读消息数量示例
     */
    public void getUnreadCountExample() {
        try {
            Long userId = 1001L;
            ResponseEntity<Map<String, Object>> response = chatRecordClient.getUnreadMessageCount(userId);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> body = response.getBody();
                System.out.println("获取未读消息数量成功: " + body);
            }
        } catch (Exception e) {
            System.err.println("获取未读消息数量失败: " + e.getMessage());
        }
    }

    /**
     * 搜索聊天记录示例
     */
    public void searchMessagesExample() {
        try {
            String sessionId = "session_1001_1002";
            String keyword = "hello";
            ResponseEntity<Map<String, Object>> response = chatRecordClient.searchMessages(sessionId, keyword);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> body = response.getBody();
                System.out.println("搜索聊天记录成功: " + body);
            }
        } catch (Exception e) {
            System.err.println("搜索聊天记录失败: " + e.getMessage());
        }
    }
}
