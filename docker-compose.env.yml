version: '3.8'

# 环境服务配置 - 空间优化版本
# 主要优化：
# 1. MySQL: 384M内存，MySQL 8.0兼容配置，禁用性能模式
# 2. Redis: 128M内存，96MB最大内存，v7.4.4版本，减少数据库数量
# 3. Nacos: 1280M内存，JVM堆内存1024M，支持多服务连接
# 4. etcd: 192M内存，512MB后端存储配额
# 5. MinIO: 192M内存，减少缓存配额
# 6. Milvus: 768M内存，减少缓存和缓冲区大小
# 7. Nginx: 48M内存，监听端口81 (避免与其他服务冲突)
# 总内存使用: ~3.0GB (Nacos增强版，支持多服务连接)

services:
  mysql:
    image: lcr.loongnix.cn/library/mysql:8.4.3
    container_name: wenshu-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: **********
      MYSQL_DATABASE: ry-cloud
      TZ: Asia/Shanghai
      MYSQL_INITDB_SKIP_TZINFO: 1
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    command:
      - --character-set-server=utf8mb4
      - --collation-server=utf8mb4_unicode_ci
      - --mysql-native-password=ON
      # 内存优化配置 - MySQL 8.0兼容版本
      - --innodb-buffer-pool-size=128M
      - --innodb-log-file-size=32M
      - --innodb-log-buffer-size=4M
      - --key-buffer-size=16M
      - --table-open-cache=128
      - --sort-buffer-size=512K
      - --read-buffer-size=512K
      - --read-rnd-buffer-size=512K
      - --myisam-sort-buffer-size=16M
      - --thread-cache-size=4
      - --tmp-table-size=16M
      - --max-heap-table-size=16M
      - --max-connections=50
      - --performance-schema=OFF
    networks:
      - wenshu_wenshu-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p**********"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 30s
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined
    # 内存限制 - 优化后
    mem_limit: 384m
    memswap_limit: 384m

  redis:
    image: lcr.loongnix.cn/library/redis:7.4.4
    container_name: wenshu-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command:
      - redis-server
      - --appendonly yes
      - --maxmemory 96mb
      - --maxmemory-policy allkeys-lru
      - --save 900 1
      - --save 300 10
      - --save 60 1000
      - --tcp-keepalive 60
      - --timeout 300
      - --databases 8
      - --maxclients 50
    networks:
      - wenshu_wenshu-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 15s
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined
    # 内存限制 - 优化后
    mem_limit: 128m
    memswap_limit: 128m

  nacos:
    image: lcr.loongnix.cn/nacos/nacos-server:v2.2.0
    container_name: wenshu-nacos
    restart: always
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: **********
      MYSQL_SERVICE_DB_NAME: ry-config
      MYSQL_SERVICE_DB_PARAM: characterEncoding=utf8&connectTimeout=10000&socketTimeout=30000&autoReconnect=true&useUnicode=true
      # 禁用认证
      NACOS_AUTH_ENABLE: false
      # 强制使用HTTP协议，禁用gRPC
      NACOS_SERVER_GRPC_PORT_OFFSET: -1
      # JVM 内存优化 - 增加内存支持多服务
      JVM_XMS: 512m
      JVM_XMX: 1024m
      JVM_XMN: 256m
      JVM_MS: 64m
      JVM_MMS: 128m
      NACOS_DEBUG: n
      TOMCAT_ACCESSLOG_ENABLED: false
      # 增加连接池和线程池配置
      NACOS_SERVER_TOMCAT_MAXTHREADS: 400
      NACOS_SERVER_TOMCAT_MINTHREADS: 50
      NACOS_SERVER_TOMCAT_MAXCONNECTIONS: 20000
      # 增加数据库连接池
      SPRING_DATASOURCE_HIKARI_MAXIMUM_POOL_SIZE: 50
      SPRING_DATASOURCE_HIKARI_MINIMUM_IDLE: 10
      # GC优化
      JAVA_OPT_EXT: "-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions -XX:+UseContainerSupport"
    volumes:
      - nacos_data:/home/<USER>/data
    ports:
      - "8848:8848"
    depends_on:
      - mysql
    networks:
      - wenshu_wenshu-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8848/nacos/actuator/health"]
      interval: 60s
      timeout: 15s
      retries: 3
      start_period: 90s
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined
    # 内存限制 - 增加以支持多服务
    mem_limit: 1280m
    memswap_limit: 1280m

  etcd:
    image: lcr.loongnix.cn/kubernetes/etcd:3.5.10-0
    container_name: wenshu-etcd
    restart: always
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=536870912  # 512MB instead of 1GB
      - ETCD_SNAPSHOT_COUNT=5000  # Further reduced
      - ETCD_MAX_REQUEST_BYTES=1048576  # 1MB
      - ETCD_GRPC_KEEPALIVE_MIN_TIME=5s
      - ETCD_GRPC_KEEPALIVE_INTERVAL=2h
      - ETCD_GRPC_KEEPALIVE_TIMEOUT=20s
    volumes:
      - etcd_data:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    networks:
      - wenshu_wenshu-network
    healthcheck:
      test: ["CMD", "etcdctl", "endpoint", "health"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 30s
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined
    # 内存限制 - 优化后
    mem_limit: 192m
    memswap_limit: 192m

  minio:
    image: lcr.loongnix.cn/minio/minio:RELEASE.2025-03-12T18-04-18Z
    container_name: wenshu-minio
    restart: always
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
      # 内存优化配置 - 进一步优化
      MINIO_CACHE_DRIVES: ""
      MINIO_CACHE_EXCLUDE: ""
      MINIO_CACHE_QUOTA: 25
      MINIO_CACHE_AFTER: 0
      MINIO_CACHE_WATERMARK_LOW: 80
      MINIO_CACHE_WATERMARK_HIGH: 95
      MINIO_API_REQUESTS_MAX: 50
      MINIO_API_REQUESTS_DEADLINE: 10s
    volumes:
      - minio_data:/minio_data
    command: minio server /minio_data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/ready"]
      interval: 60s
      timeout: 20s
      retries: 3
      start_period: 45s
    networks:
      - wenshu_wenshu-network
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined
    # 内存限制 - 优化后
    mem_limit: 192m
    memswap_limit: 192m

  milvus:
    image: lcr.loongnix.cn/milvusdb/milvus:2.5.7
    container_name: wenshu-milvus
    restart: "no"  # 临时禁用自动重启以便调试
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:9000
      # 内存优化配置 - 进一步优化
      CACHE_SIZE: 128MB
      INSERT_BUFFER_SIZE: 32MB
      PRELOAD_COLLECTION: false
      # 查询节点配置
      QUERY_NODE_GRACEFUL_TIME: 1000
      QUERY_NODE_STATS_TASK_DELAY_EXECUTE: 1000
      # 直接通过环境变量配置
      MILVUS_ETCD_ENDPOINTS: etcd:2379
      MILVUS_MINIO_ADDRESS: minio:9000
      MILVUS_MINIO_ACCESS_KEY_ID: minioadmin
      MILVUS_MINIO_SECRET_ACCESS_KEY: minioadmin
    command: ["milvus", "run", "standalone"]
    volumes:
      - milvus_data:/var/lib/milvus
    ports:
      - "19530:19530"
      - "9091:9091"
    depends_on:
      - etcd
      - minio
    networks:
      - wenshu_wenshu-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/api/v1/health"]
      interval: 60s
      timeout: 15s
      retries: 5
      start_period: 120s
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined
    # 内存限制 - 优化后
    mem_limit: 768m
    memswap_limit: 768m

  attu:
    image: lcr.loongnix.cn/library/attu:2.5.6
    container_name: wenshu-attu
    restart: always
    environment:
      MILVUS_URL: milvus:19530
      NODE_ENV: production
    ports:
      - "3000:3000"
    depends_on:
      - milvus
    networks:
      - wenshu_wenshu-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 30s
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined
    # 内存限制
    mem_limit: 128m
    memswap_limit: 128m

  nginx:
    image: lcr.loongnix.cn/library/nginx:1.24.0
    container_name: wenshu-nginx
    restart: always
    ports:
      - "81:80"  # 外部端口81映射到容器内部80端口
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/html:/usr/local/nginx-1.24.0/html
      - ./nginx/logs:/usr/nginx-1.24.0/logs
      - ./nginx/ssl:/etc/nginx/ssl
    networks:
      - wenshu_wenshu-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 30s
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined
    # 内存限制 - 优化后
    mem_limit: 48m
    memswap_limit: 48m

volumes:
  mysql_data:
  redis_data:
  nacos_data:
  milvus_data:
  etcd_data:
  minio_data:

networks:
  wenshu_wenshu-network:
    external: true
