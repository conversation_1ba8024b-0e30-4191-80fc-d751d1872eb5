# 文书智能计算系统 - Nginx 配置
# 默认服务器配置 - 监听端口80 (映射到外部81)

# 上游服务定义 - 使用Docker网络中的容器名
upstream gateway {
    server wenshu-gateway:8080;
    keepalive 32;
}

upstream nacos {
    server wenshu-nacos:8848;
    keepalive 16;
}

upstream attu {
    server wenshu-attu:3000;
    keepalive 16;
}

# 认证服务
upstream auth {
    server wenshu-auth:9200;
    keepalive 16;
}

# 系统服务
upstream system {
    server wenshu-system:9201;
    keepalive 16;
}

# 其他微服务
upstream chat {
    server wenshu-chat:8701;
    keepalive 8;
}

upstream meeting {
    server wenshu-meeting:8703;
    keepalive 8;
}

upstream multimodal {
    server wenshu-multimodal:8702;
    keepalive 8;
}

upstream wenshu-base {
    server wenshu-base:8601;
    keepalive 8;
}

upstream wenshu-team {
    server wenshu-team:1017;
    keepalive 8;
}

upstream wenshu-api {
    server wenshu-api:8704;
    keepalive 8;
}

# 主服务器配置
server {
    listen 80;
    server_name localhost;
    
    # 根目录 - 与Docker卷挂载路径一致
    root /usr/local/nginx-1.24.0/html;
    index index.html index.htm;

    # 日志配置 - 与Docker卷挂载路径一致
    access_log /usr/nginx-1.24.0/logs/wenshu.access.log main;
    error_log /usr/nginx-1.24.0/logs/wenshu.error.log;

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
    }

    # API 网关代理 - 主要入口
    location /api/ {
        proxy_pass http://gateway/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;

        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;

        # 跨域支持
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

        # 处理OPTIONS预检请求
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }

    # 直接访问认证服务
    location /auth/ {
        proxy_pass http://auth/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 直接访问系统服务
    location /system/ {
        proxy_pass http://system/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 聊天服务代理
    location /chat/ {
        proxy_pass http://chat/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 30s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }

    # 会议服务代理
    location /meeting/ {
        proxy_pass http://meeting/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 30s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }

    # 多模态服务代理
    location /multimodal/ {
        proxy_pass http://multimodal/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 30s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }

    # Nacos 控制台代理
    location /nacos/ {
        proxy_pass http://nacos/nacos/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Attu (Milvus 管理界面) 代理
    location /attu/ {
        proxy_pass http://attu/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # WebSocket 支持 (用于实时聊天等功能)
    location /ws/ {
        proxy_pass http://gateway/ws/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 7d;
        proxy_send_timeout 7d;
        proxy_read_timeout 7d;
    }

    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # 默认页面
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /50x.html {
        root /usr/local/nginx-1.24.0/html;
    }
}
