package com.ruoyi.wenshuwchat.service.impl;

import com.ruoyi.wenshuapi.pojo.wchat.FriendList;
import com.ruoyi.wenshuwchat.dao.FriendListDao;
import com.ruoyi.wenshuwchat.service.FriendListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 好友关系服务实现类
 */
@Service
public class FriendListServiceImpl implements FriendListService {

    private final FriendListDao friendListDao;

    @Autowired
    public FriendListServiceImpl(FriendListDao friendListDao) {
        this.friendListDao = friendListDao;
    }

    @Override
    @Transactional
    public Long addFriend(FriendList friendList) {
        // 检查是否已存在好友关系
        FriendList existingFriend = friendListDao.selectByUserAndFriend(
                friendList.getUserId(), friendList.getFriendId());
        if (existingFriend != null) {
            throw new IllegalArgumentException("好友关系已存在");
        }

        // 设置默认值
        if (friendList.getStatus() == null) {
            friendList.setStatus("active");
        }
        if (friendList.getCreatedAt() == null) {
            friendList.setCreatedAt(LocalDateTime.now());
        }
        if (friendList.getUpdatedAt() == null) {
            friendList.setUpdatedAt(LocalDateTime.now());
        }

        // 执行插入操作
        int result = friendListDao.insert(friendList);
        if (result == 0) {
            throw new RuntimeException("添加好友失败");
        }
        return friendList.getId();
    }

    @Override
    @Transactional
    public int deleteFriend(Long id) {
        // 检查好友关系是否存在
        FriendList existingFriend = friendListDao.selectById(id);
        if (existingFriend == null) {
            throw new IllegalArgumentException("好友关系不存在");
        }

        return friendListDao.deleteById(id);
    }

    @Override
    @Transactional
    public int deleteFriendByUserAndFriend(Long userId, Long friendId) {
        // 检查好友关系是否存在
        FriendList existingFriend = friendListDao.selectByUserAndFriend(userId, friendId);
        if (existingFriend == null) {
            throw new IllegalArgumentException("好友关系不存在");
        }

        return friendListDao.deleteByUserAndFriend(userId, friendId);
    }

    @Override
    @Transactional
    public int updateFriendStatus(FriendList friendList) {
        // 检查好友关系是否存在
        FriendList existingFriend = friendListDao.selectById(friendList.getId());
        if (existingFriend == null) {
            throw new IllegalArgumentException("好友关系不存在");
        }

        // 设置更新时间
        friendList.setUpdatedAt(LocalDateTime.now());

        return friendListDao.updateStatus(friendList);
    }

    @Override
    public FriendList getFriendById(Long id) {
        FriendList friend = friendListDao.selectById(id);
        if (friend == null) {
            throw new IllegalArgumentException("好友关系不存在");
        }
        return friend;
    }

    @Override
    public List<FriendList> getFriendsByUserId(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return friendListDao.selectByUserId(userId);
    }

    @Override
    public List<FriendList> getFriendsByUserIdAndStatus(Long userId, String status) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (status == null || status.trim().isEmpty()) {
            throw new IllegalArgumentException("状态不能为空");
        }
        return friendListDao.selectByUserIdAndStatus(userId, status);
    }

    @Override
    public FriendList checkFriendship(Long userId, Long friendId) {
        if (userId == null || friendId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return friendListDao.selectByUserAndFriend(userId, friendId);
    }

    @Override
    public List<FriendList> getAllFriends() {
        return friendListDao.selectAll();
    }

    @Override
    public List<FriendList> getActiveFriends(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return friendListDao.selectByUserIdAndStatus(userId, "active");
    }
}
