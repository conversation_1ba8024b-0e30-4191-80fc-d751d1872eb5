package com.ruoyi.wenshulivechat.service.impl;

import com.ruoyi.wenshuapi.client.wchat.ChatRecordClient;
import com.ruoyi.wenshuapi.pojo.wchat.ChatRecord;
import com.ruoyi.wenshulivechat.model.NewMessageRequest;
import com.ruoyi.wenshulivechat.model.NewMessageResponse;
import com.ruoyi.wenshulivechat.service.LiveChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 实时聊天服务实现类
 * 通过ChatRecordClient查询数据库获取新消息
 */
@Service
@Slf4j
public class LiveChatServiceImpl implements LiveChatService {

    @Autowired
    private ChatRecordClient chatRecordClient;

    /**
     * 提醒时间阈值（秒）
     * 只有发送时间在此阈值内的未读消息才会触发提醒
     */
    private static final long NOTIFICATION_TIME_THRESHOLD_SECONDS = 3;

    /**
     * 时间格式化器
     */
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public NewMessageResponse queryNewMessages(NewMessageRequest request) {
        try {
            // 参数验证
            if (request.getUserId() == null) {
                return NewMessageResponse.error("用户ID不能为空", null, "notification");
            }

            Long userId = request.getUserId();
            System.out.println("开始查询用户" + userId + "的新消息（提醒模式）");

            // 获取用户未读消息
            ResponseEntity<Map<String, Object>> apiResponse = chatRecordClient.getUnreadMessages(userId);

            if (apiResponse == null || !apiResponse.getStatusCode().is2xxSuccessful()) {
                System.out.println("查询未读消息失败，用户ID: " + userId);
                return NewMessageResponse.error("查询消息失败", userId, "notification");
            }

            Map<String, Object> responseBody = apiResponse.getBody();
            if (responseBody == null) {
                return NewMessageResponse.error("响应数据为空", userId, "notification");
            }

            Boolean success = (Boolean) responseBody.get("success");
            if (success == null || !success) {
                String message = (String) responseBody.get("message");
                return NewMessageResponse.error(message != null ? message : "查询失败", userId, "notification");
            }

            // 解析消息列表
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> rawMessages = (List<Map<String, Object>>) responseBody.get("data");
            List<ChatRecord> allMessages = convertToChatRecords(rawMessages);

            // 创建统计信息
            FilterResult filterResult = filterNotificationMessagesWithStats(allMessages, userId);

            // 获取未读消息数量
            Integer unreadCount = getUnreadMessageCount(userId);

            // 创建详细统计信息
            NewMessageResponse.MessageStatistics statistics = createStatistics(
                allMessages, filterResult, unreadCount, LocalDateTime.now());

            // 创建响应，只包含需要提醒的消息
            NewMessageResponse finalResponse = NewMessageResponse.success(
                filterResult.notificationMessages, unreadCount, userId, statistics);

            // 记录提醒逻辑
            System.out.println("用户" + userId + "查询完成 - 总未读:" + (allMessages != null ? allMessages.size() : 0) +
                             ", 需要提醒:" + filterResult.notificationMessages.size() +
                             ", 自发过滤:" + filterResult.selfSentFilteredCount +
                             ", 超时过滤:" + filterResult.timeoutFilteredCount);

            return finalResponse;

        } catch (Exception e) {
            System.out.println("查询新消息异常，用户ID: " + request.getUserId() + ", 异常: " + e.getMessage());
            e.printStackTrace();
            return NewMessageResponse.error("系统异常: " + e.getMessage(), request.getUserId(), "notification");
        }
    }

    @Override
    public NewMessageResponse subscribeAllUnreadMessages(Long userId) {
        try {
            System.out.println("开始订阅用户" + userId + "的所有未读消息");

            // 获取用户未读消息
            ResponseEntity<Map<String, Object>> apiResponse = chatRecordClient.getUnreadMessages(userId);

            if (apiResponse == null || !apiResponse.getStatusCode().is2xxSuccessful()) {
                System.out.println("订阅查询未读消息失败，用户ID: " + userId);
                return NewMessageResponse.error("查询消息失败", userId, "subscription");
            }

            Map<String, Object> responseBody = apiResponse.getBody();
            if (responseBody == null) {
                return NewMessageResponse.error("响应数据为空", userId, "subscription");
            }

            Boolean success = (Boolean) responseBody.get("success");
            if (success == null || !success) {
                String message = (String) responseBody.get("message");
                return NewMessageResponse.error(message != null ? message : "查询失败", userId, "subscription");
            }

            // 解析消息列表
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> rawMessages = (List<Map<String, Object>>) responseBody.get("data");
            List<ChatRecord> allMessages = convertToChatRecords(rawMessages);

            // 过滤掉用户自己发送的消息（订阅模式不考虑时间限制）
            List<ChatRecord> subscriptionMessages = allMessages != null ?
                allMessages.stream()
                    .filter(message -> !message.getSenderId().equals(userId))
                    .collect(Collectors.toList()) : null;

            // 创建统计信息
            NewMessageResponse.MessageStatistics statistics = createSubscriptionStatistics(
                allMessages, subscriptionMessages, userId, LocalDateTime.now());

            // 创建订阅响应
            NewMessageResponse response = NewMessageResponse.subscription(subscriptionMessages, userId, statistics);

            System.out.println("用户" + userId + "订阅完成 - 总未读:" + (allMessages != null ? allMessages.size() : 0) +
                             ", 返回消息:" + (subscriptionMessages != null ? subscriptionMessages.size() : 0));

            return response;

        } catch (Exception e) {
            System.out.println("订阅未读消息异常，用户ID: " + userId + ", 异常: " + e.getMessage());
            e.printStackTrace();
            return NewMessageResponse.error("系统异常: " + e.getMessage(), userId, "subscription");
        }
    }

    @Override
    public Integer getUnreadMessageCount(Long userId) {
        try {
            ResponseEntity<Map<String, Object>> response = chatRecordClient.getUnreadMessageCount(userId);
            
            if (response != null && response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> responseBody = response.getBody();
                if (responseBody != null && Boolean.TRUE.equals(responseBody.get("success"))) {
                    Object data = responseBody.get("data");
                    if (data instanceof Integer) {
                        return (Integer) data;
                    }
                }
            }
            
            log.warn("获取未读消息数量失败，用户ID: {}", userId);
            return 0;
            
        } catch (Exception e) {
            log.error("获取未读消息数量异常，用户ID: {}", userId, e);
            return 0;
        }
    }

    /**
     * 过滤出需要提醒的消息
     * 只有未读消息且发送时间在3秒内且不是用户自己发送的消息才需要提醒
     *
     * @param messages 所有未读消息列表
     * @param userId 当前用户ID
     * @return 需要提醒的消息列表
     */
    private List<ChatRecord> filterNotificationMessages(List<ChatRecord> messages, Long userId) {
        if (messages == null || messages.isEmpty()) {
            return messages;
        }

        LocalDateTime now = LocalDateTime.now();

        return messages.stream()
                .filter(message -> {
                    // 检查消息是否为未读
                    if (message.getIsRead() == null || message.getIsRead()) {
                        return false;
                    }

                    // 检查是否是用户自己发送的消息，如果是则不提醒
                    if (message.getSenderId() != null && message.getSenderId().equals(userId)) {
                        System.out.println("过滤掉用户自己发送的消息 - 消息ID: " + message.getId() +
                                         ", 发送者ID: " + message.getSenderId() + ", 当前用户ID: " + userId);
                        return false;
                    }

                    // 检查发送时间是否在3秒内
                    if (message.getSendTime() == null) {
                        return false;
                    }

                    // 计算时间差（秒）
                    long secondsDiff = ChronoUnit.SECONDS.between(message.getSendTime(), now);
                    boolean shouldNotify = secondsDiff <= NOTIFICATION_TIME_THRESHOLD_SECONDS;

                    System.out.println("消息过滤检查 - 消息ID: " + message.getId() +
                                     ", 发送者ID: " + message.getSenderId() +
                                     ", 接收者ID: " + message.getReceiverId() +
                                     ", 发送时间: " + message.getSendTime() +
                                     ", 当前时间: " + now +
                                     ", 时间差: " + secondsDiff + "秒" +
                                     ", 是否提醒: " + shouldNotify);

                    return shouldNotify;
                })
                .collect(Collectors.toList());
    }

    /**
     * 过滤结果类
     */
    private static class FilterResult {
        List<ChatRecord> notificationMessages;
        int selfSentFilteredCount;
        int timeoutFilteredCount;

        FilterResult(List<ChatRecord> notificationMessages, int selfSentFilteredCount, int timeoutFilteredCount) {
            this.notificationMessages = notificationMessages;
            this.selfSentFilteredCount = selfSentFilteredCount;
            this.timeoutFilteredCount = timeoutFilteredCount;
        }
    }

    /**
     * 带统计信息的过滤方法
     */
    private FilterResult filterNotificationMessagesWithStats(List<ChatRecord> messages, Long userId) {
        if (messages == null || messages.isEmpty()) {
            return new FilterResult(messages, 0, 0);
        }

        LocalDateTime now = LocalDateTime.now();
        AtomicInteger selfSentCount = new AtomicInteger(0);
        AtomicInteger timeoutCount = new AtomicInteger(0);

        List<ChatRecord> notificationMessages = messages.stream()
                .filter(message -> {
                    // 检查消息是否为未读
                    if (message.getIsRead() == null || message.getIsRead()) {
                        return false;
                    }

                    // 检查是否是用户自己发送的消息
                    if (message.getSenderId() != null && message.getSenderId().equals(userId)) {
                        selfSentCount.incrementAndGet();
                        return false;
                    }

                    // 检查发送时间是否在3秒内
                    if (message.getSendTime() == null) {
                        return false;
                    }

                    long secondsDiff = ChronoUnit.SECONDS.between(message.getSendTime(), now);
                    if (secondsDiff > NOTIFICATION_TIME_THRESHOLD_SECONDS) {
                        timeoutCount.incrementAndGet();
                        return false;
                    }

                    return true;
                })
                .collect(Collectors.toList());

        return new FilterResult(notificationMessages, selfSentCount.get(), timeoutCount.get());
    }

    /**
     * 创建统计信息
     */
    private NewMessageResponse.MessageStatistics createStatistics(
            List<ChatRecord> allMessages, FilterResult filterResult, Integer unreadCount, LocalDateTime queryTime) {

        NewMessageResponse.MessageStatistics stats = new NewMessageResponse.MessageStatistics();
        stats.setTotalUnreadCount(unreadCount);
        stats.setNotificationCount(filterResult.notificationMessages.size());
        stats.setSelfSentFilteredCount(filterResult.selfSentFilteredCount);
        stats.setTimeoutFilteredCount(filterResult.timeoutFilteredCount);
        stats.setQueryTime(queryTime.format(TIME_FORMATTER));

        // 设置最新和最旧消息时间
        if (allMessages != null && !allMessages.isEmpty()) {
            LocalDateTime latest = allMessages.stream()
                    .map(ChatRecord::getSendTime)
                    .filter(time -> time != null)
                    .max(LocalDateTime::compareTo)
                    .orElse(null);

            LocalDateTime oldest = allMessages.stream()
                    .map(ChatRecord::getSendTime)
                    .filter(time -> time != null)
                    .min(LocalDateTime::compareTo)
                    .orElse(null);

            stats.setLatestMessageTime(latest != null ? latest.format(TIME_FORMATTER) : null);
            stats.setOldestUnreadTime(oldest != null ? oldest.format(TIME_FORMATTER) : null);
        }

        return stats;
    }

    /**
     * 创建订阅统计信息
     */
    private NewMessageResponse.MessageStatistics createSubscriptionStatistics(
            List<ChatRecord> allMessages, List<ChatRecord> subscriptionMessages, Long userId, LocalDateTime queryTime) {

        NewMessageResponse.MessageStatistics stats = new NewMessageResponse.MessageStatistics();
        stats.setTotalUnreadCount(allMessages != null ? allMessages.size() : 0);
        stats.setNotificationCount(0); // 订阅模式不是提醒

        // 计算自发消息数量
        int selfSentCount = allMessages != null ?
            (int) allMessages.stream().filter(msg -> msg.getSenderId().equals(userId)).count() : 0;
        stats.setSelfSentFilteredCount(selfSentCount);
        stats.setTimeoutFilteredCount(0); // 订阅模式不考虑时间过滤
        stats.setQueryTime(queryTime.format(TIME_FORMATTER));

        // 设置最新和最旧消息时间
        if (subscriptionMessages != null && !subscriptionMessages.isEmpty()) {
            LocalDateTime latest = subscriptionMessages.stream()
                    .map(ChatRecord::getSendTime)
                    .filter(time -> time != null)
                    .max(LocalDateTime::compareTo)
                    .orElse(null);

            LocalDateTime oldest = subscriptionMessages.stream()
                    .map(ChatRecord::getSendTime)
                    .filter(time -> time != null)
                    .min(LocalDateTime::compareTo)
                    .orElse(null);

            stats.setLatestMessageTime(latest != null ? latest.format(TIME_FORMATTER) : null);
            stats.setOldestUnreadTime(oldest != null ? oldest.format(TIME_FORMATTER) : null);
        }

        return stats;
    }

    /**
     * 将Map列表转换为ChatRecord列表
     */
    private List<ChatRecord> convertToChatRecords(List<Map<String, Object>> rawMessages) {
        if (rawMessages == null || rawMessages.isEmpty()) {
            return new ArrayList<>();
        }

        List<ChatRecord> chatRecords = new ArrayList<>();
        for (Map<String, Object> rawMessage : rawMessages) {
            try {
                ChatRecord chatRecord = convertToChatRecord(rawMessage);
                if (chatRecord != null) {
                    chatRecords.add(chatRecord);
                }
            } catch (Exception e) {
                System.out.println("转换消息失败: " + e.getMessage() + ", 原始数据: " + rawMessage);
            }
        }

        return chatRecords;
    }

    /**
     * 将Map转换为ChatRecord对象
     */
    private ChatRecord convertToChatRecord(Map<String, Object> rawMessage) {
        if (rawMessage == null) {
            return null;
        }

        try {
            ChatRecord chatRecord = new ChatRecord();

            // 转换ID
            Object idObj = rawMessage.get("id");
            if (idObj != null) {
                chatRecord.setId(Long.valueOf(idObj.toString()));
            }

            // 转换sessionId
            Object sessionIdObj = rawMessage.get("sessionId");
            if (sessionIdObj != null) {
                chatRecord.setSessionId(sessionIdObj.toString());
            }

            // 转换senderId
            Object senderIdObj = rawMessage.get("senderId");
            if (senderIdObj != null) {
                chatRecord.setSenderId(Long.valueOf(senderIdObj.toString()));
            }

            // 转换receiverId
            Object receiverIdObj = rawMessage.get("receiverId");
            if (receiverIdObj != null) {
                chatRecord.setReceiverId(Long.valueOf(receiverIdObj.toString()));
            }

            // 转换sendTime
            Object sendTimeObj = rawMessage.get("sendTime");
            if (sendTimeObj != null) {
                try {
                    if (sendTimeObj instanceof String) {
                        // 尝试多种时间格式
                        String timeStr = sendTimeObj.toString();
                        if (timeStr.contains("T")) {
                            // ISO格式: 2024-01-01T10:30:00
                            chatRecord.setSendTime(LocalDateTime.parse(timeStr));
                        } else {
                            // 自定义格式: 2024-01-01 10:30:00
                            chatRecord.setSendTime(LocalDateTime.parse(timeStr, TIME_FORMATTER));
                        }
                    } else if (sendTimeObj instanceof List) {
                        // 处理数组格式的时间 [2024, 1, 1, 10, 30, 0]
                        @SuppressWarnings("unchecked")
                        List<Integer> timeArray = (List<Integer>) sendTimeObj;
                        if (timeArray.size() >= 6) {
                            chatRecord.setSendTime(LocalDateTime.of(
                                timeArray.get(0), timeArray.get(1), timeArray.get(2),
                                timeArray.get(3), timeArray.get(4), timeArray.get(5)
                            ));
                        } else if (timeArray.size() >= 3) {
                            // 只有日期部分
                            chatRecord.setSendTime(LocalDateTime.of(
                                timeArray.get(0), timeArray.get(1), timeArray.get(2), 0, 0, 0
                            ));
                        }
                    }
                } catch (Exception e) {
                    System.out.println("时间转换失败: " + e.getMessage() + ", 原始时间: " + sendTimeObj);
                    // 设置为当前时间作为默认值
                    chatRecord.setSendTime(LocalDateTime.now());
                }
            }

            // 转换content
            Object contentObj = rawMessage.get("content");
            if (contentObj != null) {
                chatRecord.setContent(contentObj.toString());
            }

            // 转换contentType
            Object contentTypeObj = rawMessage.get("contentType");
            if (contentTypeObj != null) {
                chatRecord.setContentType(contentTypeObj.toString());
            }

            // 转换isRead
            Object isReadObj = rawMessage.get("isRead");
            if (isReadObj != null) {
                if (isReadObj instanceof Boolean) {
                    chatRecord.setIsRead((Boolean) isReadObj);
                } else {
                    chatRecord.setIsRead(Boolean.valueOf(isReadObj.toString()));
                }
            }

            System.out.println("转换消息成功 - ID: " + chatRecord.getId() +
                             ", 发送者: " + chatRecord.getSenderId() +
                             ", 接收者: " + chatRecord.getReceiverId() +
                             ", 时间: " + chatRecord.getSendTime() +
                             ", 已读: " + chatRecord.getIsRead());

            return chatRecord;

        } catch (Exception e) {
            System.out.println("转换单个消息失败: " + e.getMessage() + ", 原始数据: " + rawMessage);
            e.printStackTrace();
            return null;
        }
    }
}
