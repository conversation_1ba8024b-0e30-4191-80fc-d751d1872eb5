<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>WebRtc 会议</title>
    <link rel="stylesheet" type="text/css" th:href="@{/plugins/layui/css/layui.css}">
    <style>
        .local-video {
            width: 100%;
            height: 400px;
        }
        .remote-video {
            width: 100%;
            height: 200px;
        }
        #other-container{
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #E9E7E7;
        }
    </style>
</head>
<body>
<div class="layui-container">
    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 25px;">
        <legend style="margin-left: 40%;">WebRtc 会议</legend>
    </fieldset>
    <div class="layui-row layui-col-space5">
        <div class="layui-col-md3">
            <blockquote class="layui-elem-quote">我</blockquote>
            <div>
                <video autoplay id="localVideo" muted class="local-video" th:poster="@{/images/a.jpeg}"></video>
            </div>
        </div>
        <div class="layui-col-md9">
            <div class="layui-row" id="other-container"></div>
        </div>
    </div>
    <div class="layui-row">
        <div class="layui-col-xs12">
            <blockquote class="layui-elem-quote">操作区域</blockquote>
            <div class="layui-btn-container">
                <button type="button" class="layui-btn layui-btn-normal" onclick="OPT.register()">注册</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="OPT.videoSwitch()">摄像头</button>
                <button type="button" class="layui-btn" onclick="OPT.audioSwitch()">音频</button>
                <button type="button" class="layui-btn layui-btn-normal" onclick="OPT.shareScreen()">屏幕共享</button>
            </div>
        </div>
    </div>
</div>
</body>

<!--注册弹窗-->
<div id="registerDialog" style="display: none;margin-top:10px;">
    <form class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label">用户名：</label>
            <div class="layui-input-inline">
                <input type="text" name="userId" placeholder="请输入用户名" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">房间号：</label>
            <div class="layui-input-inline">
                <input type="text" name="roomId" placeholder="请输入房间号" class="layui-input">
            </div>
        </div>
    </form>
</div>

<!--jquery-->
<script type="application/javascript" th:src="@{/javascript/jquery-2.1.4.js}"></script>
<!--layui-->
<script type="application/javascript" th:src="@{/plugins/layui/layui.js}"></script>
<!--rtc-->
<script type="application/javascript" th:src="@{/javascript/meeting/rtc.js}"></script>
<!--sip-->
<script type="application/javascript" th:src="@{/javascript/meeting/sip.js}"></script>
<!--opt-->
<script type="application/javascript" th:src="@{/javascript/meeting/opt.js}"></script>

<script>
    /**
     * PeerConnection
     */
    var PeerConnection = window.RTCPeerConnection ||
        window.mozRTCPeerConnection ||
        window.webkitRTCPeerConnection;
    /**
     * 本地peerconnection 对象
     */
    var localStream = null;

    // 声明用户ID
    var userId, roomId = null;

    var roomList = [];

    /**
     * 存放peerconnection
     */
    var RtcPcMaps = new Map();

    /**
     * 公共STUN
     * @type {{iceServers: [{urls: string}]}}
     */
    var rtcPcParams = {
        iceServers: [
            {urls: 'stun:stun.l.google.com:19302'}
        ]
    }

    var layer = null;

    $(function () {
        Layui.init();
    });

    /**
     * 初始化layui组件
     * @type {{init: Layui.init}}
     */
    var Layui = {
        /**
         * 初始化layui
         */
        init: () => {
            layui.use(['layer'], function () {
                layer = layui.layer;
            });
        }
    }
    window.onbeforeunload = function () {
        SOCKET.disConnect();
    }
</script>
</html>
