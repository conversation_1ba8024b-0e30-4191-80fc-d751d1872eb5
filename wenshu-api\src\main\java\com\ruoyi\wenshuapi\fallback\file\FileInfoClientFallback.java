package com.ruoyi.wenshuapi.fallback.file;

import com.ruoyi.wenshuapi.client.file.FileInfoClient;
import com.ruoyi.wenshuapi.pojo.file.FileInfoPojo;
import com.ruoyi.wenshuapi.util.file.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * FileInfoClient的降级处理工厂
 * 当远程服务不可用或调用失败时提供容错响应
 *
 * 实现说明：
 * 1. 记录详细错误日志便于问题排查
 * 2. 返回业务可识别的错误码和友好提示
 * 3. 避免返回null或空指针异常
 * 4. 针对不同方法返回合理的降级值（如查询返回空集合，操作返回0等）
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-06-28
 */
@Component
public class FileInfoClientFallback implements FallbackFactory<FileInfoClient> {
    private static final Logger log = LoggerFactory.getLogger(FileInfoClientFallback.class);

    @Override
    public FileInfoClient create(Throwable cause) {
        return new FileInfoClient() {
            // 统一错误码：503表示服务不可用
            private static final int SERVICE_UNAVAILABLE_CODE = 503;

            // 通用错误信息前缀
            private static final String ERROR_PREFIX = "文件服务不可用，请稍后重试。原因：";

            @Override
            public ApiResponse<Map<String, Object>> uploadFile(MultipartFile file, Integer uploaderId,
                                                               String ownerType, String fileStatus) {
                logError("文件上传", file != null ? file.getOriginalFilename() : "null", cause);
                return ApiResponse.failed(
                        buildErrorMessage("文件上传", file != null ? file.getOriginalFilename() : "未知文件"),
                        SERVICE_UNAVAILABLE_CODE
                );
            }

            @Override
            public ApiResponse<Integer> addFile(FileInfoPojo fileInfo) {
                logError("添加文件", fileInfo, cause);
                return ApiResponse.failed(
                        buildErrorMessage("添加文件", fileInfo.getFileName()),
                        SERVICE_UNAVAILABLE_CODE
                );
            }

            @Override
            public ApiResponse<Integer> deleteFile(int fileId) {
                logError("删除文件", fileId, cause);
                return ApiResponse.failed(
                        buildErrorMessage("删除文件", "ID=" + fileId),
                        SERVICE_UNAVAILABLE_CODE
                );
            }

            @Override
            public ApiResponse<Integer> updateFile(FileInfoPojo fileInfo) {
                logError("更新文件", fileInfo.getFileId(), cause);
                return ApiResponse.failed(
                        buildErrorMessage("更新文件", "ID=" + fileInfo.getFileId()),
                        SERVICE_UNAVAILABLE_CODE
                );
            }

            @Override
            public ApiResponse<FileInfoPojo> getFileById(int fileId) {
                logError("查询文件详情", fileId, cause);
                return ApiResponse.failed(
                        buildErrorMessage("查询文件详情", "ID=" + fileId),
                        SERVICE_UNAVAILABLE_CODE
                );
            }

            @Override
            public ApiResponse<List<FileInfoPojo>> getFilesByCondition(FileInfoPojo condition) {
                logError("条件查询文件", condition, cause);
                // 返回空集合避免NPE
                return ApiResponse.success(
                        Collections.emptyList(),
                        buildWarningMessage("条件查询")
                );
            }

            @Override
            public ApiResponse<FileInfoPojo> getFileByPath(String filePath) {
                logError("路径查询文件", filePath, cause);
                return ApiResponse.failed(
                        buildErrorMessage("路径查询文件", filePath),
                        SERVICE_UNAVAILABLE_CODE
                );
            }

            @Override
            public ApiResponse<Integer> changeFileStatus(int fileId, String status) {
                logError("更新文件状态", fileId + "->" + status, cause);
                return ApiResponse.failed(
                        buildErrorMessage("更新状态", "ID=" + fileId),
                        SERVICE_UNAVAILABLE_CODE
                );
            }

            @Override
            public ApiResponse<Integer> countFilesByUploader(int uploaderId) {
                logError("统计用户文件", uploaderId, cause);
                // 返回0避免业务中断
                return ApiResponse.success(0, buildWarningMessage("文件统计"));
            }

            @Override
            public ApiResponse<Integer> batchUpdateStatus(List<Integer> fileIds, String status) {
                logError("批量更新状态", fileIds.size() + "个文件", cause);
                return ApiResponse.failed(
                        buildErrorMessage("批量更新状态", fileIds.size() + "个文件"),
                        SERVICE_UNAVAILABLE_CODE
                );
            }

            @Override
            public ApiResponse<Boolean> isFileOwnedByUser(int fileId, int userId) {
                logError("验证文件归属", fileId + "/用户" + userId, cause);
                // 返回false确保安全（无权限）
                return ApiResponse.success(false, buildWarningMessage("权限验证"));
            }

            @Override
            public ApiResponse<Map<String, Object>> batchDeleteFiles(List<Integer> fileIds) {
                logError("批量删除文件", fileIds.size() + "个文件", cause);

                // 返回降级的批量删除结果
                Map<String, Object> fallbackResult = new HashMap<>();
                fallbackResult.put("totalCount", fileIds.size());
                fallbackResult.put("successCount", 0);
                fallbackResult.put("failedCount", fileIds.size());
                fallbackResult.put("physicalFileDeletedCount", 0);

                return ApiResponse.failed(
                        buildErrorMessage("批量删除", fileIds.size() + "个文件"),
                        SERVICE_UNAVAILABLE_CODE,
                        fallbackResult
                );
            }

            // 构建完整的错误信息
            private String buildErrorMessage(String action, String target) {
                return String.format("%s失败[%s]。%s%s",
                        action,
                        target,
                        ERROR_PREFIX,
                        cause != null ? cause.getMessage() : "未知错误"
                );
            }

            // 构建警告信息（用于返回成功但实际降级的场景）
            private String buildWarningMessage(String action) {
                return String.format("警告：%s操作已降级返回默认值（服务不可用：%s）",
                        action,
                        cause != null ? cause.getMessage() : "服务超时"
                );
            }

            // 统一错误日志记录
            private void logError(String action, Object target, Throwable ex) {
                log.error("[文件服务] {} 操作失败 - 目标: {} | 原因: {} | 错误堆栈: ",
                        action,
                        target,
                        ex != null ? ex.getMessage() : "未知",
                        ex
                );
            }
        };
    }
}