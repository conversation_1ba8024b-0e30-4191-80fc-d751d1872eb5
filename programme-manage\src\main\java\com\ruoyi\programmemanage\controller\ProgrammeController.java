package com.ruoyi.programmemanage.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.programmemanage.client.ProgrammeFeignClient;
import com.ruoyi.programmemanage.common.CalendarParticipant;
import com.ruoyi.programmemanage.common.Programme;
import com.ruoyi.programmemanage.common.ProgrammeVO;
import com.ruoyi.programmemanage.common.ApiResponse;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import java.time.LocalDateTime;

import java.time.format.DateTimeFormatter;
import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/programme")
public class ProgrammeController {
    private final ProgrammeFeignClient programmeClient;
    
    /**
     * 添加日程
     * @param userId 用户ID
     * @param programmeVO 日程信息
     * @return 添加结果
     */
    @PostMapping("/add/{userId}")
    public ApiResponse<?> addProgramme(@PathVariable("userId") Long userId, @RequestBody ProgrammeVO programmeVO) {
        // 参数校验
        if (programmeVO.getTitle() == null || programmeVO.getTitle().trim().isEmpty()) {
            return ApiResponse.validateFailed("日程标题不能为空");
        }
        
        if (programmeVO.getDescription() == null || programmeVO.getDescription().trim().isEmpty()) {
            return ApiResponse.validateFailed("日程描述不能为空");
        }
        // VO转换为实体
        Programme programme = convertToEntity(programmeVO);
        // 使用服务层的方法添加日程
        return programmeClient.addProgramme(userId, programme);
    }
    
    /**
     * 删除日程
     * @param event_id 日程ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{event_id}")
    public ApiResponse<?> deleteProgramme(@PathVariable("event_id") int event_id) {
        // 使用服务层方法删除日程
        return programmeClient.deleteProgramme(event_id);
    }
    
    /*
     * 根据时间范围查询所有日程
     * @param start_time 开始时间，格式：yyyy-MM-dd HH:mm:ss
     * @param end_time 结束时间，格式：yyyy-MM-dd HH:mm:ss
     * @return 日程列表
     */
    @GetMapping("/getByTime")
    public ApiResponse<?> listProgramme(@RequestParam(required = false, value = "userId") int userId, 
                                        @RequestParam(required = false, value = "startTime") String startTime, 
                                        @RequestParam(required = false, value = "endTime") String endTime) {
        System.out.println("startTime:"+startTime);
        System.out.println("endTime:"+endTime);
        
        if (startTime != null && endTime != null) {
            List<CalendarParticipant> participants = programmeClient.list(userId);
            List<Integer> eventIds = participants.stream()
                    .map(CalendarParticipant::getEventId)
                    .toList();
            return programmeClient.listProgramme(eventIds, startTime, endTime);
        } else {
            // 如果没有提供时间参数，则查询所有日程
            return programmeClient.getAll(userId);
        }
    }

    @GetMapping("/getById/{event_id}")
    public ApiResponse<?> getById(@PathVariable("event_id") int event_id) {
        return programmeClient.getById(event_id);
    }

    /**
     * 将VO转换为实体
     */
    private Programme convertToEntity(ProgrammeVO vo) {
        Programme programme = new Programme();
        if(vo.getTitle() != null && !vo.getTitle().trim().isEmpty()){
            programme.setTitle(vo.getTitle());
        }
        if(vo.getStartTime() != null){
            programme.setStartTime(vo.getStartTime());
        }
        if(vo.getDescription() != null && !vo.getDescription().trim().isEmpty()){
            programme.setDescription(vo.getDescription());
        }
        if(vo.getCreatorId() != null){
            programme.setCreatorId(vo.getCreatorId());
        }
        if(vo.getOwnerType() != null){
            programme.setOwnerType(vo.getOwnerType());
        }
        if(vo.getRepeatRule() != null){
            programme.setRepeatRule(vo.getRepeatRule());
        }
        if(vo.getRemindTime() != null){
            programme.setRemindTime(vo.getRemindTime());
        }
        programme.setEventStatus(vo.getEventStatus());
        return programme;
    }

    @PutMapping("/update/{event_id}")
    public ApiResponse<?> updateProgramme(@PathVariable("event_id") int event_id, @RequestBody ProgrammeVO programmeVO) {
        Programme programme = convertToEntity(programmeVO);
        programme.setEventId(event_id); // 设置日程ID
        return programmeClient.updateProgramme(event_id, programme); // 更新日程
    }

    @GetMapping("/getAll")
    public ApiResponse<?> getAll(@RequestParam(required = false, value = "userId") Long userId) {
        // Convert Long to int since the Feign client expects an int
        List<Programme> programmes = programmeClient.ProgetAll(userId);
        return ApiResponse.success(programmes, "查询日程成功");
    }

} 