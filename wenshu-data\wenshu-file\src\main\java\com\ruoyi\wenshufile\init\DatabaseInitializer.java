//package com.ruoyi.wenshufile.init;
//
//import com.ruoyi.wenshufile.dao.FileInfoDao;
//import com.ruoyi.wenshuapi.pojo.file.FileInfoPojo;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.stereotype.Component;
//
//import java.time.LocalDateTime;
//import java.time.temporal.ChronoUnit;
//import java.util.Arrays;
//import java.util.List;
//import java.util.Random;
//import java.util.concurrent.ThreadLocalRandom;
//
///**
// * 数据库初始化器
// * 在应用启动时自动向数据库填充100条虚构文件数据
// */
//@Component
//public class DatabaseInitializer implements CommandLineRunner {
//
//    // 文件状态选项
//    private static final List<String> FILE_STATUSES = Arrays.asList("可读写", "协作", "不可读写");
//    // 所有者类型选项
//    private static final List<String> OWNER_TYPES = Arrays.asList("个人", "团队");
//    // 文件类型选项
//    private static final List<String> FILE_TYPES = Arrays.asList("pdf", "docx", "xlsx", "pptx", "jpg", "png", "txt");
//    // 文件名前缀选项
//    private static final List<String> FILE_PREFIXES = Arrays.asList("项目", "合同", "报告", "方案", "设计", "预算", "总结", "记录", "分析", "手册");
//
//    @Autowired
//    private FileInfoDao fileInfoDao;
//
//    @Override
//    public void run(String... args) throws Exception {
//        // 检查是否已有数据
//        int existingCount = fileInfoDao.countByUploader(0);
//        if (existingCount > 0) {
//            System.out.println("数据库已有数据，跳过虚构数据生成");
//            return;
//        }
//
//        System.out.println("开始生成虚构文件数据...");
//        for (int i = 1; i <= 100; i++) {
//            FileInfoPojo file = generateFakeFileData(i);
//            fileInfoDao.insertFile(file);
//        }
//        System.out.println("成功生成100条虚构文件数据");
//    }
//
//    /**
//     * 生成虚构文件数据
//     *
//     * @param index 文件索引
//     * @return 文件信息对象
//     */
//    private FileInfoPojo generateFakeFileData(int index) {
//        Random random = ThreadLocalRandom.current();
//
//        FileInfoPojo file = new FileInfoPojo();
//
//        // 生成文件名
//        String prefix = FILE_PREFIXES.get(random.nextInt(FILE_PREFIXES.size()));
//        String type = FILE_TYPES.get(random.nextInt(FILE_TYPES.size()));
//        file.setFileName(prefix + "_" + index + "." + type);
//
//        // 生成文件路径
//        int year = 2022 + random.nextInt(3); // 2022-2024
//        int month = 1 + random.nextInt(12);
//        file.setFilePath("/storage/" + year + "/" + month + "/" + file.getFileName());
//
//        // 生成上传时间（过去1年内随机）
//        long daysAgo = random.nextInt(365);
//        file.setUploadTime(LocalDateTime.now().minus(daysAgo, ChronoUnit.DAYS));
//
//        // 生成上传者ID（1-50之间的随机用户）
//        file.setUploaderId(1 + random.nextInt(50));
//
//        // 生成文件大小（1KB - 100MB）
//        file.setFileSize(1024L + random.nextInt(99 * 1024 * 1024));
//
//        // 随机选择文件状态
//        file.setFileStatus(FILE_STATUSES.get(random.nextInt(FILE_STATUSES.size())));
//
//        // 随机选择所有者类型
//        file.setOwnerType(OWNER_TYPES.get(random.nextInt(OWNER_TYPES.size())));
//
//        return file;
//    }
//}