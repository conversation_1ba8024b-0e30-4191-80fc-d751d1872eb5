package com.ruoyi.wenshuwchat.service.impl;

import com.ruoyi.wenshuapi.pojo.wchat.ChatRecord;
import com.ruoyi.wenshuwchat.dao.ChatRecordDao;
import com.ruoyi.wenshuwchat.service.ChatRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 聊天记录服务实现类
 * 提供聊天记录的完整业务逻辑实现
 *
 * 主要功能：
 * 1. 消息发送与接收管理
 * 2. 消息状态管理（已读/未读）
 * 3. 会话管理与权限控制
 * 4. 消息查询与分页
 * 5. 消息删除与撤回
 * 6. 文件消息处理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Service
@Transactional
public class ChatRecordServiceImpl implements ChatRecordService {

    @Autowired
    private ChatRecordDao chatRecordDao;

    // 常量定义
    private static final int MAX_TEXT_LENGTH = 5000;
    private static final int MAX_PAGE_SIZE = 100;
    private static final int DEFAULT_RECALL_TIME_LIMIT = 2; // 默认撤回时间限制（分钟）
    private static final String[] SUPPORTED_CONTENT_TYPES = {"text", "image", "video", "file"};

    // 会话ID格式
    private static final String SESSION_ID_PATTERN_MD5 = "^[a-f0-9]{32}$";
    private static final String SESSION_ID_PATTERN_SIMPLE = "^session_\\d+_\\d+$";
    private static final String SESSION_ID_PREFIX = "session_";

    @Override
    public Long sendMessage(ChatRecord chatRecord) {
        // 参数验证
        validateChatRecord(chatRecord);

        // 设置默认值
        if (chatRecord.getSendTime() == null) {
            chatRecord.setSendTime(LocalDateTime.now());
        }
        if (chatRecord.getIsRead() == null) {
            chatRecord.setIsRead(false);
        }

        // 生成会话ID（如果未提供）
        if (!StringUtils.hasText(chatRecord.getSessionId())) {
            String sessionId = generateSessionId(chatRecord.getSenderId(), chatRecord.getReceiverId());
            chatRecord.setSessionId(sessionId);
        }

        // 插入数据库
        int result = chatRecordDao.insert(chatRecord);
        if (result <= 0) {
            throw new RuntimeException("消息发送失败");
        }

        return chatRecord.getId();
    }

    @Override
    public int deleteMessage(Long id) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("消息ID不能为空或无效");
        }
        return chatRecordDao.deleteById(id);
    }

    @Override
    public int deleteSessionMessages(String sessionId) {
        if (!StringUtils.hasText(sessionId)) {
            throw new IllegalArgumentException("会话ID不能为空");
        }
        return chatRecordDao.deleteBySessionId(sessionId);
    }

    @Override
    public int markMessageAsRead(Long id) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("消息ID不能为空或无效");
        }
        return chatRecordDao.updateReadStatus(id, true);
    }

    @Override
    public int markSessionMessagesAsRead(String sessionId, Long receiverId) {
        if (!StringUtils.hasText(sessionId)) {
            throw new IllegalArgumentException("会话ID不能为空");
        }
        if (receiverId == null || receiverId <= 0) {
            throw new IllegalArgumentException("接收者ID不能为空或无效");
        }
        return chatRecordDao.markSessionMessagesAsRead(sessionId, receiverId);
    }

    @Override
    @Transactional(readOnly = true)
    public ChatRecord getMessageById(Long id) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("消息ID不能为空或无效");
        }
        return chatRecordDao.selectById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ChatRecord> getMessagesBySessionId(String sessionId) {
        if (!StringUtils.hasText(sessionId)) {
            throw new IllegalArgumentException("会话ID不能为空");
        }
        return chatRecordDao.selectBySessionId(sessionId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ChatRecord> getMessagesBySessionIdWithPaging(String sessionId, int page, int size) {
        if (!StringUtils.hasText(sessionId)) {
            throw new IllegalArgumentException("会话ID不能为空");
        }
        if (page < 1) {
            throw new IllegalArgumentException("页码必须大于0");
        }
        if (size < 1 || size > MAX_PAGE_SIZE) {
            throw new IllegalArgumentException("每页大小必须在1-" + MAX_PAGE_SIZE + "之间");
        }

        int offset = (page - 1) * size;
        return chatRecordDao.selectBySessionIdWithPaging(sessionId, offset, size);
    }

    @Override
    @Transactional(readOnly = true)
    public int getUnreadMessageCount(Long receiverId) {
        if (receiverId == null || receiverId <= 0) {
            throw new IllegalArgumentException("接收者ID不能为空或无效");
        }
        return chatRecordDao.countUnreadMessages(receiverId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ChatRecord> getUnreadMessages(Long receiverId) {
        if (receiverId == null || receiverId <= 0) {
            throw new IllegalArgumentException("接收者ID不能为空或无效");
        }
        return chatRecordDao.selectUnreadMessages(receiverId);
    }

    @Override
    @Transactional(readOnly = true)
    public int getUnreadMessageCountBySession(String sessionId, Long receiverId) {
        if (!StringUtils.hasText(sessionId)) {
            throw new IllegalArgumentException("会话ID不能为空");
        }
        if (receiverId == null || receiverId <= 0) {
            throw new IllegalArgumentException("接收者ID不能为空或无效");
        }
        return chatRecordDao.countUnreadMessagesBySession(sessionId, receiverId);
    }

    @Override
    @Transactional(readOnly = true)
    public ChatRecord getLatestMessage(String sessionId) {
        if (!StringUtils.hasText(sessionId)) {
            throw new IllegalArgumentException("会话ID不能为空");
        }
        return chatRecordDao.selectLatestBySessionId(sessionId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ChatRecord> getMessagesByTimeRange(String sessionId, LocalDateTime startTime, LocalDateTime endTime) {
        if (!StringUtils.hasText(sessionId)) {
            throw new IllegalArgumentException("会话ID不能为空");
        }
        if (startTime == null || endTime == null) {
            throw new IllegalArgumentException("开始时间和结束时间不能为空");
        }
        if (startTime.isAfter(endTime)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
        return chatRecordDao.selectBySessionIdAndTimeRange(sessionId, startTime, endTime);
    }

    @Override
    public String generateSessionId(Long userId1, Long userId2) {
        if (userId1 == null || userId2 == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (userId1 <= 0 || userId2 <= 0) {
            throw new IllegalArgumentException("用户ID必须大于0");
        }
        if (Objects.equals(userId1, userId2)) {
            throw new IllegalArgumentException("不能与自己聊天");
        }

        // 确保会话ID的唯一性：较小的ID在前，较大的ID在后
        Long smallerId = Math.min(userId1, userId2);
        Long largerId = Math.max(userId1, userId2);

        // 生成会话ID字符串
        String sessionString = smallerId + "-" + largerId;

        try {
            // 使用MD5哈希生成固定长度的会话ID
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(sessionString.getBytes());

            // 转换为十六进制字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }

            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            // 如果MD5不可用，使用简单的字符串拼接
            return "session_" + smallerId + "_" + largerId;
        }
    }

    /**
     * 验证聊天记录参数
     *
     * @param chatRecord 聊天记录对象
     */
    private void validateChatRecord(ChatRecord chatRecord) {
        if (chatRecord == null) {
            throw new IllegalArgumentException("聊天记录不能为空");
        }

        if (chatRecord.getSenderId() == null || chatRecord.getSenderId() <= 0) {
            throw new IllegalArgumentException("发送者ID不能为空或无效");
        }

        if (chatRecord.getReceiverId() == null || chatRecord.getReceiverId() <= 0) {
            throw new IllegalArgumentException("接收者ID不能为空或无效");
        }

        if (Objects.equals(chatRecord.getSenderId(), chatRecord.getReceiverId())) {
            throw new IllegalArgumentException("发送者和接收者不能是同一人");
        }

        if (!StringUtils.hasText(chatRecord.getContent())) {
            throw new IllegalArgumentException("消息内容不能为空");
        }

        if (!StringUtils.hasText(chatRecord.getContentType())) {
            throw new IllegalArgumentException("消息类型不能为空");
        }

        // 验证消息类型
        String contentType = chatRecord.getContentType().toLowerCase();
        if (!Arrays.asList(SUPPORTED_CONTENT_TYPES).contains(contentType)) {
            throw new IllegalArgumentException("不支持的消息类型: " + contentType +
                "，支持的类型: " + String.join(", ", SUPPORTED_CONTENT_TYPES));
        }

        // 设置标准化的消息类型
        chatRecord.setContentType(contentType);

        // 验证文本消息长度
        if ("text".equals(contentType) && chatRecord.getContent().length() > MAX_TEXT_LENGTH) {
            throw new IllegalArgumentException("文本消息长度不能超过" + MAX_TEXT_LENGTH + "字符");
        }
    }

    /**
     * 验证会话ID格式
     *
     * @param sessionId 会话ID
     * @return 是否有效
     */
    private boolean isValidSessionId(String sessionId) {
        if (!StringUtils.hasText(sessionId)) {
            return false;
        }

        // 检查是否为MD5格式（32位十六进制字符）
        if (sessionId.matches(SESSION_ID_PATTERN_MD5)) {
            return true;
        }

        // 检查是否为简单格式（session_数字_数字）
        return sessionId.matches(SESSION_ID_PATTERN_SIMPLE);
    }

    /**
     * 从会话ID中提取用户ID（仅适用于简单格式）
     *
     * @param sessionId 会话ID
     * @return 用户ID数组，[0]为较小的ID，[1]为较大的ID
     */
    private Long[] extractUserIdsFromSessionId(String sessionId) {
        if (!StringUtils.hasText(sessionId) || !sessionId.startsWith(SESSION_ID_PREFIX)) {
            return null;
        }

        try {
            String[] parts = sessionId.split("_");
            if (parts.length != 3) {
                return null;
            }

            Long userId1 = Long.parseLong(parts[1]);
            Long userId2 = Long.parseLong(parts[2]);

            return new Long[]{Math.min(userId1, userId2), Math.max(userId1, userId2)};
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 检查用户是否有权限访问指定会话
     *
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @return 是否有权限
     */
    public boolean hasSessionPermission(String sessionId, Long userId) {
        if (!StringUtils.hasText(sessionId) || userId == null || userId <= 0) {
            return false;
        }

        // 对于MD5格式的会话ID，需要查询数据库验证
        if (sessionId.matches(SESSION_ID_PATTERN_MD5)) {
            // 查询该会话中是否有该用户的消息
            List<ChatRecord> messages = chatRecordDao.selectBySessionId(sessionId);
            return messages.stream().anyMatch(msg ->
                Objects.equals(msg.getSenderId(), userId) || Objects.equals(msg.getReceiverId(), userId));
        }

        // 对于简单格式的会话ID，直接从ID中提取用户信息
        Long[] userIds = extractUserIdsFromSessionId(sessionId);
        if (userIds == null) {
            return false;
        }

        return Objects.equals(userIds[0], userId) || Objects.equals(userIds[1], userId);
    }

    /**
     * 批量发送消息
     *
     * @param chatRecords 聊天记录列表
     * @return 成功发送的消息数量
     */
    public int batchSendMessages(List<ChatRecord> chatRecords) {
        if (chatRecords == null || chatRecords.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        for (ChatRecord chatRecord : chatRecords) {
            try {
                sendMessage(chatRecord);
                successCount++;
            } catch (Exception e) {
                // 记录错误但继续处理其他消息
                System.err.println("发送消息失败: " + e.getMessage());
            }
        }

        return successCount;
    }

    /**
     * 获取两个用户之间的聊天记录
     *
     * @param userId1 用户1的ID
     * @param userId2 用户2的ID
     * @return 聊天记录列表
     */
    @Transactional(readOnly = true)
    public List<ChatRecord> getMessagesBetweenUsers(Long userId1, Long userId2) {
        String sessionId = generateSessionId(userId1, userId2);
        return getMessagesBySessionId(sessionId);
    }

    /**
     * 获取两个用户之间的聊天记录（分页）
     *
     * @param userId1 用户1的ID
     * @param userId2 用户2的ID
     * @param page 页码
     * @param size 每页大小
     * @return 聊天记录列表
     */
    @Transactional(readOnly = true)
    public List<ChatRecord> getMessagesBetweenUsersWithPaging(Long userId1, Long userId2, int page, int size) {
        String sessionId = generateSessionId(userId1, userId2);
        return getMessagesBySessionIdWithPaging(sessionId, page, size);
    }

    /**
     * 获取用户参与的所有会话的最新消息
     *
     * @param userId 用户ID
     * @return 最新消息列表
     */
    @Transactional(readOnly = true)
    public List<ChatRecord> getLatestMessagesForUser(Long userId) {
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID不能为空或无效");
        }

        // 这里需要根据实际需求实现
        // 可能需要在DAO中添加相应的查询方法
        // 暂时返回空列表
        return List.of();
    }

    /**
     * 统计用户发送的消息数量
     *
     * @param userId 用户ID
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 消息数量
     */
    @Transactional(readOnly = true)
    public int countMessagesBySender(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID不能为空或无效");
        }

        // 这里需要在DAO中添加相应的查询方法
        // 暂时返回0
        return 0;
    }

    /**
     * 检查消息是否存在
     *
     * @param messageId 消息ID
     * @return 是否存在
     */
    @Transactional(readOnly = true)
    public boolean messageExists(Long messageId) {
        if (messageId == null || messageId <= 0) {
            return false;
        }

        ChatRecord message = getMessageById(messageId);
        return message != null;
    }

    /**
     * 获取会话中的消息总数
     *
     * @param sessionId 会话ID
     * @return 消息总数
     */
    @Transactional(readOnly = true)
    public int getMessageCountBySession(String sessionId) {
        if (!StringUtils.hasText(sessionId)) {
            throw new IllegalArgumentException("会话ID不能为空");
        }

        List<ChatRecord> messages = getMessagesBySessionId(sessionId);
        return messages.size();
    }

    /**
     * 软删除消息（标记为已删除，不实际删除）
     * 注意：这需要在数据库表中添加deleted字段
     *
     * @param messageId 消息ID
     * @param userId 操作用户ID
     * @return 是否成功
     */
    public boolean softDeleteMessage(Long messageId, Long userId) {
        if (messageId == null || messageId <= 0) {
            throw new IllegalArgumentException("消息ID不能为空或无效");
        }
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID不能为空或无效");
        }

        ChatRecord message = getMessageById(messageId);
        if (message == null) {
            return false;
        }

        // 检查权限：只有发送者可以删除自己的消息
        if (!Objects.equals(message.getSenderId(), userId)) {
            throw new SecurityException("只能删除自己发送的消息");
        }

        // 这里需要在DAO中添加软删除的方法
        // 暂时使用硬删除
        return deleteMessage(messageId) > 0;
    }

    /**
     * 撤回消息（在一定时间内允许撤回）
     *
     * @param messageId 消息ID
     * @param userId 操作用户ID
     * @param timeLimit 时间限制（分钟）
     * @return 是否成功
     */
    public boolean recallMessage(Long messageId, Long userId, int timeLimit) {
        if (messageId == null || messageId <= 0) {
            throw new IllegalArgumentException("消息ID不能为空或无效");
        }
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("用户ID不能为空或无效");
        }
        if (timeLimit <= 0) {
            timeLimit = DEFAULT_RECALL_TIME_LIMIT; // 默认撤回时间限制
        }

        ChatRecord message = getMessageById(messageId);
        if (message == null) {
            return false;
        }

        // 检查权限
        if (!Objects.equals(message.getSenderId(), userId)) {
            throw new SecurityException("只能撤回自己发送的消息");
        }

        // 检查时间限制
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime timeLimit_dt = message.getSendTime().plusMinutes(timeLimit);
        if (now.isAfter(timeLimit_dt)) {
            throw new IllegalStateException("超过撤回时间限制");
        }

        // 更新消息内容为撤回提示
        // 这里需要在DAO中添加更新消息内容的方法
        // 暂时使用删除
        return deleteMessage(messageId) > 0;
    }
}
