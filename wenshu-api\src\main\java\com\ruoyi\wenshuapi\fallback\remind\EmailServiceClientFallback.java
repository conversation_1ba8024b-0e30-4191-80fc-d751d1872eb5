// src/main/java/com/ruoyi/wenshuapi/client/remind/EmailServiceClientFallback.java
package com.ruoyi.wenshuapi.fallback.remind;

import com.ruoyi.wenshuapi.client.remind.EmailServiceClient;
import com.ruoyi.wenshuapi.util.file.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * 邮件服务Feign客户端降级处理工厂
 * 当wenshu-remind服务不可用时提供降级响应
 */
@Component
public class EmailServiceClientFallback implements FallbackFactory<EmailServiceClient> {

    private static final Logger logger = LoggerFactory.getLogger(EmailServiceClientFallback.class);

    @Override
    public EmailServiceClient create(Throwable cause) {
        return new EmailServiceClient() {

            /**
             * 纯文本邮件发送降级处理
             */
            @Override
            public ApiResponse<String> sendSimpleMail(String to, String subject, String text) {
                return handleFailure("sendSimpleMail", to, subject, cause);
            }

            /**
             * HTML邮件发送降级处理
             */
            @Override
            public ApiResponse<String> sendHtmlMail(String to, String subject, String htmlContent) {
                return handleFailure("sendHtmlMail", to, subject, cause);
            }

            /**
             * 带附件邮件发送降级处理
             */
            @Override
            public ApiResponse<String> sendAttachmentMail(String to, String subject, String text, MultipartFile attachment) {
                // 记录附件信息（如果有）
                String attachmentInfo = attachment != null ?
                        "(附件: " + attachment.getOriginalFilename() + ", " + attachment.getSize() + " bytes)" :
                        "(无附件)";

                logger.error("邮件服务降级: 发送带附件邮件失败 - 收件人: {}, 主题: {} {} | 原因: {}",
                        to, subject, attachmentInfo, cause.getMessage());

                return ApiResponse.failed("邮件服务暂时不可用，附件邮件发送失败。系统已记录您的请求，稍后将自动重试", 503);
            }

            /**
             * 通用降级处理方法
             */
            private ApiResponse<String> handleFailure(String method, String to, String subject, Throwable cause) {
                String errorMsg = String.format("邮件服务降级: %s失败 - 收件人: %s, 主题: %s | 原因: %s",
                        method, to, subject, cause.getMessage());

                logger.error(errorMsg, cause);

                return ApiResponse.failed("邮件服务暂时不可用，请稍后重试。错误代码: MS-503", 503);
            }
        };
    }
}