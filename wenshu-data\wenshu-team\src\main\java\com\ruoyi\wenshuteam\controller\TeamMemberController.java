package com.ruoyi.wenshuteam.controller;

import com.ruoyi.wenshuapi.pojo.team.TeamUserRelation;
import com.ruoyi.wenshuapi.util.file.ApiResponse;
import com.ruoyi.wenshuteam.service.TeamMemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 团队-成员关系控制器
 * 提供团队与成员关联关系的RESTful API接口
 * 顶层路径: /wenshu/teammember
 */
@RestController
@RequestMapping("/wenshu/teammember")
public class TeamMemberController {

    private final TeamMemberService teamMemberService;

    @Autowired
    public TeamMemberController(TeamMemberService teamMemberService) {
        this.teamMemberService = teamMemberService;
    }

    /**
     * 添加成员到团队
     * 
     * 路径: /add
     * 方法: POST
     * 
     * @param relation 团队-用户关系对象 (JSON格式)
     * @return 操作结果
     */
    @PostMapping("/add")
    public ApiResponse<Boolean> addMemberToTeam(@RequestBody TeamUserRelation relation) {
        try {
            boolean result = teamMemberService.addMemberToTeam(relation);
            if (result) {
                return ApiResponse.success(true, "成员添加成功");
            } else {
                return ApiResponse.success(false, "成员已在团队中");
            }
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("添加成员失败: " + e.getMessage());
        }
    }

    /**
     * 从团队中移除成员
     * 
     * 路径: /remove
     * 方法: DELETE
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 操作结果
     */
    @DeleteMapping("/remove")
    public ApiResponse<Boolean> removeMemberFromTeam(
            @RequestParam("teamId") int teamId,
            @RequestParam("userId") int userId) {
        try {
            boolean result = teamMemberService.removeMemberFromTeam(teamId, userId);
            if (result) {
                return ApiResponse.success(true, "成员移除成功");
            } else {
                return ApiResponse.success(false, "成员不在团队中");
            }
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("移除成员失败: " + e.getMessage());
        }
    }

    /**
     * 解散团队（移除团队所有成员）
     * 
     * 路径: /disband/{teamId}
     * 方法: DELETE
     * 
     * @param teamId 团队ID (路径变量)
     * @return 操作结果
     */
    @DeleteMapping("/disband/{teamId}")
    public ApiResponse<Integer> disbandTeam(@PathVariable("teamId") int teamId) {
        try {
            int removedCount = teamMemberService.disbandTeam(teamId);
            return ApiResponse.success(removedCount, "团队解散成功，移除 " + removedCount + " 名成员");
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("解散团队失败: " + e.getMessage());
        }
    }

    /**
     * 移除用户的所有团队关系
     * 
     * 路径: /removeuser/{userId}
     * 方法: DELETE
     * 
     * @param userId 用户ID (路径变量)
     * @return 操作结果
     */
    @DeleteMapping("/removeuser/{userId}")
    public ApiResponse<Integer> removeUserFromAllTeams(@PathVariable("userId") int userId) {
        try {
            int removedCount = teamMemberService.removeUserFromAllTeams(userId);
            return ApiResponse.success(removedCount, "用户已退出所有团队，移除 " + removedCount + " 个团队关系");
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("移除用户团队关系失败: " + e.getMessage());
        }
    }

    /**
     * 获取团队所有成员ID列表
     * 
     * 路径: /members/{teamId}
     * 方法: GET
     * 
     * @param teamId 团队ID (路径变量)
     * @return 成员ID列表
     */
    @GetMapping("/members/{teamId}")
    public ApiResponse<List<Integer>> getTeamMembers(@PathVariable("teamId") int teamId) {
        try {
            List<Integer> members = teamMemberService.getTeamMembers(teamId);
            return ApiResponse.success(members, "获取团队成员成功");
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("获取团队成员失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户加入的所有团队ID列表
     * 
     * 路径: /teams/{userId}
     * 方法: GET
     * 
     * @param userId 用户ID (路径变量)
     * @return 团队ID列表
     */
    @GetMapping("/teams/{userId}")
    public ApiResponse<List<Integer>> getUserTeams(@PathVariable("userId") int userId) {
        try {
            List<Integer> teams = teamMemberService.getUserTeams(userId);
            return ApiResponse.success(teams, "获取用户团队成功");
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("获取用户团队失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户是否在指定团队中
     * 
     * 路径: /check
     * 方法: GET
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 检查结果
     */
    @GetMapping("/check")
    public ApiResponse<Boolean> isUserInTeam(
            @RequestParam("teamId") int teamId,
            @RequestParam("userId") int userId) {
        try {
            boolean result = teamMemberService.isUserInTeam(teamId, userId);
            return ApiResponse.success(result, "检查完成");
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("检查失败: " + e.getMessage());
        }
    }

    /**
     * 将用户转移到新团队
     * 
     * 路径: /transfer
     * 方法: PUT
     * 
     * @param userId    用户ID
     * @param oldTeamId 原团队ID
     * @param newTeamId 新团队ID
     * @return 操作结果
     */
    @PutMapping("/transfer")
    public ApiResponse<Boolean> transferUserToNewTeam(
            @RequestParam("userId") int userId,
            @RequestParam("oldTeamId") int oldTeamId,
            @RequestParam("newTeamId") int newTeamId) {
        try {
            boolean result = teamMemberService.transferUserToNewTeam(userId, oldTeamId, newTeamId);
            if (result) {
                return ApiResponse.success(true, "成员转移成功");
            } else {
                return ApiResponse.success(false, "用户不在原团队中");
            }
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("转移成员失败: " + e.getMessage());
        }
    }

    /**
     * 获取团队成员数量
     * 
     * 路径: /count/members/{teamId}
     * 方法: GET
     * 
     * @param teamId 团队ID (路径变量)
     * @return 成员数量
     */
    @GetMapping("/count/members/{teamId}")
    public ApiResponse<Integer> countTeamMembers(@PathVariable("teamId") int teamId) {
        try {
            int count = teamMemberService.countTeamMembers(teamId);
            return ApiResponse.success(count, "获取团队成员数量成功");
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("获取成员数量失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户加入的团队数量
     * 
     * 路径: /count/teams/{userId}
     * 方法: GET
     * 
     * @param userId 用户ID (路径变量)
     * @return 团队数量
     */
    @GetMapping("/count/teams/{userId}")
    public ApiResponse<Integer> countUserTeams(@PathVariable("userId") int userId) {
        try {
            int count = teamMemberService.countUserTeams(userId);
            return ApiResponse.success(count, "获取用户团队数量成功");
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("获取团队数量失败: " + e.getMessage());
        }
    }

    /**
     * 批量添加成员到团队
     * 
     * 路径: /batch/add
     * 方法: POST
     * 
     * @param teamId  团队ID
     * @param userIds 用户ID列表 (JSON数组)
     * @return 成功添加的数量
     */
    @PostMapping("/batch/add")
    public ApiResponse<Integer> batchAddMembersToTeam(
            @RequestParam("teamId") int teamId,
            @RequestBody List<Integer> userIds) {
        try {
            int successCount = teamMemberService.batchAddMembersToTeam(teamId, userIds);
            return ApiResponse.success(successCount, "批量添加成功，添加 " + successCount + " 名成员");
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("批量添加成员失败: " + e.getMessage());
        }
    }

    /**
     * 批量从团队中移除成员
     * 
     * 路径: /batch/remove
     * 方法: DELETE
     * 
     * @param teamId  团队ID
     * @param userIds 用户ID列表 (JSON数组)
     * @return 成功移除的数量
     */
    @DeleteMapping("/batch/remove")
    public ApiResponse<Integer> batchRemoveMembersFromTeam(
            @RequestParam("teamId") int teamId,
            @RequestBody List<Integer> userIds) {
        try {
            int successCount = teamMemberService.batchRemoveMembersFromTeam(teamId, userIds);
            return ApiResponse.success(successCount, "批量移除成功，移除 " + successCount + " 名成员");
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("批量移除成员失败: " + e.getMessage());
        }
    }
}