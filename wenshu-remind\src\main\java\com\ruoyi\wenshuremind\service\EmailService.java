// src/main/java/com/example/maildemo/service/EmailService.java
package com.ruoyi.wenshuremind.service;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;




import java.io.File;

@Service
public class EmailService {

    private static final Logger logger = LoggerFactory.getLogger(EmailService.class);

    private final JavaMailSender mailSender;

    public EmailService(JavaMailSender mailSender) {
        this.mailSender = mailSender;
    }

    // 发送纯文本邮件
    public void sendSimpleMail(String to, String subject, String text) {
        logger.info("=== 开始发送邮件 ===");
        logger.info("收件人: {}", to);
        logger.info("主题: {}", subject);
        logger.info("内容: {}", text);

        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom("<EMAIL>"); // 替换为你的邮箱
            message.setTo(to);
            message.setSubject(subject);
            message.setText(text);

            logger.info("📧 邮件对象创建完成，开始发送...");
            mailSender.send(message);
            logger.info("✅ 邮件发送成功");

        } catch (Exception e) {
            logger.error("❌ 邮件发送失败");
            logger.error("异常类型: {}", e.getClass().getSimpleName());
            logger.error("异常信息: {}", e.getMessage());
            logger.error("异常堆栈: ", e);
            throw e; // 重新抛出异常，让调用方知道发送失败
        }
    }

    // 发送HTML邮件
    public void sendHtmlMail(String to, String subject, String htmlContent) 
            throws MessagingException {
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
        helper.setFrom("<EMAIL>"); // 替换为你的邮箱
        helper.setTo(to);
        helper.setSubject(subject);
        helper.setText(htmlContent, true); // true 表示HTML格式
        mailSender.send(message);
    }

    // 发送带附件的邮件
    public void sendAttachmentMail(String to, String subject, String text, File file) 
            throws MessagingException {
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
        helper.setFrom("<EMAIL>"); // 替换为你的邮箱
        helper.setTo(to);
        helper.setSubject(subject);
        helper.setText(text);
        helper.addAttachment(file.getName(), file); // 添加附件
        mailSender.send(message);
    }
}