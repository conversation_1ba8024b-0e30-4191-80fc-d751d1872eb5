package com.ruoyi.wenshuapi.client.file;

import com.ruoyi.wenshuapi.fallback.file.FileInfoClientFallback;
import com.ruoyi.wenshuapi.pojo.file.FileInfoPojo;
import com.ruoyi.wenshuapi.util.file.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 文件信息服务Feign客户端接口
 * 用于微服务间远程调用文件管理功能
 *
 * FeignClient参数说明：
 *   name = "wenshu-file"  // 目标服务在注册中心的应用名称
 *   path = "/wenshu/fileinfo" // 统一路径前缀（与Controller顶层路径匹配）
 *   contextId = "fileInfoClient" // 防止与其他同名Feign冲突
 *
 * 注意：接口方法需与Controller方法签名严格匹配（路径/参数/返回类型）
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-06-28
 */
@FeignClient(
        name = "wenshu-file",
        path = "/wenshu/fileinfo",
        contextId = "fileInfoClient",
        fallbackFactory = FileInfoClientFallback.class
)
public interface FileInfoClient {

    /**
     * 文件上传接口（Feign远程调用）
     * 支持所有文件类型，单个文件不超过100MB
     * 上传成功后自动保存文件信息到数据库
     *
     * @param file       上传的文件
     * @param uploaderId 上传者ID（可选）
     * @param ownerType  所有者类型（个人/团队，默认为个人）
     * @param fileStatus 文件状态（可读写/协作/不可读写，默认为可读写）
     * @return 上传结果，包含文件信息和访问URL
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ApiResponse<Map<String, Object>> uploadFile(
            @RequestPart("file") MultipartFile file,
            @RequestParam(value = "uploaderId", required = false) Integer uploaderId,
            @RequestParam(value = "ownerType", defaultValue = "个人") String ownerType,
            @RequestParam(value = "fileStatus", defaultValue = "可读写") String fileStatus
    );

    /**
     * 添加文件信息（Feign远程调用）
     * @param fileInfo 文件信息实体
     * @return 包含操作结果的ApiResponse
     */
    @PostMapping("/add")
    ApiResponse<Integer> addFile(@RequestBody FileInfoPojo fileInfo);

    /**
     * 删除文件信息（Feign远程调用）
     * @param fileId 文件ID（路径变量）
     * @return 包含操作结果的ApiResponse
     */
    @DeleteMapping("/delete/{fileId}")
    ApiResponse<Integer> deleteFile(@PathVariable("fileId") int fileId);

    /**
     * 更新文件信息（Feign远程调用）
     * @param fileInfo 文件信息实体
     * @return 包含操作结果的ApiResponse
     */
    @PutMapping("/update")
    ApiResponse<Integer> updateFile(@RequestBody FileInfoPojo fileInfo);

    /**
     * 根据ID获取文件详情（Feign远程调用）
     * @param fileId 文件ID（路径变量）
     * @return 包含文件详情的ApiResponse
     */
    @GetMapping("/get/{fileId}")
    ApiResponse<FileInfoPojo> getFileById(@PathVariable("fileId") int fileId);

    /**
     * 条件查询文件列表（Feign远程调用）
     * @param condition 查询条件对象（自动转换为请求参数）
     * @return 包含文件列表的ApiResponse
     */
    @GetMapping("/list")
    ApiResponse<List<FileInfoPojo>> getFilesByCondition(@SpringQueryMap FileInfoPojo condition);

    /**
     * 根据文件路径获取文件信息（Feign远程调用）
     * @param filePath 文件路径（请求参数）
     * @return 包含文件详情的ApiResponse
     */
    @GetMapping("/byPath")
    ApiResponse<FileInfoPojo> getFileByPath(@RequestParam("filePath") String filePath);

    /**
     * 更新文件状态（Feign远程调用）
     * @param fileId 文件ID（请求参数）
     * @param status 新状态值（请求参数）
     * @return 包含操作结果的ApiResponse
     */
    @PutMapping("/updateStatus")
    ApiResponse<Integer> changeFileStatus(
            @RequestParam("fileId") int fileId,
            @RequestParam("status") String status);

    /**
     * 统计用户文件数量（Feign远程调用）
     * @param uploaderId 上传者ID（请求参数）
     * @return 包含统计结果的ApiResponse
     */
    @GetMapping("/countByUser")
    ApiResponse<Integer> countFilesByUploader(@RequestParam("uploaderId") int uploaderId);

    /**
     * 批量更新文件状态（Feign远程调用）
     * @param fileIds 文件ID列表（请求参数）
     * @param status 新状态值（请求参数）
     * @return 包含操作结果的ApiResponse
     */
    @PutMapping("/batchUpdateStatus")
    ApiResponse<Integer> batchUpdateStatus(
            @RequestParam("fileIds") List<Integer> fileIds,
            @RequestParam("status") String status);

    /**
     * 检查文件归属（Feign远程调用）
     * @param fileId 文件ID（请求参数）
     * @param userId 用户ID（请求参数）
     * @return 包含验证结果的ApiResponse
     */
    @GetMapping("/isOwned")
    ApiResponse<Boolean> isFileOwnedByUser(
            @RequestParam("fileId") int fileId,
            @RequestParam("userId") int userId);

    /**
     * 批量删除文件（Feign远程调用）
     * 同时删除数据库记录和服务器上的物理文件
     *
     * @param fileIds 文件ID列表（请求参数）
     * @return 包含详细删除结果的ApiResponse
     */
    @DeleteMapping("/batchDelete")
    ApiResponse<Map<String, Object>> batchDeleteFiles(@RequestParam("fileIds") List<Integer> fileIds);
}