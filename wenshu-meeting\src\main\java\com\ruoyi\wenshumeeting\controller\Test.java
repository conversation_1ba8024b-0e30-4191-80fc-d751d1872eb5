package com.ruoyi.wenshumeeting.controller;

import com.ruoyi.wenshuapi.client.file.FileInfoClient;
import com.ruoyi.wenshuapi.pojo.file.FileInfoPojo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.Random;

@RestController
@RequestMapping("wenshu/meeting/test")
public class Test {
    @Autowired
    FileInfoClient fileInfoClient;
    @PostMapping("s")
    void save() {
        // 1. 自动生成文件信息
        FileInfoPojo fileInfo = new FileInfoPojo();

        // 生成随机文件名（示例：auto_file_12345.txt）
        String fileName = "auto_file_" + System.currentTimeMillis() + ".txt";
        fileInfo.setFileName(fileName);

        // 生成文件路径（示例：/uploads/auto/2025/07/09/）
        String datePath = LocalDateTime.now().toString().substring(0, 10).replace("-", "/");
        fileInfo.setFilePath("/uploads/auto/" + datePath + "/" + fileName);

        // 设置固定上传者ID（示例用户ID）
        fileInfo.setUploaderId(1000);

        // 生成随机文件大小（100KB - 10MB之间）
        long fileSize = new Random().nextInt(9900_000) + 100_000; // 100KB - 10MB
        fileInfo.setFileSize(fileSize);

        // 设置固定时间戳
        fileInfo.setUploadTime(LocalDateTime.now());

        // 设置默认状态和所有者类型
        fileInfo.setFileStatus("可读写");
        fileInfo.setOwnerType("个人");
        fileInfoClient.addFile(fileInfo);
        // 2. 调用Feign客户端添加文件
        System.out.println("自动创建文件信息: " + fileInfo);
    }
}
