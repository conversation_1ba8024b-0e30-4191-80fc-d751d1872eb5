spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************
    username: root
    password: 2313147023
  application:
    name: wenshu-api
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: ************:8848
      config:
        # 配置中心地址
        server-addr: ************:8848
        # 配置文件格式
        file-extension: yml
    sentinel:
      transport:
        dashboard: localhost:8090

server:
  port: 8704

# 增加日志配置
logging:
  level:
    cn.itcast.feign: DEBUG
    org.springframework.cloud.openfeign: DEBUG
    com.alibaba.nacos: INFO
feign:
  sentinel:
    enabled: true  # 开启feign对sentinel的支持
  client:
    config:
      default:
        connectTimeout: 5000  # 连接超时时间
        readTimeout: 5000     # 读取超时时间
        loggerLevel: FULL     # 设置为FULL以记录详细的请求和响应 