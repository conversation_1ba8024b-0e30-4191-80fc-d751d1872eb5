#!/bin/bash

# 微服务故障排除脚本

echo "=== 微服务故障排除 ==="

# 检查服务状态
echo "1. 检查服务状态:"
sudo docker compose -f docker-compose.app.yml ps

echo ""
echo "2. 检查网络连接:"
sudo docker network ls | grep wenshu

echo ""
echo "3. 检查容器日志 (最近50行):"

services=("gateway" "auth" "system" "wenshu-chat" "wenshu-voice")

for service in "${services[@]}"; do
    echo ""
    echo "=== $service 服务日志 ==="
    if sudo docker compose -f docker-compose.app.yml ps $service | grep -q "Up"; then
        echo "状态: 运行中"
        sudo docker compose -f docker-compose.app.yml logs $service --tail=20
    else
        echo "状态: 未运行或异常"
        sudo docker compose -f docker-compose.app.yml logs $service --tail=50
    fi
    echo "----------------------------------------"
done

echo ""
echo "4. 检查端口占用:"
netstat -tlnp | grep -E ":(8080|9200|8848|3306|6379)" || echo "netstat命令不可用，尝试使用ss:"
ss -tlnp | grep -E ":(8080|9200|8848|3306|6379)" 2>/dev/null || echo "无法检查端口占用"

echo ""
echo "5. 检查Docker资源使用:"
sudo docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"

echo ""
echo "6. 常见解决方案:"
echo "   - 如果Nacos连接失败: 检查wenshu-nacos容器是否运行"
echo "   - 如果数据库连接失败: 检查wenshu-mysql容器是否运行"
echo "   - 如果Redis连接失败: 检查wenshu-redis容器是否运行"
echo "   - 如果端口冲突: 停止占用端口的进程或修改配置"
echo "   - 如果内存不足: 减少JVM内存配置或增加系统内存"

echo ""
echo "7. 重启特定服务:"
echo "   sudo docker compose -f docker-compose.app.yml restart [服务名]"

echo ""
echo "8. 完全重启所有服务:"
echo "   sudo docker compose -f docker-compose.app.yml down && sudo docker compose -f docker-compose.app.yml up -d"

echo ""
echo "9. 查看实时日志:"
echo "   sudo docker compose -f docker-compose.app.yml logs -f [服务名]"
