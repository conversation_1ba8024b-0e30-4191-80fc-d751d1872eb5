package com.ruoyi.wenshufile.dao;

import com.ruoyi.wenshuapi.pojo.file.FileUserTeamPojo;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface FileUserTeamDao {

    // 插入数据
    @Insert("INSERT INTO wenshu_file_participant (file_id, user_id, team_id) " +
            "VALUES (#{fileId}, #{userId}, #{teamId})")
    int insert(FileUserTeamPojo pojo);

    // 按复合主键删除
    @Delete("DELETE FROM wenshu_file_participant " +
            "WHERE file_id = #{fileId} AND user_id = #{userId} AND team_id = #{teamId}")
    int deleteByPrimaryKey(@Param("fileId") int fileId, 
                           @Param("userId") int userId, 
                           @Param("teamId") int teamId);

    // 按文件ID删除
    @Delete("DELETE FROM wenshu_file_participant WHERE file_id = #{fileId}")
    int deleteByFileId(@Param("fileId") int fileId);

    // 按用户ID删除
    @Delete("DELETE FROM wenshu_file_participant WHERE user_id = #{userId}")
    int deleteByUserId(@Param("userId") int userId);

    // 按团队ID删除
    @Delete("DELETE FROM wenshu_file_participant WHERE team_id = #{teamId}")
    int deleteByTeamId(@Param("teamId") int teamId);

    // 按复合主键查询
    @Select("SELECT file_id, user_id, team_id FROM wenshu_file_participant " +
            "WHERE file_id = #{fileId} AND user_id = #{userId} AND team_id = #{teamId}")
    FileUserTeamPojo selectByPrimaryKey(@Param("fileId") int fileId, 
                                        @Param("userId") int userId, 
                                        @Param("teamId") int teamId);

    // 按文件ID查询
    @Select("SELECT file_id, user_id, team_id FROM wenshu_file_participant WHERE file_id = #{fileId}")
    List<FileUserTeamPojo> selectByFileId(@Param("fileId") int fileId);

    // 按用户ID查询
    @Select("SELECT file_id, user_id, team_id FROM wenshu_file_participant WHERE user_id = #{userId}")
    List<FileUserTeamPojo> selectByUserId(@Param("userId") int userId);

    // 按团队ID查询
    @Select("SELECT file_id, user_id, team_id FROM wenshu_file_participant WHERE team_id = #{teamId}")
    List<FileUserTeamPojo> selectByTeamId(@Param("teamId") int teamId);

    // 更新所有字段（需提供完整参数）
    @Update("UPDATE wenshu_file_participant SET " +
            "file_id = #{newPojo.fileId}, " +
            "user_id = #{newPojo.userId}, " +
            "team_id = #{newPojo.teamId} " +
            "WHERE file_id = #{oldFileId} " +
            "AND user_id = #{oldUserId} " +
            "AND team_id = #{oldTeamId}")
    int updateByPrimaryKey(@Param("oldFileId") int oldFileId,
                           @Param("oldUserId") int oldUserId,
                           @Param("oldTeamId") int oldTeamId,
                           @Param("newPojo") FileUserTeamPojo newPojo);
}