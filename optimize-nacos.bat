@echo off
chcp 65001 >nul
echo === Nacos优化脚本 ===
echo 解决多服务连接时Nacos自动重启的问题
echo.

REM 检查Docker是否运行
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: Docker未运行，请先启动Docker
    pause
    exit /b 1
)
echo ✅ Docker运行正常

REM 1. 停止现有服务
echo.
echo 1. 停止现有服务...
docker compose -f docker-compose.env.yml down nacos >nul 2>&1
docker compose -f docker-compose.app.yml down auth gateway system >nul 2>&1

REM 2. 清理Nacos数据（可选）
echo.
set /p cleanup="是否清理Nacos数据重新开始？(y/N): "
if /i "%cleanup%"=="y" (
    echo    清理Nacos数据卷...
    docker volume rm wenshuintelligentcomputing-back_nacos_data >nul 2>&1
)

REM 3. 修复数据库表结构
echo.
echo 2. 修复数据库表结构...
if exist "fix-nacos-db.sql" (
    REM 确保MySQL运行
    docker ps --filter "name=wenshu-mysql" --format "{{.Names}}" | findstr wenshu-mysql >nul
    if %errorlevel% neq 0 (
        echo    启动MySQL...
        docker compose -f docker-compose.env.yml up -d mysql
        timeout /t 15 /nobreak >nul
    )
    
    echo    执行数据库修复...
    docker exec -i wenshu-mysql mysql -uroot -p2313147023 < fix-nacos-db.sql
    if %errorlevel% equ 0 (
        echo ✅ 数据库表结构修复成功
    ) else (
        echo ⚠️  数据库修复失败，但继续启动
    )
) else (
    echo    修复脚本不存在，跳过
)

REM 4. 启动优化后的Nacos
echo.
echo 3. 启动优化后的Nacos...

REM 确保基础服务运行
echo    检查基础服务...
docker ps --filter "name=wenshu-mysql" --format "{{.Names}}" | findstr wenshu-mysql >nul
if %errorlevel% neq 0 (
    echo    启动MySQL...
    docker compose -f docker-compose.env.yml up -d mysql
    timeout /t 10 /nobreak >nul
)

docker ps --filter "name=wenshu-redis" --format "{{.Names}}" | findstr wenshu-redis >nul
if %errorlevel% neq 0 (
    echo    启动Redis...
    docker compose -f docker-compose.env.yml up -d redis
    timeout /t 5 /nobreak >nul
)

REM 启动优化后的Nacos
echo    启动Nacos（优化配置）...
docker compose -f docker-compose.env.yml up -d nacos

REM 5. 等待Nacos完全启动
echo.
echo 4. 等待Nacos启动完成...
echo    这可能需要1-2分钟，请耐心等待...

set /a count=0
:wait_nacos
set /a count+=1
curl -f http://localhost:8848/nacos/actuator/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Nacos启动成功！
    goto nacos_ready
)
if %count% geq 60 (
    goto nacos_failed
)
REM 每10秒显示一次进度
set /a mod=%count% %% 10
if %mod% equ 0 (
    echo    等待Nacos启动... (%count%/60秒)
)
timeout /t 1 /nobreak >nul
goto wait_nacos

:nacos_failed
echo ❌ Nacos启动失败！
echo.
echo 请检查日志：
echo    docker compose -f docker-compose.env.yml logs nacos
echo.
echo 常见问题：
echo    1. 内存不足 - 增加系统内存或关闭其他服务
echo    2. 端口占用 - 检查8848端口是否被占用
echo    3. 数据库连接失败 - 检查MySQL是否正常运行
pause
exit /b 1

:nacos_ready
REM 6. 逐个启动应用服务
echo.
echo 5. 启动应用服务...

echo    启动 auth 服务...
docker compose -f docker-compose.app.yml up -d auth
timeout /t 20 /nobreak >nul

REM 检查Nacos是否还在运行
docker ps --filter "name=wenshu-nacos" --format "{{.Names}}" | findstr wenshu-nacos >nul
if %errorlevel% neq 0 (
    echo ❌ Nacos在启动 auth 后停止运行！
    echo    正在重启Nacos...
    docker compose -f docker-compose.env.yml up -d nacos
    timeout /t 30 /nobreak >nul
) else (
    echo ✅ auth 服务启动成功，Nacos运行正常
)

echo    启动 system 服务...
docker compose -f docker-compose.app.yml up -d system
timeout /t 20 /nobreak >nul

docker ps --filter "name=wenshu-nacos" --format "{{.Names}}" | findstr wenshu-nacos >nul
if %errorlevel% neq 0 (
    echo ❌ Nacos在启动 system 后停止运行！
    echo    正在重启Nacos...
    docker compose -f docker-compose.env.yml up -d nacos
    timeout /t 30 /nobreak >nul
) else (
    echo ✅ system 服务启动成功，Nacos运行正常
)

echo    启动 gateway 服务...
docker compose -f docker-compose.app.yml up -d gateway
timeout /t 20 /nobreak >nul

docker ps --filter "name=wenshu-nacos" --format "{{.Names}}" | findstr wenshu-nacos >nul
if %errorlevel% neq 0 (
    echo ❌ Nacos在启动 gateway 后停止运行！
    echo    正在重启Nacos...
    docker compose -f docker-compose.env.yml up -d nacos
    timeout /t 30 /nobreak >nul
) else (
    echo ✅ gateway 服务启动成功，Nacos运行正常
)

REM 7. 最终检查
echo.
echo 6. 最终状态检查...
echo === 容器状态 ===
docker ps --filter "name=wenshu-" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo.
echo === 服务健康检查 ===
set all_healthy=true

curl -f http://localhost:8848/nacos/actuator/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ nacos 服务正常
) else (
    echo ❌ nacos 服务异常
    set all_healthy=false
)

curl -f http://localhost:9200/actuator/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ auth 服务正常
) else (
    echo ❌ auth 服务异常
    set all_healthy=false
)

curl -f http://localhost:8080/actuator/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ gateway 服务正常
) else (
    echo ❌ gateway 服务异常
    set all_healthy=false
)

echo.
if "%all_healthy%"=="true" (
    echo 🎉 所有服务启动成功！Nacos优化完成！
    echo.
    echo 访问地址：
    echo   - Nacos控制台: http://localhost:8848/nacos
    echo   - 网关服务: http://localhost:8080
    echo.
    echo 优化内容：
    echo   - JVM堆内存: 512MB → 1024MB
    echo   - 容器内存限制: 512MB → 1280MB
    echo   - Tomcat最大线程: 400
    echo   - 数据库连接池: 50
    echo   - 使用G1垃圾收集器
) else (
    echo ⚠️  部分服务异常，请检查日志
    echo.
    echo 查看日志命令：
    echo   docker compose -f docker-compose.env.yml logs nacos
    echo   docker compose -f docker-compose.app.yml logs auth
    echo   docker compose -f docker-compose.app.yml logs gateway
)

echo.
echo === 监控建议 ===
echo 1. 监控Nacos内存使用: docker stats wenshu-nacos
echo 2. 查看Nacos日志: docker compose -f docker-compose.env.yml logs -f nacos
echo 3. 如果仍有问题，可以进一步增加内存配置
echo.
pause
