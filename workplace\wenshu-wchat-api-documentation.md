# Wenshu-WChat 微信集成模块 - 详细API接口文档

## 📋 模块概述

**服务名称**: wenshu-wchat  
**服务端口**: 8706  
**版本**: v1.0.0  
**最后更新**: 2024-12-28  

**核心功能**: 微信集成服务，提供微信公众号、企业微信、小程序集成，消息推送、用户管理、支付集成等功能

**技术栈**:
- Spring Boot 3.x
- Spring Cloud 2023.x
- WxJava (微信开发SDK)
- Redis (缓存和会话)
- MySQL (数据存储)
- WebSocket (实时推送)
- Spring Security (安全认证)

---

## 🏗️ 服务架构

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                Wenshu-WChat 微信集成模块                     │
│                        (Port: 8706)                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│   公众号集成   │ │企业微信  │ │ 小程序     │
│ (Official)    │ │(Work)   │ │(MiniApp)  │
└───────────────┘ └────────┘ └───────────┘
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│ 消息推送       │ │用户管理  │ │ 支付集成   │
│ (Message)     │ │(User)   │ │(Payment)  │
└───────────────┘ └────────┘ └───────────┘
```

### 服务依赖关系
- **微信API**: 微信公众平台、企业微信API
- **wenshu-base**: 用户信息同步
- **wenshu-chat**: AI智能回复
- **Redis**: 会话和缓存管理

---

## 🌐 REST API接口

### 基础信息
- **Base URL**: `http://localhost:8706`
- **Content-Type**: `application/json` / `application/xml`
- **鉴权方式**: 微信签名验证 / JWT Token

---

## 📱 微信公众号API

### 1. 公众号管理

#### 1.1 验证服务器配置
**接口路径**: `GET /api/wechat/official/verify`

**功能描述**: 微信公众号服务器配置验证

**请求参数**:
```http
GET /api/wechat/official/verify?signature=xxx&timestamp=xxx&nonce=xxx&echostr=xxx
```

**响应**: 直接返回echostr参数值

#### 1.2 接收微信消息
**接口路径**: `POST /api/wechat/official/message`

**功能描述**: 接收和处理微信公众号消息

**请求格式**: XML格式的微信消息
```xml
<xml>
  <ToUserName><![CDATA[toUser]]></ToUserName>
  <FromUserName><![CDATA[fromUser]]></FromUserName>
  <CreateTime>123456789</CreateTime>
  <MsgType><![CDATA[text]]></MsgType>
  <Content><![CDATA[你好]]></Content>
  <MsgId>1234567890123456</MsgId>
</xml>
```

**响应格式**: XML格式的回复消息
```xml
<xml>
  <ToUserName><![CDATA[fromUser]]></ToUserName>
  <FromUserName><![CDATA[toUser]]></FromUserName>
  <CreateTime>123456789</CreateTime>
  <MsgType><![CDATA[text]]></MsgType>
  <Content><![CDATA[您好！欢迎使用文书智能办公助手。我可以帮您管理日程、处理文档、回答问题等。请问有什么可以帮助您的吗？]]></Content>
</xml>
```

#### 1.3 发送模板消息
**接口路径**: `POST /api/wechat/official/template/send`

**功能描述**: 发送模板消息给用户

**请求参数**:
```http
POST /api/wechat/official/template/send
Content-Type: application/json

{
  "toUser": "openid_12345",
  "templateId": "template_001",
  "url": "https://wenshu.com/schedule/detail/123",
  "data": {
    "first": {
      "value": "您有一个重要会议提醒",
      "color": "#173177"
    },
    "keyword1": {
      "value": "项目评审会议",
      "color": "#173177"
    },
    "keyword2": {
      "value": "2024年12月29日 14:00",
      "color": "#173177"
    },
    "keyword3": {
      "value": "会议室A",
      "color": "#173177"
    },
    "remark": {
      "value": "请准时参加，带好相关资料。",
      "color": "#173177"
    }
  }
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "模板消息发送成功",
  "data": {
    "msgId": "msg_12345",
    "toUser": "openid_12345",
    "templateId": "template_001",
    "sendTime": "2024-12-28T17:00:00",
    "status": "sent"
  },
  "timestamp": 1640995200000
}
```

#### 1.4 获取用户信息
**接口路径**: `GET /api/wechat/official/user/{openId}`

**功能描述**: 获取微信用户信息

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "openId": "openid_12345",
    "unionId": "unionid_67890",
    "nickname": "张三",
    "sex": 1,
    "city": "北京",
    "country": "中国",
    "province": "北京",
    "language": "zh_CN",
    "headImgUrl": "http://thirdwx.qlogo.cn/...",
    "subscribeTime": "2024-12-20T10:00:00",
    "subscribe": 1,
    "remark": "",
    "groupId": 0,
    "tagIdList": [1, 2],
    "subscribeScene": "ADD_SCENE_QR_CODE"
  },
  "timestamp": 1640995200000
}
```

---

## 🏢 企业微信API

### 2. 企业微信集成

#### 2.1 企业微信消息接收
**接口路径**: `POST /api/wechat/work/message`

**功能描述**: 接收企业微信应用消息

**请求格式**: 加密的XML消息
```xml
<xml>
  <ToUserName><![CDATA[corpid]]></ToUserName>
  <AgentID><![CDATA[1000001]]></AgentID>
  <Encrypt><![CDATA[encrypted_message]]></Encrypt>
</xml>
```

**响应格式**: 加密的回复消息

#### 2.2 发送企业微信消息
**接口路径**: `POST /api/wechat/work/send`

**功能描述**: 发送消息给企业微信用户

**请求参数**:
```http
POST /api/wechat/work/send
Content-Type: application/json

{
  "toUser": "zhangsan|lisi",
  "toParty": "",
  "toTag": "",
  "msgType": "text",
  "agentId": 1000001,
  "content": {
    "text": "您有新的工作任务分配，请及时查看处理。"
  },
  "safe": 0,
  "enableIdTrans": 0,
  "enableDuplicateCheck": 0,
  "duplicateCheckInterval": 1800
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "消息发送成功",
  "data": {
    "errcode": 0,
    "errmsg": "ok",
    "msgId": "msg_work_12345",
    "sendTime": "2024-12-28T17:00:00",
    "invalidUser": "",
    "invalidParty": "",
    "invalidTag": ""
  },
  "timestamp": 1640995200000
}
```

#### 2.3 获取企业微信用户信息
**接口路径**: `GET /api/wechat/work/user/{userId}`

**功能描述**: 获取企业微信用户详细信息

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "userId": "zhangsan",
    "name": "张三",
    "mobile": "13800138000",
    "department": [1, 2],
    "order": [1, 2],
    "position": "软件工程师",
    "gender": "1",
    "email": "<EMAIL>",
    "isLeaderInDept": [1, 0],
    "avatar": "http://wx.qlogo.cn/...",
    "thumbAvatar": "http://wx.qlogo.cn/...",
    "telephone": "010-12345678",
    "alias": "jackzhang",
    "status": 1,
    "qrCode": "https://open.work.weixin.qq.com/...",
    "externalProfile": {
      "externalCorpName": "企业名称",
      "externalAttr": []
    }
  },
  "timestamp": 1640995200000
}
```

---

## 📱 小程序API

### 3. 小程序集成

#### 3.1 小程序登录
**接口路径**: `POST /api/wechat/miniapp/login`

**功能描述**: 小程序用户登录

**请求参数**:
```http
POST /api/wechat/miniapp/login
Content-Type: application/json

{
  "code": "js_code_from_miniapp",
  "encryptedData": "encrypted_user_info",
  "iv": "initialization_vector"
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "登录成功",
  "data": {
    "openId": "openid_miniapp_12345",
    "unionId": "unionid_67890",
    "sessionKey": "session_key_abc",
    "token": "jwt_token_here",
    "expiresIn": 7200,
    "userInfo": {
      "nickName": "张三",
      "gender": 1,
      "city": "北京",
      "province": "北京",
      "country": "中国",
      "avatarUrl": "https://wx.qlogo.cn/..."
    }
  },
  "timestamp": 1640995200000
}
```

#### 3.2 发送小程序订阅消息
**接口路径**: `POST /api/wechat/miniapp/subscribe/send`

**功能描述**: 发送小程序订阅消息

**请求参数**:
```http
POST /api/wechat/miniapp/subscribe/send
Content-Type: application/json

{
  "toUser": "openid_miniapp_12345",
  "templateId": "template_miniapp_001",
  "page": "pages/schedule/detail?id=123",
  "data": {
    "thing1": {
      "value": "项目评审会议"
    },
    "time2": {
      "value": "2024年12月29日 14:00"
    },
    "thing3": {
      "value": "会议室A"
    }
  },
  "miniprogramState": "formal"
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "订阅消息发送成功",
  "data": {
    "errcode": 0,
    "errmsg": "ok",
    "msgId": "msg_miniapp_12345",
    "sendTime": "2024-12-28T17:00:00"
  },
  "timestamp": 1640995200000
}
```

---

## 💰 支付集成API

### 4. 微信支付

#### 4.1 创建支付订单
**接口路径**: `POST /api/wechat/pay/create`

**功能描述**: 创建微信支付订单

**请求参数**:
```http
POST /api/wechat/pay/create
Content-Type: application/json

{
  "outTradeNo": "order_20241228001",
  "description": "文书办公系统-高级版订阅",
  "amount": {
    "total": 9900,
    "currency": "CNY"
  },
  "payer": {
    "openid": "openid_12345"
  },
  "notifyUrl": "https://api.wenshu.com/api/wechat/pay/notify",
  "timeExpire": "2024-12-28T18:00:00+08:00"
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "订单创建成功",
  "data": {
    "prepayId": "prepay_id_12345",
    "outTradeNo": "order_20241228001",
    "paySign": "payment_signature",
    "timeStamp": "1640995200",
    "nonceStr": "random_string",
    "package": "prepay_id=prepay_id_12345",
    "signType": "RSA"
  },
  "timestamp": 1640995200000
}
```

#### 4.2 支付结果通知
**接口路径**: `POST /api/wechat/pay/notify`

**功能描述**: 接收微信支付结果通知

**请求格式**: 微信支付通知的加密数据

**响应格式**:
```json
{
  "code": "SUCCESS",
  "message": "成功"
}
```

---

*文档版本: v1.0.0 | 最后更新: 2024-12-28 | Wenshu-WChat微信集成模块*
