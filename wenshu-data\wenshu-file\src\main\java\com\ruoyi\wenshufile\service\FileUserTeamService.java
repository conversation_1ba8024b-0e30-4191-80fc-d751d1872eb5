package com.ruoyi.wenshufile.service;


import com.ruoyi.wenshuapi.pojo.file.FileUserTeamPojo;

import java.util.List;

/**
 * 文件-用户-团队关联服务接口
 * 方法名与DAO层保持一致
 */
public interface FileUserTeamService {

    /**
     * 插入关联数据
     * @param pojo 关联实体
     * @return 插入结果
     */
    int insert(FileUserTeamPojo pojo);

    /**
     * 通过复合主键删除
     * @param fileId 文件ID
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 删除结果
     */
    int deleteByPrimaryKey(int fileId, int userId, int teamId);

    /**
     * 通过文件ID删除
     * @param fileId 文件ID
     * @return 删除结果
     */
    int deleteByFileId(int fileId);

    /**
     * 通过用户ID删除
     * @param userId 用户ID
     * @return 删除结果
     */
    int deleteByUserId(int userId);

    /**
     * 通过团队ID删除
     * @param teamId 团队ID
     * @return 删除结果
     */
    int deleteByTeamId(int teamId);

    /**
     * 通过复合主键查询
     * @param fileId 文件ID
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 关联实体
     */
    FileUserTeamPojo selectByPrimaryKey(int fileId, int userId, int teamId);

    /**
     * 通过文件ID查询
     * @param fileId 文件ID
     * @return 关联列表
     */
    List<FileUserTeamPojo> selectByFileId(int fileId);

    /**
     * 通过用户ID查询
     * @param userId 用户ID
     * @return 关联列表
     */
    List<FileUserTeamPojo> selectByUserId(int userId);

    /**
     * 通过团队ID查询
     * @param teamId 团队ID
     * @return 关联列表
     */
    List<FileUserTeamPojo> selectByTeamId(int teamId);

    /**
     * 更新关联数据
     * @param oldFileId 原文件ID
     * @param oldUserId 原用户ID
     * @param oldTeamId 原团队ID
     * @param newPojo 新实体数据
     * @return 更新结果
     */
    int updateByPrimaryKey(int oldFileId, int oldUserId, int oldTeamId, FileUserTeamPojo newPojo);
}