package com.ruoyi.wenshubase.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.wenshuapi.common.MessageWrapper;
import com.ruoyi.wenshubase.dao.ConversationDao;
import com.ruoyi.wenshubase.entity.BaseConversation;
import com.ruoyi.wenshubase.service.ChatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 聊天服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ChatServiceImpl extends ServiceImpl<ConversationDao,BaseConversation> implements ChatService {

    private final ConversationDao conversationDao;

    @Override
    public BaseConversation create(int userId) {
        // 生成唯一会话ID
        String conversationId = UUID.randomUUID().toString();
        
        // 创建会话
        BaseConversation conversation = BaseConversation.builder()
                .conversationId(conversationId)
                .userId(userId)
                .title("新对话")
                .content("")
                .build();
        
        conversationDao.insert(conversation);
        
        return conversation;
    }

    @Override
    public void edit(String conversationId, String title,int userId) {
        BaseConversation conversation = getConversationById(conversationId,userId);
        conversation.setTitle(title);
        conversationDao.updateById(conversation);
    }
    
    @Override
    public void updateConversation(BaseConversation conversation,int userId) {
        if (conversation == null) {
            log.warn("尝试更新空的会话对象");
            return;
        }
        
        // 检查会话是否存在
        BaseConversation existingConversation = getConversationById(conversation.getConversationId(),userId);
        if (existingConversation == null) {
            log.warn("尝试更新不存在的会话: {}", conversation.getConversationId());
            return;
        }
        conversation.setId(existingConversation.getId());
        // 更新会话
        conversationDao.updateById(conversation);
        log.info("成功更新会话: {}", conversation.getConversationId());
    }

    @Override
    public List<BaseConversation> list(int userId) {
        return conversationDao.selectList(
                new LambdaQueryWrapper<BaseConversation>()
                        .eq(BaseConversation::getUserId,userId)
                        .orderByDesc(BaseConversation::getUpdatedTime)

        );
    }

    @Override
    public void delete(String conversationId) {
        conversationDao.delete(
                new LambdaQueryWrapper<BaseConversation>()
                        .eq(BaseConversation::getConversationId, conversationId)
        );
    }

    @Override
    public BaseConversation get(String conversationId,int userId) {
        return getConversationById(conversationId,userId);
    }
    
    /**
     * 根据会话ID获取会话
     */
    private BaseConversation getConversationById(String conversationId,int userId) {
        return conversationDao.selectOne(
                new LambdaQueryWrapper<BaseConversation>()
                        .eq(BaseConversation::getConversationId, conversationId)
                        .eq(BaseConversation::getUserId,userId)
        );
    }
} 