package com.ruoyi.wenshumeeting.controller;

import com.ruoyi.wenshuapi.client.chat.ChatFeignClientInter;
import com.ruoyi.wenshuapi.client.file.FileInfoClient;
import com.ruoyi.wenshuapi.common.ApiResponse;
import com.ruoyi.wenshuapi.pojo.chat.RequestBodyDTO;
import com.ruoyi.wenshuapi.pojo.file.FileInfoPojo;
import com.ruoyi.wenshumeeting.pojo.VideoAnalysis;
import com.ruoyi.wenshumeeting.service.AudioExtractor;
import com.ruoyi.wenshumeeting.service.VideVoiceAiService;
import com.ruoyi.wenshumeeting.service.VideoAnalysisService;
import com.ruoyi.wenshumeeting.service.VideoSplitter;
import com.ruoyi.wenshumeeting.util.VideAiQvqMaxUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("wenshu/meeting")
public class analysisController {

    private static final Logger logger = LoggerFactory.getLogger(analysisController.class);

    // 文本切分的最大长度（字符数）
    private static final int MAX_TEXT_LENGTH = 5000;

    @Autowired
    VideoAnalysisService videoAnalysisService;
    @Autowired
    FileInfoClient fileInfoClient;
    @Autowired
    VideVoiceAiService videVoiceAiService;
    @Autowired
    ChatFeignClientInter chatFeignClientInter;
    @Autowired
    VideoSplitter videoSplitter;
    @Autowired
    AudioExtractor audioExtractor;
    /**
     * 开始分析接口
     * 对视频文件进行全面分析，包括视频内容分析、音频识别、会议分析和日程提取
     *
     * @param analysisId 分析任务ID
     * @param userId 用户ID
     * @return 分析结果响应
     */
    @PostMapping("/analysis")
    public ApiResponse analysis(@RequestParam("analysisId") Long analysisId, @RequestParam("userId") String userId) {
        logger.info("开始分析任务，analysisId: {}, userId: {}", analysisId, userId);

        // 1. 验证分析任务是否存在
        Optional<VideoAnalysis> byId = videoAnalysisService.findById(analysisId);
        if (byId.isEmpty()) {
            logger.warn("分析任务不存在，analysisId: {}", analysisId);
            return ApiResponse.failed("查询id不存在");
        }

        VideoAnalysis videoAnalysis = byId.get();

        // 2. 更新任务状态为分析中
        videoAnalysis.setStatus(1); // 任务状态: 0未开始 1分析中 2成功 3失败
        videoAnalysisService.update(videoAnalysis);
        logger.info("任务状态已更新为分析中，analysisId: {}", analysisId);

        // 3. 异步执行分析任务
        new Thread(() -> {
            try {
                performVideoAnalysis(videoAnalysis, userId);
            } catch (Exception e) {
                logger.error("分析任务执行失败，analysisId: {}, error: {}", analysisId, e.getMessage(), e);
                handleAnalysisFailure(videoAnalysis, e.getMessage());
            }
        }).start();

        return ApiResponse.success("开始分析，请耐心等待");
    }

    /**
     * 执行视频分析的核心逻辑
     *
     * @param videoAnalysis 视频分析任务对象
     * @param userId 用户ID
     */
    private void performVideoAnalysis(VideoAnalysis videoAnalysis, String userId) {
        String fileId = videoAnalysis.getFileId();
        logger.info("开始执行视频分析，fileId: {}", fileId);

        try {
            // 1. 获取文件信息
            com.ruoyi.wenshuapi.util.file.ApiResponse<FileInfoPojo> fileResponse = fileInfoClient.getFileById(Integer.parseInt(fileId));
            if (fileResponse == null || fileResponse.getData() == null) {
                throw new RuntimeException("无法获取文件信息，fileId: " + fileId);
            }

            String filePath = "D:/wenshu/file-storage/" + fileResponse.getData().getFilePath();
            logger.info("文件路径: {}", filePath);

            // 2. 检查文件是否存在
            File videoFile = new File(filePath);
            if (!videoFile.exists()) {
                throw new RuntimeException("视频文件不存在: " + filePath);
            }
            logger.info("文件存在性检查通过，文件大小: {} MB", videoFile.length() / 1024 / 1024);

            // 3. 执行视频分析
            String videoAnalyzeResults = performVideoContentAnalysis(filePath);
           logger.info("视频内容分析完成，结果长度: {}", videoAnalyzeResults.length());

            // 4. 提取音频并进行语音识别
            String audioAnalyzeResults = performAudioAnalysis(filePath);
            logger.info("音频分析完成，结果长度: {}", audioAnalyzeResults.length());

            // 5. 执行AI文本分析
            String textAnalyzeResults = performTextAnalysis(audioAnalyzeResults, userId);
            logger.info("文本分析完成，结果长度: {}", textAnalyzeResults.length());

            // 6. 执行日程分析
            String scheduleAnalyzeResults = performScheduleAnalysis(audioAnalyzeResults, userId);
            logger.info("日程分析完成，结果长度: {}", scheduleAnalyzeResults.length());

            // 7. 更新数据库
            updateAnalysisResults(videoAnalysis, videoAnalyzeResults, audioAnalyzeResults,
                                textAnalyzeResults, scheduleAnalyzeResults);

            logger.info("视频分析任务完成，analysisId: {}", videoAnalysis.getId());

        } catch (Exception e) {
            logger.error("视频分析过程中发生异常: {}", e.getMessage(), e);
            throw new RuntimeException("视频分析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行视频内容分析
     *
     * @param filePath 视频文件路径
     * @return 视频分析结果
     */
    private String performVideoContentAnalysis(String filePath) {
        logger.info("开始视频内容分析，文件路径: {}", filePath);
        List<String> videoSegments = null;

        try {
            // 1. 对视频进行切分（每10秒一段）
            videoSegments = videoSplitter.splitVideo(filePath);
            logger.info("视频切分完成，共生成 {} 个片段", videoSegments.size());

            if (videoSegments.isEmpty()) {
                logger.warn("视频切分未生成任何片段");
                return "视频切分失败，未生成任何片段";
            }

            StringBuilder videoAnalyzeResults = new StringBuilder();

            // 2. 对每个视频片段进行AI分析
            for (int i = 0; i < videoSegments.size(); i++) {
                String segmentPath = videoSegments.get(i);
                boolean isLastSegment = (i == videoSegments.size() - 1);

                // 添加文件诊断信息
                File segmentFile = new File(segmentPath);
                long fileSize = segmentFile.exists() ? segmentFile.length() : 0;
                logger.info("正在分析第 {} 个视频片段: {} (共{}个), 文件大小: {} bytes, 是否最后一个: {}",
                           i + 1, segmentPath, videoSegments.size(), fileSize, isLastSegment);

                try {
                    // 对最后一个切片进行特殊处理
                    if (isLastSegment) {
                        // 检查最后一个切片是否太小（可能损坏）
                        if (fileSize < 1024) { // 小于1KB
                            logger.warn("最后一个切片文件过小 ({} bytes)，跳过分析", fileSize);
                            videoAnalyzeResults.append("【片段").append(i + 1).append("】文件过小，跳过分析\n");
                            continue;
                        }
                        logger.info("开始处理最后一个切片，将使用更宽松的处理策略");
                    }

                    // API频率控制已在VideAiQvqMaxUtil中处理，这里直接调用
                    String segmentResult;
                    if (isLastSegment) {
                        // 最后一个切片使用特殊处理，更短的超时时间
                        segmentResult = VideAiQvqMaxUtil.analyzeVideoForLastSegment(segmentPath);
                    } else {
                        segmentResult = VideAiQvqMaxUtil.analyzeVideo(segmentPath);
                    }
                    videoAnalyzeResults.append("【片段").append(i + 1).append("】").append(segmentResult).append("\n");
                    logger.info("第 {} 个视频片段分析完成，结果长度: {}", i + 1, segmentResult.length());
                } catch (Exception e) {
                    logger.error("第 {} 个视频片段分析失败: {}", i + 1, e.getMessage(), e);
                    videoAnalyzeResults.append("【片段").append(i + 1).append("】分析失败: ").append(e.getMessage()).append("\n");

                    // 如果是中断异常，需要特殊处理
                    if (e.getCause() instanceof InterruptedException) {
                        Thread.currentThread().interrupt();
                        logger.error("视频分析被中断");
                        break;
                    }
                }

                // 添加进度日志
                logger.info("视频分析进度: {}/{}", i + 1, videoSegments.size());
            }

            String finalResult = videoAnalyzeResults.toString();
            if (finalResult.trim().isEmpty()) {
                logger.warn("所有视频片段分析都失败了");
                return "视频内容分析失败：所有片段都无法处理";
            } else {
                logger.info("视频片段分析完成，总结果长度: {}，成功处理的片段数: {}",
                           finalResult.length(), countSuccessfulSegments(finalResult));
                return finalResult;
            }

        } catch (Exception e) {
            logger.error("视频内容分析失败: {}", e.getMessage(), e);
            return "视频内容分析失败: " + e.getMessage();
        } finally {
            // 清理临时文件
            if (videoSegments != null && !videoSegments.isEmpty()) {
                cleanupVideoSegments(videoSegments);
            }
        }
    }

    /**
     * 执行音频分析
     *
     * @param filePath 视频文件路径
     * @return 音频识别结果
     */
    private String performAudioAnalysis(String filePath) {
        logger.info("开始音频分析，文件路径: {}", filePath);

        try {
            // 1. 从视频中提取音频
            String audioPath = audioExtractor.extractAudio(filePath);
            logger.info("音频提取完成，音频文件路径: {}", audioPath);

            // 2. 将音频文件转换为MultipartFile进行语音识别
            File audioFile = new File(audioPath);
            if (!audioFile.exists()) {
                throw new RuntimeException("提取的音频文件不存在: " + audioPath);
            }

            // 创建自定义MultipartFile用于语音识别
            try (FileInputStream fis = new FileInputStream(audioFile)) {
                byte[] audioBytes = fis.readAllBytes();
                MultipartFile multipartFile = new CustomMultipartFile(
                    "file",
                    audioFile.getName(),
                    "audio/mp3",
                    audioBytes
                );

                // 3. 调用语音识别服务
                String audioAnalyzeResults = videVoiceAiService.speechRecognition(multipartFile);
                logger.info("语音识别完成，结果长度: {}", audioAnalyzeResults.length());

                return audioAnalyzeResults;

            } catch (IOException e) {
                throw new RuntimeException("读取音频文件失败: " + e.getMessage(), e);
            }

        } catch (Exception e) {
            logger.error("音频分析失败: {}", e.getMessage(), e);
            return "音频分析失败: " + e.getMessage();
        }
    }

    /**
     * 执行文本分析
     *
     * @param audioText 音频识别的文本
     * @param userId 用户ID
     * @return 文本分析结果
     */
    private String performTextAnalysis(String audioText, String userId) {
        logger.info("开始文本分析，文本长度: {}", audioText.length());

        if (audioText == null || audioText.trim().isEmpty()) {
            logger.warn("音频文本为空，跳过文本分析");
            return "音频文本为空，无法进行分析";
        }

        try {
            StringBuilder textAnalyzeResults = new StringBuilder();

            // 将长文本按5000字符切分进行分析
            List<String> textChunks = splitTextIntoChunks(audioText, MAX_TEXT_LENGTH);
            logger.info("文本切分完成，共 {} 个片段", textChunks.size());

            for (int i = 0; i < textChunks.size(); i++) {
                String chunk = textChunks.get(i);
                logger.info("正在分析第 {} 个文本片段，长度: {}", i + 1, chunk.length());

                try {
                    String prompt = MEETING_ANALYSIS_PROMPT + chunk;
                    // 创建RequestBodyDTO对象
                    RequestBodyDTO requestBodyDTO = new RequestBodyDTO(prompt, userId);
                    String chunkResult = chatFeignClientInter.sendMessageEasy(requestBodyDTO);
                    textAnalyzeResults.append("").append("").append("").append(chunkResult).append("\n");
                    logger.info("第 {} 个文本片段分析完成", i + 1);
                } catch (Exception e) {
                    logger.warn("第 {} 个文本片段分析失败: {}", i + 1, e.getMessage());
                    //textAnalyzeResults.append("【文本片段").append(i + 1).append("】分析失败: ").append(e.getMessage()).append("\n");
                }
            }

            return textAnalyzeResults.toString();

        } catch (Exception e) {
            logger.error("文本分析失败: {}", e.getMessage(), e);
            return "文本分析失败: " + e.getMessage();
        }
    }

    /**
     * 执行日程分析
     *
     * @param audioText 音频识别的文本
     * @param userId 用户ID
     * @return 日程分析结果
     */
    private String performScheduleAnalysis(String audioText, String userId) {
        logger.info("开始日程分析，文本长度: {}", audioText.length());

        if (audioText == null || audioText.trim().isEmpty()) {
            logger.warn("音频文本为空，跳过日程分析");
            return "音频文本为空，无法进行日程分析";
        }

        try {
            StringBuilder scheduleAnalyzeResults = new StringBuilder();

            // 将长文本按5000字符切分进行分析
            List<String> textChunks = splitTextIntoChunks(audioText, MAX_TEXT_LENGTH);
            logger.info("日程分析文本切分完成，共 {} 个片段", textChunks.size());

            for (int i = 0; i < textChunks.size(); i++) {
                String chunk = textChunks.get(i);
                logger.info("正在进行第 {} 个文本片段的日程分析，长度: {}", i + 1, chunk.length());

                try {
                    String prompt = getAgentAddSchedulePrompt() + chunk;
                    // 创建RequestBodyDTO对象
                    RequestBodyDTO requestBodyDTO = new RequestBodyDTO(prompt, userId);
                    String chunkResult = chatFeignClientInter.sendMessageEasy(requestBodyDTO);
                    scheduleAnalyzeResults.append("").append("").append("").append(chunkResult).append("\n");
                    logger.info("第 {} 个文本片段日程分析完成", i + 1);
                } catch (Exception e) {
                    logger.warn("第 {} 个文本片段日程分析失败: {}", i + 1, e.getMessage());
                    //scheduleAnalyzeResults.append("【日程片段").append(i + 1).append("】分析失败: ").append(e.getMessage()).append("\n");
                }
            }

            return scheduleAnalyzeResults.toString();

        } catch (Exception e) {
            logger.error("日程分析失败: {}", e.getMessage(), e);
            return "日程分析失败: " + e.getMessage();
        }
    }

    /**
     * 将长文本切分为指定长度的片段
     *
     * @param text 原始文本
     * @param maxLength 每个片段的最大长度
     * @return 切分后的文本片段列表
     */
    private List<String> splitTextIntoChunks(String text, int maxLength) {
        List<String> chunks = new java.util.ArrayList<>();

        if (text == null || text.isEmpty()) {
            return chunks;
        }

        int start = 0;
        while (start < text.length()) {
            int end = Math.min(start + maxLength, text.length());

            // 尝试在句号、感叹号或问号处切分，避免切断句子
            if (end < text.length()) {
                int lastSentenceEnd = Math.max(
                    Math.max(text.lastIndexOf('。', end), text.lastIndexOf('！', end)),
                    text.lastIndexOf('？', end)
                );

                if (lastSentenceEnd > start) {
                    end = lastSentenceEnd + 1;
                }
            }

            chunks.add(text.substring(start, end));
            start = end;
        }

        return chunks;
    }

    /**
     * 更新分析结果到数据库
     *
     * @param videoAnalysis 视频分析对象
     * @param videoResults 视频分析结果
     * @param audioResults 音频识别结果
     * @param textResults 文本分析结果
     * @param scheduleResults 日程分析结果
     */
    private void updateAnalysisResults(VideoAnalysis videoAnalysis, String videoResults,
                                     String audioResults, String textResults, String scheduleResults) {
        try {
            // 构建初始文本（原始识别结果）
            String initialText = "【视频识别开始】" + videoResults + "【视频识别结束】" +
                               "【音频识别开始】" + audioResults + "【音频识别结束】";

            // 构建分析文本（AI分析结果）
            String analysisText = "【会议分析开始】" + textResults + "【会议分析结束】" +
                                "【日程分析开始】" + scheduleResults + "【日程分析结束】";

            videoAnalysis.setInitialText(initialText);
            videoAnalysis.setAnalysisText(analysisText);
            videoAnalysis.setStatus(2); // 设置为成功状态
            videoAnalysis.setFinishTime(LocalDateTime.now());

            videoAnalysisService.update(videoAnalysis);
            logger.info("分析结果已更新到数据库，analysisId: {}", videoAnalysis.getId());

        } catch (Exception e) {
            logger.error("更新分析结果到数据库失败: {}", e.getMessage(), e);
            throw new RuntimeException("更新分析结果失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清理视频切片临时文件
     *
     * @param videoSegments 视频切片路径列表
     */
    private void cleanupVideoSegments(List<String> videoSegments) {
        if (videoSegments == null || videoSegments.isEmpty()) {
            return;
        }

        logger.info("开始清理 {} 个视频切片临时文件", videoSegments.size());
        int deletedCount = 0;

        for (String segmentPath : videoSegments) {
            try {
                File segmentFile = new File(segmentPath);
                if (segmentFile.exists()) {
                    if (segmentFile.delete()) {
                        deletedCount++;
                        logger.debug("已删除临时文件: {}", segmentPath);
                    } else {
                        logger.warn("无法删除临时文件: {}", segmentPath);
                    }
                }
            } catch (Exception e) {
                logger.warn("删除临时文件时发生异常: {}, 错误: {}", segmentPath, e.getMessage());
            }
        }

        // 尝试删除临时目录
        if (!videoSegments.isEmpty()) {
            try {
                String firstSegmentPath = videoSegments.get(0);
                File segmentDir = new File(firstSegmentPath).getParentFile();
                if (segmentDir != null && segmentDir.exists() && segmentDir.getName().startsWith("segments_")) {
                    if (segmentDir.delete()) {
                        logger.info("已删除临时目录: {}", segmentDir.getPath());
                    } else {
                        logger.warn("无法删除临时目录: {}", segmentDir.getPath());
                    }
                }
            } catch (Exception e) {
                logger.warn("删除临时目录时发生异常: {}", e.getMessage());
            }
        }

        logger.info("临时文件清理完成，成功删除 {}/{} 个文件", deletedCount, videoSegments.size());
    }

    /**
     * 计算成功处理的视频片段数量
     *
     * @param result 分析结果字符串
     * @return 成功处理的片段数量
     */
    private int countSuccessfulSegments(String result) {
        if (result == null || result.trim().isEmpty()) {
            return 0;
        }

        // 计算包含"【片段"但不包含"分析失败"的行数
        String[] lines = result.split("\n");
        int successCount = 0;
        for (String line : lines) {
            if (line.contains("【片段") && !line.contains("分析失败") && !line.contains("分析被中断")) {
                successCount++;
            }
        }
        return successCount;
    }

    /**
     * 处理分析失败的情况
     *
     * @param videoAnalysis 视频分析对象
     * @param errorMessage 错误信息
     */
    private void handleAnalysisFailure(VideoAnalysis videoAnalysis, String errorMessage) {
        try {
            videoAnalysis.setStatus(3); // 设置为失败状态
            videoAnalysis.setFailureReason(errorMessage);
            videoAnalysis.setFinishTime(LocalDateTime.now());
            videoAnalysisService.update(videoAnalysis);
            logger.info("分析失败信息已更新到数据库，analysisId: {}, error: {}",
                       videoAnalysis.getId(), errorMessage);
        } catch (Exception e) {
            logger.error("更新分析失败信息到数据库时发生异常: {}", e.getMessage(), e);
        }
    }
    /**
     * 会议分析提示词
     */
    private static final String MEETING_ANALYSIS_PROMPT = "任务目标\n" +
            "对在线会议音频转写的原始文本进行多维度深度分析，提取结构化洞察，输出专业会议分析报告。\n" +
            "\n" +
            "核心分析维度\n" +
            "\n" +
            "文本预处理优化\n" +
            "\n" +
            "自动校正同音错字（例：\"试场\"→\"市场\"；\"云服务\"→\"云服务器\"）\n" +
            "\n" +
            "保留并标注专业术语（K8s/ROI测算/AIops等），建立术语表\n" +
            "\n" +
            "屏蔽非实质内容：\n" +
            "▪\uFE0F 技术故障片段（\"能听到我吗？\"、\"屏幕共享卡顿了\"）\n" +
            "▪\uFE0F 社交性对话（\"早上好\"、\"周末愉快\"）\n" +
            "▪\uFE0F 重复性填充词（\"呃...\"\"那个...\" 保留但不计入分析）\n" +
            "\n" +
            "发言者角色建模\n" +
            "\n" +
            "识别发言者身份（主持人/决策者/执行层/外部专家）\n" +
            "\n" +
            "构建发言网络图：\n" +
            "▪\uFE0F 关键观点发起者\n" +
            "▪\uFE0F 意见附议/反对关系\n" +
            "▪\uFE0F 决策链路径追溯\n" +
            "\n" +
            "内容深度挖掘\n" +
            "\n" +
            "议题演化分析：\n" +
            "▪\uFE0F 核心议题的提出→讨论→结论演进路径\n" +
            "▪\uFE0F 观点冲突点标注（技术路线/资源分配/时间线分歧）\n" +
            "\n" +
            "决策点提取：\n" +
            "▪\uFE0F 明确决议项（通过动词识别：决定/批准/驳回）\n" +
            "▪\uFE0F 待办事项生成（责任人+DDL+交付物）\n" +
            "\n" +
            "风险预警：\n" +
            "▪\uFE0F 识别模糊承诺（\"尽量完成\"/\"可能延迟\"）\n" +
            "▪\uFE0F 标注资源缺口表述（人力/预算/技术瓶颈）\n" +
            "\n" +
            "专业语境增强\n" +
            "\n" +
            "行业术语库匹配（科技/金融/医疗等领域自定义词库）\n" +
            "\n" +
            "业务指标关联（提及的KPI/OKR自动关联历史数据）\n" +
            "\n" +
            "技术方案完整性评估（需求→方案→验证闭环检测）\n" +
            "\n" +
            "输出要求\n" +
            "\n" +
            "1.不需要马克当格式输出" +
            "2.按照作文格式输出" +
            "3.尽量少使用特殊符合，如#@%&*等" +
            "4.非会议内容请不按照以上给出分析条件进行分析，分析范围多元化，富有深度，面面俱到" +
            "5.保留原始口语化表达中的有效信息\n" +
            "\n" +
            "6.禁止生成图表/可视化\n" +
            "\n" +
            "7.标注分析置信度（低置信度内容需标黄提示复核）" +
            "以下是识别文本内容:\n";
    /**
     * 获取日程分析提示词
     *
     * @return 包含当前时间的日程分析提示词
     */
    private String getAgentAddSchedulePrompt() {
        return "当前时间：" + TimeNow() +
            "\n你会基于我以下的文本进行判断，以下文本为语音识别内容，请对错别字以及混乱字义进行联想" +
            "\n输出示例：" +
            "{[{\n" +
            "  \"title\": \"日程标题\",\n" +
            "  \"description\": \"描述信息，同时用于提取时间信息，如\\\"下午5点开会，地点在明行楼\\\"\",\n" +
            "  \"start_time\": \"日程开始时间（如果设置了此字段，则优先使用）\",\n" +
            "  \"owner_type\": \"所有者类型（个人、团队）\",\n" +
            "  \"repeat_rule\": \"重复规则（每天重复、一次性）\",\n" +
            "  \"remind_time\": \"提醒时间（默认提前十分钟，可修改）\",\n" +
            "  \"event_status\": \"日程状态(0-取消,1-正常)\"\n" +
            "}]}" +
            "\n输出要求：" +
            "\n1.基于给定识别文本内容对给定示例进行日程内容填充" +
            "\n2.未明确的内容可虚构，但要符合客观规律"+
            "\n3.除了给定示例内容，不允许输出多余内容" +
            "\n4.输出内容符合给定的json格式，不允许出现多余内容" +
            "\n5.去除特殊符合，只能使用 {} [] , : 等符合json格式的符号" +
            "\n6.不使用java的转义符号，去除示例中本身带有的转义符号" +
            "\n以下是我的识别内容：\n";
    }
    /**
     * 获取当前时间的格式化字符串
     *
     * @return 格式化的当前时间
     */
    private static String TimeNow() {
        // 获取当前时间（包含年月日时分秒）
        LocalDateTime now = LocalDateTime.now();

        // 定义格式化模式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 格式化时间
        return now.format(formatter);
    }

    /**
     * 自定义MultipartFile实现，用于替代MockMultipartFile
     */
    private static class CustomMultipartFile implements MultipartFile {
        private final String name;
        private final String originalFilename;
        private final String contentType;
        private final byte[] content;

        public CustomMultipartFile(String name, String originalFilename, String contentType, byte[] content) {
            this.name = name;
            this.originalFilename = originalFilename;
            this.contentType = contentType;
            this.content = content;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String getOriginalFilename() {
            return originalFilename;
        }

        @Override
        public String getContentType() {
            return contentType;
        }

        @Override
        public boolean isEmpty() {
            return content.length == 0;
        }

        @Override
        public long getSize() {
            return content.length;
        }

        @Override
        public byte[] getBytes() throws IOException {
            return content;
        }

        @Override
        public InputStream getInputStream() throws IOException {
            return new ByteArrayInputStream(content);
        }

        @Override
        public void transferTo(File dest) throws IOException, IllegalStateException {
            try (FileOutputStream fos = new FileOutputStream(dest)) {
                fos.write(content);
            }
        }
    }
}
