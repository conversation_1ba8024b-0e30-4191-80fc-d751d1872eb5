package com.ruoyi.wenshumeeting.config;

import io.reactivex.exceptions.UndeliverableException;
import io.reactivex.plugins.RxJavaPlugins;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.beans.factory.InitializingBean;

import java.io.IOException;
import java.net.SocketException;

/**
 * RxJava全局配置
 * 处理UndeliverableException等RxJava相关异常
 */
@Configuration
public class RxJavaConfig implements InitializingBean {

    private static final Logger logger = LoggerFactory.getLogger(RxJavaConfig.class);

    /**
     * 配置RxJava全局错误处理器
     * 在Bean初始化完成后自动调用
     */
    @Override
    public void afterPropertiesSet() {
        RxJavaPlugins.setErrorHandler(throwable -> {
            if (throwable instanceof UndeliverableException) {
                // 处理UndeliverableException
                handleUndeliverableException((UndeliverableException) throwable);
            } else {
                // 其他未处理的异常，记录警告日志
                logger.warn("RxJava未处理的异常: {}", throwable.getMessage(), throwable);
                
                // 对于严重错误，仍然抛出
                if (throwable instanceof VirtualMachineError) {
                    throw (VirtualMachineError) throwable;
                } else if (throwable instanceof ThreadDeath) {
                    throw (ThreadDeath) throwable;
                } else if (throwable instanceof LinkageError) {
                    throw (LinkageError) throwable;
                }
            }
        });
        
        logger.info("RxJava全局错误处理器已配置");
    }

    /**
     * 处理UndeliverableException
     * 
     * @param exception UndeliverableException实例
     */
    private void handleUndeliverableException(UndeliverableException exception) {
        Throwable cause = exception.getCause();
        
        if (cause instanceof IOException || cause instanceof SocketException) {
            // 网络相关异常，记录调试日志
            logger.debug("RxJava流处理网络异常（已忽略）: {}", cause.getMessage());
        } else if (cause != null && cause.getMessage() != null && 
                   cause.getMessage().contains("RequestTimeOut")) {
            // API超时异常，记录信息日志
            logger.info("RxJava流处理API超时异常（已忽略）: {}", cause.getMessage());
        } else if (cause instanceof InterruptedException) {
            // 中断异常，记录调试日志
            logger.debug("RxJava流处理中断异常（已忽略）: {}", cause.getMessage());
        } else {
            // 其他UndeliverableException，记录警告日志
            logger.warn("RxJava流处理未投递异常: {}", exception.getMessage(), exception);
        }
    }
}
