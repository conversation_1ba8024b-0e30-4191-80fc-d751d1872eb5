//package com.ruoyi.wenshuvoice.controller;
//
//import com.ruoyi.wenshuapi.client.team.TeamInfoClient;
//import com.ruoyi.wenshuapi.pojo.team.TeamInfo;
//import com.ruoyi.wenshuapi.util.file.ApiResponse;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.Collections;
//import java.util.List;
//
///**
// * 团队信息管理网关控制器
// *
// * <p>作为团队服务的网关层，通过Feign Client调用团队微服务</p>
// *
// * <p><strong>路径说明:</strong>
// * - 顶层路径: /api/teaminfo (与微服务路径隔离)
// * - 实际调用: /wenshu/teaminfo (微服务路径)</p>
// */
//@RestController
//@RequestMapping("/api/teaminfo")
//public class TeamInfoGatewayController {
//
//    private final TeamInfoClient teamInfoClient;
//
//    @Autowired
//    public TeamInfoGatewayController(TeamInfoClient teamInfoClient) {
//        this.teamInfoClient = teamInfoClient;
//    }
//
//    /**
//     * 创建新团队（网关层）
//     *
//     * <p><strong>路径:</strong> POST /api/teaminfo</p>
//     *
//     * @param teamInfo 团队信息实体
//     * @return 包含新团队ID的响应
//     *
//     * <p><strong>实现说明:</strong>
//     * 1. 直接转发到团队服务的创建接口
//     * 2. 添加网关层日志记录
//     * 3. 统一处理降级响应</p>
//     */
//    @PostMapping
//    public ApiResponse<Integer> createTeam(@RequestBody TeamInfo teamInfo) {
//        // 日志记录请求信息（实际生产环境使用AOP统一记录）
//        System.out.println("创建团队请求: " + teamInfo.getTeamName());
//
//        // 调用Feign Client
//        ApiResponse<Integer> response = teamInfoClient.createTeam(teamInfo);
//
//        // 处理降级响应
//        if (response.getCode() == 503) {
//            return ApiResponse.failed("团队服务暂时不可用，创建操作未执行", 503);
//        }
//        return response;
//    }
//
//    /**
//     * 删除团队（网关层）
//     *
//     * <p><strong>路径:</strong> DELETE /api/teaminfo/{teamId}</p>
//     *
//     * @param teamId 团队ID
//     * @return 操作结果响应
//     */
//    @DeleteMapping("/{teamId}")
//    public ApiResponse<Void> deleteTeam(@PathVariable("teamId") Integer teamId) {
//        ApiResponse<Void> response = teamInfoClient.deleteTeam(teamId);
//
//        if (response.getCode() == 503) {
//            return ApiResponse.failed("团队服务暂时不可用，删除操作未执行", 503);
//        }
//        return response;
//    }
//
//    /**
//     * 更新团队信息（网关层）
//     *
//     * <p><strong>路径:</strong> PUT /api/teaminfo</p>
//     *
//     * @param teamInfo 团队信息实体
//     * @return 操作结果响应
//     */
//    @PutMapping
//    public ApiResponse<Void> updateTeam(@RequestBody TeamInfo teamInfo) {
//        ApiResponse<Void> response = teamInfoClient.updateTeam(teamInfo);
//
//        if (response.getCode() == 503) {
//            return ApiResponse.failed("团队服务暂时不可用，更新操作未执行", 503);
//        }
//        return response;
//    }
//
//    /**
//     * 根据ID获取团队详情（网关层）
//     *
//     * <p><strong>路径:</strong> GET /api/teaminfo/{teamId}</p>
//     *
//     * @param teamId 团队ID
//     * @return 团队信息响应
//     *
//     * <p><strong>增强处理:</strong>
//     * - 对降级响应返回空团队对象
//     * - 添加缓存逻辑（示例）</p>
//     */
//    @GetMapping("/{teamId}")
//    public ApiResponse<TeamInfo> getTeamById(@PathVariable("teamId") Integer teamId) {
//        // 实际生产环境可在此添加缓存逻辑
//        // TeamInfo cached = cacheService.getTeam(teamId);
//        // if (cached != null) return ApiResponse.success(cached);
//
//        ApiResponse<TeamInfo> response = teamInfoClient.getTeamById(teamId);
//
//        // 降级处理：返回空对象避免前端报错
//        if (response.getCode() == 503) {
//            TeamInfo fallbackTeam = new TeamInfo();
//            fallbackTeam.setTeamId(-1);
//            fallbackTeam.setTeamName("团队信息暂不可用");
//            return ApiResponse.success(fallbackTeam, "团队服务降级响应");
//        }
//        return response;
//    }
//
//    /**
//     * 获取所有团队列表（网关层）
//     *
//     * <p><strong>路径:</strong> GET /api/teaminfo/all</p>
//     *
//     * @return 团队列表响应
//     */
//    @GetMapping("/all")
//    public ApiResponse<List<TeamInfo>> getAllTeams() {
//        ApiResponse<List<TeamInfo>> response = teamInfoClient.getAllTeams();
//
//        // 降级处理：返回空列表
//        if (response.getCode() == 503) {
//            return ApiResponse.success(Collections.emptyList(), "团队服务降级响应");
//        }
//        return response;
//    }
//
//    /**
//     * 根据状态获取团队列表（网关层）
//     *
//     * <p><strong>路径:</strong> GET /api/teaminfo/status/{status}</p>
//     *
//     * @param status 团队状态 (0-禁用, 1-启用)
//     * @return 团队列表响应
//     */
//    @GetMapping("/status/{status}")
//    public ApiResponse<List<TeamInfo>> getTeamsByStatus(@PathVariable("status") Byte status) {
//        ApiResponse<List<TeamInfo>> response = teamInfoClient.getTeamsByStatus(status);
//
//        if (response.getCode() == 503) {
//            return ApiResponse.success(Collections.emptyList(), "团队服务降级响应");
//        }
//        return response;
//    }
//
//    /**
//     * 根据团队名称搜索团队（网关层）
//     *
//     * <p><strong>路径:</strong> GET /api/teaminfo/search</p>
//     *
//     * @param name 团队名称关键词
//     * @return 团队列表响应
//     *
//     * <p><strong>增强功能:</strong>
//     * - 添加搜索关键字清洗
//     * - 结果缓存处理</p>
//     */
//    @GetMapping("/search")
//    public ApiResponse<List<TeamInfo>> searchTeamsByName(@RequestParam("name") String name) {
//        // 清洗搜索关键字（防止SQL注入等）
//        String cleanedName = name.trim().replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "");
//
//        if (cleanedName.isEmpty()) {
//            return ApiResponse.validateFailed("搜索关键字不合法");
//        }
//
//        ApiResponse<List<TeamInfo>> response = teamInfoClient.searchTeamsByName(cleanedName);
//
//        if (response.getCode() == 503) {
//            return ApiResponse.success(Collections.emptyList(), "团队服务降级响应");
//        }
//        return response;
//    }
//
//
//}