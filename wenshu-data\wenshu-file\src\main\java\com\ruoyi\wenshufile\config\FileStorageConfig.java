package com.ruoyi.wenshufile.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.support.StandardServletMultipartResolver;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import jakarta.servlet.MultipartConfigElement;
import java.io.File;

/**
 * 文件存储配置类
 * 配置文件上传、存储路径、访问路径、跨域支持等参数
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-28
 */
@Configuration
public class FileStorageConfig implements WebMvcConfigurer {

    /**
     * 文件存储根路径
     */
    @Value("${wenshu.file.storage.path:D:/wenshu/file-storage}")
    private String fileStorageBasePath;

    /**
     * 文件访问前缀
     */
    @Value("${wenshu.file.prefix:/wenshu-files}")
    private String filePrefix;

    /**
     * 最大文件大小（10GB）
     */
    @Value("${wenshu.file.max-size:10737418240}")
    private long maxFileSize;

    /**
     * 最大请求大小（10GB）
     */
    @Value("${wenshu.file.max-request-size:10737418240}")
    private long maxRequestSize;

    /**
     * 文件大小阈值，超过此值将写入临时文件（2KB）
     */
    @Value("${wenshu.file.file-size-threshold:2048}")
    private long fileSizeThreshold;

    /**
     * 配置文件上传解析器
     * 支持大文件上传
     */
    @Bean
    public MultipartResolver multipartResolver() {
        StandardServletMultipartResolver resolver = new StandardServletMultipartResolver();
        return resolver;
    }

    /**
     * 配置文件上传参数
     * 设置最大文件大小为100MB
     */
    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        
        // 设置单个文件最大大小
        factory.setMaxFileSize(DataSize.ofBytes(maxFileSize));
        
        // 设置总上传数据最大大小
        factory.setMaxRequestSize(DataSize.ofBytes(maxRequestSize));
        
        // 设置内存临界值，超过此值将写入临时文件
        factory.setFileSizeThreshold(DataSize.ofBytes(fileSizeThreshold));
        
        // 设置临时文件存储位置
        factory.setLocation(System.getProperty("java.io.tmpdir"));
        
        return factory.createMultipartConfig();
    }

    /**
     * 配置静态资源处理器
     * 允许通过HTTP访问上传的文件
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置文件访问路径
        registry.addResourceHandler(filePrefix + "/**")
                .addResourceLocations("file:" + fileStorageBasePath + File.separator)
                .setCachePeriod(3600) // 设置缓存时间为1小时
                .resourceChain(true);
    }

    /**
     * 配置跨域访问
     * 允许前端跨域访问文件
     */
    @Bean
    public CorsFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();
        
        // 允许所有域名跨域访问
        config.addAllowedOriginPattern("*");
        
        // 允许的请求方法
        config.addAllowedMethod("GET");
        config.addAllowedMethod("POST");
        config.addAllowedMethod("PUT");
        config.addAllowedMethod("DELETE");
        config.addAllowedMethod("OPTIONS");
        
        // 允许的请求头
        config.addAllowedHeader("*");
        
        // 允许发送Cookie
        config.setAllowCredentials(true);
        
        // 预检请求的缓存时间
        config.setMaxAge(3600L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        
        // 对文件访问路径应用跨域配置
        source.registerCorsConfiguration(filePrefix + "/**", config);
        
        // 对API接口应用跨域配置
        source.registerCorsConfiguration("/wenshu/fileinfo/**", config);
        
        return new CorsFilter(source);
    }

    /**
     * 获取文件存储根路径
     * 
     * @return 文件存储根路径
     */
    public String getFileStorageBasePath() {
        return fileStorageBasePath;
    }

    /**
     * 获取文件访问前缀
     * 
     * @return 文件访问前缀
     */
    public String getFilePrefix() {
        return filePrefix;
    }

    /**
     * 获取最大文件大小
     * 
     * @return 最大文件大小（字节）
     */
    public long getMaxFileSize() {
        return maxFileSize;
    }

    /**
     * 获取最大请求大小
     * 
     * @return 最大请求大小（字节）
     */
    public long getMaxRequestSize() {
        return maxRequestSize;
    }

    /**
     * 获取文件大小阈值
     * 
     * @return 文件大小阈值（字节）
     */
    public long getFileSizeThreshold() {
        return fileSizeThreshold;
    }
}
