package com.ruoyi.wenshulivechat.config;

import com.fasterxml.jackson.core.JsonParseException;
import com.ruoyi.wenshulivechat.model.NewMessageResponse;
import org.springframework.messaging.handler.annotation.MessageExceptionHandler;
import org.springframework.messaging.simp.annotation.SendToUser;
import org.springframework.web.bind.annotation.ControllerAdvice;

import java.nio.charset.StandardCharsets;

/**
 * WebSocket全局异常处理器
 * 处理WebSocket消息处理过程中的各种异常
 */
@ControllerAdvice
public class WebSocketExceptionHandler {

    /**
     * 处理JSON解析异常
     * 当客户端发送的消息不是有效的JSON格式时触发
     */
    @MessageExceptionHandler(JsonParseException.class)
    @SendToUser("/queue/errors")
    public NewMessageResponse handleJsonParseException(JsonParseException ex) {
        System.err.println("JSON解析异常: " + ex.getMessage());
        
        // 检查是否是UTF-8编码问题
        if (ex.getMessage().contains("Invalid UTF-8")) {
            return NewMessageResponse.error("消息编码错误，请确保使用UTF-8编码发送消息", null, "error");
        }
        
        return NewMessageResponse.error("消息格式错误，请发送有效的JSON格式消息", null, "error");
    }

    /**
     * 处理消息转换异常
     * 当消息转换过程中出现问题时触发
     */
    @MessageExceptionHandler(org.springframework.messaging.converter.MessageConversionException.class)
    @SendToUser("/queue/errors")
    public NewMessageResponse handleMessageConversionException(
            org.springframework.messaging.converter.MessageConversionException ex) {
        System.err.println("消息转换异常: " + ex.getMessage());
        
        // 检查是否是LocalDateTime序列化问题
        if (ex.getMessage().contains("LocalDateTime")) {
            return NewMessageResponse.error("日期时间序列化错误，服务器配置问题", null, "error");
        }
        
        return NewMessageResponse.error("消息转换失败: " + ex.getMessage(), null, "error");
    }

    /**
     * 处理一般异常
     * 捕获其他未处理的异常
     */
    @MessageExceptionHandler(Exception.class)
    @SendToUser("/queue/errors")
    public NewMessageResponse handleGeneralException(Exception ex) {
        System.err.println("WebSocket处理异常: " + ex.getMessage());
        ex.printStackTrace();
        
        return NewMessageResponse.error("服务器处理异常: " + ex.getMessage(), null, "error");
    }

    /**
     * 处理编码相关的异常
     * 专门处理字符编码问题
     */
    @MessageExceptionHandler(java.nio.charset.MalformedInputException.class)
    @SendToUser("/queue/errors")
    public NewMessageResponse handleEncodingException(java.nio.charset.MalformedInputException ex) {
        System.err.println("字符编码异常: " + ex.getMessage());
        
        return NewMessageResponse.error("字符编码错误，请使用UTF-8编码", null, "error");
    }

    /**
     * 辅助方法：安全地处理可能包含无效字符的字符串
     */
    public static String sanitizeString(String input) {
        if (input == null) {
            return "{}";
        }
        
        try {
            // 尝试将字符串转换为UTF-8字节数组再转回来，过滤无效字符
            byte[] bytes = input.getBytes(StandardCharsets.UTF_8);
            return new String(bytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            System.err.println("字符串清理失败: " + e.getMessage());
            return "{}";
        }
    }
}
