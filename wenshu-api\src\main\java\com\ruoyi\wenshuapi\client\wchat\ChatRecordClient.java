package com.ruoyi.wenshuapi.client.wchat;

import com.ruoyi.wenshuapi.fallback.wchat.ChatRecordClientFallback;
import com.ruoyi.wenshuapi.pojo.wchat.ChatRecord;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 聊天记录服务Feign客户端接口
 * 用于微服务间远程调用聊天记录管理功能
 * 
 * FeignClient参数说明：
 *   name = "wenshu-wchat"  // 目标服务在注册中心的应用名称
 *   path = "/wenshu/wchat/chat" // 统一路径前缀（与Controller顶层路径匹配）
 *   contextId = "chatRecordClient" // 防止与其他同名Feign冲突
 *   fallback = ChatRecordClientFallback.class // 降级处理类
 * 
 * 注意：接口方法需与Controller方法签名严格匹配（路径/参数/返回类型）
 */
@FeignClient(
    name = "wenshu-wchat",
    path = "/wenshu/wchat/chat",
    contextId = "chatRecordClient",
    fallback = ChatRecordClientFallback.class
)
public interface ChatRecordClient {

    /**
     * 统一发送消息接口
     * 支持文本、图片、视频、文件四种类型的消息
     *
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @param contentType 内容类型 (text, image, video, file)
     * @param textContent 文本内容（当contentType为text时使用）
     * @param file 文件（当contentType为image/video/file时使用）
     * @return 包含新消息ID的响应
     */
    @PostMapping("/send")
    ResponseEntity<Map<String, Object>> sendMessage(
            @RequestParam("senderId") Long senderId,
            @RequestParam("receiverId") Long receiverId,
            @RequestParam("contentType") String contentType,
            @RequestParam(value = "textContent", required = false) String textContent,
            @RequestParam(value = "file", required = false) MultipartFile file);

    /**
     * 发送文本消息
     *
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @param content 文本内容
     * @return 包含新消息ID的响应
     */
    @PostMapping("/send/text")
    ResponseEntity<Map<String, Object>> sendTextMessage(
            @RequestParam("senderId") Long senderId,
            @RequestParam("receiverId") Long receiverId,
            @RequestParam("content") String content);

    /**
     * 发送图片消息
     *
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @param file 图片文件
     * @return 包含新消息ID的响应
     */
    @PostMapping("/send/image")
    ResponseEntity<Map<String, Object>> sendImageMessage(
            @RequestParam("senderId") Long senderId,
            @RequestParam("receiverId") Long receiverId,
            @RequestParam("file") MultipartFile file);

    /**
     * 发送视频消息
     *
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @param file 视频文件
     * @return 包含新消息ID的响应
     */
    @PostMapping("/send/video")
    ResponseEntity<Map<String, Object>> sendVideoMessage(
            @RequestParam("senderId") Long senderId,
            @RequestParam("receiverId") Long receiverId,
            @RequestParam("file") MultipartFile file);

    /**
     * 发送文件消息
     *
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @param file 文件
     * @return 包含新消息ID的响应
     */
    @PostMapping("/send/file")
    ResponseEntity<Map<String, Object>> sendFileMessage(
            @RequestParam("senderId") Long senderId,
            @RequestParam("receiverId") Long receiverId,
            @RequestParam("file") MultipartFile file);

    /**
     * 获取文件信息
     *
     * @param filePath 文件相对路径
     * @return 文件信息响应
     */
    @GetMapping("/file/info")
    ResponseEntity<Map<String, Object>> getFileInfo(@RequestParam("path") String filePath);

    /**
     * 下载文件
     *
     * @param filePath 文件相对路径
     * @return 文件内容响应
     */
    @GetMapping("/file/download")
    ResponseEntity<byte[]> downloadFile(@RequestParam("path") String filePath);

    /**
     * 根据消息ID删除聊天记录
     *
     * @param messageId 消息ID
     * @return 删除结果响应
     */
    @DeleteMapping("/{messageId}")
    ResponseEntity<Map<String, Object>> deleteMessage(@PathVariable("messageId") Long messageId);

    /**
     * 标记消息为已读
     *
     * @param messageId 消息ID
     * @return 更新结果响应
     */
    @PutMapping("/{messageId}/read")
    ResponseEntity<Map<String, Object>> markAsRead(@PathVariable("messageId") Long messageId);

    /**
     * 批量标记消息为已读
     *
     * @param messageIds 消息ID列表
     * @return 更新结果响应
     */
    @PutMapping("/batch/read")
    ResponseEntity<Map<String, Object>> batchMarkAsRead(@RequestBody List<Long> messageIds);

    /**
     * 根据发送者和接收者ID获取聊天记录
     *
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @return 聊天记录列表响应
     */
    @GetMapping("/messages")
    ResponseEntity<Map<String, Object>> getMessagesBySenderAndReceiver(
            @RequestParam("senderId") Long senderId,
            @RequestParam("receiverId") Long receiverId);

    /**
     * 分页获取聊天记录
     *
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 聊天记录列表响应
     */
    @GetMapping("/messages/page")
    ResponseEntity<Map<String, Object>> getMessagesBySenderAndReceiverWithPaging(
            @RequestParam("senderId") Long senderId,
            @RequestParam("receiverId") Long receiverId,
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "size", defaultValue = "20") int size);

    /**
     * 根据会话ID获取聊天记录
     *
     * @param sessionId 会话ID
     * @return 聊天记录列表响应
     */
    @GetMapping("/session/{sessionId}")
    ResponseEntity<Map<String, Object>> getMessagesBySessionId(@PathVariable("sessionId") String sessionId);

    /**
     * 分页获取会话聊天记录
     *
     * @param sessionId 会话ID
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 聊天记录列表响应
     */
    @GetMapping("/session/{sessionId}/page")
    ResponseEntity<Map<String, Object>> getMessagesBySessionIdWithPaging(
            @PathVariable("sessionId") String sessionId,
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "size", defaultValue = "20") int size);

    /**
     * 获取用户的未读消息数量
     *
     * @param userId 用户ID
     * @return 未读消息数量响应
     */
    @GetMapping("/unread/user/{userId}")
    ResponseEntity<Map<String, Object>> getUnreadMessageCount(@PathVariable("userId") Long userId);

    /**
     * 获取用户的未读消息列表
     *
     * @param userId 用户ID
     * @return 未读消息列表响应
     */
    @GetMapping("/unread/{userId}")
    ResponseEntity<Map<String, Object>> getUnreadMessages(@PathVariable("userId") Long userId);

    /**
     * 根据时间范围获取聊天记录
     *
     * @param sessionId 会话ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 聊天记录列表响应
     */
    @GetMapping("/session/{sessionId}/timerange")
    ResponseEntity<Map<String, Object>> getMessagesByTimeRange(
            @PathVariable("sessionId") String sessionId,
            @RequestParam("startTime") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam("endTime") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime);

    /**
     * 生成会话ID
     *
     * @param userId1 用户1的ID
     * @param userId2 用户2的ID
     * @return 会话ID响应
     */
    @GetMapping("/session/generate/{userId1}/{userId2}")
    ResponseEntity<Map<String, Object>> generateSessionId(
            @PathVariable("userId1") Long userId1, 
            @PathVariable("userId2") Long userId2);

    /**
     * 根据消息ID获取聊天记录详情
     *
     * @param messageId 消息ID
     * @return 聊天记录详情响应
     */
    @GetMapping("/{messageId}")
    ResponseEntity<Map<String, Object>> getMessageById(@PathVariable("messageId") Long messageId);

    /**
     * 搜索聊天记录
     *
     * @param sessionId 会话ID
     * @param keyword 搜索关键词
     * @return 搜索结果响应
     */
    @GetMapping("/search")
    ResponseEntity<Map<String, Object>> searchMessages(
            @RequestParam("sessionId") String sessionId,
            @RequestParam("keyword") String keyword);
}
