package com.ruoyi.wenshuwchat.service;

import com.ruoyi.wenshuapi.pojo.wchat.FriendList;
import java.util.List;

/**
 * 好友关系服务层接口
 * 提供好友关系的业务逻辑操作
 */
public interface FriendListService {

    /**
     * 添加好友关系
     * 
     * @param friendList 好友关系实体
     * @return 创建成功的好友关系ID
     */
    Long addFriend(FriendList friendList);

    /**
     * 删除好友关系
     * 
     * @param id 好友关系ID
     * @return 删除操作影响的行数
     */
    int deleteFriend(Long id);

    /**
     * 根据用户ID和好友ID删除好友关系
     * 
     * @param userId 用户ID
     * @param friendId 好友ID
     * @return 删除操作影响的行数
     */
    int deleteFriendByUserAndFriend(Long userId, Long friendId);

    /**
     * 更新好友关系状态
     * 
     * @param friendList 好友关系实体
     * @return 更新操作影响的行数
     */
    int updateFriendStatus(FriendList friendList);

    /**
     * 根据ID获取好友关系详情
     * 
     * @param id 好友关系ID
     * @return 好友关系实体
     */
    FriendList getFriendById(Long id);

    /**
     * 根据用户ID获取所有好友关系
     * 
     * @param userId 用户ID
     * @return 好友关系列表
     */
    List<FriendList> getFriendsByUserId(Long userId);

    /**
     * 根据用户ID和状态获取好友关系
     * 
     * @param userId 用户ID
     * @param status 关系状态
     * @return 好友关系列表
     */
    List<FriendList> getFriendsByUserIdAndStatus(Long userId, String status);

    /**
     * 检查两个用户之间是否存在好友关系
     * 
     * @param userId 用户ID
     * @param friendId 好友ID
     * @return 好友关系实体，如果不存在则返回null
     */
    FriendList checkFriendship(Long userId, Long friendId);

    /**
     * 获取所有好友关系列表
     * 
     * @return 好友关系列表
     */
    List<FriendList> getAllFriends();

    /**
     * 获取用户的活跃好友列表
     * 
     * @param userId 用户ID
     * @return 活跃好友关系列表
     */
    List<FriendList> getActiveFriends(Long userId);
}
