package com.ruoyi.wenshuapi.pojo.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 日程管理VO类，用于接收前端请求参数
 * 从description字段中提取时间信息，如果设置了start_time则优先使用
 */
@Data
public class ProgrammeVO {
    private String title;
    private String description; // 描述信息，同时用于提取时间信息，如"下午5点开会，地点在明行楼"
    private LocalDateTime startTime; // 如果设置了此字段，则优先使用
    private Integer creatorId;
    private String ownerType;
    private String repeatRule;
    private LocalDateTime remindTime;
    private Integer eventStatus;
} 