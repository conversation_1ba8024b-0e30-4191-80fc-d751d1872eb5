# wenshu-livechat WebSocket接口配置

## 📋 项目概述

wenshu-livechat 是基于 Spring Boot WebSocket (STOMP) 协议的实时聊天服务，提供高性能、低延迟的实时通信功能。

## 🌐 接口配置

### 统一接口路径
所有controller层的顶层接口统一为：`/wenshu/livechat`

### WebSocket 端点配置
- **连接端点**: `/ws`
- **完整连接地址**: `ws://localhost:1020/ws` 或 `http://localhost:1020/ws` (SockJS)

### 消息路由配置

#### 发送消息路径 (客户端 -> 服务器)
- **查询新消息**: `/send/{userId}/query-new-messages` (只返回3秒内的未读消息)
- **订阅所有未读消息**: `/send/{userId}/subscribe-all-unread` (返回所有未读消息)
- **发送心跳**: `/send/{userId}/heartbeat`

#### 接收消息路径 (服务器 -> 客户端)
- **接收新消息**: `/recv/{userId}/new-messages` (提醒模式)
- **接收所有未读消息**: `/recv/{userId}/all-unread-messages` (订阅模式)
- **接收心跳响应**: `/recv/{userId}/heartbeat`

#### 实现说明
- 用户发送自身用户ID给客户端（通过路径参数）
- 客户端查询数据库（ChatRecordClient）判断是否有新消息
- 有新消息则返回新消息列表及详细信息，状态为true
- 消息发送通过ChatRecordClient接口实现，不通过WebSocket
- 用户通过WebSocket实时获取新消息

#### 提醒逻辑
- 只有未读消息且发送时间与当前时间间隔不超过3秒的消息才会触发提醒
- 如果消息的发送者ID等于当前用户ID，说明是用户自己发送的消息，不会提醒
- 超过3秒的未读消息不会在WebSocket中推送提醒，但仍计入未读消息总数
- 这样可以避免用户重新连接时收到大量历史未读消息的干扰，也避免用户收到自己发送消息的提醒

## 📡 API接口文档

### WebSocket接口

#### 1. 查询新消息
- **路径**: `/send/{userId}/query-new-messages`
- **方法**: WebSocket消息
- **参数**:
  - `userId`: 用户ID（路径参数）
- **请求体**: 任意内容（服务端只使用路径中的userId）
- **响应路径**: `/recv/{userId}/new-messages`
- **响应格式**:
```json
{
  "success": true,
  "message": "查询成功",
  "hasNewMessages": true,
  "newMessages": [
    {
      "id": 123,
      "sessionId": "session_1001_1002",
      "senderId": 1002,
      "receiverId": 1001,
      "sendTime": "2024-01-01T10:30:00",
      "content": "Hello!",
      "contentType": "text",
      "isRead": false
    }
  ],
  "unreadCount": 5,
  "notificationCount": 1,
  "timestamp": 1704096600000
}
```

**字段说明**:
- `hasNewMessages`: 是否有需要提醒的新消息（3秒内的未读消息）
- `newMessages`: 需要提醒的消息列表（只包含3秒内的未读消息）
- `unreadCount`: 用户的未读消息总数（包括所有未读消息）
- `notificationCount`: 需要提醒的消息数量（3秒内的未读消息数量）
- `userId`: 查询的用户ID
- `responseType`: 响应类型（notification: 提醒消息, subscription: 订阅消息）
- `statistics`: 详细统计信息
  - `totalUnreadCount`: 总未读消息数
  - `notificationCount`: 需要提醒的消息数
  - `selfSentFilteredCount`: 过滤掉的自发消息数
  - `timeoutFilteredCount`: 过滤掉的超时消息数
  - `queryTime`: 查询时间
  - `latestMessageTime`: 最新消息时间
  - `oldestUnreadTime`: 最旧未读消息时间
```

#### 2. 订阅所有未读消息
- **路径**: `/send/{userId}/subscribe-all-unread`
- **方法**: WebSocket消息
- **参数**:
  - `userId`: 用户ID（路径参数）
- **请求体**: 任意内容（服务端只使用路径中的userId）
- **响应路径**: `/recv/{userId}/all-unread-messages`
- **响应格式**: 同上，但`responseType`为`"subscription"`，`notificationCount`为0

#### 3. 心跳检测
- **路径**: `/send/{userId}/heartbeat`
- **方法**: WebSocket消息
- **参数**:
  - `userId`: 用户ID（路径参数）
- **请求体**: 任意内容
- **响应路径**: `/recv/{userId}/heartbeat`
- **响应格式**:
```json
{
  "success": true,
  "message": "心跳响应",
  "timestamp": 1704096600000,
  "userId": 1001
}
```

### REST API接口

#### 1. 查询用户新消息（HTTP）
- **路径**: `GET /wenshu/livechat/users/{userId}/new-messages`
- **参数**:
  - `userId`: 用户ID（路径参数）
  - `unreadOnly`: 是否只查询未读消息（可选，默认true）
  - `limit`: 查询限制数量（可选，默认50）
- **响应**: 同WebSocket查询新消息响应

#### 2. 订阅所有未读消息（HTTP）
- **路径**: `GET /wenshu/livechat/users/{userId}/all-unread-messages`
- **参数**:
  - `userId`: 用户ID（路径参数）
- **响应**: 同WebSocket订阅所有未读消息响应

#### 3. 获取未读消息数量
- **路径**: `GET /wenshu/livechat/users/{userId}/unread-count`
- **参数**:
  - `userId`: 用户ID（路径参数）
- **响应格式**:
```json
{
  "success": true,
  "message": "查询成功",
  "userId": 1001,
  "unreadCount": 5,
  "timestamp": 1704096600000
}
```

#### 3. 健康检查
- **路径**: `GET /wenshu/livechat/health`
- **响应格式**:
```json
{
  "status": "UP",
  "service": "wenshu-livechat",
  "timestamp": 1704096600000
}
```

#### 4. WebSocket配置信息
- **路径**: `GET /wenshu/livechat/websocket/info`
- **响应**: WebSocket配置和路由信息

## 🔧 配置文件

### bootstrap.yml 配置
```yaml
# 自定义配置
livechat:
  # WebSocket配置
  websocket:
    # 消息大小限制（KB）
    message-size-limit: 64
    # 发送缓冲区大小限制（KB）
    send-buffer-size-limit: 512
    # 发送超时时间（秒）
    send-timeout: 15
    # WebSocket端点路径
    endpoint: /ws
    # 允许的跨域来源
    allowed-origins: "*"

  # 用户会话配置
  session:
    # 用户离线超时时间（分钟）
    offline-timeout: 5
    # 清理任务执行间隔（分钟）
    cleanup-interval: 5

  # 心跳配置
  heartbeat:
    # 心跳间隔（秒）
    interval: 30
    # 心跳超时（秒）
    timeout: 60

  # 消息路由配置
  routing:
    # 接收消息前缀
    recv-prefix: /recv
    # 发送消息前缀  
    send-prefix: /send
    # 系统通知前缀
    system-prefix: /system
```

## 🚀 快速开始

### 1. 启动服务
```bash
cd wenshu-livechat
mvn spring-boot:run
```

### 2. 访问测试页面
打开浏览器访问：`http://localhost:1020/test.html`

### 3. WebSocket连接示例 (JavaScript)
```javascript
// 创建WebSocket连接
const socket = new SockJS('http://localhost:1020/ws');
const stompClient = new StompJs.Client({
    webSocketFactory: () => socket,
    connectHeaders: {
        'userId': '1001',
        'userName': '测试用户',
        'userStatus': 'online'
    }
});

// 连接成功后订阅消息
stompClient.onConnect = function (frame) {
    // 订阅接收消息
    stompClient.subscribe('/recv/1001/message', function (message) {
        console.log('收到消息:', JSON.parse(message.body));
    });
    
    // 订阅系统通知
    stompClient.subscribe('/system/notifications', function (notification) {
        console.log('系统通知:', JSON.parse(notification.body));
    });
};

// 激活连接
stompClient.activate();
```

### 4. 发送消息示例
```javascript
// 发送聊天消息
const message = {
    senderId: 1001,
    receiverId: 1002,
    content: "Hello, World!",
    contentType: "text",
    sendTime: new Date().toISOString()
};

stompClient.publish({
    destination: '/send/1001/message',
    body: JSON.stringify(message)
});
```

## 📡 REST API 接口

### 服务状态
- **GET** `/wenshu/livechat/status` - 获取服务状态

### 用户管理
- **GET** `/wenshu/livechat/online-users` - 获取在线用户列表
- **GET** `/wenshu/livechat/user/{userId}/status` - 获取用户状态
- **GET** `/wenshu/livechat/user/{userId}/connection` - 获取用户连接信息

### 通知管理
- **POST** `/wenshu/livechat/user/{userId}/notification` - 发送通知给指定用户
- **POST** `/wenshu/livechat/broadcast/notification` - 广播通知给所有用户

### 连接管理
- **POST** `/wenshu/livechat/user/{userId}/disconnect` - 强制断开用户连接

### 统计信息
- **GET** `/wenshu/livechat/statistics` - 获取连接统计信息

## 📦 项目结构

```
wenshu-livechat/
├── src/main/java/com/ruoyi/wenshulivechat/
│   ├── config/                         # 配置类
│   │   ├── WebSocketConfig.java        # WebSocket配置
│   │   ├── CorsConfig.java             # 跨域配置
│   │   └── LiveChatProperties.java     # 配置属性
│   ├── controller/                     # 控制器
│   │   ├── LiveChatController.java     # WebSocket消息控制器
│   │   └── LiveChatRestController.java # REST API控制器
│   ├── service/                        # 服务层
│   │   ├── LiveChatService.java        # 服务接口
│   │   └── impl/
│   │       └── LiveChatServiceImpl.java # 服务实现
│   ├── model/                          # 数据模型
│   │   ├── NewMessageRequest.java      # 新消息查询请求
│   │   └── NewMessageResponse.java     # 新消息查询响应
│   ├── listener/                  # 事件监听器
│   │   └── WebSocketEventListener.java # WebSocket事件监听
│   └── util/                      # 工具类
│       └── ConsoleLogger.java     # 控制台日志
├── src/main/resources/
│   ├── bootstrap.yml              # 配置文件
│   └── static/
│       └── test.html              # 测试页面
└── README.md                      # 项目文档
```

## 🔍 消息格式

### 聊天消息格式
```json
{
    "messageId": "uuid",
    "senderId": 1001,
    "receiverId": 1002,
    "content": "消息内容",
    "contentType": "text",
    "sendTime": "2024-06-26T10:30:00",
    "status": "sent"
}
```

### 用户状态格式
```json
{
    "userId": 1001,
    "userName": "用户名",
    "status": "online",
    "connectTime": "2024-06-26T10:00:00",
    "lastActiveTime": "2024-06-26T10:30:00"
}
```

### 输入状态格式
```json
{
    "userId": 1001,
    "targetUserId": 1002,
    "isTyping": true,
    "timestamp": "2024-06-26T10:30:00"
}
```

## 🛠️ 开发说明

1. **统一接口路径**: 所有controller层接口都以 `/wenshu/livechat` 开头
2. **用户特定路径**: WebSocket使用用户特定路径，如 `/recv/{userId}` 和 `/send/{userId}`
3. **消息类型**: 支持文本、图片、视频、文件等多种消息类型
4. **状态管理**: 实时管理用户在线状态和连接信息
5. **错误处理**: 完善的异常处理和错误响应机制

## 📞 技术支持

- **测试页面**: `http://localhost:1020/test.html`
- **服务状态**: `http://localhost:1020/wenshu/livechat/status`
- **端口**: 1020
- **协议**: WebSocket (STOMP) + HTTP REST
