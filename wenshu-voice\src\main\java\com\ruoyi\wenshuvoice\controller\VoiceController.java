package com.ruoyi.wenshuvoice.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ruoyi.wenshuapi.client.chat.ChatFeignClientInter;
import com.ruoyi.wenshuapi.client.programme.WenshuCalebdarFeignClientInter;
import com.ruoyi.wenshuapi.common.ApiResponse;
import com.ruoyi.wenshuapi.pojo.chat.RequestBodyDTO;
import com.ruoyi.wenshucommon.entity.Programme;
import com.ruoyi.wenshucommon.util.voiceutil.*;
import com.ruoyi.wenshuvoice.config.RestResultL;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api/voice")
public class VoiceController {
    private static final int DELETE_DELAY = 500; // 延迟删除文件时间(毫秒)

    @Autowired
    private ChatFeignClientInter chatFeignClientInter;
    @Autowired
    private WenshuCalebdarFeignClientInter wenshuCalebdarFeignClientInter;

    @Value("${file.upload-dir:./uploads}")
    private String uploadDir;

    private final RestTemplate restTemplate; // 用于HTTP请求
    private final ObjectMapper objectMapper; // 用于JSON序列化/反序列化

    /**
     * 日期格式测试接口
     * @param a 起始日期字符串 (格式: yyyy-MM-dd HH:mm:ss)
     * @param b 结束日期字符串 (格式: yyyy-MM-dd HH:mm:ss)
     * @return 格式化后的日期数组
     */
    @GetMapping("/test")
    public RestResultL<String[]> test(String a, String b) {
        System.out.println("========== 测试接口调用开始 ==========");
        System.out.println("接收参数: a=" + a + ", b=" + b);

        try {
            // 定义输入格式（匹配：yyyy-MM-dd HH:mm:ss）
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            // 解析参数
            LocalDateTime dateA = LocalDateTime.parse(a, inputFormatter);
            LocalDateTime dateB = LocalDateTime.parse(b, inputFormatter);
            System.out.println("解析成功: dateA=" + dateA + ", dateB=" + dateB);

            // 定义输出格式（保持相同格式）
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            // 格式化结果
            String[] formattedDates = {
                    dateA.format(outputFormatter),
                    dateB.format(outputFormatter)
            };

            System.out.println("格式化结果: [" + formattedDates[0] + ", " + formattedDates[1] + "]");
            System.out.println("========== 测试接口成功返回 ==========");
            return RestResultL.success("日期格式化成功", formattedDates);
        } catch (DateTimeParseException e) {
            System.err.println("日期解析错误: " + e.getMessage());
            System.out.println("========== 测试接口错误返回 ==========");
            return RestResultL.error(500, "日期格式错误，请使用：yyyy-MM-dd HH:mm:ss");
        }
    }

    /**
     * 语音识别接口 - 处理音频上传并转换为文本
     * @param file 上传的音频文件
     * @return 处理结果（原始文本和去重后文本）
     */
    @PostMapping(value = "/SpeechRecognition", consumes = "multipart/form-data")
    public RestResultL<String[]> handleAudioUpload( @RequestPart("file") MultipartFile file) {
        System.out.println("\n========== 语音识别接口调用开始 ==========");
        System.out.println("收到音频文件: " + file.getOriginalFilename() + " (" + file.getSize() + " bytes)");

        AudioFileProcessor.ProcessedAudioFile processedFile = null;

        try {
            // 1. 处理文件上传和元数据提取
            processedFile = AudioFileProcessor.processUpload(file, uploadDir);
            Path filePath = processedFile.getFilePath();
            String audioFormat = processedFile.getAudioFormat();
            System.out.println("文件处理完成: 存储路径=" + filePath + ", 音频格式=" + audioFormat);

            // 2. 音频转文本处理
            FileToVoice fileToVoice = new FileToVoice();
            String[] processingResult = fileToVoice.consFilePath(filePath.toAbsolutePath(), audioFormat);
            System.out.println("音频转文本结果: 原始文本=" + processingResult[0] + ", 识别置信度=" + processingResult[1]);

            // 3. 文本去重处理
            String originalText = processingResult[0];
            String deduplicatedText = TextDeDuplication.processResult(processingResult[0]);
            processingResult[0] = deduplicatedText;
            System.out.println("文本去重结果: 原始长度=" + originalText.length() +
                    ", 去重后长度=" + deduplicatedText.length() +
                    ", 内容=" + deduplicatedText);

            System.out.println("========== 语音识别接口处理成功 ==========");
            return RestResultL.success("音频处理成功", processingResult);

        } catch (Exception e) {
            System.err.println("语音识别处理失败: " + e.getMessage());
            e.printStackTrace();

            // 根据异常类型提供更具体的错误信息
            String errorMessage = getDetailedErrorMessage(e);
            System.out.println("========== 语音识别接口错误返回 ==========");
            return RestResultL.error(500, errorMessage);
        } finally {
            // 4. 延迟删除文件
            if (processedFile != null) {
                System.out.println("计划删除临时文件: " + processedFile.getFilePath());
                FileCleaner.scheduleDeletion(processedFile.getFilePath(), DELETE_DELAY);
            }
        }
    }

    /**
     * 语音交互接口 - 处理音频并执行AI交互
     * @param file 上传的音频文件
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return AI交互结果
     */
    @PostMapping(value = "/VoiceInteraction", consumes = "multipart/form-data")
    public RestResultL<Map<String, Object>> VoiceInteraction(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "conversationId") String conversationId,
            @RequestParam(value = "userId") int userId) {

        System.out.println("\n========== 语音交互接口调用开始 ==========");
        System.out.println("参数: conversationId=" + conversationId + ", userId=" + userId);
        System.out.println("收到音频文件: " + file.getOriginalFilename() + " (" + file.getSize() + " bytes)");

        AudioFileProcessor.ProcessedAudioFile processedFile = null;

        try {
            // 1. 处理文件上传和元数据提取
            processedFile = AudioFileProcessor.processUpload(file, uploadDir);
            Path filePath = processedFile.getFilePath();
            String audioFormat = processedFile.getAudioFormat();
            System.out.println("文件处理完成: 存储路径=" + filePath + ", 音频格式=" + audioFormat);

            // 2. 音频转文本处理
            FileToVoice fileToVoice = new FileToVoice();
            String[] processingResult = fileToVoice.consFilePath(filePath.toAbsolutePath(), audioFormat);
            System.out.println("音频转文本结果: 原始文本=" + processingResult[0] + ", 识别置信度=" + processingResult[1]);

            // 3. 文本去重处理
            String originalText = processingResult[0];
            String deduplicatedText = TextDeDuplication.processResult(processingResult[0]);
            processingResult[0] = deduplicatedText;
            System.out.println("文本去重结果: 原始长度=" + originalText.length() +
                    ", 去重后长度=" + deduplicatedText.length() +
                    ", 内容=" + deduplicatedText);

            // 4. AI交互处理
            System.out.println("开始AI交互处理...");
            Map<String, Object> result = processAIInteraction(deduplicatedText, conversationId, userId);

            System.out.println("AI交互处理完成，返回结果: " + result);
            result.put("userSpeak",processingResult[0]);
            System.out.println("========== 语音交互接口处理成功 ==========");
            return RestResultL.success("语音交互处理成功", result);

        } catch (Exception e) {
            System.err.println("语音交互处理失败: " + e.getMessage());
            e.printStackTrace();

            // 根据异常类型提供更具体的错误信息
            String errorMessage = getDetailedErrorMessage(e);
            System.out.println("========== 语音交互接口错误返回 ==========");
            return RestResultL.error(500, errorMessage);
        } finally {
            // 5. 延迟删除文件
            if (processedFile != null) {
                System.out.println("计划删除临时文件: " + processedFile.getFilePath());
                FileCleaner.scheduleDeletion(processedFile.getFilePath(), DELETE_DELAY);
            }
        }
    }

    /**
     * AI交互处理核心逻辑
     * @param userInput 用户输入文本
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 包含AI响应的结果映射
     */
    private Map<String, Object> processAIInteraction(String userInput, String conversationId, int userId) throws Exception {
        System.out.println("\n----- AI交互处理开始 -----");
        System.out.println("用户输入: " + userInput);
        System.out.println("会话ID: " + conversationId);
        System.out.println("用户ID: " + userId);

        Map<String, Object> result = new HashMap<>();

        // 1. 首次AI交互（模式判断）
        String modePrompt = VoiceAgents.agentSwitchPrompt + userInput;
        System.out.println("[模式判断] 发送提示: " + modePrompt);
        String modeResponse = aiSend(modePrompt, String.valueOf(userId));
        System.out.println("[模式判断] AI响应: " + modeResponse);

        // 2. 根据模式类型进行不同处理
        String promptPref = VoiceAgents.ifSwitch(modeResponse);
        String modeType = modeResponse;
        System.out.println("识别模式: " + modeType + ", 提示前缀: " + promptPref);

        if ("日程创建".equals(modeType)) {
            System.out.println("处理模式: 日程创建");

            // 2.1 发送日程创建请求
            String createPrompt = promptPref + userInput;
            System.out.println("[日程创建] 发送提示: " + createPrompt);
            String jsonResponse = aiSend(createPrompt, String.valueOf(userId));
            System.out.println("[日程创建] AI响应(JSON): " + jsonResponse);

            // 2.2 发送聊天请求
            String chatPrompt = VoiceAgents.agentChatPrompt + "【用户内容开始】" + userInput + "【用户内容结束】";
            System.out.println("[聊天响应] 发送提示: " + chatPrompt);
            String chatResponse = aiSendMemory(chatPrompt, conversationId, String.valueOf(userId));
            System.out.println("[聊天响应] AI响应: " + chatResponse);

            // 2.3 解析JSON响应
            ObjectMapper mapper = new ObjectMapper();
            Object jsonObj = mapper.readValue(jsonResponse, Object.class);

            // 2.4 构建结果
            result.put("jsonResponse", jsonObj);
            result.put("chatResponse", chatResponse);

            result.put("mode", "create");

        } else if ("日程查询".equals(modeType)) {
            System.out.println("处理模式: 日程查询");

            // 2.1 发送日程查询请求
            String queryPrompt = promptPref + userInput;
            System.out.println("[日程查询] 发送提示: " + queryPrompt);
            String jsonResponse = aiSend(queryPrompt, String.valueOf(userId));
            System.out.println("[日程查询] AI响应(JSON): " + jsonResponse);

            // 2.2 解析时间范围
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(jsonResponse);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            LocalDateTime startTime = LocalDateTime.parse(rootNode.get("startTime").asText(), formatter);
            LocalDateTime endTime = LocalDateTime.parse(rootNode.get("endTime").asText(), formatter);
            System.out.println("解析时间范围: start=" + startTime.format(formatter) +
                    ", end=" + endTime.format(formatter));

            // 2.3 查询日程
            System.out.println("查询用户日程: userId=" + userId);
            List<Programme> programmes = getProgrammesByTimeRange(startTime, endTime, userId);
            System.out.println("查询结果: " + (programmes != null ? programmes.size() + "条记录" : "空"));

            // 2.4 构建聊天提示
            String chatPrompt = VoiceAgents.agentChatPrompt + "【用户内容开始】" + userInput + "【用户内容结束】";
            if (programmes != null && !programmes.isEmpty()) {
                chatPrompt += "\n查到内容：" + programmes.toString();
                System.out.println("[聊天响应] 添加查询结果到提示");
            } else {
                chatPrompt += "\n没有查到内容";
                System.out.println("[聊天响应] 添加无结果提示");
            }

            // 2.5 发送聊天请求
            System.out.println("[聊天响应] 发送提示: " + chatPrompt);
            String chatResponse = aiSendMemory(chatPrompt, conversationId, String.valueOf(userId));
            System.out.println("[聊天响应] AI响应: " + chatResponse);

            // 2.6 构建结果
            result.put("jsonResponse", programmes);
            result.put("chatResponse", chatResponse);

            result.put("mode", "query");

        } else {
            System.out.println("处理模式: 普通聊天");

            // 2.1 发送聊天请求
            String chatPrompt = promptPref + "【用户内容开始】" + userInput + "【用户内容结束】";
            System.out.println("[普通聊天] 发送提示: " + chatPrompt);
            String chatResponse = aiSendMemory(chatPrompt, conversationId, String.valueOf(userId));
            System.out.println("[普通聊天] AI响应: " + chatResponse);

            // 2.2 构建结果
            result.put("chatResponse", chatResponse);
            result.put("mode", "chat");
        }

        System.out.println("----- AI交互处理完成 -----\n");
        return result;
    }
//sda
    /**
     * 发送AI请求（无记忆）
     * @param prompt 提示文本
     * @return AI响应内容
     */
    private String aiSend(String prompt,String userid) {
        System.out.println("[AI请求] 发送提示(无记忆): " + prompt);

        // 创建RequestBodyDTO对象
        RequestBodyDTO requestBodyDTO = new RequestBodyDTO(prompt, userid);
        String response = chatFeignClientInter.sendMessageEasy(requestBodyDTO);

        return response;
    }

    /**
     * 发送AI请求（带记忆）
     * @param prompt 提示文本
     * @param conversationId 会话ID
     * @return AI响应内容
     */
    private String aiSendMemory(String prompt, String conversationId,String userid) {
        System.out.println("[AI请求] 发送提示(带记忆): " + prompt);
        System.out.println("[AI请求] 会话ID: " + conversationId);

        // 创建RequestBodyDTO对象，包含消息、会话ID和用户ID
        RequestBodyDTO requestBodyDTO = new RequestBodyDTO(prompt, conversationId, userid);
        ResponseEntity<String> response = chatFeignClientInter.sendMessage(requestBodyDTO);
        System.out.println("[AI响应] 状态码: " + response.getStatusCode());
        return response.getBody();
    }

    private List<Programme> getProgrammesByTimeRange(LocalDateTime startTime, LocalDateTime endTime, int userId) {
        System.out.println("\n----- 日程查询开始 -----");
        System.out.println("查询参数: userId=" + userId +
                ", start=" + startTime +
                ", end=" + endTime);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        try {
            ApiResponse<?> response = wenshuCalebdarFeignClientInter.listProgramme(
                    (long) userId,
                    startTime.format(formatter),
                    endTime.format(formatter)
            );

            System.out.println("收到响应: 状态码=" + response.getCode());

            if (response.getCode() == 200 && response.getData() != null) {
                Object data = response.getData();
                System.out.println("响应数据类型: " + data.getClass().getSimpleName());

                if (data instanceof List) {
                    // 创建支持Java 8日期时间的ObjectMapper
                    ObjectMapper mapperWithJavaTime = new ObjectMapper();
                    mapperWithJavaTime.registerModule(new JavaTimeModule());

                    List<Programme> programmes = mapperWithJavaTime.convertValue(
                            data,
                            new TypeReference<List<Programme>>() {}
                    );
                    System.out.println("解析到日程数量: " + programmes.size());
                    System.out.println("----- 日程查询成功 -----\n");
                    return programmes;
                }
            } else {
                System.out.println("API返回错误码: " + response.getCode());
            }
        } catch (Exception e) {
            System.err.println("日程查询失败: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("----- 日程查询失败 -----\n");
        return Collections.emptyList();
    }

    /**
     * 根据异常类型提供详细的错误信息
     * @param e 异常对象
     * @return 详细的错误信息
     */
    private String getDetailedErrorMessage(Exception e) {
        String message = e.getMessage();

        // 检查是否是音频格式相关错误
        if (message != null) {
            if (message.contains("only single channel stream is allowed")) {
                return "音频格式错误：仅支持单声道音频，请转换音频格式后重试";
            }
            if (message.contains("inputSampleRate") && message.contains("not equals real")) {
                return "音频采样率不匹配：系统已自动检测并调整采样率，请重试";
            }
            if (message.contains("resample audio") && message.contains("failed")) {
                return "音频重采样失败：系统已优化音频处理流程，请重试";
            }
            if (message.contains("ssf_mp3_decoder_decode") || message.contains("mp3_decoder")) {
                return "MP3解码失败：系统已优化为WAV格式处理，请重试";
            }
            if (message.contains("Failed to decode audio")) {
                return "音频解码失败：音频文件可能损坏或格式不支持，请检查音频文件";
            }
            if (message.contains("AUDIO_DECODE_ERROR")) {
                return "音频解码错误：音频格式不符合要求，建议使用WAV格式";
            }
            if (message.contains("State invalid")) {
                return "语音识别服务状态异常：请稍后重试";
            }
            if (message.contains("network error")) {
                return "网络连接错误：请检查网络连接后重试";
            }
            if (message.contains("API key")) {
                return "API密钥错误：语音识别服务配置异常";
            }
            if (message.contains("file size") || message.contains("文件大小")) {
                return "文件大小超限：音频文件过大，请压缩后重试";
            }
            if (message.contains("timeout")) {
                return "处理超时：音频文件过长或网络延迟，请尝试分段处理";
            }
        }

        // 检查异常类型
        if (e instanceof java.util.concurrent.ExecutionException) {
            Throwable cause = e.getCause();
            if (cause != null) {
                return getDetailedErrorMessage(new Exception(cause.getMessage(), cause));
            }
        }

        if (e instanceof java.io.FileNotFoundException) {
            return "文件未找到：音频文件可能已被删除或路径错误";
        }

        if (e instanceof java.io.IOException) {
            return "文件读写错误：音频文件可能损坏或权限不足";
        }

        if (e instanceof IllegalArgumentException) {
            return "参数错误：" + (message != null ? message : "请检查音频文件格式");
        }

        // 默认错误信息
        return "音频处理失败：" + (message != null ? message : "未知错误，请联系技术支持");
    }
}