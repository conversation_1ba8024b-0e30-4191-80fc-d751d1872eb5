services:
  gateway:
    build:
      context: .
      dockerfile: ruoyi-gateway/Dockerfile
      args:
        JAR_FILE: ruoyi-gateway/target/ruoyi-gateway-*.jar
        SERVER_PORT: 8080
        SERVICE_NAME: gateway
    container_name: wenshu-gateway
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: nacos:8848
      JAVA_OPTS: "-Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    ports:
      - "8080:8080"
    networks:
      - wenshu_wenshu-network
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined

  auth:
    build:
      context: .
      dockerfile: ruoyi-auth/Dockerfile
      args:
        JAR_FILE: ruoyi-auth/target/ruoyi-auth-*.jar
        SERVER_PORT: 9200
        SERVICE_NAME: auth
    container_name: wenshu-auth
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: nacos:8848
      SPRING_DATASOURCE_URL: ****************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: 2313147023
      SPRING_DATA_REDIS_HOST: redis
      SPRING_DATA_REDIS_PORT: 6379
      JAVA_OPTS: "-Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    volumes:
      - ./logs/ruoyi-auth:/app/logs
    networks:
      - wenshu_wenshu-network
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined

  system:
    <<: *common-service
    build:
      context: .
      dockerfile: ruoyi-modules/ruoyi-system/Dockerfile
      args:
        JAR_FILE: ruoyi-modules/ruoyi-system/target/ruoyi-system-*.jar
        SERVER_PORT: 9201
        SERVICE_NAME: system
    container_name: wenshu-system
    environment:
      <<: *redis-environment
    volumes:
      - ./logs/ruoyi-system:/app/logs
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  file:
    <<: *common-service
    build:
      context: .
      dockerfile: ruoyi-modules/ruoyi-file/Dockerfile
    container_name: ruoyi-file
    privileged: true
    environment:
      <<: *redis-environment
    volumes:
      - ./logs/ruoyi-file:/app/logs
      - ./uploadPath:/app/uploadPath
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.3'
        reservations:
          memory: 128M
          cpus: '0.15'

  job:
    build:
      context: .
      dockerfile: ruoyi-modules/ruoyi-job/Dockerfile
    container_name: wenshu-job
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: nacos:8848
      SPRING_DATASOURCE_URL: ****************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: 2313147023
      SPRING_DATA_REDIS_HOST: redis
      SPRING_DATA_REDIS_PORT: 6379
      JAVA_OPTS: "-Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    volumes:
      - ./logs/ruoyi-job:/app/logs
    networks:
      - wenshu_wenshu-network
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined

  monitor:
    build:
      context: .
      dockerfile: ruoyi-visual/ruoyi-monitor/Dockerfile
    container_name: wenshu-monitor
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: nacos:8848
      JAVA_OPTS: "-Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    ports:
      - "9100:9100"
    networks:
      - wenshu_wenshu-network
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined

  gen:
    build:
      context: .
      dockerfile: ruoyi-modules/ruoyi-gen/Dockerfile
    container_name: wenshu-gen
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: nacos:8848
      SPRING_DATASOURCE_URL: ****************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: 2313147023
      SPRING_DATA_REDIS_HOST: redis
      SPRING_DATA_REDIS_PORT: 6379
      JAVA_OPTS: "-Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    volumes:
      - ./logs/ruoyi-gen:/app/logs
    networks:
      - wenshu_wenshu-network
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined

  chat:
    build:
      context: .
      dockerfile: wenshu-chat/Dockerfile
    container_name: wenshu-chat
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: nacos:8848
      SPRING_DATASOURCE_URL: ****************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: 2313147023
      SPRING_DATA_REDIS_HOST: redis
      SPRING_DATA_REDIS_PORT: 6379
      SPRING_AI_DASHSCOPE_API-KEY: sk-574f46304e5c4405aa5bbe26af6489b0
      JAVA_OPTS: "-Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    volumes:
      - ./logs/wenshu-chat:/app/logs
    ports:
      - "8701:8701"
    networks:
      - wenshu_wenshu-network
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined

  voice:
    build:
      context: .
      dockerfile: wenshu-voice/Dockerfile
    container_name: wenshu-voice
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: nacos:8848
      SPRING_DATASOURCE_URL: ****************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: 2313147023
      SPRING_AI_DASHSCOPE_API-KEY: sk-a4b80017093447aab1688acad39d24b6
      JAVA_OPTS: "-Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    volumes:
      - ./logs/wenshu-voice:/app/logs
    ports:
      - "1014:1014"
    networks:
      - wenshu_wenshu-network
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined

  meeting:
    build:
      context: .
      dockerfile: wenshu-meeting/Dockerfile
    container_name: wenshu-meeting
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: nacos:8848
      SPRING_DATASOURCE_URL: ****************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: 2313147023
      SPRING_DATA_REDIS_HOST: redis
      SPRING_DATA_REDIS_PORT: 6379
      JAVA_OPTS: "-Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    volumes:
      - ./logs/wenshu-meeting:/app/logs
      - ./temp:/app/temp
      - ./uploads:/app/uploads
      - /dev/shm:/dev/shm
    ports:
      - "1021:1021"
    networks:
      - wenshu_wenshu-network
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined

  remind:
    build:
      context: .
      dockerfile: wenshu-remind/Dockerfile
    container_name: wenshu-remind
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: nacos:8848
      SPRING_DATASOURCE_URL: ****************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: 2313147023
      JAVA_OPTS: "-Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    volumes:
      - ./logs/wenshu-remind:/app/logs
    ports:
      - "1015:1015"
    networks:
      - wenshu_wenshu-network
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined


  multimodal:
    build:
      context: .
      dockerfile: wenshu-multimodal/Dockerfile
    container_name: wenshu-multimodal
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: nacos:8848
      SPRING_DATASOURCE_URL: ****************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: 2313147023
      JAVA_OPTS: "-Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    volumes:
      - ./logs/wenshu-multimodal:/app/logs
    ports:
      - "8702:8702"
    networks:
      - wenshu_wenshu-network
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined

  wenshu-base:
    build:
      context: .
      dockerfile: wenshu-data/wenshu-base/Dockerfile
    container_name: wenshu-base
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: nacos:8848
      SPRING_DATASOURCE_URL: ****************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: 2313147023
      JAVA_OPTS: "-Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    ports:
      - "8601:8601"
    networks:
      - wenshu_wenshu-network
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined

  wenshu-team:
    build:
      context: .
      dockerfile: wenshu-data/wenshu-team/Dockerfile
    container_name: wenshu-team
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: nacos:8848
      SPRING_DATASOURCE_URL: ****************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: 2313147023
      JAVA_OPTS: "-Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    ports:
      - "1017:1017"
    networks:
      - wenshu_wenshu-network
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined

  wenshu-calebdar:
    build:
      context: .
      dockerfile: wenshu-data/wenshu-calebdar/Dockerfile
    container_name: wenshu-calebdar
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: nacos:8848
      SPRING_DATASOURCE_URL: ****************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: 2313147023
      JAVA_OPTS: "-Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    ports:
      - "8705:8705"
    networks:
      - wenshu_wenshu-network
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined

  programme-manage:
    build:
      context: .
      dockerfile: programme-manage/Dockerfile
    container_name: programme-manage
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: nacos:8848
      SPRING_DATASOURCE_URL: ****************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: 2313147023
      JAVA_OPTS: "-Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    ports:
      - "8605:8605"
    networks:
      - wenshu_wenshu-network
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined

  wenshu-api:
    build:
      context: .
      dockerfile: wenshu-api/Dockerfile
    container_name: wenshu-api
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: nacos:8848
      SPRING_DATASOURCE_URL: ****************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: 2313147023
      JAVA_OPTS: "-Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    ports:
      - "8704:8704"
    networks:
      - wenshu_wenshu-network
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined

  wenshu-audit:
    build:
      context: .
      dockerfile: wenshu-data/wenshu-audit/Dockerfile
    container_name: wenshu-audit
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: nacos:8848
      SPRING_DATASOURCE_URL: ****************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: 2313147023
      JAVA_OPTS: "-Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    volumes:
      - ./logs/wenshu-audit:/app/logs
    ports:
      - "8603:8603"
    networks:
      - wenshu_wenshu-network
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined

  wenshu-file:
    build:
      context: .
      dockerfile: wenshu-data/wenshu-file/Dockerfile
    container_name: wenshu-file
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: nacos:8848
      SPRING_DATASOURCE_URL: ****************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: 2313147023
      JAVA_OPTS: "-Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    volumes:
      - ./logs/wenshu-file:/app/logs
      - ./uploadPath:/app/uploadPath
    ports:
      - "1016:1016"
    networks:
      - wenshu_wenshu-network
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined



networks:
  wenshu_wenshu-network:
    external: true
