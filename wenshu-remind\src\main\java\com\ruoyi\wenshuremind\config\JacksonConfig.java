package com.ruoyi.wenshuremind.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Jackson配置类
 * 全局配置ObjectMapper以正确处理LocalDateTime等Java 8时间类型
 */
@Configuration
public class JacksonConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(JacksonConfig.class);

    /**
     * 配置全局ObjectMapper
     * 解决LocalDateTime序列化问题
     */
    @Bean
    @Primary
    public ObjectMapper objectMappers() {
        logger.info("🔧 配置全局ObjectMapper...");
        
        ObjectMapper mapper = new ObjectMapper();
        
        // 注册JavaTimeModule以支持LocalDateTime等Java 8时间类型
        mapper.registerModule(new JavaTimeModule());
        
        // 禁用将日期写为时间戳的功能，使用ISO-8601格式
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        
        // 配置其他序列化选项
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        
        logger.info("✅ 全局ObjectMapper配置完成:");
        logger.info("  - 已注册JavaTimeModule");
        logger.info("  - 禁用WRITE_DATES_AS_TIMESTAMPS");
        logger.info("  - 使用ISO-8601日期格式");
        logger.info("  - 允许空Bean序列化");
        
        return mapper;
    }
}
