package com.ruoyi.wenshuteam.dao;

import com.ruoyi.wenshuapi.pojo.team.TeamUserRelation;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 团队-成员关系数据访问层
 * 提供对wenshu_team_member表的CRUD操作
 */
@Mapper
public interface TeamMemberDao {

    /**
     * 插入新的团队成员关系
     *
     * @param relation 团队-用户关系对象
     * @return 插入操作影响的行数
     *
     * SQL说明:
     *   INSERT INTO wenshu_team_member: 向指定表插入数据
     *   (team_id, user_id): 指定插入的字段
     *   VALUES (#{teamId}, #{userId}): 使用POJO对象的属性值
     */
    @Insert("INSERT INTO wenshu_team_member (team_id, user_id) " +
            "VALUES (#{teamId}, #{userId})")
    int insert(TeamUserRelation relation);

    /**
     * 根据团队ID和用户ID删除成员关系
     *
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 删除操作影响的行数
     *
     * SQL说明:
     *   DELETE FROM wenshu_team_member: 从指定表删除数据
     *   WHERE team_id = #{teamId} AND user_id = #{userId}: 精确匹配条件
     */
    @Delete("DELETE FROM wenshu_team_member " +
            "WHERE team_id = #{teamId} AND user_id = #{userId}")
    int deleteByTeamAndUser(@Param("teamId") int teamId, @Param("userId") int userId);

    /**
     * 根据团队ID删除所有成员关系
     *
     * @param teamId 要删除的团队ID
     * @return 删除操作影响的行数
     *
     * SQL说明:
     *   WHERE team_id = #{teamId}: 删除指定团队的所有成员记录
     */
    @Delete("DELETE FROM wenshu_team_member WHERE team_id = #{teamId}")
    int deleteByTeamId(@Param("teamId") int teamId);

    /**
     * 根据用户ID删除所有团队关系
     *
     * @param userId 要删除的用户ID
     * @return 删除操作影响的行数
     *
     * SQL说明:
     *   WHERE user_id = #{userId}: 删除指定用户的所有团队关联
     */
    @Delete("DELETE FROM wenshu_team_member WHERE user_id = #{userId}")
    int deleteByUserId(@Param("userId") int userId);

    /**
     * 根据团队ID查询所有成员ID
     *
     * @param teamId 要查询的团队ID
     * @return 用户ID列表
     *
     * SQL说明:
     *   SELECT user_id: 查询用户ID字段
     *   FROM wenshu_team_member: 从指定表查询
     *   WHERE team_id = #{teamId}: 按团队ID过滤
     */
    @Select("SELECT user_id FROM wenshu_team_member " +
            "WHERE team_id = #{teamId}")
    List<Integer> selectUserIdsByTeamId(@Param("teamId") int teamId);

    /**
     * 根据用户ID查询所有团队ID
     *
     * @param userId 要查询的用户ID
     * @return 团队ID列表
     *
     * SQL说明:
     *   SELECT team_id: 查询团队ID字段
     *   WHERE user_id = #{userId}: 按用户ID过滤
     */
    @Select("SELECT team_id FROM wenshu_team_member " +
            "WHERE user_id = #{userId}")
    List<Integer> selectTeamIdsByUserId(@Param("userId") int userId);

    /**
     * 检查指定团队-用户关系是否存在
     *
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 存在返回1，不存在返回0
     *
     * SQL说明:
     *   SELECT COUNT(*): 统计匹配的记录数
     *   WHERE team_id = #{teamId} AND user_id = #{userId}: 精确匹配条件
     */
    @Select("SELECT COUNT(*) FROM wenshu_team_member " +
            "WHERE team_id = #{teamId} AND user_id = #{userId}")
    int existsRelation(@Param("teamId") int teamId, @Param("userId") int userId);

    /**
     * 更新成员所属团队
     *
     * @param oldTeamId 原团队ID
     * @param newTeamId 新团队ID
     * @param userId 用户ID
     * @return 更新操作影响的行数
     *
     * SQL说明:
     *   UPDATE wenshu_team_member: 更新指定表
     *   SET team_id = #{newTeamId}: 设置新的团队ID
     *   WHERE team_id = #{oldTeamId} AND user_id = #{userId}: 更新条件
     */
    @Update("UPDATE wenshu_team_member " +
            "SET team_id = #{newTeamId} " +
            "WHERE team_id = #{oldTeamId} AND user_id = #{userId}")
    int updateUserTeam(@Param("oldTeamId") int oldTeamId,
                       @Param("newTeamId") int newTeamId,
                       @Param("userId") int userId);

    /**
     * 统计团队成员数量
     *
     * @param teamId 团队ID
     * @return 团队成员数量
     *
     * SQL说明:
     *   SELECT COUNT(*): 统计记录数
     *   WHERE team_id = #{teamId}: 按团队ID分组统计
     */
    @Select("SELECT COUNT(*) FROM wenshu_team_member " +
            "WHERE team_id = #{teamId}")
    int countMembersByTeamId(@Param("teamId") int teamId);

    /**
     * 统计用户加入的团队数量
     *
     * @param userId 用户ID
     * @return 用户加入的团队数量
     *
     * SQL说明:
     *   WHERE user_id = #{userId}: 按用户ID分组统计
     */
    @Select("SELECT COUNT(*) FROM wenshu_team_member " +
            "WHERE user_id = #{userId}")
    int countTeamsByUserId(@Param("userId") int userId);
}