package com.ruoyi.wenshulivechat;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * wenshu-livechat 实时聊天服务启动类
 *
 * 功能特性：
 * - WebSocket实时聊天
 * - 用户在线状态管理
 * - 消息推送和路由
 * - 联动wenshu-wchat微服务
 */
@SpringBootApplication
@EnableFeignClients(basePackages = {
    "com.ruoyi.wenshuapi.client"  // 扫描wenshu-api客户端接口
})
@ComponentScan(basePackages = {
    "com.ruoyi.wenshulivechat",   // 扫描本模块组件
    "com.ruoyi.wenshuapi.fallback", // 扫描wenshu-api降级处理类
    "com.ruoyi.wenshuapi.config"  // 扫描wenshu-api配置类
})
@EnableScheduling  // 启用定时任务
public class WenshuLivechatApplication {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static void printStartupBanner() {
        System.out.println();
        System.out.println("██╗    ██╗███████╗███╗   ██╗███████╗██╗  ██╗██╗   ██╗");
        System.out.println("██║    ██║██╔════╝████╗  ██║██╔════╝██║  ██║██║   ██║");
        System.out.println("██║ █╗ ██║█████╗  ██╔██╗ ██║███████╗███████║██║   ██║");
        System.out.println("██║███╗██║██╔══╝  ██║╚██╗██║╚════██║██╔══██║██║   ██║");
        System.out.println("╚███╔███╔╝███████╗██║ ╚████║███████║██║  ██║╚██████╔╝");
        System.out.println(" ╚══╝╚══╝ ╚══════╝╚═╝  ╚═══╝╚══════╝╚═╝  ╚═╝ ╚═════╝ ");
        System.out.println();
        System.out.println("WENSHU-LIVECHAT 实时聊天服务");
        System.out.println("启动时间: " + LocalDateTime.now().format(FORMATTER));
        System.out.println("服务端口: 1020");
        System.out.println("WebSocket: /ws | /ws-native");
        System.out.println();
    }

    public static void main(String[] args) {
        printStartupBanner();

        try {
            System.setProperty("spring.cloud.bootstrap.location", "classpath:bootstrap.yml");
            System.setProperty("spring.config.location", "classpath:bootstrap.yml");
            SpringApplication.run(WenshuLivechatApplication.class, args);
        } catch (Exception e) {
            System.err.println("服务启动失败: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }

}
