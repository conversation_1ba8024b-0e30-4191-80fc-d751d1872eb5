# wenshu-wchat 模块配置
server:
  port: 8083
  tomcat:
    max-http-form-post-size: 10GB
    max-swallow-size: 10GB
  max-http-request-header-size: 8KB

spring:
  application:
    name: wenshu-wchat
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************
    username: root
    password: root
  servlet:
    multipart:
      max-file-size: 10GB
      max-request-size: 10GB
      enabled: true
      file-size-threshold: 2KB
      location: ${java.io.tmpdir}

# 聊天文件配置
chat:
  file:
    # 文件存储根路径
    path: D:/wenshu/chat-files
    # 文件访问域名
    domain: http://localhost:8083
    # 文件访问前缀
    prefix: /chat-files

# MyBatis配置
mybatis:
  mapper-locations: classpath*:mapper/*.xml
  type-aliases-package: com.ruoyi.wenshuapi.pojo.wchat
  configuration:
    map-underscore-to-camel-case: true

# 数据初始化配置
wchat:
  data:
    init:
      # 是否启用数据初始化（生产环境建议设为false）
      enabled: true
      # 好友关系数量
      friend-count: 30
      # 聊天记录数量
      chat-count: 70
      # 虚拟用户ID范围
      min-user-id: 1001
      max-user-id: 1020

# 日志配置
logging:
  level:
    com.ruoyi.wenshuwchat: debug
    org.springframework.web: debug
