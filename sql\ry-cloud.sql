/*
 Navicat Premium Data Transfer

 Source Server         : ************_3306
 Source Server Type    : MySQL
 Source Server Version : 80035
 Source Host           : ************:3306
 Source Schema         : ry-cloud

 Target Server Type    : MySQL
 Target Server Version : 80035
 File Encoding         : 65001

 Date: 18/07/2025 19:48:48
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for base_conversation
-- ----------------------------
DROP TABLE IF EXISTS `base_conversation`;
CREATE TABLE `base_conversation`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `conversation_Id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `created_time` datetime(0) DEFAULT NULL,
  `updated_time` datetime(0) DEFAULT NULL,
  `user_id` int(0) DEFAULT NULL,
  PRIMARY KEY (`id`, `conversation_Id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 171 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of base_conversation
-- ----------------------------
INSERT INTO `base_conversation` VALUES (1, 'c87ed8bd446342b0acbabae3fd97642f', '周米粒', '[{\"type\":\"USER\",\"content\":\"你好，你以后就叫小米粒\"},{\"type\":\"USER\",\"content\":\"你好，你以后就叫小米粒\"},{\"type\":\"USER\",\"content\":\"你好，你以后就叫小米粒\"},{\"type\":\"USER\",\"content\":\"你好，你以后就叫小米粒\"},{\"type\":\"USER\",\"content\":\"你好，你以后就叫小米粒\"},{\"type\":\"USER\",\"content\":\"你好，你以后就叫小米粒\"},{\"type\":\"USER\",\"content\":\"你好，你以后就叫小米粒\"},{\"type\":\"USER\",\"content\":\"你好，你以后就叫小米粒\"},{\"type\":\"ASSISTANT\",\"content\":\"好的，我明白了！从现在开始，你可以叫我小米粒。有什么我可以帮你的吗？😊\"},{\"type\":\"USER\",\"content\":\"123\"},{\"type\":\"ASSISTANT\",\"content\":\"您好！看起来您可能输入了错误的内容或未提供具体指令。如果您需要进行日程管理，请告诉我具体的日程操作，例如创建、查询、更新或删除日程。请以自然语言的方式表达您的需求，我会帮您处理！\"},{\"type\":\"USER\",\"content\":\"123\"},{\"type\":\"ASSISTANT\",\"content\":\"您好！请输入与日程管理相关的指令，例如创建、查询、更新或删除日程，我将为您处理。如果需要帮助，请告诉我您的具体需求！\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (3, 'd3232a2d184d406faad187c0ff18b09c', 'New Chat', '[{\"type\":\"USER\",\"content\":\"%E8%AF%B7%E4%BD%9C%E4%B8%BA%E4%B8%80%E4%B8%AA%E6%99%BA%E8%83%BD%E6%97%A5%E7%A8%8B%E5%8A%A9%E6%89%8B%EF%BC%8C%E5%88%86%E6%9E%90%E4%BB%A5%E4%B8%8B%E7%94%A8%E6%88%B7%E6%B6%88%E6%81%AF%EF%BC%8C%E5%B9%B6%E6%8F%90%E5%8F%96%E6%97%A5%E7%A8%8B%E7%9B%B8%E5%85%B3%E4%BF%A1%E6%81%AF%E3%80%82%E5%B0%86%E7%BB%93%E6%9E%9C%E6%A0%BC%E5%BC%8F%E5%8C%96%E4%B8%BAJSON%EF%BC%8C%E5%8C%85%E5%90%AB%E4%BB%A5%E4%B8%8B%E5%AD%97%E6%AE%B5%EF%BC%9A%0A1.%20type:%20%E6%84%8F%E5%9B%BE%E7%B1%BB%E5%9E%8B%EF%BC%8C%E5%8F%AF%E9%80%89%E5%80%BC%E4%B8%BAADD(%E6%B7%BB%E5%8A%A0)%E3%80%81DELETE(%E5%88%A0%E9%99%A4)%E3%80%81UPDATE(%E4%BF%AE%E6%94%B9)%E3%80%81QUERY(%E6%9F%A5%E8%AF%A2)%0A2.%20title:%20%E6%97%A5%E7%A8%8B%E6%A0%87%E9%A2%98%0A3.%20location:%20%E5%9C%B0%E7%82%B9%0A4.%20date:%20%E6%97%A5%E6%9C%9F(yyyy-MM-dd%E6%A0%BC%E5%BC%8F)%0A5.%20time:%20%E6%97%B6%E9%97%B4(HH:mm%E6%A0%BC%E5%BC%8F)%0A%0A%E7%94%A8%E6%88%B7%E6%B6%88%E6%81%AF:%20%22%E6%88%91%E6%98%8E%E5%A4%A9%E4%B8%8B%E5%8D%885%E7%82%B9%E8%A6%81%E5%86%8D%E6%98%8E%E6%98%9F%E8%B7%AF%E5%BC%80%E4%BC%9A%22%0A%0AJSON%E5%9B%9E%E5%A4%8D:\"},{\"type\":\"ASSISTANT\",\"content\":\"根据用户的输入信息，我们可以解析出以下内容：\\n\\n1. **type**: 从用户的消息中可以推测这是一个 `QUERY` 类型的操作，因为用户只是提到一个日程相关的事件，并没有明确表示添加、删除或修改。\\n2. **title**: 用户提到“明天下午5点要再明星路开会”，可以提取标题为“开会”。\\n3. **location**: 地点是“明星路”。\\n4. **date**: 时间是“明天”，需要转换为具体的日期格式（假设今天是 2023-10-18，则明天为 2023-10-19）。\\n5. **time**: 具体时间为“下午5点”，即 17:00。\\n\\n以下是格式化后的 JSON 回复：\\n\\n```json\\n{\\n  \\\"type\\\": \\\"QUERY\\\",\\n  \\\"title\\\": \\\"开会\\\",\\n  \\\"location\\\": \\\"明星路\\\",\\n  \\\"date\\\": \\\"2023-10-19\\\",\\n  \\\"time\\\": \\\"17:00\\\"\\n}\\n```\\n\\n如果需要更精确的处理，例如动态计算日期，可以根据当前系统时间来生成具体的日期值。\"},{\"type\":\"USER\",\"content\":\"%E8%AF%B7%E4%BD%9C%E4%B8%BA%E4%B8%80%E4%B8%AA%E6%99%BA%E8%83%BD%E6%97%A5%E7%A8%8B%E5%8A%A9%E6%89%8B%EF%BC%8C%E5%88%86%E6%9E%90%E4%BB%A5%E4%B8%8B%E7%94%A8%E6%88%B7%E6%B6%88%E6%81%AF%EF%BC%8C%E5%B9%B6%E6%8F%90%E5%8F%96%E6%97%A5%E7%A8%8B%E7%9B%B8%E5%85%B3%E4%BF%A1%E6%81%AF%E3%80%82%E5%B0%86%E7%BB%93%E6%9E%9C%E6%A0%BC%E5%BC%8F%E5%8C%96%E4%B8%BAJSON%EF%BC%8C%E5%8C%85%E5%90%AB%E4%BB%A5%E4%B8%8B%E5%AD%97%E6%AE%B5%EF%BC%9A%0A1.%20type:%20%E6%84%8F%E5%9B%BE%E7%B1%BB%E5%9E%8B%EF%BC%8C%E5%8F%AF%E9%80%89%E5%80%BC%E4%B8%BAADD(%E6%B7%BB%E5%8A%A0)%E3%80%81DELETE(%E5%88%A0%E9%99%A4)%E3%80%81UPDATE(%E4%BF%AE%E6%94%B9)%E3%80%81QUERY(%E6%9F%A5%E8%AF%A2)%0A2.%20title:%20%E6%97%A5%E7%A8%8B%E6%A0%87%E9%A2%98%0A3.%20location:%20%E5%9C%B0%E7%82%B9%0A4.%20date:%20%E6%97%A5%E6%9C%9F(yyyy-MM-dd%E6%A0%BC%E5%BC%8F)%0A5.%20time:%20%E6%97%B6%E9%97%B4(HH:mm%E6%A0%BC%E5%BC%8F)%0A%0A%E7%94%A8%E6%88%B7%E6%B6%88%E6%81%AF:%20%22%E6%88%91%E6%98%8E%E5%A4%A9%E4%B8%8B%E5%8D%885%E7%82%B9%E8%A6%81%E5%86%8D%E6%98%8E%E6%98%9F%E8%B7%AF%E5%BC%80%E4%BC%9A%22%0A%0AJSON%E5%9B%9E%E5%A4%8D:\"},{\"type\":\"ASSISTANT\",\"content\":\"根据用户提供的消息“我明天下午5点要再明星路开会”，我们可以提取出以下信息：\\n\\n1. **type**: 这是一条新增的日程信息，因此类型为 `ADD`。\\n2. **title**: 从消息中可以推测标题为“开会”。\\n3. **location**: 地点是“明星路”。\\n4. **date**: 时间是“明天”，假设今天是 2023-10-18，则明天的日期为 2023-10-19。\\n5. **time**: 具体时间为“下午5点”，即 17:00。\\n\\n以下是格式化后的 JSON 回复：\\n\\n```json\\n{\\n  \\\"type\\\": \\\"ADD\\\",\\n  \\\"title\\\": \\\"开会\\\",\\n  \\\"location\\\": \\\"明星路\\\",\\n  \\\"date\\\": \\\"2023-10-19\\\",\\n  \\\"time\\\": \\\"17:00\\\"\\n}\\n```\\n\\n如果需要更动态的日期计算，可以根据当前系统时间自动生成具体的日期值。\"},{\"type\":\"USER\",\"content\":\"当前时间：2025-06-19 10:31:28\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】好，我想知道我下午的计划。【用户内容结束】\\n查到内容：[]\"},{\"type\":\"ASSISTANT\",\"content\":\"好的，让我帮您看看下午的计划。根据您的日程表，目前没有查到任何安排呢！😊 您可以考虑把下午的时间用来处理一些重要事项，或者提前规划一下需要做的事情。如果需要，我也可以帮您添加新的计划哦！您有什么特别想安排的吗？\"},{\"type\":\"USER\",\"content\":\"当前时间：2025-06-19 10:31:28\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】你好，我想知道我下午的计划。【用户内容结束】\\n查到内容：[]\"},{\"type\":\"ASSISTANT\",\"content\":\"你好！看起来您下午的计划目前还是空的呢 😊 您要不要考虑安排一些任务或者休息时间？如果需要的话，我可以帮您添加新的计划哦！比如开会、锻炼或者简单地放松一下都可以。您有什么想法吗？\"},{\"type\":\"USER\",\"content\":\"当前时间：2025-06-19 10:31:28\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】你好，我想知道我下午的计划。【用户内容结束】\\n查到内容：[]\"},{\"type\":\"ASSISTANT\",\"content\":\"你好！看了下您的日程，下午目前还没有任何安排呢！ 😊 这样空白的时间正好可以用来处理一些重要但不紧急的事情，或者提前规划一下接下来的工作。您需要我帮您添加个新计划吗？比如开会、外出拜访，还是简单地留点时间休息放松？\"},{\"type\":\"USER\",\"content\":\"当前时间：2025-06-19 10:36:57\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】好，我想知道我下午的计划。【用户内容结束】\\n查到内容：[Programme(eventId=2, title=开会, description=下个月下午5点开会，地点在明行喽, createTime=2025-06-17T10:29:59, startTime=2025-06-19T15:29:59, creatorId=1, ownerType=团队, repeatRule=一次性, remindTime=2025-06-17T20:03:18, eventStatus=1)]\"},{\"type\":\"ASSISTANT\",\"content\":\"好的，我帮您查了一下下午的计划。您有一个 **团队会议**，地点在“明行喽”（可能是“明行楼”或某个类似的地方，您可以再确认下），时间是下午 **5点** 开始。记得提前做好准备哦！ 😊 如果需要调整地点或者添加提醒，您可以告诉我，我来帮您修改。\"},{\"type\":\"USER\",\"content\":\"当前时间：2025-06-20 01:18:51\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】好，我想知道我下午的计划。【用户内容结束】\\n查到内容：[]\"},{\"type\":\"ASSISTANT\",\"content\":\"好的，让我帮您看看下午的计划……嗯，根据您的日程表，下午暂时没有安排任何计划呢！ 😊  \\n\\n如果您需要，我可以帮您添加一些任务或提醒。比如工作会议、客户拜访，或者给自己留点休息时间？您有什么打算吗？如果有需要，随时告诉我哦！\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (10, 'ea5c2a944460', '阿萨', '', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (11, 'defd03896d8d', '新对话', '', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (12, 'f09dcac5e3d4', '新对话', '', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (20, '38cd8eaa1849', '新对话', '', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (22, '5cf043da5049', '新对话', '', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (23, '89e45e2eb92d', '新对话', '', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (26, 'cd5c1f2e4344', '新对话', '', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (27, 'd06122ba4d80', '1', '', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (34, '84566ed3a094', '新对话', '[{\"type\":\"USER\",\"content\":\"当前时间：2025-06-19 21:36:55\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】1加1等于几？。【用户内容结束】\"},{\"type\":\"ASSISTANT\",\"content\":\"1加1等于2呀。这可是最基本的数学题呢。你怎么突然问这么简单的题目，是想考考我吗？还是说你在和小朋友一起学习啊？\"},{\"type\":\"USER\",\"content\":\"查询明天的日程安排\"},{\"type\":\"ASSISTANT\",\"content\":\"以下是明天的日程安排（2025-06-20）：\\n\\n1. **ID: 9**  \\n   - 标题: 1312  \\n   - 描述: 21312  \\n   - 时间: 03:23  \\n\\n2. **ID: 14**  \\n   - 标题: 2  \\n   - 描述: 2  \\n   - 时间: 00:00  \\n\\n3. **ID: 10**  \\n   - 标题: 21312  \\n   - 描述: 1231231  \\n   - 时间: 13:23  \\n\\n如果需要进一步操作（如修改或删除某日程），请告诉我对应的ID和具体需求！\"},{\"type\":\"USER\",\"content\":\"查询明天的日程安排\"},{\"type\":\"ASSISTANT\",\"content\":\"明天的日程安排如下：\\n\\n- **ID: 15**  \\n  标题: 312  \\n  描述: 12312  \\n  时间: 2025-06-19T03:03:00  \\n\\n- **ID: 2**  \\n  标题: 开会  \\n  描述: 下个月下午5点开会，地点在明行喽  \\n  时间: 2025-06-19T15:29:59  \\n\\n- **ID: 5**  \\n  标题: 开会  \\n  描述: 下个月下午5点开会，地点在明行喽  \\n  时间: 2025-06-19T15:58:38  \\n\\n- **ID: 6**  \\n  标题: 开会  \\n  描述: 下个月下午5点开会，地点在明行喽  \\n  时间: 2025-06-19T21:17:27  \\n\\n- **ID: 7**  \\n  标题: 2  \\n  描述: 2  \\n  时间: 2025-06-19T22:22:00  \\n\\n如果需要进一步的操作（如修改或删除某项日程），请告诉我！\"},{\"type\":\"USER\",\"content\":\"1\"},{\"type\":\"ASSISTANT\",\"content\":\"您好！请告诉我需要怎样的日程管理帮助？例如创建、查询、更新或删除日程。如果您想创建日程，可以提供时间、标题等信息哦！\"},{\"type\":\"USER\",\"content\":\"查询明天的日程安排\"},{\"type\":\"ASSISTANT\",\"content\":\"以下是明天的日程安排列表：\\n\\n1. **ID: 2**\\n   - 标题: 开会\\n   - 描述: 下个月下午5点开会，地点在明行喽\\n   - 时间: 2025-06-19T15:29:59\\n\\n2. **ID: 5**\\n   - 标题: 开会\\n   - 描述: 下个月下午5点开会，地点在明行喽\\n   - 时间: 2025-06-19T15:58:38\\n\\n3. **ID: 6**\\n   - 标题: 开会\\n   - 描述: 下个月下午5点开会，地点在明行喽\\n   - 时间: 2025-06-19T21:17:27\\n\\n4. **ID: 7**\\n   - 标题: 2\\n   - 描述: 2\\n   - 时间: 2025-06-19T22:22:00\\n\\n5. **ID: 15**\\n   - 标题: 312\\n   - 描述: 12312\\n   - 时间: 2025-06-19T03:03:00\\n\\n如果需要进一步操作（如修改或删除某项日程），请告诉我对应的ID和具体需求！\"},{\"type\":\"USER\",\"content\":\"请为我查询今天的日程\"},{\"type\":\"ASSISTANT\",\"content\":\"很抱歉，查询今天的日程时遇到了一个问题，无法解析具体的日期时间。为了帮助您更好地查询，请提供更明确的日期，例如“2023年11月8日的日程”或者“本周三的日程”。如果您需要，我也可以直接为您查询当前日期的日程，请确认！\"},{\"type\":\"USER\",\"content\":\"查询当日日程\"},{\"type\":\"ASSISTANT\",\"content\":\"以下是您查询的当日（2025-06-19）的日程安排：\\n\\n- **ID: 2**  \\n  标题: 开会  \\n  描述: 下个月下午5点开会，地点在明行喽  \\n  时间: 15:29:59  \\n\\n- **ID: 5**  \\n  标题: 开会  \\n  描述: 下个月下午5点开会，地点在明行喽  \\n  时间: 15:58:38  \\n\\n- **ID: 6**  \\n  标题: 开会  \\n  描述: 下个月下午5点开会，地点在明行喽  \\n  时间: 21:17:27  \\n\\n- **ID: 7**  \\n  标题: 2  \\n  描述: 2  \\n  时间: 22:22:00  \\n\\n- **ID: 15**  \\n  标题: 312  \\n  描述: 12312  \\n  时间: 03:03:00  \\n\\n如需进一步操作（如修改或删除某项日程），请告诉我对应的ID和具体需求！\"},{\"type\":\"USER\",\"content\":\"当前时间：2025-06-19 21:36:55\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】查询明天的日程。【用户内容结束】\\n查到内容：[Programme(eventId=9, title=1312, description=21312, createTime=2025-06-19T21:31:52, startTime=2025-06-20T03:23, creatorId=1, ownerType=个人, repeatRule=一次性, remindTime=2025-06-19T19:13, eventStatus=1), Programme(eventId=10, title=21312, description=1231231, createTime=2025-06-19T21:34:50, startTime=2025-06-20T13:23, creatorId=1, ownerType=个人, repeatRule=一次性, remindTime=2025-06-20T05:13, eventStatus=1), Programme(eventId=14, title=2, description=2, createTime=2025-06-19T21:35:43, startTime=2025-06-20T00:00, creatorId=1, ownerType=个人, repeatRule=一次性, remindTime=2025-06-19T15:50, eventStatus=1)]\"},{\"type\":\"ASSISTANT\",\"content\":\"明天的日程安排我已经帮您查到了，一共有三项：\\n\\n1. **第一项日程**：  \\n   - 标题：1312  \\n   - 描述：21312  \\n   - 时间：凌晨3:23  \\n   - 提醒时间：今晚7:13  \\n\\n2. **第二项日程**：  \\n   - 标题：21312  \\n   - 描述：1231231  \\n   - 时间：下午1:23  \\n   - 提醒时间：明天早上5:13  \\n\\n3. **第三项日程**：  \\n   - 标题：2  \\n   - 描述：2  \\n   - 时间：凌晨0:00  \\n   - 提醒时间：今天下午3:50  \\n\\n看起来您明天的日程还挺满的呢！如果需要调整或者添加新的日程，随时告诉我哦！ 😊\"},{\"type\":\"USER\",\"content\":\"我国第二十二条法律是什么\"},{\"type\":\"ASSISTANT\",\"content\":\"我国第二十二条法律需要具体看是哪一部法律中的第二十二条，因为不同的法律其条文内容不同。例如，《中华人民共和国宪法》第二十二条的内容与《中华人民共和国民法典》第二十二条的内容就不相同。\\n\\n以《中华人民共和国民法典》为例，其第二十二条规定：“不能完全辨认自己行为的成年人为限制民事行为能力人，实施民事法律行为由其法定代理人代理或者经其法定代理人同意、追认；但是，可以独立实施纯获利益的民事法律行为或者与其智力、精神健康状况相适应的民事法律行为。”\\n\\n如果您指的是其他具体的法律，请明确告知，以便提供更准确的信息。不过，这和日程管理没有关系，请告诉我您的日程管理需求！\"},{\"type\":\"USER\",\"content\":\"为我写一个计算机专业的短篇论文\"},{\"type\":\"ASSISTANT\",\"content\":\"您好！虽然我可以帮您构思和生成一篇计算机专业的短篇论文，但这与当前的日程管理主题无关。如果您需要撰写论文，可以另寻合适的机会讨论。不过，如果您希望将论文写作安排到日程中，我可以为您创建相关任务。\\n\\n请问您是否希望我帮您 **创建一个日程** 来提醒您完成论文写作？例如：\\n- 时间：明天上午10点\\n- 标题：撰写计算机专业短篇论文\\n\\n请告诉我您的具体需求！\"},{\"type\":\"USER\",\"content\":\"不用帮我添加日程,直接给我写\"},{\"type\":\"ASSISTANT\",\"content\":\"好的！请告诉我您需要的日程信息，包括时间、标题以及任何其他相关细节，我将直接为您写下日程内容。\\n\\n例如：  \\n**时间**：2023年10月5日 14:00  \\n**标题**：开会  \\n**详情**：与市场部讨论新季度计划  \\n\\n请提供具体信息，我随时可以为您整理！\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (35, 'a7daf781dc44', '新对话', '[{\"type\":\"USER\",\"content\":\"不用帮我添加日程,直接给我写\"},{\"type\":\"ASSISTANT\",\"content\":\"好的！请告诉我您需要的日程信息，包括时间、标题和任何其他相关细节，我将直接为您写出来。\\n\\n例如：  \\n**时间**：2023-11-15 14:00  \\n**标题**：开会  \\n**详情**：与市场部讨论下季度计划  \\n\\n请提供具体信息，我将按照您的要求整理！\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (36, '70eb2bc593dc', '新对话', '[{\"type\":\"USER\",\"content\":\"为我写一个计算机专业的短篇论文\"},{\"type\":\"ASSISTANT\",\"content\":\"您好！虽然我可以帮您构思和生成一篇计算机专业的短论文，但这与日程管理无关。如果您需要帮助撰写论文，请提供更详细的题材或方向，例如人工智能、网络安全、数据结构等，我将很乐意协助。\\n\\n不过，如果您希望先安排时间来撰写论文，我可以帮您创建一个日程。例如：\\n\\n**创建日程：**\\n- 时间：明天下午2点\\n- 标题：撰写计算机专业短篇论文\\n\\n请告诉我您的具体需求！如果需要立即开始撰写，请告诉我论文的主题和要求。😊\"},{\"type\":\"USER\",\"content\":\"当前时间：2025-06-19 21:36:55\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】为我写一个计算机专业的短篇论文。【用户内容结束】\"},{\"type\":\"ASSISTANT\",\"content\":\"好嘞！既然您想让我写一篇计算机专业的短篇论文，那咱们就轻松点聊，先定个有趣的方向。比如现在很火的人工智能（AI），或者区块链、大数据分析啥的。这些方向既前沿又实用。\\n\\n要不我们就以 **“人工智能在教育领域的应用”** 为题写一篇？这个主题不仅贴近生活，还能展示技术的实际价值。以下是一篇简短的论文草稿：\\n\\n---\\n\\n### 人工智能在教育领域的应用\\n\\n#### 引言  \\n随着科技的发展，人工智能（AI）逐渐成为推动社会进步的重要力量之一。特别是在教育领域，AI 的出现改变了传统教学模式，使个性化学习和高效资源分配成为可能。\\n\\n#### AI 在教育中的主要应用  \\n1. **个性化学习路径**  \\n   每个学生的学习能力和兴趣不同，而 AI 能够通过数据分析生成个性化的学习计划。例如，基于学生的表现记录，系统可以推荐适合其水平的练习题或课程内容。这种定制化方式显著提高了学习效率。  \\n\\n2. **智能导师系统**  \\n   智能导师是一种虚拟教师助手，它能够实时解答学生的疑问，并提供反馈。像 Duolingo 和 Coursera 这样的在线平台已经广泛使用了类似技术，让学生即使在没有真人老师的情况下也能获得高质量指导。  \\n\\n3. **自动化评估与批改**  \\n   AI 技术还可以帮助教师快速完成作业和考试的批改工作，节省大量时间。同时，AI 可以对学生的错误进行深入分析，从而发现普遍存在的问题并改进教学方法。  \\n\\n#### 面临的挑战  \\n尽管 AI 在教育中有诸多优势，但也面临一些问题：如隐私保护、算法偏见以及高昂的技术成本等。因此，在推广 AI 技术时，必须平衡技术创新和社会伦理之间的关系。  \\n\\n#### 结论  \\n人工智能正在深刻改变教育行业，让学习变得更加灵活和高效。未来，随着技术的不断进步，我们可以期待一个更加公平且富有创造力的教育环境。\\n\\n---\\n\\n这篇论文大概有300多字，您可以直接拿去用，也可以根据需要调整内容哦！如果觉得这个方向不太合适，我们还可以换其他主题，比如网络安全、云计算之类的。您看咋样？ 😊\"},{\"type\":\"USER\",\"content\":\"当前时间：2025-06-19 21:36:55\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】呃，我明天和后天都需要开会，时间在5点。\\n嗯，我创建一个日程.。\\n.。\\n嗯，我创建一个日程。【用户内容结束】\"},{\"type\":\"ASSISTANT\",\"content\":\"好的，听起来您明天和后天都有会议，时间都是下午5点对吧？那我帮您规划一下日程。\\n\\n### 日程安排如下：\\n1. **明天（6月20日）**  \\n   - 时间：17:00（下午5点）  \\n   - 内容：参加会议  \\n\\n2. **后天（6月21日）**  \\n   - 时间：17:00（下午5点）  \\n   - 内容：参加会议  \\n\\n#### 提醒：\\n- 建议您提前10-15分钟准备好相关材料，尤其是如果需要展示或讨论某些内容的话。\\n- 如果会议是线上进行，记得检查网络连接和设备状态哦！\\n\\n另外，如果您还需要设置闹钟提醒或者添加更多细节到日程里，随时告诉我就行啦！ 😊\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (37, '1d3e2d26678a', '新对话', '[{\"type\":\"USER\",\"content\":\"null【用户内容开始】【用户内容结束】\"},{\"type\":\"ASSISTANT\",\"content\":\"您好！看起来您的消息内容为空。如果您有任何问题或需要帮助，请随时告诉我，我会尽力提供支持！\"},{\"type\":\"USER\",\"content\":\"当前时间：2025-06-20 01:11:06\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】请为我创建一个日程。【用户内容结束】\"},{\"type\":\"ASSISTANT\",\"content\":\"好的！请告诉我具体需要创建什么样的日程呢？比如时间、地点、事项内容等，我可以根据您的需求来帮您安排。比如说：“2025年6月21日上午10点，在公司会议室开项目讨论会。” 这样我就能更清楚地为您设置了！ 😊\"},{\"type\":\"USER\",\"content\":\"12\"},{\"type\":\"ASSISTANT\",\"content\":\"您输入的“12”不太明确呢。如果您想管理日程，请告诉我具体的需求，比如创建、查询、更新或删除日程。例如，您可以告诉我“创建一个明天上午10点开会的日程”，或者“查看今天的所有日程”。我随时为您服务！\"},{\"type\":\"USER\",\"content\":\"你是谁\"},{\"type\":\"ASSISTANT\",\"content\":\"我是您的智能日程管理助手，可以帮助您进行日程的创建、查询、更新和删除等操作。有什么需要我帮忙的日程管理任务吗？\"},{\"type\":\"USER\",\"content\":\"请为我创建一个日程\"},{\"type\":\"ASSISTANT\",\"content\":\"好的，我需要更多详细信息才能为您创建日程，请提供以下内容：  \\n1. **日程标题**：这个日程是关于什么的？  \\n2. **时间**：具体日期和时间是什么时候？（例如：2023-10-15 14:00）  \\n3. （可选）**地点**：日程发生的地点在哪里？  \\n4. （可选）**备注**：是否需要添加额外的说明？\\n\\n请告诉我这些信息，我将立即为您安排！\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (39, 'fff2620a59e4', '新对话', '[{\"type\":\"USER\",\"content\":\"请帮我创建一个明天上午10点的项目会议日程，主题是季度业绩回顾\"},{\"type\":\"ASSISTANT\",\"content\":\"{\\n  \\\"title\\\": \\\"季度业绩回顾项目会议\\\",\\n  \\\"description\\\": \\\"明天上午10点召开关于季度业绩回顾的项目会议\\\",\\n  \\\"start_time\\\": \\\"明天上午10点\\\",\\n  \\\"owner_type\\\": \\\"团队\\\",\\n  \\\"repeat_rule\\\": \\\"一次性\\\",\\n  \\\"remind_time\\\": \\\"提前十分钟\\\",\\n  \\\"event_status\\\": \\\"1\\\"\\n}\"},{\"type\":\"USER\",\"content\":\"请帮我创建一个明天上午10点的项目会议日程，主题是季度业绩回顾\"},{\"type\":\"ASSISTANT\",\"content\":\"{\\n  \\\"title\\\": \\\"季度业绩回顾项目会议\\\",\\n  \\\"description\\\": \\\"明天上午10点召开关于季度业绩回顾的项目会议\\\",\\n  \\\"start_time\\\": \\\"明天上午10点\\\",\\n  \\\"owner_type\\\": \\\"团队\\\",\\n  \\\"repeat_rule\\\": \\\"一次性\\\",\\n  \\\"remind_time\\\": \\\"提前十分钟\\\",\\n  \\\"event_status\\\": \\\"1\\\"\\n}\"},{\"type\":\"USER\",\"content\":\"我刚刚问了你什么\"},{\"type\":\"ASSISTANT\",\"content\":\"你刚刚问我能不能帮你创建一个明天上午10点的项目会议日程，主题是季度业绩回顾。我已经为你创建好了这个日程。如果你还需要其他帮助，比如添加参会人员或者设定提醒等，随时告诉我哦！\"},{\"type\":\"USER\",\"content\":\"请帮我创建一个明天上午10点的项目会议日程，主题是季度业绩回顾\"},{\"type\":\"ASSISTANT\",\"content\":\"{\\n  \\\"title\\\": \\\"季度业绩回顾项目会议\\\",\\n  \\\"description\\\": \\\"明天上午10点召开关于季度业绩回顾的项目会议\\\",\\n  \\\"start_time\\\": \\\"明天上午10点\\\",\\n  \\\"owner_type\\\": \\\"团队\\\",\\n  \\\"repeat_rule\\\": \\\"一次性\\\",\\n  \\\"remind_time\\\": \\\"提前十分钟\\\",\\n  \\\"event_status\\\": \\\"1\\\"\\n}\"},{\"type\":\"USER\",\"content\":\"请帮我查询明天上午10以后的所有日程安排\"},{\"type\":\"ASSISTANT\",\"content\":\"{\\\"startTime\\\":\\\"2025-06-27 10:00:00\\\",\\\"endTime\\\":\\\"2025-06-27 23:59:59\\\"}\"}]', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (40, 'f35c41162545', '新对话', '', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (41, 'ceed7bf20b4c', '新对话', '[{\"type\":\"USER\",\"content\":\"当前时间：2025-06-20 08:31:47\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】一个日常。【用户内容结束】\"},{\"type\":\"ASSISTANT\",\"content\":\"好的呀！那咱们就聊聊日常吧～你今天过得怎么样？有没有什么有趣的事情想跟我分享？比如吃了什么好吃的，或者遇到了什么开心或烦恼的小事？\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (42, '72de78b143fd', '新对话', '[{\"type\":\"USER\",\"content\":\"帮我查询一下我的当日的日程\"},{\"type\":\"ASSISTANT\",\"content\":\"以下是您当日的日程安排列表：\\n\\n- **ID: 27**  \\n  标题: 1231  \\n  描述: 21231  \\n  时间: 2025-06-19T12:21:00  \\n\\n- **ID: 2**  \\n  标题: 开会  \\n  描述: 下个月下午5点开会，地点在明行喽  \\n  时间: 2025-06-19T15:29:59  \\n\\n- **ID: 5**  \\n  标题: 开会  \\n  描述: 下个月下午5点开会，地点在明行喽  \\n  时间: 2025-06-19T15:58:00  \\n\\n- **ID: 6**  \\n  标题: 开会  \\n  描述: 下个月下午5点开会，地点在明行喽  \\n  时间: 2025-06-19T21:17:27  \\n\\n- **ID: 7**  \\n  标题: 2  \\n  描述: 2  \\n  时间: 2025-06-19T22:22:00  \\n\\n- **ID: 17**  \\n  标题: 21321  \\n  描述: 12312  \\n  时间: 2025-06-19T23:48:00  \\n\\n如需进一步操作（如删除或修改日程），请告诉我具体的日程ID和您的需求！\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (43, 'a59ba572d08b', '新对话', '[{\"type\":\"USER\",\"content\":\"当前时间：2025-06-20 08:31:47\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】请允许我查询。\\n国庆假期也是2天。\\n国庆假期的这条。【用户内容结束】\\n查到内容：[]\"},{\"type\":\"ASSISTANT\",\"content\":\"你好呀！关于你的查询，我来给你解答一下。\\n\\n国庆假期通常是指10月1日至10月7日，是7天的长假哦！不过在某些情况下，调休或者特殊安排可能会让实际休息时间有所不同。你说的“2天”可能是某个具体年份或者单位的特殊安排？如果需要更准确的信息，可以告诉我具体是哪一年或哪个地区的安排，我再帮你确认一下！\\n\\n如果你还有其他问题，随时问我哈～ 😊\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (44, 'd32773650e75', '新对话', '[{\"type\":\"USER\",\"content\":\"null【用户内容开始】【用户内容结束】\"},{\"type\":\"ASSISTANT\",\"content\":\"您好！看起来您的消息内容为空。如果您有任何问题或需要帮助，请随时告诉我，我会尽力提供支持！\"},{\"type\":\"USER\",\"content\":\"当前时间：2025-06-20 08:31:47\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】帮我查询一下今天的日程。【用户内容结束】\\n查到内容：[Programme(eventId=9, title=1312, description=21312, createTime=2025-06-19T21:31:52, startTime=2025-06-20T03:23, creatorId=1, ownerType=团队, repeatRule=一次性, remindTime=2025-06-19T19:13, eventStatus=1), Programme(eventId=14, title=2, description=2, createTime=2025-06-19T21:35:43, startTime=2025-06-20T00:00, creatorId=1, ownerType=个人, repeatRule=一次性, remindTime=2025-06-19T22:25, eventStatus=1), Programme(eventId=36, title=123, description=2131, createTime=2025-06-20T00:54:59, startTime=2025-06-20T01:05, creatorId=1, ownerType=个人, repeatRule=一次性, remindTime=2025-06-20T00:55, eventStatus=1)]\"},{\"type\":\"ASSISTANT\",\"content\":\"好的，我帮您整理了一下今天的日程安排：\\n\\n1. **团队活动**：  \\n   - 时间：03:23 开始  \\n   - 标题：可能是“1312”（具体内容不太清晰，建议核实）  \\n\\n2. **个人任务**：  \\n   - 时间：00:00 开始  \\n   - 标题：可能是“2”（描述也比较简单，您可以再确认一下）  \\n\\n3. **个人任务**：  \\n   - 时间：01:05 开始  \\n   - 标题：可能是“123”（描述为“2131”，可能需要进一步明确）  \\n\\n看起来有些标题和描述比较简略，您可以根据需要补充更多信息哦！如果还有其他问题，随时告诉我呀～ 😊\"},{\"type\":\"USER\",\"content\":\"当前时间：2025-06-20 08:31:47\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】添加一个大人的日程，我今天下午有一个日程是在软件楼507进行答辩。【用户内容结束】\"},{\"type\":\"ASSISTANT\",\"content\":\"好的，明白了！我帮您添加一个日程：  \\n\\n- **标题**：答辩  \\n- **地点**：软件楼507  \\n- **时间**：今天下午（具体时间没提到，默认是整个下午，如果需要调整可以告诉我哦）  \\n\\n这个日程是您的个人日程吗？还是属于某个团队的？另外，需要设置提醒吗？比如提前30分钟通知您？如果有更多细节也可以补充哦！ 😊\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (45, '979c4d79bb5e', '新对话', '[{\"type\":\"USER\",\"content\":\"帮我撰写一篇springai的文章\"},{\"type\":\"ASSISTANT\",\"content\":\"您的请求是关于撰写一篇关于SpringAI的文章，但这似乎与日程管理无直接关联。不过，我很乐意帮助您构思一篇关于SpringAI的文章。\\n\\n但在此之前，请允许我确认：您是否希望将此任务安排进日程管理？例如，设定一个撰写文章的截止日期或提醒？\\n\\n如果不需要加入日程管理，以下是一个关于SpringAI文章的大纲示例：\\n\\n标题：《SpringAI：开启人工智能的新篇章》\\n\\n1. 引言\\n   - 介绍SpringAI的背景和发展历程。\\n   \\n2. 核心技术\\n   - 描述SpringAI所使用的核心技术及其创新点。\\n   \\n3. 应用场景\\n   - 探讨SpringAI在不同领域的应用案例和实际效果。\\n   \\n4. 未来展望\\n   - 分析SpringAI未来的发展方向及其可能带来的影响。\\n\\n5. 结论\\n   - 总结SpringAI的重要性及对社会的潜在贡献。\\n\\n请告诉我您对此大纲的看法，或者是否有特定的主题和角度需要包含在文章中。如果您想把写作任务放进日程，请提供具体的时间信息。\"},{\"type\":\"USER\",\"content\":\"不进行日程操作，帮我撰写一篇springai的文章\"},{\"type\":\"ASSISTANT\",\"content\":\"好的，以下是一篇关于SpringAI的文章：\\n\\n---\\n\\n### SpringAI：开启人工智能的春天\\n\\n随着技术的飞速发展，人工智能（AI）已经从一个遥远的概念逐渐走入我们的日常生活。而在这个领域中，有一颗新星正在冉冉升起——那就是SpringAI。\\n\\n#### 什么是SpringAI？\\n\\nSpringAI是一个专注于自然语言处理和智能对话系统的人工智能平台。它的目标是通过深度学习和大数据分析，为用户提供更加智能化、个性化的服务。无论是日常交流、任务管理还是复杂的数据分析，SpringAI都能以极高的准确率和效率完成。\\n\\n#### SpringAI的核心功能\\n\\n1. **智能问答**  \\n   SpringAI能够理解用户的自然语言输入，并提供精准的答案。无论是简单的查询还是复杂的推理问题，它都能轻松应对。\\n\\n2. **日程管理**  \\n   用户可以通过自然语言与SpringAI交互，完成日程的创建、查询、更新和删除等操作。这种无缝衔接的体验让用户无需再为繁琐的日程安排烦恼。\\n\\n3. **个性化推荐**  \\n   基于用户的行为数据，SpringAI可以为用户提供定制化的推荐服务，涵盖新闻、娱乐、学习等多个领域。\\n\\n4. **多语言支持**  \\n   SpringAI支持多种语言的识别和翻译，帮助用户跨越语言障碍，实现无障碍沟通。\\n\\n#### SpringAI的应用场景\\n\\n- **企业级应用**  \\n  在企业环境中，SpringAI可以帮助员工提高工作效率，例如自动整理会议纪要、生成报告等。此外，它还能为企业提供数据分析和市场预测等高级功能。\\n\\n- **个人助手**  \\n  对于普通用户来说，SpringAI可以成为一位贴心的个人助理。无论是提醒重要事项、规划旅行路线，还是解答生活中的各种问题，它都能胜任。\\n\\n#### 展望未来\\n\\nSpringAI不仅仅是一个工具，更是一种生活方式的变革者。在未来，它将继续拓展其功能边界，深入更多领域，为人类社会带来更大的价值。我们有理由相信，SpringAI将为人工智能的发展注入新的活力，开启属于它的春天。\\n\\n---\\n\\n希望这篇文章能满足您的需求！如果需要进一步调整或补充，请随时告诉我。\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (46, '7c7254670121', '新对话', '[{\"type\":\"USER\",\"content\":\"当前时间：2025-06-20 08:31:47\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】请给我一天。【用户内容结束】\"},{\"type\":\"ASSISTANT\",\"content\":\"好呀，你想好好利用这一天来做些什么呢？是打算休息放松，还是有具体的计划？如果需要，我可以帮你规划一下哦。\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (48, 'a1b30aa8386e', '新对话', '[{\"type\":\"USER\",\"content\":\"为我添加一个今天下午5点的答辩日程，地点再软件大楼5点7分\"},{\"type\":\"ASSISTANT\",\"content\":\"好的，我为您创建了一个日程：  \\n- **时间**：今天下午5点  \\n- **标题**：答辩  \\n- **地点**：软件大楼507  \\n\\n日程已成功添加！如果有其他需要，请随时告诉我。\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (49, 'ea6e3a8d0035', '新对话', '[{\"type\":\"USER\",\"content\":\"当前时间：2025-06-20 08:31:47\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】你是谁？。【用户内容结束】\"},{\"type\":\"ASSISTANT\",\"content\":\"我是你的智能助手，可以帮您处理各种办公相关的问题，比如撰写邮件、分析数据、安排日程等。有什么我可以帮到您的吗？如果您有任何问题或需要创作的内容，尽管告诉我哦！\"},{\"type\":\"USER\",\"content\":\"null【用户内容开始】【用户内容结束】\"},{\"type\":\"ASSISTANT\",\"content\":\"您好！看起来您还没有输入具体的问题或需求呢。既然我是一个专业的AI办公助手，那您可以告诉我需要处理什么办公相关的事情呀？比如起草文档、数据分析、日程管理等，我都非常乐意帮忙哦！ 😊\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (50, '9879c15b4327', '新对话', '[{\"type\":\"USER\",\"content\":\"当前时间：2025-06-20 08:31:47\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】查询一下今天日期。【用户内容结束】\\n查到内容：[]\"},{\"type\":\"ASSISTANT\",\"content\":\"今天是2025年6月20日，星期五哦！你需要查询今天的日期吗？还是有其他相关的需求呀？比如查看天气、节日提醒之类的，都可以告诉我哦！\"},{\"type\":\"USER\",\"content\":\"当前时间：2025-06-20 08:31:47\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】查询一下今天的日程安排。\\n查询一下今天的日常安排。【用户内容结束】\\n查到内容：[]\"},{\"type\":\"ASSISTANT\",\"content\":\"看起来你的日程安排还没有记录或者查询结果为空呢！不过没关系，我可以帮你一起规划一下今天的时间。你是不是想了解有没有重要的会议、任务或者活动需要处理呀？如果方便的话，可以告诉我你是用什么工具管理日程的，比如邮件、日历应用或者其他方式，这样我能更好地帮到你哦！\"},{\"type\":\"USER\",\"content\":\"为我创建一个明天下午五点的日程\"},{\"type\":\"ASSISTANT\",\"content\":\"好的，您的日程已创建成功：**明天下午五点的日程**。如果有更多需求，请随时告诉我！\"},{\"type\":\"USER\",\"content\":\"为我创建一个明天下午五点的日程\"},{\"type\":\"ASSISTANT\",\"content\":\"好的，您的日程已创建成功：  \\n**标题**：为我创建一个明天下午五点的日程  \\n**时间**：明天下午五点  \\n\\n如有其他需要，请随时告诉我！\"},{\"type\":\"USER\",\"content\":\"当前时间：2025-06-22 23:44:43\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】请为我创建明天下午5点的。\\n日程地点在办公楼。【用户内容结束】\"},{\"type\":\"ASSISTANT\",\"content\":\"好的，我来帮您创建日程。明天下午5点，地点是办公楼，但您还没有告诉我这个日程的具体内容是什么呢？比如是一个会议、洽谈还是其他安排？如果方便的话，请补充一下，这样我会帮您记录得更清楚！如果没有补充，我也会先帮您把时间地点记好哦！\"},{\"type\":\"USER\",\"content\":\"当前时间：2025-06-22 23:44:43\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】为我查询明天的日程。【用户内容结束】\\n查到内容：[]\"},{\"type\":\"ASSISTANT\",\"content\":\"我查了一下，明天的日程目前还没有任何安排哦！如果你需要，可以告诉我想要添加的内容，我可以帮你规划一下明天的时间。比如开会、约见客户或者个人事务都可以哦！\"},{\"type\":\"USER\",\"content\":\"当前时间：2025-06-22 23:44:43\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】为我查询后天为我查询后天的日程。【用户内容结束】\\n查到内容：[]\"},{\"type\":\"ASSISTANT\",\"content\":\"后天的日程目前还没有任何安排呢！如果你有重要的事情要处理，比如会议、约会或者需要提醒的事项，可以告诉我，我帮你提前规划一下哦！这样你就不用担心会遗漏重要事务啦！\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (51, 'f4116f70a27d', '新对话', '[{\"type\":\"USER\",\"content\":\"当前时间：2025-06-20 08:31:47\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】请为我查询当日的日请为我查询当日的日程。【用户内容结束】\\n查到内容：[Programme(eventId=9, title=1312, description=21312, createTime=2025-06-19T21:31:52, startTime=2025-06-20T03:23, creatorId=1, ownerType=团队, repeatRule=一次性, remindTime=2025-06-19T19:13, eventStatus=1), Programme(eventId=14, title=2, description=2, createTime=2025-06-19T21:35:43, startTime=2025-06-20T00:00, creatorId=1, ownerType=个人, repeatRule=一次性, remindTime=2025-06-19T22:25, eventStatus=1), Programme(eventId=36, title=123, description=2131, createTime=2025-06-20T00:54:59, startTime=2025-06-20T01:05, creatorId=1, ownerType=个人, repeatRule=一次性, remindTime=2025-06-20T00:55, eventStatus=1), Programme(eventId=40, title=222, description=222, createTime=2025-06-20T08:46:10, startTime=2025-06-20T05:57, creatorId=1, ownerType=个人, repeatRule=一次性, remindTime=2025-06-20T05:47, eventStatus=1), Programme(eventId=46, title=231, description=12, createTime=2025-06-20T09:12:25, startTime=2025-06-20T09:23, creatorId=1, ownerType=个人, repeatRule=一次性, remindTime=2025-06-20T09:13, eventStatus=1)]\"},{\"type\":\"ASSISTANT\",\"content\":\"好的，我来帮您梳理一下今天的日程安排：\\n\\n1. **凌晨 00:00** - 您有一个个人事项，标题为“2”，描述也是“2”。这是最早的一个安排。\\n\\n2. **凌晨 01:05** - 您有一个个人事项，标题为“123”，描述是“2131”。\\n\\n3. **凌晨 03:23** - 您有一个团队事项，标题为“1312”，描述是“21312”。\\n\\n4. **早上 05:57** - 您有一个个人事项，标题为“222”，描述也是“222”。\\n\\n5. **上午 09:23** - 您还有一个个人事项，标题为“231”，描述是“12”。\\n\\n这些都是今天的一次性任务，提醒时间也都已设置好了。如果您需要更详细的解释或者调整某一项内容，请告诉我哦！\"},{\"type\":\"USER\",\"content\":\"当前时间：2025-06-21 17:27:36\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】呃，帮我查询一下明天的日程。【用户内容结束】\\n查到内容：[Programme(eventId=34, title=321, description=123, createTime=2025-06-20T00:51:58, startTime=2025-06-22T21:12, creatorId=1, ownerType=个人, repeatRule=一次性, remindTime=2025-06-22T13:02, eventStatus=1)]\"},{\"type\":\"ASSISTANT\",\"content\":\"好的，帮您查了一下明天的日程，具体内容如下：\\n\\n**时间：2025年6月22日 晚上21:12**  \\n- **事项标题**：321  \\n- **事项描述**：123  \\n- **类型**：个人事项  \\n- **提醒时间**：2025年6月22日 下午13:02  \\n\\n这是一个一次性任务，记得准时完成哦！如果需要调整或添加更多细节，可以随时告诉我～\"},{\"type\":\"USER\",\"content\":\"当前时间：2025-06-21 17:27:36\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】呃，帮我介绍一下湖南科技职业学院。【用户内容结束】\"},{\"type\":\"ASSISTANT\",\"content\":\"湖南科技职业学院是一所位于中国湖南省的高等职业院校，致力于为学生提供高质量的职业教育和技能培训。该校注重实践与理论相结合，旨在培养适应社会需求的技术型人才。\\n\\n学校设有多个专业领域，包括但不限于信息技术、机械工程、经济管理、艺术设计等，涵盖了广泛的行业需求。此外，湖南科技职业学院还与众多企业建立了紧密的合作关系，为学生提供了丰富的实习和就业机会。\\n\\n如果你对某个具体专业或方面的信息感兴趣，可以告诉我，我会为你详细解答哦！\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (54, 'df0ae25af514', '新对话', '', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (55, '3453b3ac5bab', '新对话', '[{\"type\":\"USER\",\"content\":\"不用进行日程管理，帮我写一篇springai的文章\"},{\"type\":\"ASSISTANT\",\"content\":\"好的，虽然您当前不需要进行日程管理，但我很乐意帮您撰写一篇关于 **SpringAI** 的文章。以下是文章内容：\\n\\n---\\n\\n### 探索未来：SpringAI 的无限可能\\n\\n在当今快速发展的科技时代，人工智能（AI）已经成为推动社会进步的重要力量。作为这一领域的佼佼者，**SpringAI** 正以其独特的技术和创新理念，引领着新一轮的技术革命。\\n\\n#### 1. SpringAI 是什么？\\n\\nSpringAI 是一家专注于人工智能技术研发与应用的企业。它致力于通过先进的算法和强大的计算能力，为用户提供智能化的解决方案。无论是自然语言处理、图像识别，还是数据分析，SpringAI 都能提供高效且精准的服务。\\n\\n#### 2. 核心技术与优势\\n\\nSpringAI 的核心竞争力在于其对以下关键技术的掌握：\\n\\n- **自然语言理解（NLU）**：SpringAI 能够准确理解人类语言中的复杂语义，并根据上下文生成符合逻辑的回答。\\n- **深度学习模型**：借助大规模数据训练，SpringAI 的模型能够在多种场景中表现出色，如语音合成、情感分析等。\\n- **多模态融合**：将文本、图像、音频等多种信息形式结合起来，实现更全面的数据理解和应用。\\n\\n此外，SpringAI 还注重用户体验，确保其产品和服务既强大又易于使用，满足不同用户群体的需求。\\n\\n#### 3. 应用场景\\n\\nSpringAI 的技术已经被广泛应用于多个领域，包括但不限于：\\n\\n- **教育行业**：通过智能辅导系统帮助学生提高学习效率。\\n- **医疗健康**：利用 AI 分析患者的病历数据，辅助医生制定治疗方案。\\n- **企业服务**：为企业提供定制化的 AI 解决方案，优化运营流程并降低成本。\\n- **娱乐产业**：开发虚拟角色和交互式内容，丰富用户的娱乐体验。\\n\\n#### 4. 展望未来\\n\\n随着技术的不断进步，SpringAI 将继续探索新的可能性。从自动驾驶到智能家居，从金融风控到环境保护，SpringAI 希望用自己的力量改变世界，让每个人都能享受到科技进步带来的便利。\\n\\n---\\n\\n如果您有任何具体需求或想要调整文章的方向，请随时告诉我！同时，如果未来需要安排与 SpringAI 相关的日程（例如阅读这篇文章的时间），也可以随时向我发起请求哦！\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (56, '8cdeb10aa451', '新对话', '[{\"type\":\"USER\",\"content\":\"添加一个2025年6月29日的日程，日程内容是再软件大楼开会，下午五点\"},{\"type\":\"ASSISTANT\",\"content\":\"好的，我已经帮您创建了以下日程：  \\n**日程ID：1**  \\n**时间：2025年6月29日下午5点**  \\n**标题：在软件大楼开会**  \\n\\n如果需要修改或删除，请告诉我日程ID！\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (57, 'a4b2a73a34da', '新对话', '[{\"type\":\"USER\",\"content\":\"我今天有什么待做的任务哦\"},{\"type\":\"ASSISTANT\",\"content\":\"您今天的日程如下：（此处应列出用户今日的日程列表，如果有的话。）  \\n\\n如果您还没有创建任何日程，您可以告诉我需要添加的任务，我可以帮您创建！例如：“今天下午3点开会”或“明天早上9点健身”。\"},{\"type\":\"USER\",\"content\":\"你好呀\"},{\"type\":\"ASSISTANT\",\"content\":\"你好！有什么日程需要我帮忙管理吗？你可以告诉我创建、查询、更新或删除日程的具体需求哦。\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (58, 'd98f96f8e2a2', '新对话', '[{\"type\":\"USER\",\"content\":\"帮我构思一个新项目的创意点子\"},{\"type\":\"ASSISTANT\",\"content\":\"虽然您的问题与日程管理无关，但我很乐意帮您构思一个新项目的创意点子！\\n\\n**项目名称：智能时间银行**\\n\\n**核心理念：**  \\n创建一个平台，让用户可以将自己未使用的时间（例如空闲时段）存储到“时间银行”中。这些时间可以用于交换他人的服务、学习新技能、或捐赠给需要帮助的人（如老人陪护、社区志愿活动等）。通过这种方式，用户不仅能更高效地利用时间，还能建立更强的社会联系。\\n\\n**功能亮点：**  \\n1. **时间存取：** 用户可以记录自己的空闲时间，并将其转化为“时间货币”。  \\n2. **技能共享：** 提供一项技能（如编程教学、语言辅导）以换取其他人的服务（如健身指导、艺术课程）。  \\n3. **公益捐赠：** 将空闲时间捐赠给公益组织，由专业志愿者安排具体任务。  \\n4. **数据分析：** 平台会根据用户的日程安排和兴趣爱好，推荐最适合的时间交换机会。\\n\\n希望这个点子能激发您的灵感！如果需要进一步完善或有其他需求，请随时告诉我。\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (59, 'b22b77961885', '新对话', '[{\"type\":\"USER\",\"content\":\"你好\"},{\"type\":\"ASSISTANT\",\"content\":\"你好！需要我帮忙管理日程吗？你可以告诉我想要创建、查询、更新或删除的日程信息。\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (60, '1875d1a5-4637-492b-9be0-a9dacfe8418b', '新对话', '', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (61, '85c57ee11aa9', '新对话', '[{\"type\":\"USER\",\"content\":\"当前时间：2025-06-22 23:44:43\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】为我创建明天下午的。\\n为我创建明天下午的日程。【用户内容结束】\"},{\"type\":\"ASSISTANT\",\"content\":\"好的！明天是2025年6月23日，下午的日程，请问具体时间以及内容是什么？比如是开会、约会，还是其他安排？如果你不确定细节，我可以帮你设置一个通用的日程提醒！ 😊\"}]', NULL, NULL, NULL);
INSERT INTO `base_conversation` VALUES (62, '8798987620b4', '新对话', '[{\"type\":\"USER\",\"content\":\"当前时间：2025-06-22 23:44:43\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】请问我创建明天下午5点钟的日程，提前30分钟。【用户内容结束】\"},{\"type\":\"ASSISTANT\",\"content\":\"好的，您想创建一个明天下午5点钟的日程，并且希望提前30分钟收到提醒，是吗？那我帮您确认一下，日程时间是2025年6月23日下午5点，提醒时间是下午4点30分，对吧？需要我为您设置吗？顺便问一下，这个日程是什么内容呢？让我帮您备注得更清楚一些！\"},{\"type\":\"USER\",\"content\":\"请帮我创建一个明天上午10点的项目会议日程，主题是季度业绩回顾\"},{\"type\":\"ASSISTANT\",\"content\":\"{\\n  \\\"title\\\": \\\"季度业绩回顾会议\\\",\\n  \\\"description\\\": \\\"明天上午10点召开项目会议，主题是季度业绩回顾\\\",\\n  \\\"start_time\\\": \\\"2025-06-23T10:00:00\\\",\\n  \\\"owner_type\\\": \\\"个人\\\",\\n  \\\"repeat_rule\\\": \\\"一次性\\\",\\n  \\\"remind_time\\\": \\\"提前十分钟\\\",\\n  \\\"event_status\\\": \\\"1\\\"\\n}\"}]', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (120, '986746ba-8e00-4698-906b-47ade9fd261d', '新对话', '', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (121, 'c8336e1f-d5be-4aaf-bc8f-5ff70856e142', '新对话', '', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (122, '9e87e6e4-8eef-431e-b6a3-1b258d388101', '新对话', '', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (123, 'c9b39f7f-237d-401a-aa56-f68120b65340', '新对话', '', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (124, '3fb836d8-91e8-452f-9dca-f0ec8f8ba3df', '新对话', '', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (125, 'd8159334-3e1f-4f16-918c-d96667c132ec', '新对话', '', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (126, '0e214afc-cabe-4c46-bfee-64c4ac9e70b5', '周米粒', '', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (128, '527c372e-1baa-44a2-9355-3a75df02e2a0', '新对话', '', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (129, '3a494bdb-bc4d-4568-99ab-ac917c57b2ab', '周米粒', '', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (130, 'a1be434c-96d4-4237-9bf1-6351972bea5f', '新对话', '', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (131, 'b2986442-167e-4f0e-b683-504c656eda7e', '新对话', '', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (132, '2ce5de84-f8c4-4bc8-922d-6ee4facee421', '新对话', '', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (142, 'effa8265-4a99-48df-9f45-8ba910c74ed1', '新对话', '', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (143, '1f172800-56e1-4f84-9d59-ffe562051911', '新对话', '', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (144, 'aadd56a9-bfc9-462e-b2e8-092635f992e0', '新对话', '', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (145, 'd68c6772-97f5-498c-9aec-2f270558ebc3', '新对话', '', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (146, '984af5da-ca5b-4083-a1b3-834badbea5f7', '新对话', '', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (153, '95ad1419-9074-4b5c-9f3e-d5f75c3153ea', '1', '', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (154, '4549d04c-62f4-48a7-8293-85fc0452c794', '新对话', '[{\"type\":\"USER\",\"content\":\"你是谁\"},{\"type\":\"ASSISTANT\",\"content\":\"我是来自阿里云的超大规模语言模型，我叫通义千问。你可以把我想象成一个能和你聊天、提供信息和帮助的好朋友。有什么我可以帮到你的吗？\"},{\"type\":\"USER\",\"content\":\"前端语言中js与ts的区别\"},{\"type\":\"ASSISTANT\",\"content\":\"好的，咱们聊聊JS和TS的区别吧！\\n\\nJavaScript（简称JS）是一种广泛使用的脚型编程语言，主要用于网页开发。它可以直接在浏览器中运行，也可以通过Node.js等环境在服务器端运行。\\n\\nTypeScript（简称TS）则是JavaScript的一个超集，也就是说，所有的JavaScript代码都是有效的TypeScript代码。TypeScript在JavaScript的基础上添加了静态类型检查功能，这意味着你可以在编写代码时定义变量的类型，从而在编译阶段发现一些潜在的错误。\\n\\n简单来说，TypeScript提供了更多的类型安全性和更好的工具支持，特别适合大型项目和团队协作。而JavaScript则更加灵活，更适合快速开发和小型项目。\\n\\n你觉得这些区别对你有帮助吗？还有其他想了解的吗？\"}]', NULL, '2025-07-08 21:52:57', 1);
INSERT INTO `base_conversation` VALUES (155, '5246d277-9018-48e1-ad4d-b4a7dc8dd1cc', '新对话', '', NULL, NULL, 1);
INSERT INTO `base_conversation` VALUES (156, '3da14d36-7e89-4841-b772-b2fbff5a15e7', '新对话', '', NULL, NULL, 1);
INSERT INTO `base_conversation` VALUES (157, 'c1a18f0d-3abd-4444-96ea-7aee7e17ccef', '新对话', '', NULL, NULL, 1);
INSERT INTO `base_conversation` VALUES (158, '4778be86-558a-48d2-bc9d-5b8729f54c94', '新对话', '', NULL, NULL, 1);
INSERT INTO `base_conversation` VALUES (159, 'ec9dd60a-fcee-462d-bbf0-302c3de46f73', '新对话', '', NULL, NULL, 1);
INSERT INTO `base_conversation` VALUES (160, '227637f7-adf2-4c8c-ab40-038d9aefd238', '新对话', '', NULL, NULL, 1);
INSERT INTO `base_conversation` VALUES (161, '1a1c8cc9-694c-4105-b81a-c6aab95f1d3a', '新对话', '', NULL, NULL, 1);
INSERT INTO `base_conversation` VALUES (162, '0c792dbc-935d-469e-b070-add14b76e934', '新对话', '', NULL, NULL, 1);
INSERT INTO `base_conversation` VALUES (163, 'f3a3af6e-387a-43cd-8fa9-37d7d2aa8b8d', '新对话', '', NULL, NULL, 1);
INSERT INTO `base_conversation` VALUES (164, '8e22b712-c303-4c97-9405-ebab8a9d8f1b', '新对话', '', NULL, NULL, 1);
INSERT INTO `base_conversation` VALUES (165, '40c76196-6967-4fc8-9e70-ebc3573df333', '新对话', '', NULL, NULL, 1);
INSERT INTO `base_conversation` VALUES (166, 'd9687576-f47b-4160-b833-b6ffd3d5bb6f', '新对话', '[{\"type\":\"USER\",\"content\":\"帮我构思一个新项目的创意点子\"},{\"type\":\"ASSISTANT\",\"content\":\"当然可以！为了更好地帮助你构思，我需要了解一些额外的信息。比如，你对哪个领域感兴趣？是科技、教育、健康、娱乐还是其他什么领域？还有，你是更倾向于开发一个产品、服务还是一种新的商业模式呢？给我点线索吧！这样我能给你提供更加具体的建议哦。\"},{\"type\":\"USER\",\"content\":\"当前时间：2025-07-09 10:03:20\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】好，康老师，请问明天下午是否有时间来明德楼302教室开会？。\\n我们需要讨论一下项目之后的规划。\\nAnd.。\\n好的，我有空闲时间，我会好的，我有空闲时间，我会准时参与。【用户内容结束】\"},{\"type\":\"ASSISTANT\",\"content\":\"好的，明天下午我会准时到明德楼302教室参加开会。我们到时候详细讨论一下项目的后续规划。有什么特别需要准备的吗？\"},{\"type\":\"USER\",\"content\":\"当前时间：2025-07-09 10:03:20\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】你好，我想知道我下午的计划。【用户内容结束】\\n没有查到内容\"},{\"type\":\"ASSISTANT\",\"content\":\"你好！你下午的计划是什么？如果你有具体的安排或者需要我帮你查一下，可以告诉我更多的信息哦。\"},{\"type\":\"USER\",\"content\":\"当前时间：2025-07-09 10:57:15\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】hello world,这里是阿里巴巴语音实验室。【用户内容结束】\"},{\"type\":\"ASSISTANT\",\"content\":\"你好！这里是阿里巴巴语音实验室，有什么可以帮助你的吗？\"}]', NULL, '2025-07-09 10:57:28', 1);
INSERT INTO `base_conversation` VALUES (167, '8e369adc-942f-4d77-a2ed-9ad3f24a0494', '123', '[{\"type\":\"USER\",\"content\":\"a\"},{\"type\":\"ASSISTANT\",\"content\":\"嗨！看起来你可能只是发了一个字母\\\"a\\\"过来，是有什么特定的意思吗？还是不小心按错了？如果你有想聊的话题或者问题，随时告诉我哦！\"}]', NULL, NULL, 0);
INSERT INTO `base_conversation` VALUES (168, '04e0a3e1-16ec-48be-9b67-33dc5271d691', '新对话', '', NULL, NULL, 1);
INSERT INTO `base_conversation` VALUES (169, 'b56d8866-a567-4e3b-8739-eb822e326c2f', '新对话', '[{\"type\":\"USER\",\"content\":\"当前时间：2025-07-10 16:03:59\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】【用户内容结束】\"},{\"type\":\"ASSISTANT\",\"content\":\"看起来你的信息里没有具体要聊的内容呢，从【用户内容开始】到【用户内容结束】之间是空的。你有什么想聊的吗？或者有啥好玩的事情分享一下？\"}]', NULL, '2025-07-10 16:04:17', 1);
INSERT INTO `base_conversation` VALUES (170, '2778a515-9d77-46b7-b33f-9723608946bb', '新对话', '[{\"type\":\"USER\",\"content\":\"当前时间：2025-07-14 23:08:46\\n你会基于我以下的文本进行回复,满足基础聊天需求，以下文本为语音识别内容，请对错别字以及混乱字义进行联想输出要求：1.分析识别内容进行回复2.以聊天的方式进行回复3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可\\n以下是我的识别内容：\\n【用户内容开始】你好你好，您好。【用户内容结束】\"},{\"type\":\"ASSISTANT\",\"content\":\"你好呀！有什么可以帮到你的吗？\"},{\"type\":\"USER\",\"content\":\"9\"},{\"type\":\"ASSISTANT\",\"content\":\"嗨！你刚才说的“9”是有什么特别的意思吗？还是不小心按到数字了呢？\"}]', NULL, '2025-07-14 23:10:50', 1);

-- ----------------------------
-- Table structure for gen_table
-- ----------------------------
DROP TABLE IF EXISTS `gen_table`;
CREATE TABLE `gen_table`  (
  `table_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '表名称',
  `table_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '表描述',
  `sub_table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联子表的表名',
  `sub_table_fk_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '子表关联的外键名',
  `class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '实体类名称',
  `tpl_category` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作）',
  `tpl_web_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '前端模板类型（element-ui模版 element-plus模版）',
  `package_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生成包路径',
  `module_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生成模块名',
  `business_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生成业务名',
  `function_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生成功能名',
  `function_author` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生成功能作者',
  `gen_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
  `gen_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '/' COMMENT '生成路径（不填默认项目路径）',
  `options` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '其它生成选项',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`table_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成业务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of gen_table
-- ----------------------------
INSERT INTO `gen_table` VALUES (1, 'base_conversation', '', NULL, NULL, 'BaseConversation', 'crud', '', 'com.ruoyi.system', 'system', 'conversation', NULL, 'ruoyi', '0', '/', NULL, 'admin', '2025-07-08 22:26:13', '', NULL, NULL);

-- ----------------------------
-- Table structure for gen_table_column
-- ----------------------------
DROP TABLE IF EXISTS `gen_table_column`;
CREATE TABLE `gen_table_column`  (
  `column_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_id` bigint(0) DEFAULT NULL COMMENT '归属表编号',
  `column_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '列名称',
  `column_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '列描述',
  `column_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '列类型',
  `java_type` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'JAVA类型',
  `java_field` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'JAVA字段名',
  `is_pk` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否主键（1是）',
  `is_increment` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否自增（1是）',
  `is_required` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否必填（1是）',
  `is_insert` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否为插入字段（1是）',
  `is_edit` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否编辑字段（1是）',
  `is_list` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否列表字段（1是）',
  `is_query` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否查询字段（1是）',
  `query_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
  `html_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  `dict_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典类型',
  `sort` int(0) DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`column_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成业务表字段' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of gen_table_column
-- ----------------------------
INSERT INTO `gen_table_column` VALUES (1, 1, 'id', NULL, 'bigint', 'Long', 'id', '1', '1', NULL, '1', NULL, NULL, NULL, 'EQ', 'input', '', 1, 'admin', '2025-07-08 22:26:14', '', NULL);
INSERT INTO `gen_table_column` VALUES (2, 1, 'conversation_Id', NULL, 'varchar(255)', 'String', 'conversationId', '1', '0', NULL, '1', NULL, NULL, NULL, 'EQ', 'input', '', 2, 'admin', '2025-07-08 22:26:14', '', NULL);
INSERT INTO `gen_table_column` VALUES (3, 1, 'title', NULL, 'varchar(255)', 'String', 'title', '0', '0', NULL, '1', '1', '1', '1', 'EQ', 'input', '', 3, 'admin', '2025-07-08 22:26:14', '', NULL);
INSERT INTO `gen_table_column` VALUES (4, 1, 'content', NULL, 'longtext', 'String', 'content', '0', '0', NULL, '1', '1', '1', '1', 'EQ', 'editor', '', 4, 'admin', '2025-07-08 22:26:14', '', NULL);
INSERT INTO `gen_table_column` VALUES (5, 1, 'created_time', NULL, 'datetime', 'Date', 'createdTime', '0', '0', NULL, '1', '1', '1', '1', 'EQ', 'datetime', '', 5, 'admin', '2025-07-08 22:26:14', '', NULL);
INSERT INTO `gen_table_column` VALUES (6, 1, 'updated_time', NULL, 'datetime', 'Date', 'updatedTime', '0', '0', NULL, '1', '1', '1', '1', 'EQ', 'datetime', '', 6, 'admin', '2025-07-08 22:26:14', '', NULL);
INSERT INTO `gen_table_column` VALUES (7, 1, 'user_id', NULL, 'int', 'Long', 'userId', '0', '0', NULL, '1', '1', '1', '1', 'EQ', 'input', '', 7, 'admin', '2025-07-08 22:26:14', '', NULL);

-- ----------------------------
-- Table structure for onlyoffice_document_collaborators
-- ----------------------------
DROP TABLE IF EXISTS `onlyoffice_document_collaborators`;
CREATE TABLE `onlyoffice_document_collaborators`  (
  `id` int(0) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `document_id` int(0) NOT NULL COMMENT '文档ID',
  `user_id` int(0) NOT NULL COMMENT '用户ID',
  `user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名称',
  `connected_at` datetime(0) NOT NULL COMMENT '连接时间',
  `last_activity` datetime(0) NOT NULL COMMENT '最后活动时间',
  `connection_status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'active' COMMENT '连接状态',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_doc_user`(`document_id`, `user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'OnlyOffice文档协作者' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for onlyoffice_document_history
-- ----------------------------
DROP TABLE IF EXISTS `onlyoffice_document_history`;
CREATE TABLE `onlyoffice_document_history`  (
  `id` int(0) NOT NULL AUTO_INCREMENT COMMENT '历史记录ID',
  `document_id` int(0) NOT NULL COMMENT '文档ID',
  `user_id` int(0) NOT NULL COMMENT '用户ID',
  `user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名称',
  `action` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型',
  `version` int(0) NOT NULL DEFAULT 1 COMMENT '版本号',
  `created_at` datetime(0) NOT NULL COMMENT '创建时间',
  `changes_summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '变更摘要',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_document`(`document_id`) USING BTREE,
  INDEX `idx_user`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'OnlyOffice文档编辑历史' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for onlyoffice_document_permissions
-- ----------------------------
DROP TABLE IF EXISTS `onlyoffice_document_permissions`;
CREATE TABLE `onlyoffice_document_permissions`  (
  `id` int(0) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `document_id` int(0) NOT NULL COMMENT '文档ID',
  `user_id` int(0) NOT NULL COMMENT '用户ID',
  `permission_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '权限类型(view, edit, comment, review)',
  `created_at` datetime(0) NOT NULL COMMENT '创建时间',
  `created_by` int(0) NOT NULL COMMENT '创建者ID',
  `modified_at` datetime(0) NOT NULL COMMENT '修改时间',
  `modified_by` int(0) NOT NULL COMMENT '修改者ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_document_user`(`document_id`, `user_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'OnlyOffice文档权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for onlyoffice_documents
-- ----------------------------
DROP TABLE IF EXISTS `onlyoffice_documents`;
CREATE TABLE `onlyoffice_documents`  (
  `id` int(0) NOT NULL AUTO_INCREMENT COMMENT '文档ID',
  `doc_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文档唯一键',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件名(含扩展名)',
  `file_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件类型/扩展名',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文档标题',
  `creator_id` int(0) NOT NULL COMMENT '创建者ID',
  `creator_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者名称',
  `created_at` datetime(0) NOT NULL COMMENT '创建时间',
  `modified_at` datetime(0) NOT NULL COMMENT '最后修改时间',
  `file_size` bigint(0) NOT NULL DEFAULT 0 COMMENT '文件大小(字节)',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'active' COMMENT '状态(active,deleted)',
  `is_folder` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为文件夹',
  `document_server_status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'OnlyOffice文档服务器状态',
  `last_status_update` datetime(0) DEFAULT NULL COMMENT '最后状态更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_doc_key`(`doc_key`) USING BTREE,
  INDEX `idx_creator`(`creator_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'OnlyOffice文档表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of onlyoffice_documents
-- ----------------------------
INSERT INTO `onlyoffice_documents` VALUES (1, 'acd3f99dddbbb8a0b4441de8c2331c10', '测试文件', 'docx', '测试文件', 1, '测试人员', '2025-07-10 14:23:43', '2025-07-10 14:23:43', 0, 'active', 0, NULL, NULL);

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `config_id` int(0) NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '参数配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', '2024-08-18 22:19:55', '', NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO `sys_config` VALUES (2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', '2024-08-18 22:19:55', '', NULL, '初始化密码 123456');
INSERT INTO `sys_config` VALUES (3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', '2024-08-18 22:19:55', '', NULL, '深色主题theme-dark，浅色主题theme-light');
INSERT INTO `sys_config` VALUES (4, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', '2024-08-18 22:19:55', '', NULL, '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (5, '用户登录-黑名单列表', 'sys.login.blackIPList', '', 'Y', 'admin', '2024-08-18 22:19:55', '', NULL, '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `dept_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint(0) DEFAULT 0 COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '部门名称',
  `order_num` int(0) DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 112 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '部门表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO `sys_dept` VALUES (100, 0, '0', '智创未来', 0, '谢志华', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-08-18 22:19:55', 'admin', '2025-07-08 22:07:23');
INSERT INTO `sys_dept` VALUES (101, 100, '0,100', '湖南科技职业学院', 1, '蒋校长', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-08-18 22:19:55', 'admin', '2025-07-08 22:09:05');
INSERT INTO `sys_dept` VALUES (102, 100, '0,100', '长沙分公司', 2, '若依', '15888888888', '<EMAIL>', '0', '2', 'admin', '2024-08-18 22:19:55', '', NULL);
INSERT INTO `sys_dept` VALUES (103, 101, '0,100,101', '软件学院', 1, '江院', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-08-18 22:19:55', 'admin', '2025-07-08 22:09:28');
INSERT INTO `sys_dept` VALUES (104, 101, '0,100,101', '市场部门', 2, '若依', '15888888888', '<EMAIL>', '0', '2', 'admin', '2024-08-18 22:19:55', '', NULL);
INSERT INTO `sys_dept` VALUES (105, 101, '0,100,101', '人工智能学院', 3, '人工智能院长', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-08-18 22:19:55', 'admin', '2025-07-08 22:09:49');
INSERT INTO `sys_dept` VALUES (106, 101, '0,100,101', '财务部门', 4, '若依', '15888888888', '<EMAIL>', '0', '2', 'admin', '2024-08-18 22:19:55', '', NULL);
INSERT INTO `sys_dept` VALUES (107, 101, '0,100,101', '运维部门', 5, '若依', '15888888888', '<EMAIL>', '0', '2', 'admin', '2024-08-18 22:19:55', '', NULL);
INSERT INTO `sys_dept` VALUES (108, 102, '0,100,102', '市场部门', 1, '若依', '15888888888', '<EMAIL>', '0', '2', 'admin', '2024-08-18 22:19:55', '', NULL);
INSERT INTO `sys_dept` VALUES (109, 102, '0,100,102', '财务部门', 2, '若依', '15888888888', '<EMAIL>', '0', '2', 'admin', '2024-08-18 22:19:55', '', NULL);
INSERT INTO `sys_dept` VALUES (110, 100, '0,100', '长沙民政', 2, '校长', '13333333333', '<EMAIL>', '0', '0', 'admin', '2025-07-08 22:10:29', '', NULL);
INSERT INTO `sys_dept` VALUES (111, 110, '0,100,110', '软件学院', 1, '院长', '13333333331', '<EMAIL>', '0', '0', 'admin', '2025-07-08 22:10:56', '', NULL);

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `dict_code` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int(0) DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 30 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '性别男');
INSERT INTO `sys_dict_data` VALUES (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '性别女');
INSERT INTO `sys_dict_data` VALUES (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '性别未知');
INSERT INTO `sys_dict_data` VALUES (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '显示菜单');
INSERT INTO `sys_dict_data` VALUES (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '隐藏菜单');
INSERT INTO `sys_dict_data` VALUES (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '默认分组');
INSERT INTO `sys_dict_data` VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '系统分组');
INSERT INTO `sys_dict_data` VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '系统默认是');
INSERT INTO `sys_dict_data` VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '系统默认否');
INSERT INTO `sys_dict_data` VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '通知');
INSERT INTO `sys_dict_data` VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '公告');
INSERT INTO `sys_dict_data` VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '关闭状态');
INSERT INTO `sys_dict_data` VALUES (18, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '其他操作');
INSERT INTO `sys_dict_data` VALUES (19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '新增操作');
INSERT INTO `sys_dict_data` VALUES (20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '修改操作');
INSERT INTO `sys_dict_data` VALUES (21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '删除操作');
INSERT INTO `sys_dict_data` VALUES (22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '授权操作');
INSERT INTO `sys_dict_data` VALUES (23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '导出操作');
INSERT INTO `sys_dict_data` VALUES (24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '导入操作');
INSERT INTO `sys_dict_data` VALUES (25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '强退操作');
INSERT INTO `sys_dict_data` VALUES (26, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '生成操作');
INSERT INTO `sys_dict_data` VALUES (27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '清空操作');
INSERT INTO `sys_dict_data` VALUES (28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '停用状态');

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (1, '用户性别', 'sys_user_sex', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '用户性别列表');
INSERT INTO `sys_dict_type` VALUES (2, '菜单状态', 'sys_show_hide', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '菜单状态列表');
INSERT INTO `sys_dict_type` VALUES (3, '系统开关', 'sys_normal_disable', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '系统开关列表');
INSERT INTO `sys_dict_type` VALUES (4, '任务状态', 'sys_job_status', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '任务状态列表');
INSERT INTO `sys_dict_type` VALUES (5, '任务分组', 'sys_job_group', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '任务分组列表');
INSERT INTO `sys_dict_type` VALUES (6, '系统是否', 'sys_yes_no', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '系统是否列表');
INSERT INTO `sys_dict_type` VALUES (7, '通知类型', 'sys_notice_type', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '通知类型列表');
INSERT INTO `sys_dict_type` VALUES (8, '通知状态', 'sys_notice_status', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '通知状态列表');
INSERT INTO `sys_dict_type` VALUES (9, '操作类型', 'sys_oper_type', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '操作类型列表');
INSERT INTO `sys_dict_type` VALUES (10, '系统状态', 'sys_common_status', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '登录状态列表');

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`  (
  `job_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务调度表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_job
-- ----------------------------
INSERT INTO `sys_job` VALUES (1, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '0/10 * * * * ?', '3', '1', '1', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_job` VALUES (2, '系统默认（有参）', 'DEFAULT', 'ryTask.ryParams(\'ry\')', '0/15 * * * * ?', '3', '1', '1', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_job` VALUES (3, '系统默认（多参）', 'DEFAULT', 'ryTask.ryMultipleParams(\'ry\', true, 2000L, 316.50D, 100)', '0/20 * * * * ?', '3', '1', '1', 'admin', '2024-08-18 22:19:55', '', NULL, '');

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`  (
  `job_log_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '日志信息',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '异常信息',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务调度日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor`  (
  `info_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '登录IP地址',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '提示信息',
  `access_time` datetime(0) DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`) USING BTREE,
  INDEX `idx_sys_logininfor_s`(`status`) USING BTREE,
  INDEX `idx_sys_logininfor_lt`(`access_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 259 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统访问记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_logininfor
-- ----------------------------
INSERT INTO `sys_logininfor` VALUES (100, 'admin', '127.0.0.1', '0', '退出成功', '2024-08-18 22:28:58');
INSERT INTO `sys_logininfor` VALUES (101, 'admin', '127.0.0.1', '0', '登录成功', '2024-08-18 22:29:03');
INSERT INTO `sys_logininfor` VALUES (102, 'admin', '127.0.0.1', '0', '登录成功', '2024-08-19 15:18:57');
INSERT INTO `sys_logininfor` VALUES (103, 'admin', '127.0.0.1', '0', '登录成功', '2024-08-19 16:01:56');
INSERT INTO `sys_logininfor` VALUES (104, 'admin', '127.0.0.1', '0', '退出成功', '2024-08-19 19:08:59');
INSERT INTO `sys_logininfor` VALUES (105, 'admin', '127.0.0.1', '0', '登录成功', '2024-08-20 15:56:18');
INSERT INTO `sys_logininfor` VALUES (106, 'admin', '127.0.0.1', '0', '登录成功', '2024-08-22 15:56:00');
INSERT INTO `sys_logininfor` VALUES (107, 'admin', '127.0.0.1', '0', '登录成功', '2024-08-22 15:57:16');
INSERT INTO `sys_logininfor` VALUES (108, 'admin', '127.0.0.1', '0', '登录成功', '2024-08-22 16:05:37');
INSERT INTO `sys_logininfor` VALUES (109, 'admin', '127.0.0.1', '0', '登录成功', '2024-10-14 22:33:59');
INSERT INTO `sys_logininfor` VALUES (110, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-08 13:43:10');
INSERT INTO `sys_logininfor` VALUES (111, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-19 14:08:05');
INSERT INTO `sys_logininfor` VALUES (112, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-19 14:33:34');
INSERT INTO `sys_logininfor` VALUES (113, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-19 14:48:05');
INSERT INTO `sys_logininfor` VALUES (114, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-19 14:48:10');
INSERT INTO `sys_logininfor` VALUES (115, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-19 15:15:51');
INSERT INTO `sys_logininfor` VALUES (116, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-19 15:57:27');
INSERT INTO `sys_logininfor` VALUES (117, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-19 16:07:44');
INSERT INTO `sys_logininfor` VALUES (118, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-19 16:16:13');
INSERT INTO `sys_logininfor` VALUES (119, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-19 16:57:41');
INSERT INTO `sys_logininfor` VALUES (120, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-19 16:57:41');
INSERT INTO `sys_logininfor` VALUES (121, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-19 20:57:57');
INSERT INTO `sys_logininfor` VALUES (122, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-19 23:08:37');
INSERT INTO `sys_logininfor` VALUES (123, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-19 23:19:22');
INSERT INTO `sys_logininfor` VALUES (124, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-19 23:42:26');
INSERT INTO `sys_logininfor` VALUES (125, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-19 23:42:27');
INSERT INTO `sys_logininfor` VALUES (126, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-19 23:42:37');
INSERT INTO `sys_logininfor` VALUES (127, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-19 23:45:15');
INSERT INTO `sys_logininfor` VALUES (128, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-19 23:45:20');
INSERT INTO `sys_logininfor` VALUES (129, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-19 23:48:18');
INSERT INTO `sys_logininfor` VALUES (130, 'ry', '192.168.1.103', '0', '登录成功', '2025-06-20 00:26:00');
INSERT INTO `sys_logininfor` VALUES (131, 'ry', '192.168.1.103', '0', '退出成功', '2025-06-20 00:26:21');
INSERT INTO `sys_logininfor` VALUES (132, 'ry', '192.168.1.103', '0', '登录成功', '2025-06-20 00:30:05');
INSERT INTO `sys_logininfor` VALUES (133, 'ry', '192.168.1.103', '0', '退出成功', '2025-06-20 00:31:17');
INSERT INTO `sys_logininfor` VALUES (134, 'admin', '192.168.1.103', '0', '登录成功', '2025-06-20 00:31:22');
INSERT INTO `sys_logininfor` VALUES (135, 'admin', '192.168.1.103', '0', '登录成功', '2025-06-20 00:31:50');
INSERT INTO `sys_logininfor` VALUES (136, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-20 01:10:07');
INSERT INTO `sys_logininfor` VALUES (137, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-20 01:10:31');
INSERT INTO `sys_logininfor` VALUES (138, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-20 08:29:06');
INSERT INTO `sys_logininfor` VALUES (139, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-20 08:29:21');
INSERT INTO `sys_logininfor` VALUES (140, 'admin', '10.11.6.188', '0', '登录成功', '2025-06-20 08:31:26');
INSERT INTO `sys_logininfor` VALUES (141, 'admin', '10.11.6.188', '0', '退出成功', '2025-06-20 08:34:41');
INSERT INTO `sys_logininfor` VALUES (142, 'admin', '10.11.6.188', '0', '登录成功', '2025-06-20 08:36:59');
INSERT INTO `sys_logininfor` VALUES (143, 'admin', '10.11.6.188', '0', '登录成功', '2025-06-20 08:38:20');
INSERT INTO `sys_logininfor` VALUES (144, 'admin', '10.11.6.188', '0', '登录成功', '2025-06-20 08:41:29');
INSERT INTO `sys_logininfor` VALUES (145, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-20 09:02:21');
INSERT INTO `sys_logininfor` VALUES (146, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-20 09:08:24');
INSERT INTO `sys_logininfor` VALUES (147, 'admin', '10.10.11.89', '0', '登录成功', '2025-06-20 09:22:16');
INSERT INTO `sys_logininfor` VALUES (148, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-20 09:30:10');
INSERT INTO `sys_logininfor` VALUES (149, 'admin', '10.11.6.188', '0', '登录成功', '2025-06-20 09:30:11');
INSERT INTO `sys_logininfor` VALUES (150, 'fei', '127.0.0.1', '1', '登录用户不存在', '2025-06-20 09:30:18');
INSERT INTO `sys_logininfor` VALUES (151, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-20 09:30:27');
INSERT INTO `sys_logininfor` VALUES (152, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-20 09:32:13');
INSERT INTO `sys_logininfor` VALUES (153, 'fei', '127.0.0.1', '0', '登录成功', '2025-06-20 09:32:21');
INSERT INTO `sys_logininfor` VALUES (154, 'fei', '127.0.0.1', '0', '退出成功', '2025-06-20 09:32:27');
INSERT INTO `sys_logininfor` VALUES (155, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-20 09:32:41');
INSERT INTO `sys_logininfor` VALUES (156, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-20 09:34:47');
INSERT INTO `sys_logininfor` VALUES (157, 'admin', '10.11.6.188', '0', '登录成功', '2025-06-20 09:37:47');
INSERT INTO `sys_logininfor` VALUES (158, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-20 11:29:53');
INSERT INTO `sys_logininfor` VALUES (159, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-20 13:08:26');
INSERT INTO `sys_logininfor` VALUES (160, 'admin', '10.11.105.26', '0', '登录成功', '2025-06-20 13:09:30');
INSERT INTO `sys_logininfor` VALUES (161, 'admin', '10.11.105.26', '0', '登录成功', '2025-06-20 13:14:31');
INSERT INTO `sys_logininfor` VALUES (162, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-21 17:23:15');
INSERT INTO `sys_logininfor` VALUES (163, 'admin', '192.168.190.181', '0', '登录成功', '2025-06-21 17:24:03');
INSERT INTO `sys_logininfor` VALUES (164, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-22 16:30:39');
INSERT INTO `sys_logininfor` VALUES (165, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-22 23:20:00');
INSERT INTO `sys_logininfor` VALUES (166, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-22 23:20:00');
INSERT INTO `sys_logininfor` VALUES (167, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-22 23:21:04');
INSERT INTO `sys_logininfor` VALUES (168, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-25 14:10:42');
INSERT INTO `sys_logininfor` VALUES (169, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-25 14:12:28');
INSERT INTO `sys_logininfor` VALUES (170, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-25 21:24:03');
INSERT INTO `sys_logininfor` VALUES (171, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-25 21:37:21');
INSERT INTO `sys_logininfor` VALUES (172, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-25 22:18:15');
INSERT INTO `sys_logininfor` VALUES (173, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-25 22:19:17');
INSERT INTO `sys_logininfor` VALUES (174, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-26 10:39:10');
INSERT INTO `sys_logininfor` VALUES (175, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-26 11:18:34');
INSERT INTO `sys_logininfor` VALUES (176, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-26 11:18:34');
INSERT INTO `sys_logininfor` VALUES (177, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-26 11:20:07');
INSERT INTO `sys_logininfor` VALUES (178, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-26 11:20:07');
INSERT INTO `sys_logininfor` VALUES (179, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-26 11:23:14');
INSERT INTO `sys_logininfor` VALUES (180, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-26 11:23:14');
INSERT INTO `sys_logininfor` VALUES (181, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-26 11:29:30');
INSERT INTO `sys_logininfor` VALUES (182, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-26 14:39:49');
INSERT INTO `sys_logininfor` VALUES (183, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-26 14:39:54');
INSERT INTO `sys_logininfor` VALUES (184, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-27 08:52:20');
INSERT INTO `sys_logininfor` VALUES (185, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-27 11:38:58');
INSERT INTO `sys_logininfor` VALUES (186, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-27 21:32:42');
INSERT INTO `sys_logininfor` VALUES (187, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-27 22:18:49');
INSERT INTO `sys_logininfor` VALUES (188, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-27 22:18:58');
INSERT INTO `sys_logininfor` VALUES (189, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-27 22:18:59');
INSERT INTO `sys_logininfor` VALUES (190, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-27 22:19:24');
INSERT INTO `sys_logininfor` VALUES (191, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-27 22:19:24');
INSERT INTO `sys_logininfor` VALUES (192, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-27 22:19:56');
INSERT INTO `sys_logininfor` VALUES (193, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-27 22:19:57');
INSERT INTO `sys_logininfor` VALUES (194, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-27 22:21:25');
INSERT INTO `sys_logininfor` VALUES (195, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-28 09:34:17');
INSERT INTO `sys_logininfor` VALUES (196, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-28 09:36:27');
INSERT INTO `sys_logininfor` VALUES (197, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-28 09:36:31');
INSERT INTO `sys_logininfor` VALUES (198, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-28 10:26:21');
INSERT INTO `sys_logininfor` VALUES (199, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-28 10:26:34');
INSERT INTO `sys_logininfor` VALUES (200, 'admin', '127.0.0.1', '0', '退出成功', '2025-06-28 11:23:27');
INSERT INTO `sys_logininfor` VALUES (201, 'admin', '127.0.0.1', '0', '登录成功', '2025-06-28 11:23:35');
INSERT INTO `sys_logininfor` VALUES (202, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-01 14:30:10');
INSERT INTO `sys_logininfor` VALUES (203, 'admin', '127.0.0.1', '0', '退出成功', '2025-07-01 17:17:19');
INSERT INTO `sys_logininfor` VALUES (204, 'admin', '127.0.0.1', '0', '退出成功', '2025-07-01 17:17:19');
INSERT INTO `sys_logininfor` VALUES (205, 'admin', '127.0.0.1', '0', '退出成功', '2025-07-01 17:17:19');
INSERT INTO `sys_logininfor` VALUES (206, 'admin', '127.0.0.1', '1', '登录用户不存在', '2025-07-01 17:17:20');
INSERT INTO `sys_logininfor` VALUES (207, 'admin', '127.0.0.1', '1', '登录用户不存在', '2025-07-01 17:17:20');
INSERT INTO `sys_logininfor` VALUES (208, 'admin', '127.0.0.1', '1', '登录用户不存在', '2025-07-01 17:17:20');
INSERT INTO `sys_logininfor` VALUES (209, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-01 20:26:17');
INSERT INTO `sys_logininfor` VALUES (210, 'admin', '127.0.0.1', '0', '退出成功', '2025-07-01 20:54:02');
INSERT INTO `sys_logininfor` VALUES (211, 'admin', '127.0.0.1', '0', '退出成功', '2025-07-01 20:55:03');
INSERT INTO `sys_logininfor` VALUES (212, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-01 20:58:24');
INSERT INTO `sys_logininfor` VALUES (213, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-03 20:28:14');
INSERT INTO `sys_logininfor` VALUES (214, 'admin', '127.0.0.1', '0', '退出成功', '2025-07-03 20:38:27');
INSERT INTO `sys_logininfor` VALUES (215, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-03 20:40:25');
INSERT INTO `sys_logininfor` VALUES (216, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-04 10:36:54');
INSERT INTO `sys_logininfor` VALUES (217, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-04 20:55:08');
INSERT INTO `sys_logininfor` VALUES (218, 'admin', '127.0.0.1', '0', '退出成功', '2025-07-04 21:03:47');
INSERT INTO `sys_logininfor` VALUES (219, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-04 21:08:20');
INSERT INTO `sys_logininfor` VALUES (220, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-05 11:25:43');
INSERT INTO `sys_logininfor` VALUES (221, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-05 11:28:22');
INSERT INTO `sys_logininfor` VALUES (222, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-05 11:30:29');
INSERT INTO `sys_logininfor` VALUES (223, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-08 10:23:06');
INSERT INTO `sys_logininfor` VALUES (224, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-08 15:36:18');
INSERT INTO `sys_logininfor` VALUES (225, 'admin', '127.0.0.1', '0', '退出成功', '2025-07-08 16:18:45');
INSERT INTO `sys_logininfor` VALUES (226, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-08 17:16:23');
INSERT INTO `sys_logininfor` VALUES (227, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-08 21:50:21');
INSERT INTO `sys_logininfor` VALUES (228, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-09 08:54:25');
INSERT INTO `sys_logininfor` VALUES (229, 'admin', '127.0.0.1', '0', '退出成功', '2025-07-09 09:38:56');
INSERT INTO `sys_logininfor` VALUES (230, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-09 09:46:45');
INSERT INTO `sys_logininfor` VALUES (231, 'admin', '127.0.0.1', '0', '退出成功', '2025-07-09 10:06:38');
INSERT INTO `sys_logininfor` VALUES (232, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-09 10:07:23');
INSERT INTO `sys_logininfor` VALUES (233, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-09 12:01:32');
INSERT INTO `sys_logininfor` VALUES (234, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-09 15:56:14');
INSERT INTO `sys_logininfor` VALUES (235, 'admin', '127.0.0.1', '0', '退出成功', '2025-07-09 16:59:19');
INSERT INTO `sys_logininfor` VALUES (236, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-09 17:25:16');
INSERT INTO `sys_logininfor` VALUES (237, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-10 09:39:49');
INSERT INTO `sys_logininfor` VALUES (238, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-10 15:55:50');
INSERT INTO `sys_logininfor` VALUES (239, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-10 21:25:27');
INSERT INTO `sys_logininfor` VALUES (240, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-11 14:54:42');
INSERT INTO `sys_logininfor` VALUES (241, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-12 17:55:41');
INSERT INTO `sys_logininfor` VALUES (242, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-13 17:16:17');
INSERT INTO `sys_logininfor` VALUES (243, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-13 20:59:46');
INSERT INTO `sys_logininfor` VALUES (244, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-14 16:39:33');
INSERT INTO `sys_logininfor` VALUES (245, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-14 22:42:06');
INSERT INTO `sys_logininfor` VALUES (246, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-14 23:17:20');
INSERT INTO `sys_logininfor` VALUES (247, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-15 09:44:17');
INSERT INTO `sys_logininfor` VALUES (248, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-15 14:35:03');
INSERT INTO `sys_logininfor` VALUES (249, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-15 19:49:52');
INSERT INTO `sys_logininfor` VALUES (250, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-15 20:41:45');
INSERT INTO `sys_logininfor` VALUES (251, 'admin', '127.0.0.1', '1', '登录用户不存在', '2025-07-15 22:48:09');
INSERT INTO `sys_logininfor` VALUES (252, 'admin', '127.0.0.1', '1', '登录用户不存在', '2025-07-15 22:48:10');
INSERT INTO `sys_logininfor` VALUES (253, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-15 23:14:00');
INSERT INTO `sys_logininfor` VALUES (254, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-16 19:42:57');
INSERT INTO `sys_logininfor` VALUES (255, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-17 08:59:19');
INSERT INTO `sys_logininfor` VALUES (256, 'admin', '127.0.0.1', '0', '退出成功', '2025-07-17 09:21:13');
INSERT INTO `sys_logininfor` VALUES (257, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-17 09:21:20');
INSERT INTO `sys_logininfor` VALUES (258, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-17 11:31:19');
INSERT INTO `sys_logininfor` VALUES (259, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-18 09:47:42');
INSERT INTO `sys_logininfor` VALUES (260, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-18 09:50:44');
INSERT INTO `sys_logininfor` VALUES (261, 'admin', '127.0.0.1', '0', '登录成功', '2025-07-18 14:09:59');
INSERT INTO `sys_logininfor` VALUES (262, 'admin', '127.0.0.1', '0', '退出成功', '2025-07-18 17:32:08');

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint(0) DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int(0) DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '路由名称',
  `is_frame` int(0) DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `is_cache` int(0) DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1075 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '系统管理', 0, 1, 'system', NULL, '', '', 1, 0, 'M', '0', '0', '', 'system', 'admin', '2024-08-18 22:19:55', '', NULL, '系统管理目录');
INSERT INTO `sys_menu` VALUES (2, '系统监控', 0, 2, 'monitor', NULL, '', '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', '2024-08-18 22:19:55', '', NULL, '系统监控目录');
INSERT INTO `sys_menu` VALUES (3, '系统工具', 0, 3, 'tool', NULL, '', '', 1, 0, 'M', '0', '0', '', 'tool', 'admin', '2024-08-18 22:19:55', '', NULL, '系统工具目录');
INSERT INTO `sys_menu` VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', '', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', '2024-08-18 22:19:55', '', NULL, '用户管理菜单');
INSERT INTO `sys_menu` VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', '', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', '2024-08-18 22:19:55', '', NULL, '角色管理菜单');
INSERT INTO `sys_menu` VALUES (102, '菜单管理', 1, 3, 'menu', 'system/menu/index', '', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', '2024-08-18 22:19:55', '', NULL, '菜单管理菜单');
INSERT INTO `sys_menu` VALUES (103, '租户管理', 1, 4, 'dept', 'system/dept/index', '', '', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', '2024-08-18 22:19:55', 'admin', '2025-07-08 22:08:12', '部门管理菜单');
INSERT INTO `sys_menu` VALUES (104, '岗位管理', 1, 5, 'post', 'system/post/index', '', '', 1, 0, 'C', '0', '0', 'system:post:list', 'post', 'admin', '2024-08-18 22:19:55', '', NULL, '岗位管理菜单');
INSERT INTO `sys_menu` VALUES (105, '字典管理', 1, 6, 'dict', 'system/dict/index', '', '', 1, 0, 'C', '1', '1', 'system:dict:list', 'dict', 'admin', '2024-08-18 22:19:55', 'admin', '2025-07-08 22:14:16', '字典管理菜单');
INSERT INTO `sys_menu` VALUES (106, '参数设置', 1, 7, 'config', 'system/config/index', '', '', 1, 0, 'C', '1', '1', 'system:config:list', 'edit', 'admin', '2024-08-18 22:19:55', 'admin', '2025-07-08 22:14:24', '参数设置菜单');
INSERT INTO `sys_menu` VALUES (107, '通知公告', 1, 8, 'notice', 'system/notice/index', '', '', 1, 0, 'C', '0', '0', 'system:notice:list', 'message', 'admin', '2024-08-18 22:19:55', '', NULL, '通知公告菜单');
INSERT INTO `sys_menu` VALUES (108, '日志管理', 1, 9, 'log', '', '', '', 1, 0, 'M', '1', '1', '', 'log', 'admin', '2024-08-18 22:19:55', 'admin', '2025-07-08 22:14:31', '日志管理菜单');
INSERT INTO `sys_menu` VALUES (109, '在线用户', 2, 1, 'online', 'monitor/online/index', '', '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'admin', '2024-08-18 22:19:55', '', NULL, '在线用户菜单');
INSERT INTO `sys_menu` VALUES (110, '定时任务', 2, 2, 'job', 'monitor/job/index', '', '', 1, 0, 'C', '1', '1', 'monitor:job:list', 'job', 'admin', '2024-08-18 22:19:55', 'admin', '2025-07-08 22:15:19', '定时任务菜单');
INSERT INTO `sys_menu` VALUES (111, 'Sentinel控制台', 2, 3, 'http://localhost:8718', '', '', '', 0, 0, 'C', '1', '1', 'monitor:sentinel:list', 'sentinel', 'admin', '2024-08-18 22:19:55', 'admin', '2025-07-08 22:15:14', '流量控制菜单');
INSERT INTO `sys_menu` VALUES (112, 'Nacos控制台', 2, 4, 'http://localhost:8848/nacos', '', '', '', 0, 0, 'C', '1', '1', 'monitor:nacos:list', 'nacos', 'admin', '2024-08-18 22:19:55', 'admin', '2025-07-08 22:15:11', '服务治理菜单');
INSERT INTO `sys_menu` VALUES (113, 'Admin控制台', 2, 5, 'http://localhost:9100/login', '', '', '', 0, 0, 'C', '1', '1', 'monitor:server:list', 'server', 'admin', '2024-08-18 22:19:55', 'admin', '2025-07-08 22:15:07', '服务监控菜单');
INSERT INTO `sys_menu` VALUES (114, '表单构建', 3, 1, 'build', 'tool/build/index', '', '', 1, 0, 'C', '0', '0', 'tool:build:list', 'build', 'admin', '2024-08-18 22:19:55', '', NULL, '表单构建菜单');
INSERT INTO `sys_menu` VALUES (115, '代码生成', 3, 2, 'gen', 'tool/gen/index', '', '', 1, 0, 'C', '1', '1', 'tool:gen:list', 'code', 'admin', '2024-08-18 22:19:55', 'admin', '2025-07-08 22:27:25', '代码生成菜单');
INSERT INTO `sys_menu` VALUES (116, '系统接口', 3, 3, 'http://localhost:8080/swagger-ui/index.html', '', '', '', 0, 0, 'C', '0', '0', 'tool:swagger:list', 'swagger', 'admin', '2024-08-18 22:19:55', '', NULL, '系统接口菜单');
INSERT INTO `sys_menu` VALUES (500, '操作日志', 108, 1, 'operlog', 'system/operlog/index', '', '', 1, 0, 'C', '0', '0', 'system:operlog:list', 'form', 'admin', '2024-08-18 22:19:55', '', NULL, '操作日志菜单');
INSERT INTO `sys_menu` VALUES (501, '登录日志', 108, 2, 'logininfor', 'system/logininfor/index', '', '', 1, 0, 'C', '0', '0', 'system:logininfor:list', 'logininfor', 'admin', '2024-08-18 22:19:55', '', NULL, '登录日志菜单');
INSERT INTO `sys_menu` VALUES (1000, '用户查询', 100, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1001, '用户新增', 100, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1002, '用户修改', 100, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1003, '用户删除', 100, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:remove', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1004, '用户导出', 100, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:export', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1005, '用户导入', 100, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:import', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1006, '重置密码', 100, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1007, '角色查询', 101, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1008, '角色新增', 101, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1009, '角色修改', 101, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1010, '角色删除', 101, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1011, '角色导出', 101, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1012, '菜单查询', 102, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1013, '菜单新增', 102, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1014, '菜单修改', 102, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1015, '菜单删除', 102, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1016, '部门查询', 103, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:query', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1017, '部门新增', 103, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:add', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1018, '部门修改', 103, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:edit', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1019, '部门删除', 103, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:remove', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1020, '岗位查询', 104, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:query', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1021, '岗位新增', 104, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:add', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1022, '岗位修改', 104, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:edit', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1023, '岗位删除', 104, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:remove', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1024, '岗位导出', 104, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:export', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1025, '字典查询', 105, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1026, '字典新增', 105, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1027, '字典修改', 105, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1028, '字典删除', 105, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1029, '字典导出', 105, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1030, '参数查询', 106, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:query', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1031, '参数新增', 106, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:add', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1032, '参数修改', 106, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:edit', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1033, '参数删除', 106, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:remove', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1034, '参数导出', 106, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:export', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1035, '公告查询', 107, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:query', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1036, '公告新增', 107, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:add', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1037, '公告修改', 107, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:edit', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1038, '公告删除', 107, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:remove', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1039, '操作查询', 500, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:operlog:query', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1040, '操作删除', 500, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:operlog:remove', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1041, '日志导出', 500, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:operlog:export', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1042, '登录查询', 501, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:logininfor:query', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1043, '登录删除', 501, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:logininfor:remove', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1044, '日志导出', 501, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:logininfor:export', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1045, '账户解锁', 501, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:logininfor:unlock', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1046, '在线查询', 109, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1047, '批量强退', 109, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1048, '单条强退', 109, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1049, '任务查询', 110, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:query', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1050, '任务新增', 110, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:add', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1051, '任务修改', 110, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:edit', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1052, '任务删除', 110, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:remove', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1053, '状态修改', 110, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:changeStatus', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1054, '任务导出', 110, 6, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:export', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1055, '生成查询', 115, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:query', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1056, '生成修改', 115, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:edit', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1057, '生成删除', 115, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:remove', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1058, '导入代码', 115, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:import', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1059, '预览代码', 115, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:preview', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1060, '生成代码', 115, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:code', '#', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1061, '智能办公', 0, 4, 'office', '', '', '', 1, 0, 'M', '0', '0', '', 'lock', 'admin', '2025-06-05 10:45:56', '', '2025-06-19 13:45:38', '');
INSERT INTO `sys_menu` VALUES (1063, '智能日程', 1061, 1, 'schedule', 'office/schedule/index', NULL, '', 1, 0, 'C', '0', '0', NULL, 'date', 'admin', '2025-06-19 14:36:11', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1067, '文档分析', 1061, 2, 'document-analysis', 'office/document-analysis/index', NULL, '', 1, 0, 'C', '0', '0', NULL, 'edit', 'admin', '2025-06-20 09:08:37', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1069, '即时聊天', 0, 3, 'user-chat', 'office/user-chat/index', NULL, '', 1, 0, 'C', '0', '0', '', 'wechat', 'admin', '2025-06-20 09:36:47', 'admin', '2025-07-10 10:13:36', '');
INSERT INTO `sys_menu` VALUES (1070, '我的文件', 1061, 4, 'file-management', 'office/file-management/index', NULL, '', 1, 0, 'C', '0', '0', '', 'dict', 'admin', '2025-06-25 21:39:30', 'admin', '2025-07-10 10:10:19', '');
INSERT INTO `sys_menu` VALUES (1071, '合同合约分析', 1061, 2, 'contract-analysis', 'office/contract-analysis/index', NULL, '', 1, 0, 'C', '0', '0', '', 'dict', 'admin', '2025-07-10 10:12:48', 'admin', '2025-07-10 10:13:27', '');
INSERT INTO `sys_menu` VALUES (1072, '文书会议', 0, 5, 'meeting', 'office/meeting/index', NULL, '', 1, 0, 'C', '0', '0', '', 'button', 'admin', '2025-07-13 17:17:27', 'admin', '2025-07-17 11:27:27', '');
INSERT INTO `sys_menu` VALUES (1073, '多模态处理', 0, 4, 'multimodal', 'office/multimodal/index', NULL, '', 1, 0, 'C', '0', '0', NULL, 'client', 'admin', '2025-07-13 17:20:18', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1074, '团队管理', 0, 4, 'team-management', 'office/team-management/index', NULL, '', 1, 0, 'C', '0', '0', '', 'peoples', 'admin', '2025-07-15 15:57:16', 'admin', '2025-07-15 16:24:28', '');

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `notice_id` int(0) NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公告标题',
  `notice_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob COMMENT '公告内容',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知公告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_notice
-- ----------------------------
INSERT INTO `sys_notice` VALUES (1, '温馨提醒：2018-07-01 若依新版本发布啦', '2', 0xE696B0E78988E69CACE58685E5AEB9, '0', 'admin', '2024-08-18 22:19:55', '', NULL, '管理员');
INSERT INTO `sys_notice` VALUES (2, '维护通知：2018-07-01 若依系统凌晨维护', '1', 0xE7BBB4E68AA4E58685E5AEB9, '0', 'admin', '2024-08-18 22:19:55', '', NULL, '管理员');

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`  (
  `oper_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '模块标题',
  `business_type` int(0) DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '请求方式',
  `operator_type` int(0) DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '返回参数',
  `status` int(0) DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime(0) DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint(0) DEFAULT 0 COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`) USING BTREE,
  INDEX `idx_sys_oper_log_bt`(`business_type`) USING BTREE,
  INDEX `idx_sys_oper_log_s`(`status`) USING BTREE,
  INDEX `idx_sys_oper_log_ot`(`oper_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 128 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '操作日志记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_oper_log
-- ----------------------------
INSERT INTO `sys_oper_log` VALUES (1, '代码生成', 6, 'com.ruoyi.gen.controller.GenController.importTableSave()', 'POST', 1, 'admin', NULL, '/gen/importTable', '127.0.0.1', '', '{\"tables\":\"cl_course\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-08-19 15:33:40', 66);
INSERT INTO `sys_oper_log` VALUES (2, '代码生成', 2, 'com.ruoyi.gen.controller.GenController.editSave()', 'PUT', 1, 'admin', NULL, '/gen', '127.0.0.1', '', '{\"businessName\":\"course\",\"className\":\"ClCourse\",\"columns\":[{\"capJavaField\":\"CourseId\",\"columnComment\":\"课程id\",\"columnId\":1,\"columnName\":\"course_id\",\"columnType\":\"bigint(20)\",\"createBy\":\"admin\",\"createTime\":\"2024-08-19 15:33:40\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isPk\":\"1\",\"javaField\":\"courseId\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":1,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"CourseName\",\"columnComment\":\"课程名\",\"columnId\":2,\"columnName\":\"course_name\",\"columnType\":\"varchar(255)\",\"createBy\":\"admin\",\"createTime\":\"2024-08-19 15:33:40\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"javaField\":\"courseName\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"LIKE\",\"required\":false,\"sort\":2,\"superColumn\":false,\"tableId\":1,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"UserId\",\"columnComment\":\"教师id\",\"columnId\":3,\"columnName\":\"user_id\",\"columnType\":\"bigint(20)\",\"createBy\":\"admin\",\"createTime\":\"2024-08-19 15:33:40\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"javaField\":\"userId\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":false,\"sort\":3,\"superColumn\":false,\"tableId\":1,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Code\",\"columnComment\":\"课程码\",\"columnId\":4,\"columnName\":\"code\",\"columnType\":\"varchar(255)\",\"createBy\":\"admin\",\"createTime\":\"2024-08-19 15:33:40\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"javaField\":\"code\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-08-19 15:34:56', 28);
INSERT INTO `sys_oper_log` VALUES (3, '菜单管理', 1, 'com.ruoyi.system.controller.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"createBy\":\"admin\",\"icon\":\"row\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"课程管理\",\"menuType\":\"M\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"clazz\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-08-19 15:35:50', 13);
INSERT INTO `sys_oper_log` VALUES (4, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/4', '127.0.0.1', '', '{}', '{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}', 0, NULL, '2024-08-19 15:35:53', 3);
INSERT INTO `sys_oper_log` VALUES (5, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2024-08-18 22:19:55\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,3,114,115,1055,1056,1058,1057,1059,1060,116,1061],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-08-19 15:36:06', 27);
INSERT INTO `sys_oper_log` VALUES (6, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/4', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-08-19 15:36:11', 9);
INSERT INTO `sys_oper_log` VALUES (7, '代码生成', 2, 'com.ruoyi.gen.controller.GenController.editSave()', 'PUT', 1, 'admin', NULL, '/gen', '127.0.0.1', '', '{\"businessName\":\"course\",\"className\":\"ClCourse\",\"columns\":[{\"capJavaField\":\"CourseId\",\"columnComment\":\"课程id\",\"columnId\":1,\"columnName\":\"course_id\",\"columnType\":\"bigint(20)\",\"createBy\":\"admin\",\"createTime\":\"2024-08-19 15:33:40\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isPk\":\"1\",\"javaField\":\"courseId\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":1,\"updateBy\":\"\",\"updateTime\":\"2024-08-19 15:34:56\",\"usableColumn\":false},{\"capJavaField\":\"CourseName\",\"columnComment\":\"课程名\",\"columnId\":2,\"columnName\":\"course_name\",\"columnType\":\"varchar(255)\",\"createBy\":\"admin\",\"createTime\":\"2024-08-19 15:33:40\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"javaField\":\"courseName\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"LIKE\",\"required\":false,\"sort\":2,\"superColumn\":false,\"tableId\":1,\"updateBy\":\"\",\"updateTime\":\"2024-08-19 15:34:56\",\"usableColumn\":false},{\"capJavaField\":\"UserId\",\"columnComment\":\"教师id\",\"columnId\":3,\"columnName\":\"user_id\",\"columnType\":\"bigint(20)\",\"createBy\":\"admin\",\"createTime\":\"2024-08-19 15:33:40\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"javaField\":\"userId\",\"javaType\":\"Long\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":false,\"sort\":3,\"superColumn\":false,\"tableId\":1,\"updateBy\":\"\",\"updateTime\":\"2024-08-19 15:34:56\",\"usableColumn\":false},{\"capJavaField\":\"Code\",\"columnComment\":\"课程码\",\"columnId\":4,\"columnName\":\"code\",\"columnType\":\"varchar(255)\",\"createBy\":\"admin\",\"createTime\":\"2024-08-19 15:33:40\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\"', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-08-19 15:42:08', 44);
INSERT INTO `sys_oper_log` VALUES (8, '代码生成', 8, 'com.ruoyi.gen.controller.GenController.batchGenCode()', 'GET', 1, 'admin', NULL, '/gen/batchGenCode', '127.0.0.1', '', '{\"tables\":\"cl_course\"}', NULL, 0, NULL, '2024-08-19 15:42:11', 30);
INSERT INTO `sys_oper_log` VALUES (9, '角色管理', 1, 'com.ruoyi.system.controller.SysRoleController.add()', 'POST', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createBy\":\"admin\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1061,1062,1063,1064,1065,1066,1067],\"params\":{},\"roleId\":3,\"roleKey\":\"teacher\",\"roleName\":\"教师\",\"roleSort\":0,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-08-19 16:04:05', 23);
INSERT INTO `sys_oper_log` VALUES (10, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2024-08-19 16:04:05\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1061,1062,1063,1064,1065,1066,1067],\"params\":{},\"roleId\":3,\"roleKey\":\"teacher\",\"roleName\":\"教师\",\"roleSort\":3,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-08-19 16:05:25', 11);
INSERT INTO `sys_oper_log` VALUES (11, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2024-08-19 16:04:05\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1061,1062,1063,1064,1065,1066,1067],\"params\":{},\"roleId\":4,\"roleKey\":\"teacher\",\"roleName\":\"教师\",\"roleSort\":4,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-08-19 16:06:50', 12);
INSERT INTO `sys_oper_log` VALUES (12, '用户管理', 1, 'com.ruoyi.system.controller.SysUserController.add()', 'POST', 1, 'admin', NULL, '/user', '127.0.0.1', '', '{\"admin\":false,\"createBy\":\"admin\",\"nickName\":\"教师A\",\"params\":{},\"postIds\":[],\"roleIds\":[4],\"status\":\"0\",\"userId\":3,\"userName\":\"teacher01\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-08-19 16:11:17', 74);
INSERT INTO `sys_oper_log` VALUES (13, '我的课程', 1, 'com.ruoyi.clazz.controller.ClCourseController.add()', 'POST', 1, 'admin', NULL, '/course', '127.0.0.1', '', '{\"code\":\"123456\",\"courseName\":\"java\",\"createTime\":\"2024-08-19 16:13:02\",\"params\":{},\"remark\":\"java\",\"userId\":3}', NULL, 1, '\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'course_id\' doesn\'t have a default value\r\n### The error may exist in file [E:\\workspace\\RuoYi-Cloud-Test\\RuoYi-Cloud\\ruoyi-modules\\ruoyi-clazz\\target\\classes\\mapper\\clazz\\ClCourseMapper.xml]\r\n### The error may involve com.ruoyi.clazz.mapper.ClCourseMapper.insertClCourse-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into cl_course          ( course_name,             user_id,             code,                          create_time,                                       remark )           values ( ?,             ?,             ?,                          ?,                                       ? )\r\n### Cause: java.sql.SQLException: Field \'course_id\' doesn\'t have a default value\n; Field \'course_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'course_id\' doesn\'t have a default value', '2024-08-19 16:13:02', 96);
INSERT INTO `sys_oper_log` VALUES (14, '我的课程', 1, 'com.ruoyi.clazz.controller.ClCourseController.add()', 'POST', 1, 'admin', NULL, '/course', '127.0.0.1', '', '{\"code\":\"123456\",\"courseName\":\"java\",\"createTime\":\"2024-08-19 16:14:41\",\"params\":{},\"remark\":\"java\",\"userId\":3}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-08-19 16:14:41', 6);
INSERT INTO `sys_oper_log` VALUES (15, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1061', '127.0.0.1', '', '{}', '{\"msg\":\"存在子菜单,不允许删除\",\"code\":601}', 0, NULL, '2024-10-14 22:15:54', 8);
INSERT INTO `sys_oper_log` VALUES (16, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1062', '127.0.0.1', '', '{}', '{\"msg\":\"存在子菜单,不允许删除\",\"code\":601}', 0, NULL, '2024-10-14 22:15:56', 2);
INSERT INTO `sys_oper_log` VALUES (17, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1063', '127.0.0.1', '', '{}', '{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}', 0, NULL, '2024-10-14 22:16:02', 5);
INSERT INTO `sys_oper_log` VALUES (18, '角色管理', 3, 'com.ruoyi.system.controller.SysRoleController.remove()', 'DELETE', 1, 'admin', NULL, '/role/4', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-14 22:16:09', 18);
INSERT INTO `sys_oper_log` VALUES (19, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1063', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-14 22:16:16', 6);
INSERT INTO `sys_oper_log` VALUES (20, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1065', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-14 22:16:18', 5);
INSERT INTO `sys_oper_log` VALUES (21, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1066', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-14 22:16:20', 5);
INSERT INTO `sys_oper_log` VALUES (22, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1067', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-14 22:16:22', 4);
INSERT INTO `sys_oper_log` VALUES (23, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1064', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-14 22:16:24', 5);
INSERT INTO `sys_oper_log` VALUES (24, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1062', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-14 22:16:26', 4);
INSERT INTO `sys_oper_log` VALUES (25, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1061', '127.0.0.1', '', '{}', '{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}', 0, NULL, '2024-10-14 22:16:29', 3);
INSERT INTO `sys_oper_log` VALUES (26, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2024-08-18 22:19:55\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,3,114,115,1055,1056,1058,1057,1059,1060,116],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-14 22:16:37', 37);
INSERT INTO `sys_oper_log` VALUES (27, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1061', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-14 22:16:41', 4);
INSERT INTO `sys_oper_log` VALUES (28, '代码生成', 6, 'com.ruoyi.gen.controller.GenController.importTableSave()', 'POST', 1, 'admin', NULL, '/gen/importTable', '127.0.0.1', '', '{\"tables\":\"sys_notice\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-14 22:17:11', 102);
INSERT INTO `sys_oper_log` VALUES (29, '代码生成', 3, 'com.ruoyi.gen.controller.GenController.remove()', 'DELETE', 1, 'admin', NULL, '/gen/1', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-14 22:17:13', 6);
INSERT INTO `sys_oper_log` VALUES (30, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1062', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-19 14:35:20', 391);
INSERT INTO `sys_oper_log` VALUES (31, '菜单管理', 1, 'com.ruoyi.system.controller.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/schedule/index\",\"createBy\":\"admin\",\"icon\":\"date\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"智能日程\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1061,\"path\":\"schedule\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-19 14:36:12', 1327);
INSERT INTO `sys_oper_log` VALUES (32, '菜单管理', 1, 'com.ruoyi.system.controller.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/schedule/index\",\"createBy\":\"admin\",\"icon\":\"date\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"智能日程\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1061,\"path\":\"schedule\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-19 14:36:12', 1354);
INSERT INTO `sys_oper_log` VALUES (33, '菜单管理', 1, 'com.ruoyi.system.controller.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/schedule/index\",\"createBy\":\"admin\",\"icon\":\"date\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"智能日程\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1061,\"path\":\"schedule\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-19 14:36:12', 1298);
INSERT INTO `sys_oper_log` VALUES (34, '菜单管理', 1, 'com.ruoyi.system.controller.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/schedule/index\",\"createBy\":\"admin\",\"icon\":\"date\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"智能日程\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":1061,\"path\":\"schedule\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-19 14:36:12', 1333);
INSERT INTO `sys_oper_log` VALUES (35, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1066', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-19 14:36:18', 163);
INSERT INTO `sys_oper_log` VALUES (36, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1065', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-19 14:36:20', 132);
INSERT INTO `sys_oper_log` VALUES (37, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1064', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-19 14:36:24', 659);
INSERT INTO `sys_oper_log` VALUES (38, '用户管理', 5, 'com.ruoyi.system.controller.SysUserController.export()', 'POST', 1, 'admin', NULL, '/user/export', '127.0.0.1', '', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2025-06-19 16:57:31', 13651);
INSERT INTO `sys_oper_log` VALUES (39, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2024-08-18 22:19:55\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,3,114,115,1055,1058,1056,1057,1059,1060,116,1061,1063],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 00:31:19', 337);
INSERT INTO `sys_oper_log` VALUES (40, '菜单管理', 1, 'com.ruoyi.system.controller.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/document-analysis/index\",\"createBy\":\"admin\",\"icon\":\"edit\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"文档分析\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":1061,\"path\":\"document-analysis\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 09:08:38', 242);
INSERT INTO `sys_oper_log` VALUES (41, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2024-08-18 22:19:55\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,3,114,115,1055,1058,1056,1057,1059,1060,116,1061,1063,1067],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 09:08:49', 446);
INSERT INTO `sys_oper_log` VALUES (42, '菜单管理', 1, 'com.ruoyi.system.controller.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/user-chat/index\",\"createBy\":\"admin\",\"icon\":\"wechat\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"即时聊天\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":1061,\"path\":\"user-chat\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 09:09:53', 108);
INSERT INTO `sys_oper_log` VALUES (43, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2024-08-18 22:19:55\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,3,114,115,1055,1056,1058,1057,1059,1060,116,1061,1063,1067,1068],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 09:10:10', 366);
INSERT INTO `sys_oper_log` VALUES (44, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1068', '127.0.0.1', '', '{}', '{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}', 0, NULL, '2025-06-20 09:19:08', 91);
INSERT INTO `sys_oper_log` VALUES (45, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2024-08-18 22:19:55\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1061,1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,3,114,115,1055,1056,1058,1057,1059,1060,116,1063,1067],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 09:19:17', 353);
INSERT INTO `sys_oper_log` VALUES (46, '菜单管理', 3, 'com.ruoyi.system.controller.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/menu/1068', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 09:19:21', 117);
INSERT INTO `sys_oper_log` VALUES (47, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2024-08-18 22:19:55\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,3,114,115,1055,1058,1056,1057,1059,1060,116],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 09:28:39', 383);
INSERT INTO `sys_oper_log` VALUES (48, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2024-08-18 22:19:55\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,3,114,115,1055,1058,1056,1057,1059,1060,116,1061,1063,1067],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 09:28:53', 447);
INSERT INTO `sys_oper_log` VALUES (49, '角色管理', 1, 'com.ruoyi.system.controller.SysRoleController.add()', 'POST', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,3,114,115,1055,1058,1056,1057,1059,1060,116],\"params\":{},\"roleKey\":\"common\",\"roleName\":\"fei\",\"roleSort\":2,\"status\":\"0\"}', '{\"msg\":\"新增角色\'fei\'失败，角色权限已存在\",\"code\":500}', 0, NULL, '2025-06-20 09:29:50', 84);
INSERT INTO `sys_oper_log` VALUES (50, '角色管理', 1, 'com.ruoyi.system.controller.SysRoleController.add()', 'POST', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createBy\":\"admin\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,3,114,115,1055,1058,1056,1057,1059,1060,116],\"params\":{},\"roleId\":5,\"roleKey\":\"qw\",\"roleName\":\"fei\",\"roleSort\":2,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 09:29:56', 423);
INSERT INTO `sys_oper_log` VALUES (51, '角色管理', 4, 'com.ruoyi.system.controller.SysRoleController.selectAuthUserAll()', 'PUT', 1, 'admin', NULL, '/role/authUser/selectAll', '127.0.0.1', '', '{\"roleId\":\"5\",\"userIds\":\"2\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 09:30:49', 85);
INSERT INTO `sys_oper_log` VALUES (52, '角色管理', 3, 'com.ruoyi.system.controller.SysRoleController.remove()', 'DELETE', 1, 'admin', NULL, '/role/5', '127.0.0.1', '', '{}', NULL, 1, 'fei已分配,不能删除', '2025-06-20 09:31:02', 368);
INSERT INTO `sys_oper_log` VALUES (53, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2025-06-20 09:29:56\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":false,\"menuIds\":[],\"params\":{},\"roleId\":5,\"roleKey\":\"qw\",\"roleName\":\"fei\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 09:31:11', 337);
INSERT INTO `sys_oper_log` VALUES (54, '角色管理', 3, 'com.ruoyi.system.controller.SysRoleController.remove()', 'DELETE', 1, 'admin', NULL, '/role/5', '127.0.0.1', '', '{}', NULL, 1, 'fei已分配,不能删除', '2025-06-20 09:31:14', 152);
INSERT INTO `sys_oper_log` VALUES (55, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2025-06-20 09:29:56\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":false,\"menuIds\":[],\"params\":{},\"roleId\":5,\"roleKey\":\"qw\",\"roleName\":\"fei\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 09:31:18', 289);
INSERT INTO `sys_oper_log` VALUES (56, '角色管理', 3, 'com.ruoyi.system.controller.SysRoleController.remove()', 'DELETE', 1, 'admin', NULL, '/role/5', '127.0.0.1', '', '{}', NULL, 1, 'fei已分配,不能删除', '2025-06-20 09:31:20', 150);
INSERT INTO `sys_oper_log` VALUES (57, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2025-06-20 09:29:56\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":false,\"menuIds\":[],\"params\":{},\"roleId\":5,\"roleKey\":\"qw\",\"roleName\":\"fei\",\"roleSort\":2,\"status\":\"1\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 09:31:26', 264);
INSERT INTO `sys_oper_log` VALUES (58, '角色管理', 3, 'com.ruoyi.system.controller.SysRoleController.remove()', 'DELETE', 1, 'admin', NULL, '/role/5', '127.0.0.1', '', '{}', NULL, 1, 'fei已分配,不能删除', '2025-06-20 09:31:28', 149);
INSERT INTO `sys_oper_log` VALUES (59, '用户管理', 1, 'com.ruoyi.system.controller.SysUserController.add()', 'POST', 1, 'admin', NULL, '/user', '127.0.0.1', '', '{\"admin\":false,\"createBy\":\"admin\",\"deptId\":100,\"nickName\":\"fei\",\"params\":{},\"phonenumber\":\"15526497836\",\"postIds\":[4],\"roleIds\":[2],\"sex\":\"0\",\"status\":\"0\",\"userId\":3,\"userName\":\"fei\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 09:31:59', 565);
INSERT INTO `sys_oper_log` VALUES (60, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2024-08-18 22:19:55\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,3,114,115,1055,1058,1056,1057,1059,1060,116],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 09:32:10', 325);
INSERT INTO `sys_oper_log` VALUES (61, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2024-08-18 22:19:55\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,3,114,115,1055,1058,1056,1057,1059,1060,116,1061,1063,1067],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 09:35:34', 368);
INSERT INTO `sys_oper_log` VALUES (62, '菜单管理', 1, 'com.ruoyi.system.controller.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/user-chat/index\",\"createBy\":\"admin\",\"icon\":\"wechat\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"即时聊天\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":1061,\"path\":\"user-chat\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-20 09:36:47', 112);
INSERT INTO `sys_oper_log` VALUES (63, '在线用户', 7, 'com.ruoyi.system.controller.SysUserOnlineController.forceLogout()', 'DELETE', 1, 'admin', NULL, '/online/6cdf7341-d169-46af-ba0d-02a9b9538b26', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-25 14:12:24', 11);
INSERT INTO `sys_oper_log` VALUES (64, '菜单管理', 1, 'com.ruoyi.system.controller.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/file-management/index\",\"createBy\":\"admin\",\"icon\":\"dict\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"智能文档\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":1061,\"path\":\"file-management\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-06-25 21:39:31', 117);
INSERT INTO `sys_oper_log` VALUES (65, '用户头像', 2, 'com.ruoyi.system.controller.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/user/profile/avatar', '127.0.0.1', '', '', '{\"msg\":\"文件服务异常，请联系管理员\",\"code\":500}', 0, NULL, '2025-07-01 14:31:53', 149);
INSERT INTO `sys_oper_log` VALUES (66, '用户头像', 2, 'com.ruoyi.system.controller.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/user/profile/avatar', '127.0.0.1', '', '', '{\"msg\":\"文件服务异常，请联系管理员\",\"code\":500}', 0, NULL, '2025-07-01 14:31:54', 11);
INSERT INTO `sys_oper_log` VALUES (67, '个人信息', 2, 'com.ruoyi.system.controller.SysProfileController.updateProfile()', 'PUT', 1, 'admin', NULL, '/user/profile', '127.0.0.1', '', '{\"admin\":false,\"email\":\"<EMAIL>\",\"nickName\":\"必安\",\"params\":{},\"phonenumber\":\"15888888888\",\"sex\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-01 14:31:58', 266);
INSERT INTO `sys_oper_log` VALUES (68, '角色管理', 1, 'com.ruoyi.system.controller.SysRoleController.add()', 'POST', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createBy\":\"admin\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1061,1063,1067,1069,1070],\"params\":{},\"roleId\":6,\"roleKey\":\"Team administrator\",\"roleName\":\"团队管理员\",\"roleSort\":1,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 10:46:15', 341);
INSERT INTO `sys_oper_log` VALUES (69, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2024-08-18 22:19:55\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1061,1063,1067],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通成员\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 10:46:31', 281);
INSERT INTO `sys_oper_log` VALUES (70, '角色管理', 3, 'com.ruoyi.system.controller.SysRoleController.remove()', 'DELETE', 1, 'admin', NULL, '/role/5', '127.0.0.1', '', '{}', NULL, 1, 'fei已分配,不能删除', '2025-07-08 10:46:34', 123);
INSERT INTO `sys_oper_log` VALUES (71, '角色管理', 4, 'com.ruoyi.system.controller.SysRoleController.cancelAuthUser()', 'PUT', 1, 'admin', NULL, '/role/authUser/cancel', '127.0.0.1', '', '{\"roleId\":5,\"userId\":2}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 10:46:41', 53);
INSERT INTO `sys_oper_log` VALUES (72, '角色管理', 3, 'com.ruoyi.system.controller.SysRoleController.remove()', 'DELETE', 1, 'admin', NULL, '/role/5', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 10:46:45', 289);
INSERT INTO `sys_oper_log` VALUES (73, '角色管理', 1, 'com.ruoyi.system.controller.SysRoleController.add()', 'POST', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createBy\":\"admin\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1061,1063,1067,1069,1070],\"params\":{},\"roleId\":7,\"roleKey\":\"captain\",\"roleName\":\"团队队长\",\"roleSort\":1,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 10:48:24', 222);
INSERT INTO `sys_oper_log` VALUES (74, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2024-08-18 22:19:55\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1061,1063,1067],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"团队成员\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 10:48:35', 269);
INSERT INTO `sys_oper_log` VALUES (75, '角色管理', 1, 'com.ruoyi.system.controller.SysRoleController.add()', 'POST', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createBy\":\"admin\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1061,1063,1067,1069,1070],\"params\":{},\"roleId\":8,\"roleKey\":\"rank and file\",\"roleName\":\"普通成员\",\"roleSort\":2,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 10:49:02', 215);
INSERT INTO `sys_oper_log` VALUES (76, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/109', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:06:05', 222);
INSERT INTO `sys_oper_log` VALUES (77, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/108', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:06:09', 128);
INSERT INTO `sys_oper_log` VALUES (78, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/101', '127.0.0.1', '', '{}', '{\"msg\":\"存在下级部门,不允许删除\",\"code\":601}', 0, NULL, '2025-07-08 22:06:11', 28);
INSERT INTO `sys_oper_log` VALUES (79, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/105', '127.0.0.1', '', '{}', '{\"msg\":\"部门存在用户,不允许删除\",\"code\":601}', 0, NULL, '2025-07-08 22:06:14', 58);
INSERT INTO `sys_oper_log` VALUES (80, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/103', '127.0.0.1', '', '{}', '{\"msg\":\"部门存在用户,不允许删除\",\"code\":601}', 0, NULL, '2025-07-08 22:06:15', 62);
INSERT INTO `sys_oper_log` VALUES (81, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/102', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:06:18', 128);
INSERT INTO `sys_oper_log` VALUES (82, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/107', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:06:20', 120);
INSERT INTO `sys_oper_log` VALUES (83, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/106', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:06:21', 139);
INSERT INTO `sys_oper_log` VALUES (84, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/105', '127.0.0.1', '', '{}', '{\"msg\":\"部门存在用户,不允许删除\",\"code\":601}', 0, NULL, '2025-07-08 22:06:23', 57);
INSERT INTO `sys_oper_log` VALUES (85, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/103', '127.0.0.1', '', '{}', '{\"msg\":\"部门存在用户,不允许删除\",\"code\":601}', 0, NULL, '2025-07-08 22:06:25', 72);
INSERT INTO `sys_oper_log` VALUES (86, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/104', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:06:27', 122);
INSERT INTO `sys_oper_log` VALUES (87, '部门管理', 3, 'com.ruoyi.system.controller.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/dept/101', '127.0.0.1', '', '{}', '{\"msg\":\"存在下级部门,不允许删除\",\"code\":601}', 0, NULL, '2025-07-08 22:06:29', 32);
INSERT INTO `sys_oper_log` VALUES (88, '部门管理', 2, 'com.ruoyi.system.controller.SysDeptController.edit()', 'PUT', 1, 'admin', NULL, '/dept', '127.0.0.1', '', '{\"ancestors\":\"0\",\"children\":[],\"createBy\":\"admin\",\"createTime\":\"2024-08-18 22:19:55\",\"delFlag\":\"0\",\"deptId\":100,\"deptName\":\"智创未来\",\"email\":\"<EMAIL>\",\"leader\":\"谢志华\",\"orderNum\":0,\"params\":{},\"parentId\":0,\"phone\":\"15888888888\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:07:23', 181);
INSERT INTO `sys_oper_log` VALUES (89, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"system/dept/index\",\"createTime\":\"2024-08-18 22:19:55\",\"icon\":\"tree\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":103,\"menuName\":\"租户管理\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":1,\"path\":\"dept\",\"perms\":\"system:dept:list\",\"query\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:08:12', 695);
INSERT INTO `sys_oper_log` VALUES (90, '部门管理', 2, 'com.ruoyi.system.controller.SysDeptController.edit()', 'PUT', 1, 'admin', NULL, '/dept', '127.0.0.1', '', '{\"ancestors\":\"0,100\",\"children\":[],\"createBy\":\"admin\",\"createTime\":\"2024-08-18 22:19:55\",\"delFlag\":\"0\",\"deptId\":101,\"deptName\":\"湖南科技职业学院\",\"email\":\"<EMAIL>\",\"leader\":\"蒋校长\",\"orderNum\":1,\"params\":{},\"parentId\":100,\"phone\":\"15888888888\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:09:05', 307);
INSERT INTO `sys_oper_log` VALUES (91, '部门管理', 2, 'com.ruoyi.system.controller.SysDeptController.edit()', 'PUT', 1, 'admin', NULL, '/dept', '127.0.0.1', '', '{\"ancestors\":\"0,100,101\",\"children\":[],\"createBy\":\"admin\",\"createTime\":\"2024-08-18 22:19:55\",\"delFlag\":\"0\",\"deptId\":103,\"deptName\":\"软件学院\",\"email\":\"<EMAIL>\",\"leader\":\"江院\",\"orderNum\":1,\"params\":{},\"parentId\":101,\"phone\":\"15888888888\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:09:28', 240);
INSERT INTO `sys_oper_log` VALUES (92, '部门管理', 2, 'com.ruoyi.system.controller.SysDeptController.edit()', 'PUT', 1, 'admin', NULL, '/dept', '127.0.0.1', '', '{\"ancestors\":\"0,100,101\",\"children\":[],\"createBy\":\"admin\",\"createTime\":\"2024-08-18 22:19:55\",\"delFlag\":\"0\",\"deptId\":105,\"deptName\":\"人工智能学院\",\"email\":\"<EMAIL>\",\"leader\":\"人工智能院长\",\"orderNum\":3,\"params\":{},\"parentId\":101,\"phone\":\"15888888888\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:09:50', 257);
INSERT INTO `sys_oper_log` VALUES (93, '部门管理', 1, 'com.ruoyi.system.controller.SysDeptController.add()', 'POST', 1, 'admin', NULL, '/dept', '127.0.0.1', '', '{\"ancestors\":\"0,100\",\"children\":[],\"createBy\":\"admin\",\"deptName\":\"长沙民政\",\"email\":\"<EMAIL>\",\"leader\":\"校长\",\"orderNum\":2,\"params\":{},\"parentId\":100,\"phone\":\"13333333333\",\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:10:29', 146);
INSERT INTO `sys_oper_log` VALUES (94, '部门管理', 1, 'com.ruoyi.system.controller.SysDeptController.add()', 'POST', 1, 'admin', NULL, '/dept', '127.0.0.1', '', '{\"ancestors\":\"0,100,110\",\"children\":[],\"createBy\":\"admin\",\"deptName\":\"软件学院\",\"email\":\"<EMAIL>\",\"leader\":\"院长\",\"orderNum\":1,\"params\":{},\"parentId\":110,\"phone\":\"13333333331\",\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:10:56', 121);
INSERT INTO `sys_oper_log` VALUES (95, '用户管理', 2, 'com.ruoyi.system.controller.SysUserController.edit()', 'PUT', 1, 'admin', NULL, '/user', '127.0.0.1', '', '{\"admin\":false,\"avatar\":\"\",\"createBy\":\"admin\",\"createTime\":\"2024-08-18 22:19:55\",\"delFlag\":\"0\",\"dept\":{\"ancestors\":\"0,100,101\",\"children\":[],\"deptId\":105,\"deptName\":\"人工智能学院\",\"leader\":\"人工智能院长\",\"orderNum\":3,\"params\":{},\"parentId\":101,\"status\":\"0\"},\"deptId\":105,\"email\":\"<EMAIL>\",\"loginDate\":\"2024-08-18 22:19:55\",\"loginIp\":\"127.0.0.1\",\"nickName\":\"廖飞翔\",\"params\":{},\"phonenumber\":\"15666666666\",\"postIds\":[2],\"remark\":\"测试员\",\"roleIds\":[2,7],\"roles\":[{\"admin\":false,\"dataScope\":\"2\",\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"团队成员\",\"roleSort\":2,\"status\":\"0\"}],\"sex\":\"0\",\"status\":\"0\",\"updateBy\":\"admin\",\"userId\":2,\"userName\":\"ry\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:11:39', 610);
INSERT INTO `sys_oper_log` VALUES (96, '个人信息', 2, 'com.ruoyi.system.controller.SysProfileController.updateProfile()', 'PUT', 1, 'admin', NULL, '/user/profile', '127.0.0.1', '', '{\"admin\":false,\"email\":\"<EMAIL>\",\"nickName\":\"谢志华\",\"params\":{},\"phonenumber\":\"15888888888\",\"sex\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:12:06', 155);
INSERT INTO `sys_oper_log` VALUES (97, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2025-07-08 10:46:14\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1061,1063,1067,1069,1070],\"params\":{},\"roleId\":6,\"roleKey\":\"Team administrator\",\"roleName\":\"学校管理员\",\"roleSort\":1,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:13:16', 384);
INSERT INTO `sys_oper_log` VALUES (98, '角色管理', 2, 'com.ruoyi.system.controller.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/role', '127.0.0.1', '', '{\"admin\":false,\"createTime\":\"2025-07-08 10:48:24\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1061,1063,1067,1069,1070],\"params\":{},\"roleId\":7,\"roleKey\":\"captain\",\"roleName\":\"学院管理员\",\"roleSort\":1,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:13:29', 399);
INSERT INTO `sys_oper_log` VALUES (99, '角色管理', 3, 'com.ruoyi.system.controller.SysRoleController.remove()', 'DELETE', 1, 'admin', NULL, '/role/8', '127.0.0.1', '', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:13:39', 409);
INSERT INTO `sys_oper_log` VALUES (100, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"system/dict/index\",\"createTime\":\"2024-08-18 22:19:55\",\"icon\":\"dict\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":105,\"menuName\":\"字典管理\",\"menuType\":\"C\",\"orderNum\":6,\"params\":{},\"parentId\":1,\"path\":\"dict\",\"perms\":\"system:dict:list\",\"query\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:14:16', 116);
INSERT INTO `sys_oper_log` VALUES (101, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"system/config/index\",\"createTime\":\"2024-08-18 22:19:55\",\"icon\":\"edit\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":106,\"menuName\":\"参数设置\",\"menuType\":\"C\",\"orderNum\":7,\"params\":{},\"parentId\":1,\"path\":\"config\",\"perms\":\"system:config:list\",\"query\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:14:24', 127);
INSERT INTO `sys_oper_log` VALUES (102, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"\",\"createTime\":\"2024-08-18 22:19:55\",\"icon\":\"log\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":108,\"menuName\":\"日志管理\",\"menuType\":\"M\",\"orderNum\":9,\"params\":{},\"parentId\":1,\"path\":\"log\",\"perms\":\"\",\"query\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:14:31', 115);
INSERT INTO `sys_oper_log` VALUES (103, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"\",\"createTime\":\"2024-08-18 22:19:55\",\"icon\":\"server\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuId\":113,\"menuName\":\"Admin控制台\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":2,\"path\":\"http://localhost:9100/login\",\"perms\":\"monitor:server:list\",\"query\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:15:07', 112);
INSERT INTO `sys_oper_log` VALUES (104, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"\",\"createTime\":\"2024-08-18 22:19:55\",\"icon\":\"nacos\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuId\":112,\"menuName\":\"Nacos控制台\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":2,\"path\":\"http://localhost:8848/nacos\",\"perms\":\"monitor:nacos:list\",\"query\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:15:11', 94);
INSERT INTO `sys_oper_log` VALUES (105, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"\",\"createTime\":\"2024-08-18 22:19:55\",\"icon\":\"sentinel\",\"isCache\":\"0\",\"isFrame\":\"0\",\"menuId\":111,\"menuName\":\"Sentinel控制台\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2,\"path\":\"http://localhost:8718\",\"perms\":\"monitor:sentinel:list\",\"query\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:15:14', 112);
INSERT INTO `sys_oper_log` VALUES (106, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"monitor/job/index\",\"createTime\":\"2024-08-18 22:19:55\",\"icon\":\"job\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":110,\"menuName\":\"定时任务\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2,\"path\":\"job\",\"perms\":\"monitor:job:list\",\"query\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:15:19', 108);
INSERT INTO `sys_oper_log` VALUES (107, '代码生成', 6, 'com.ruoyi.gen.controller.GenController.importTableSave()', 'POST', 1, 'admin', NULL, '/gen/importTable', '127.0.0.1', '', '{\"tables\":\"base_conversation\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:26:14', 796);
INSERT INTO `sys_oper_log` VALUES (108, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"tool/gen/index\",\"createTime\":\"2024-08-18 22:19:55\",\"icon\":\"code\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":115,\"menuName\":\"代码生成\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":3,\"path\":\"gen\",\"perms\":\"tool:gen:list\",\"query\":\"\",\"status\":\"1\",\"updateBy\":\"admin\",\"visible\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-08 22:27:25', 182);
INSERT INTO `sys_oper_log` VALUES (109, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/file-management/index\",\"createTime\":\"2025-06-25 21:39:30\",\"icon\":\"dict\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1070,\"menuName\":\"我的文档\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":1061,\"path\":\"file-management\",\"perms\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-10 10:09:58', 101);
INSERT INTO `sys_oper_log` VALUES (110, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/file-management/index\",\"createTime\":\"2025-06-25 21:39:30\",\"icon\":\"dict\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1070,\"menuName\":\"我的文件\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":1061,\"path\":\"file-management\",\"perms\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-10 10:10:19', 71);
INSERT INTO `sys_oper_log` VALUES (111, '菜单管理', 1, 'com.ruoyi.system.controller.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/contract-analysis/index\",\"createBy\":\"admin\",\"icon\":\"dict\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"合同合约分析\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":1061,\"path\":\"contract-analysis\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-10 10:12:48', 74);
INSERT INTO `sys_oper_log` VALUES (112, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/contract-analysis/index\",\"createTime\":\"2025-07-10 10:12:48\",\"icon\":\"dict\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1071,\"menuName\":\"合同合约分析\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":1061,\"path\":\"contract-analysis\",\"perms\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-10 10:13:27', 71);
INSERT INTO `sys_oper_log` VALUES (113, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/user-chat/index\",\"createTime\":\"2025-06-20 09:36:47\",\"icon\":\"wechat\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1069,\"menuName\":\"即时聊天\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":0,\"path\":\"user-chat\",\"perms\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-10 10:13:36', 71);
INSERT INTO `sys_oper_log` VALUES (114, '菜单管理', 1, 'com.ruoyi.system.controller.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/meeting/index\",\"createBy\":\"admin\",\"icon\":\"button\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"文书会议\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"meeting\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-13 17:17:27', 142);
INSERT INTO `sys_oper_log` VALUES (115, '菜单管理', 1, 'com.ruoyi.system.controller.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/multimodal/index\",\"createBy\":\"admin\",\"icon\":\"client\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"多模态处理\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"multimodal\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-13 17:20:18', 104);
INSERT INTO `sys_oper_log` VALUES (116, '菜单管理', 1, 'com.ruoyi.system.controller.SysMenuController.add()', 'POST', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/team-management/index\",\"createBy\":\"admin\",\"icon\":\"peoples\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"团队管理\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"team-management\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-15 15:57:17', 274);
INSERT INTO `sys_oper_log` VALUES (117, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/team-management/api-testing\",\"createTime\":\"2025-07-15 15:57:16\",\"icon\":\"peoples\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1074,\"menuName\":\"团队管理\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"team-management\",\"perms\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-15 15:59:08', 103);
INSERT INTO `sys_oper_log` VALUES (118, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/team-management/indexindex\",\"createTime\":\"2025-07-15 15:57:16\",\"icon\":\"peoples\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1074,\"menuName\":\"团队管理\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"team-management\",\"perms\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-15 16:16:47', 83);
INSERT INTO `sys_oper_log` VALUES (119, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/team-management/index\",\"createTime\":\"2025-07-15 15:57:16\",\"icon\":\"peoples\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1074,\"menuName\":\"团队管理\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"team-management\",\"perms\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-15 16:24:28', 104);
INSERT INTO `sys_oper_log` VALUES (120, '用户头像', 2, 'com.ruoyi.system.controller.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/user/profile/avatar', '127.0.0.1', '', '', '{\"msg\":\"文件服务异常，请联系管理员\",\"code\":500}', 0, NULL, '2025-07-17 09:00:25', 220);
INSERT INTO `sys_oper_log` VALUES (121, '用户头像', 2, 'com.ruoyi.system.controller.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/user/profile/avatar', '127.0.0.1', '', '', '{\"msg\":\"文件服务异常，请联系管理员\",\"code\":500}', 0, NULL, '2025-07-17 09:00:26', 12);
INSERT INTO `sys_oper_log` VALUES (122, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/meeting/video\",\"createTime\":\"2025-07-13 17:17:27\",\"icon\":\"button\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1072,\"menuName\":\"文书会议\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"meeting\",\"perms\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-17 09:12:08', 157);
INSERT INTO `sys_oper_log` VALUES (123, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/meeting/index\",\"createTime\":\"2025-07-13 17:17:27\",\"icon\":\"button\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1072,\"menuName\":\"文书会议\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"meeting\",\"perms\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-17 09:20:39', 84);
INSERT INTO `sys_oper_log` VALUES (124, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/meeting/video\",\"createTime\":\"2025-07-13 17:17:27\",\"icon\":\"button\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1072,\"menuName\":\"文书会议\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"meeting\",\"perms\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-17 10:59:13', 168);
INSERT INTO `sys_oper_log` VALUES (125, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/meeting/video-test\",\"createTime\":\"2025-07-13 17:17:27\",\"icon\":\"button\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1072,\"menuName\":\"文书会议\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"meeting\",\"perms\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-17 11:00:49', 76);
INSERT INTO `sys_oper_log` VALUES (126, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/meeting/video\",\"createTime\":\"2025-07-13 17:17:27\",\"icon\":\"button\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1072,\"menuName\":\"文书会议\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"meeting\",\"perms\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-17 11:01:45', 75);
INSERT INTO `sys_oper_log` VALUES (127, '菜单管理', 2, 'com.ruoyi.system.controller.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/menu', '127.0.0.1', '', '{\"children\":[],\"component\":\"office/meeting/index\",\"createTime\":\"2025-07-13 17:17:27\",\"icon\":\"button\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1072,\"menuName\":\"文书会议\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"meeting\",\"perms\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-07-17 11:27:27', 101);

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`  (
  `post_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位名称',
  `post_sort` int(0) NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '岗位信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_post
-- ----------------------------
INSERT INTO `sys_post` VALUES (1, 'ceo', '董事长', 1, '0', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_post` VALUES (2, 'se', '项目经理', 2, '0', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_post` VALUES (3, 'hr', '人力资源', 3, '0', 'admin', '2024-08-18 22:19:55', '', NULL, '');
INSERT INTO `sys_post` VALUES (4, 'user', '普通员工', 4, '0', 'admin', '2024-08-18 22:19:55', '', NULL, '');

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int(0) NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) DEFAULT 1 COMMENT '部门树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', '2024-08-18 22:19:55', '', NULL, '超级管理员');
INSERT INTO `sys_role` VALUES (2, '团队成员', 'common', 2, '2', 1, 1, '0', '0', 'admin', '2024-08-18 22:19:55', 'admin', '2025-07-08 10:48:34', '普通角色');
INSERT INTO `sys_role` VALUES (4, '教师', 'teacher', 4, '1', 1, 1, '0', '2', 'admin', '2024-08-19 16:04:05', 'admin', '2024-08-19 16:06:50', NULL);
INSERT INTO `sys_role` VALUES (5, 'fei', 'qw', 2, '1', 0, 1, '1', '2', 'admin', '2025-06-20 09:29:56', 'admin', '2025-06-20 09:31:25', NULL);
INSERT INTO `sys_role` VALUES (6, '学校管理员', 'Team administrator', 1, '1', 1, 1, '0', '0', 'admin', '2025-07-08 10:46:14', 'admin', '2025-07-08 22:13:16', NULL);
INSERT INTO `sys_role` VALUES (7, '学院管理员', 'captain', 1, '1', 1, 1, '0', '0', 'admin', '2025-07-08 10:48:24', 'admin', '2025-07-08 22:13:29', NULL);
INSERT INTO `sys_role` VALUES (8, '普通成员', 'rank and file', 2, '1', 1, 1, '0', '2', 'admin', '2025-07-08 10:49:02', '', NULL, NULL);

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `role_id` bigint(0) NOT NULL COMMENT '角色ID',
  `dept_id` bigint(0) NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和部门关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------
INSERT INTO `sys_role_dept` VALUES (2, 100);
INSERT INTO `sys_role_dept` VALUES (2, 101);
INSERT INTO `sys_role_dept` VALUES (2, 105);

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint(0) NOT NULL COMMENT '角色ID',
  `menu_id` bigint(0) NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (2, 1061);
INSERT INTO `sys_role_menu` VALUES (2, 1063);
INSERT INTO `sys_role_menu` VALUES (2, 1067);
INSERT INTO `sys_role_menu` VALUES (6, 1061);
INSERT INTO `sys_role_menu` VALUES (6, 1063);
INSERT INTO `sys_role_menu` VALUES (6, 1067);
INSERT INTO `sys_role_menu` VALUES (6, 1069);
INSERT INTO `sys_role_menu` VALUES (6, 1070);
INSERT INTO `sys_role_menu` VALUES (7, 1061);
INSERT INTO `sys_role_menu` VALUES (7, 1063);
INSERT INTO `sys_role_menu` VALUES (7, 1067);
INSERT INTO `sys_role_menu` VALUES (7, 1069);
INSERT INTO `sys_role_menu` VALUES (7, 1070);

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint(0) DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime(0) DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 103, 'admin', '谢志华', '00', '<EMAIL>', '15888888888', '0', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2024-08-20 15:56:18', 'admin', '2024-08-18 22:19:55', '', '2025-07-08 22:12:06', '管理员');
INSERT INTO `sys_user` VALUES (2, 105, 'ry', '廖飞翔', '00', '<EMAIL>', '15666666666', '0', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2024-08-18 22:19:55', 'admin', '2024-08-18 22:19:55', 'admin', '2025-07-08 22:11:38', '测试员');
INSERT INTO `sys_user` VALUES (3, 100, 'fei', 'fei', '00', '', '15526497836', '0', '', '$2a$10$CcF19WOoMMZyTXRn0grHXu.uBUMvA68mgLTRrKPsBBaX/VjxfHNbC', '0', '0', '', NULL, 'admin', '2025-06-20 09:31:59', '', NULL, NULL);

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post`  (
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `post_id` bigint(0) NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`, `post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户与岗位关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_post
-- ----------------------------
INSERT INTO `sys_user_post` VALUES (1, 1);
INSERT INTO `sys_user_post` VALUES (2, 2);
INSERT INTO `sys_user_post` VALUES (3, 4);

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `role_id` bigint(0) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户和角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 1);
INSERT INTO `sys_user_role` VALUES (2, 2);
INSERT INTO `sys_user_role` VALUES (2, 7);
INSERT INTO `sys_user_role` VALUES (3, 2);

-- ----------------------------
-- Table structure for vector_search_history
-- ----------------------------
DROP TABLE IF EXISTS `vector_search_history`;
CREATE TABLE `vector_search_history`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `query` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '查询内容',
  `user_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户ID',
  `timestamp` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '时间戳标识',
  `result_count` int(0) NOT NULL DEFAULT 0 COMMENT '结果数量',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '向量查询历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for video_analysis
-- ----------------------------
DROP TABLE IF EXISTS `video_analysis`;
CREATE TABLE `video_analysis`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '任务唯一标识',
  `user_id` int(0) NOT NULL COMMENT '提交任务的用户ID',
  `file_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '视频文件的唯一标识',
  `initial_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '初始识别生成的文本内容',
  `analysis_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '最终分析生成的文本内容',
  `failure_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '任务失败的具体原因',
  `status` tinyint(0) NOT NULL DEFAULT 0 COMMENT '任务状态: 0未开始 1分析中 2成功 3失败',
  `upload_time` datetime(0) NOT NULL COMMENT '视频提交的时间戳',
  `start_time` datetime(0) DEFAULT NULL COMMENT '分析开始处理的时间',
  `finish_time` datetime(0) DEFAULT NULL COMMENT '任务结束的时间(成功/失败)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视频分析任务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of video_analysis
-- ----------------------------
INSERT INTO `video_analysis` VALUES (3, 1, '712', 'abc', NULL, NULL, 0, '2025-07-01 12:07:44', NULL, NULL);
INSERT INTO `video_analysis` VALUES (4, 1, '713', 'abc', NULL, NULL, 0, '2025-07-01 12:09:28', NULL, NULL);
INSERT INTO `video_analysis` VALUES (5, 1, '714', 'abc', NULL, NULL, 0, '2025-07-01 12:12:18', NULL, NULL);
INSERT INTO `video_analysis` VALUES (6, 1, '715', 'abc', NULL, NULL, 0, '2025-07-01 13:22:55', NULL, NULL);
INSERT INTO `video_analysis` VALUES (7, 1, '752', 's', NULL, NULL, 1, '2025-07-04 10:50:02', NULL, NULL);
INSERT INTO `video_analysis` VALUES (8, 1, '753', '【视频识别开始】【片段1】视频展示了一位街头表演者在夜晚的城市街道上进行演出，周围点缀着霓虹灯饰，他手持设备并自述正身处一条陌生的街道。\n【片段2】视频展示了一位男子在夜晚的街头进行直播表演，他正在演唱《安河桥》，并使用手机支架辅助拍摄，背景是装饰灯光闪烁的城市夜景。\n【片段3】视频展示了一位男子在夜晚的城市街头进行直播表演，他手持麦克风站在音响设备前，周围聚集了许多听众，背景是装饰着彩灯的繁华街道。\n【片段4】视频展示了城市夜景中街头艺人的表演与校园学生前往晚自习的场景，通过对比日常生活中的不同片段，传达了平凡生活中蕴含的小确幸与趣味。\n【片段5】视频展示了某学校药学楼的外观，画面中可见绿色草坪、现代建筑以及穿行的学生。\n【片段6】视频展示了校园内的软件大楼及其周边环境，包括湖泊、绿化区域和行走的学生，突出了该建筑作为教学或科研设施的功能。\n【片段7】视频记录了一位学生背着书包进入宿舍的过程，并通过字幕表达了篮球在其生活中的重要性。\n【片段8】视频展示了一个年轻人在夜晚的户外篮球场上投篮得分的瞬间，通过慢动作镜头捕捉了篮球入框的精准动作与清脆声响，营造出专注而充满活力的运动氛围。\n【片段9】视频展示了一位年轻人在夜晚的篮球场上独自投篮，通过中英文字幕传达出对朋友陪伴的怀念与运动带来的疲惫感。\n【片段10】视频展示了两位年轻人在夜晚的户外篮球场进行投篮练习，通过中英文字幕展现了他们对球技进步的讨论、玩笑互动以及未来组建球队的规划，营造出轻松愉悦的运动氛围。\n【片段11】一群朋友在夜晚的户外篮球场上打球，享受着微风吹拂下的运动乐趣与放松时光。\n【片段12】视频记录了某人周日从宿舍出发前往校外理发的过程，通过开门、下楼等动作展示其离校路线及校园环境。\n【片段13】视频记录了一个人在校园附近的理发店剪完头发后返回宿舍的过程，展示了室内外场景及剪发后的整洁环境。\n【片段14】视频分析失败: java.util.concurrent.TimeoutException: The source did not signal an event for 15 seconds and has been terminated.\n【视频识别结束】【音频识别开始】I\'m on a, you know, street right now.。\nAnd I see.。\nI\'m on a, you know, street right now, and I see.。\nI see again.。\nHe sang about the Anhe bread, which was quite good.。\nHe also has a mobile phone in front of him, so he should be broadcasting live.。\nThey are a.。\nLot of people listen.。\nTo insert.。\nAnd there are aTo insert.。\nAnd they are a.。\nThis little boy in a beef.。\nWhich is quite interesting.。\nI\'m going to evening stuff, starting now.。\nThis is our school\'s basketball coach.。\nThis is the building of the school.。\nThe school clinic is also here.。\nThere is also a lake, next to it.。\nThis is the surfer, will boarding.。\nAnd this also the place where I want to evening self study are right at the destination, goodbye.。\nBasketball is a part of my life.。\nThe following movement on the court.。\nThe sound of the bicycle entering the box.。\nAnd most importantly,And most importantly, The company of friends.。\nThe company of friends.。\nWhen we were tired from playing, we just sit on the ground randomly and start training, talking about whose skills have improved or jogging around, playing whether we should form a basketball team in the future or something.。\nThe gentle breeze blows by, taking away our sweat and leaving only pure happiness and relaxation.。\n放心的。\nI set out from the dormitory.。\nTo have my hair.。\nGet outside the school.。\nToday, it write.。\nToday, it is writer.。\nAfter cutting my hair, I went back to the dormitory.。\nIt was another ordinary weekend.。【音频识别结束】', '【会议分析开始】【文本片段1】根据提供的会议音频转写文本，可以看出这段文字似乎并不是一个正式的会议记录，而更像是个人的随笔或日记。因此，直接应用上述分析维度可能并不完全合适。不过，为了演示如何进行这样的分析，我将尝试从给述的文本中提取一些结构化洞察，并按照要求输出报告。请注意，由于原文本内容与典型会议场景不符，以下分析将基于假设性处理。\n\n### [决策摘要]\n- 无明确决策事项记录。\n- 文档主要描述了作者日常活动及感受。\n\n### [行动项表]（责任人/任务/DDL）\n- **无**：文档未提及具体待办事项。\n\n### [争议问题清单]\n- **无**：文档内没有明显的观点冲突或议题讨论。\n\n### [术语索引表]\n- **无特定行业术语**：文档中未出现需要特别标注的专业术语。\n\n### [风险预警]\n- **模糊承诺**：“尽量完成”、“可能延迟”等表达方式未在文中出现。\n- **资源缺口表述**：文档中未提到任何关于人力、预算或技术瓶颈的问题。\n\n### [保留原始口语化表达中的有效信息]\n- \"I\'m on a, you know, street right now.\" 表达了说话者当前的位置。\n- \"And I see.\" 可能表示说话者对某事有了新的理解或观察。\n- 提到了“Anhe面包”，可能是某个品牌或者地方特色食品。\n- 描述了一个人正在直播的情景。\n- 记录了学校的一些设施如篮球场、校医室以及附近的湖。\n- 强调了朋友陪伴的重要性，特别是在运动后休息时分享彼此的进步。\n- 最后提到了周末的一次理发经历。\n\n### 分析置信度\n- **低置信度提示**：由于文本性质较为随意且缺乏结构性，对于某些部分的理解可能存在偏差，特别是涉及个人感受的部分，建议进一步核实上下文以提高准确性。\n\n---\n\n请确认是否需要针对更具体的会议内容或者其他类型的文本进行类似分析？如果提供了更贴合会议场景的材料，可以更好地展示出各个维度的分析结果。\n【会议分析结束】【日程分析开始】【日程片段1】[\n  {\n    \"title\": \"篮球教练介绍\",\n    \"description\": \"在学校里遇到了篮球教练，他正在直播，很多人在听。然后去学校诊所附近湖边的建筑，计划在那里进行晚自习。\",\n    \"start_time\": \"2025-07-04 18:00:00\",\n    \"owner_type\": \"个人\",\n    \"repeat_rule\": \"一次性\",\n    \"remind_time\": \"提前十分钟\",\n    \"event_status\": \"1\"\n  },\n  {\n    \"title\": \"剪发\",\n    \"description\": \"从宿舍出发去校外剪发，剪完后返回宿舍。\",\n    \"start_time\": \"2025-07-04 16:00:00\",\n    \"owner_type\": \"个人\",\n    \"repeat_rule\": \"一次性\",\n    \"remind_time\": \"提前十分钟\",\n    \"event_status\": \"1\"\n  }\n]\n【日程分析结束】', NULL, 2, '2025-07-04 10:53:01', NULL, '2025-07-04 15:44:47');
INSERT INTO `video_analysis` VALUES (9, 1, '801', '12', NULL, NULL, 0, '2025-07-16 20:31:55', NULL, NULL);
INSERT INTO `video_analysis` VALUES (10, 1, '802', '12', NULL, NULL, 0, '2025-07-16 20:32:01', NULL, NULL);
INSERT INTO `video_analysis` VALUES (11, 1, '803', '1', NULL, NULL, 0, '2025-07-16 20:56:30', NULL, NULL);
INSERT INTO `video_analysis` VALUES (12, 1, '804', 'auto_1752672682241', NULL, NULL, 0, '2025-07-16 21:31:24', NULL, NULL);
INSERT INTO `video_analysis` VALUES (13, 1, '805', 'auto_1752672705478', NULL, NULL, 0, '2025-07-16 21:31:46', NULL, NULL);
INSERT INTO `video_analysis` VALUES (14, 1, '806', 'auto_1752674082240', NULL, NULL, 0, '2025-07-16 21:54:43', NULL, NULL);
INSERT INTO `video_analysis` VALUES (15, 1, '807', '【视频识别开始】视频切分失败，未生成任何片段【视频识别结束】【音频识别开始】音频分析失败: Cannot run program \"ffmpeg\": CreateProcess error=2, 系统找不到指定的文件。【音频识别结束】', '【会议分析开始】【会议分析结束】【日程分析开始】【日程分析结束】', NULL, 2, '2025-07-16 22:47:26', NULL, '2025-07-16 22:47:32');
INSERT INTO `video_analysis` VALUES (16, 1, '818', '【视频识别开始】视频切分失败，未生成任何片段【视频识别结束】【音频识别开始】音频分析失败: Cannot run program \"ffmpeg\": CreateProcess error=2, 系统找不到指定的文件。【音频识别结束】', '【会议分析开始】【会议分析结束】【日程分析开始】【日程分析结束】', NULL, 2, '2025-07-18 10:29:10', NULL, '2025-07-18 10:29:16');

-- ----------------------------
-- Table structure for wenshu-chat_records
-- ----------------------------
DROP TABLE IF EXISTS `wenshu-chat_records`;
CREATE TABLE `wenshu-chat_records`  (
  `id` bigint unsigned NOT NULL COMMENT '消息ID',
  `session_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '会话ID(MD5哈希: LEAST(user1,user2)-GREATEST(user1,user2))',
  `sender_id` bigint unsigned NOT NULL COMMENT '发送人ID',
  `receiver_id` bigint unsigned NOT NULL COMMENT '接收人ID',
  `send_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '发送时间(毫秒精度)',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息内容',
  `content_type` enum('text','image','video','file') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'text' COMMENT '消息类型',
  `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读(0未读,1已读)',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_session`(`session_id`) USING BTREE,
  INDEX `idx_sender`(`sender_id`) USING BTREE,
  INDEX `idx_receiver`(`receiver_id`) USING BTREE,
  INDEX `idx_session_time`(`session_id`, `send_time`) USING BTREE COMMENT '会话消息时序索引'
) ENGINE = InnoDB AUTO_INCREMENT = 150 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '聊天记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of wenshu-chat_records
-- ----------------------------
INSERT INTO `wenshu-chat_records` VALUES (136, '2fe85fe29bb15ed105ea3ef409564c28', 1008, 1002, '2025-06-27 16:02:06.722', '你好呀', 'text', 0);
INSERT INTO `wenshu-chat_records` VALUES (137, '2fe85fe29bb15ed105ea3ef409564c28', 1002, 1008, '2025-06-27 16:02:27.221', 'video/2025/06/27/1751011347215_d5ae4cfacd1147c5884c5f6e6727dbaa.mp4', 'video', 0);
INSERT INTO `wenshu-chat_records` VALUES (138, '2fe85fe29bb15ed105ea3ef409564c28', 1008, 1002, '2025-06-27 16:02:38.252', 'image/2025/06/27/1751011358247_05250f120c1d4093b02c02f94b7a6dab.png', 'image', 0);
INSERT INTO `wenshu-chat_records` VALUES (139, '2fe85fe29bb15ed105ea3ef409564c28', 1002, 1008, '2025-06-27 16:03:03.155', 'file/2025/06/27/1751011383153_0b29af01924446e7ab23e4913b319dc2.txt', 'file', 0);
INSERT INTO `wenshu-chat_records` VALUES (140, '2fe85fe29bb15ed105ea3ef409564c28', 1002, 1008, '2025-06-27 16:03:17.378', '年后', 'text', 0);
INSERT INTO `wenshu-chat_records` VALUES (141, '2fe85fe29bb15ed105ea3ef409564c28', 1002, 1008, '2025-06-27 16:04:43.994', '你好呀', 'text', 0);
INSERT INTO `wenshu-chat_records` VALUES (142, '2fe85fe29bb15ed105ea3ef409564c28', 1002, 1008, '2025-06-27 16:49:34.691', '你好', 'text', 0);
INSERT INTO `wenshu-chat_records` VALUES (143, '2fe85fe29bb15ed105ea3ef409564c28', 1002, 1008, '2025-06-27 19:38:42.219', '三大', 'text', 0);
INSERT INTO `wenshu-chat_records` VALUES (144, '2fe85fe29bb15ed105ea3ef409564c28', 1002, 1008, '2025-06-27 19:42:40.784', '你好呀', 'text', 0);
INSERT INTO `wenshu-chat_records` VALUES (145, '2fe85fe29bb15ed105ea3ef409564c28', 1008, 1002, '2025-06-27 22:17:11.493', 'file/2025/06/27/1751033831488_289a916c6e664b15a67964e7eb2efa8d.png', 'file', 0);
INSERT INTO `wenshu-chat_records` VALUES (146, '2fe85fe29bb15ed105ea3ef409564c28', 1002, 1008, '2025-07-08 22:17:55.353', '你好', 'text', 0);
INSERT INTO `wenshu-chat_records` VALUES (147, '2fe85fe29bb15ed105ea3ef409564c28', 1008, 1002, '2025-07-10 21:38:30.624', '你好', 'text', 0);
INSERT INTO `wenshu-chat_records` VALUES (148, '2fe85fe29bb15ed105ea3ef409564c28', 1008, 1002, '2025-07-10 21:38:54.866', '你好', 'text', 0);
INSERT INTO `wenshu-chat_records` VALUES (149, '2fe85fe29bb15ed105ea3ef409564c28', 1008, 1002, '2025-07-10 21:39:15.580', 'file/2025/07/10/1752154755573_17cc61f4230c4b78b6c2eb43d8196b2f.png', 'file', 0);
INSERT INTO `wenshu-chat_records` VALUES (150, '2fe85fe29bb15ed105ea3ef409564c28', 1002, 1008, '2025-07-10 21:48:48.801', '你好\r\n我是廖飞翔', 'text', 0);

-- ----------------------------
-- Table structure for wenshu-friend_list
-- ----------------------------
DROP TABLE IF EXISTS `wenshu-friend_list`;
CREATE TABLE `wenshu-friend_list`  (
  `id` bigint unsigned NOT NULL COMMENT '好友关系ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `friend_id` bigint unsigned NOT NULL COMMENT '好友用户ID',
  `status` enum('active','deleted') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'active' COMMENT '关系状态: active(正常好友), deleted(已删除)',
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '关系建立时间',
  `updated_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最后更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_user_friend`(`user_id`, `friend_id`) USING BTREE,
  INDEX `idx_user`(`user_id`) USING BTREE,
  INDEX `idx_friend`(`friend_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '好友关系表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of wenshu-friend_list
-- ----------------------------
INSERT INTO `wenshu-friend_list` VALUES (1, 1017, 1004, 'active', '2025-06-06 04:23:42', '2025-06-26 12:38:42');
INSERT INTO `wenshu-friend_list` VALUES (2, 1020, 1004, 'active', '2025-06-07 22:37:43', '2025-06-26 12:38:43');
INSERT INTO `wenshu-friend_list` VALUES (3, 1019, 1005, 'active', '2025-06-20 15:05:43', '2025-06-26 12:38:43');
INSERT INTO `wenshu-friend_list` VALUES (4, 1020, 1011, 'active', '2025-06-23 17:57:43', '2025-06-26 12:38:43');
INSERT INTO `wenshu-friend_list` VALUES (5, 1020, 1016, 'active', '2025-06-08 08:57:43', '2025-06-26 12:38:43');
INSERT INTO `wenshu-friend_list` VALUES (6, 1002, 1008, 'active', '2025-06-23 10:05:43', '2025-06-26 12:38:43');
INSERT INTO `wenshu-friend_list` VALUES (7, 1006, 1015, 'active', '2025-06-08 00:56:43', '2025-06-26 12:38:43');
INSERT INTO `wenshu-friend_list` VALUES (8, 1020, 1009, 'active', '2025-06-02 14:28:44', '2025-06-26 12:38:44');
INSERT INTO `wenshu-friend_list` VALUES (9, 1010, 1018, 'active', '2025-06-07 17:38:44', '2025-06-26 12:38:44');
INSERT INTO `wenshu-friend_list` VALUES (10, 1007, 1013, 'active', '2025-06-18 12:57:44', '2025-06-26 12:38:44');
INSERT INTO `wenshu-friend_list` VALUES (11, 1001, 1010, 'active', '2025-06-26 09:51:44', '2025-06-26 12:38:44');
INSERT INTO `wenshu-friend_list` VALUES (12, 1016, 1006, 'active', '2025-06-09 19:17:44', '2025-06-26 12:38:44');
INSERT INTO `wenshu-friend_list` VALUES (13, 1017, 1009, 'active', '2025-06-26 11:18:44', '2025-06-26 12:38:44');
INSERT INTO `wenshu-friend_list` VALUES (14, 1008, 1006, 'active', '2025-06-20 08:53:45', '2025-06-26 12:38:45');
INSERT INTO `wenshu-friend_list` VALUES (15, 1015, 1017, 'active', '2025-06-03 08:57:45', '2025-06-26 12:38:45');
INSERT INTO `wenshu-friend_list` VALUES (16, 1008, 1013, 'active', '2025-06-04 14:26:45', '2025-06-26 12:38:45');
INSERT INTO `wenshu-friend_list` VALUES (17, 1002, 1009, 'active', '2025-06-10 11:55:45', '2025-06-26 12:38:45');
INSERT INTO `wenshu-friend_list` VALUES (18, 1012, 1017, 'active', '2025-06-11 03:30:45', '2025-06-26 12:38:45');
INSERT INTO `wenshu-friend_list` VALUES (19, 1010, 1015, 'active', '2025-06-18 06:33:45', '2025-06-26 12:38:45');
INSERT INTO `wenshu-friend_list` VALUES (20, 1010, 1019, 'active', '2025-06-18 21:45:45', '2025-06-26 12:38:45');
INSERT INTO `wenshu-friend_list` VALUES (21, 1012, 1013, 'active', '2025-06-01 13:51:46', '2025-06-26 12:38:46');
INSERT INTO `wenshu-friend_list` VALUES (22, 1014, 1018, 'active', '2025-06-05 02:05:46', '2025-06-26 12:38:46');
INSERT INTO `wenshu-friend_list` VALUES (23, 1017, 1002, 'active', '2025-05-29 08:37:46', '2025-06-26 12:38:46');
INSERT INTO `wenshu-friend_list` VALUES (24, 1012, 1007, 'active', '2025-06-08 09:08:46', '2025-06-26 12:38:46');
INSERT INTO `wenshu-friend_list` VALUES (25, 1017, 1013, 'active', '2025-06-21 17:43:46', '2025-06-26 12:38:46');
INSERT INTO `wenshu-friend_list` VALUES (26, 1015, 1016, 'active', '2025-06-09 16:46:46', '2025-06-26 12:38:46');
INSERT INTO `wenshu-friend_list` VALUES (27, 1008, 1003, 'active', '2025-06-15 22:49:47', '2025-06-26 12:38:47');
INSERT INTO `wenshu-friend_list` VALUES (28, 1019, 1001, 'active', '2025-05-29 14:38:47', '2025-06-26 12:38:47');
INSERT INTO `wenshu-friend_list` VALUES (29, 1006, 1018, 'active', '2025-06-15 00:56:47', '2025-06-26 12:38:47');
INSERT INTO `wenshu-friend_list` VALUES (30, 1005, 1016, 'active', '2025-05-31 19:02:47', '2025-06-26 12:38:47');
INSERT INTO `wenshu-friend_list` VALUES (31, 1008, 1002, 'active', '2025-06-27 15:32:15', '2025-06-27 15:32:15');

-- ----------------------------
-- Table structure for wenshu_audit_log
-- ----------------------------
DROP TABLE IF EXISTS `wenshu_audit_log`;
CREATE TABLE `wenshu_audit_log`  (
  `log_id` int(0) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `audit_id` int(0) DEFAULT NULL COMMENT '关联审核ID',
  `operation_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '操作内容描述',
  `operator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`log_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统操作日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of wenshu_audit_log
-- ----------------------------
INSERT INTO `wenshu_audit_log` VALUES (1, 21, '拒绝申请', '谢志华');

-- ----------------------------
-- Table structure for wenshu_calendar_event
-- ----------------------------
DROP TABLE IF EXISTS `wenshu_calendar_event`;
CREATE TABLE `wenshu_calendar_event`  (
  `event_id` int(0) NOT NULL AUTO_INCREMENT COMMENT '日程ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '日程标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '日程描述',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `start_time` datetime(0) DEFAULT NULL COMMENT '日程开始时间',
  `creator_id` int(0) DEFAULT NULL COMMENT '创建者ID',
  `owner_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '所有者类型（个人/团队）',
  `repeat_rule` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '重复规则（每天重复/一次性）',
  `remind_time` datetime(0) DEFAULT NULL COMMENT '提醒时间（分钟，默认提前10分钟）',
  `event_status` tinyint(0) DEFAULT 1 COMMENT '日程状态（0-取消,1-正常）',
  `address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '地点',
  PRIMARY KEY (`event_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 80 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '日程主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of wenshu_calendar_event
-- ----------------------------
INSERT INTO `wenshu_calendar_event` VALUES (2, '开会', '下个月下午5点开会，地点在明行喽', '2025-06-17 10:29:59', '2025-06-19 15:29:59', 1, '团队', '一次性', '2025-06-28 11:27:18', 0, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (3, 'aa1', '下个月下午5点开会，地点在明行喽', '2025-06-19 12:16:20', '2025-06-18 10:00:00', 1, '个人', '一次性', '2025-06-25 20:57:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (5, '开会', '下个月下午5点开会，地点在明行喽', '2025-06-19 15:58:37', '2025-06-20 09:07:00', 0, '团队', '一次性', '2025-06-20 08:57:00', 0, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (6, '开会', '下个月下午5点开会，地点在明行喽', '2025-06-19 21:17:25', '2025-06-19 21:17:27', 1, '团队', NULL, '2025-06-17 20:03:18', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (8, '213', '1212312', '2025-06-19 21:31:32', '2025-06-21 21:21:00', 1, '个人', '一次性', '2025-06-21 13:11:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (10, '21312', '1231231', '2025-06-19 21:34:50', '2025-06-20 08:44:00', 0, '个人', '一次性', '2025-06-20 08:34:00', 0, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (11, '1231', '123', '2025-06-19 21:35:02', '2025-06-23 22:02:00', 1, '个人', '一次性', '2025-06-23 13:52:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (12, '213', '1231', '2025-06-19 21:35:12', '2025-06-25 21:21:00', 1, '个人', '一次性', '2025-06-25 13:11:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (13, '231', '123', '2025-06-19 21:35:31', '2025-06-17 01:23:00', 1, '个人', '一次性', '2025-06-16 17:13:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (14, '2', '2', '2025-06-19 21:35:43', '2025-06-20 00:00:00', 1, '个人', '一次性', '2025-06-19 22:25:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (15, '312', '12312', '2025-06-19 21:35:59', '2025-06-19 03:03:00', 1, '个人', '一次性', '2025-06-18 18:53:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (16, '312', '123', '2025-06-19 21:36:24', '2025-06-17 23:59:00', 1, '个人', '一次性', '2025-06-17 15:49:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (18, '213', '1', '2025-06-19 23:56:04', '2025-06-12 21:21:00', 1, '个人', '一次性', '2025-06-12 13:11:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (19, '开户', '开谢志华的户', '2025-06-19 23:58:35', '2025-06-11 23:11:00', 1, '个人', '一次性', '2025-06-11 15:01:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (20, 'dasa\'s\'da', 'da\'s', '2025-06-19 23:59:52', '2025-06-10 23:11:00', 1, '个人', '一次性', '2025-06-10 15:01:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (21, '123', '123', '2025-06-20 00:00:19', '2025-06-09 22:02:00', 1, '个人', '一次性', '2025-06-09 13:52:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (22, '231', '123', '2025-06-20 00:00:30', '2025-06-08 22:02:00', 1, '个人', '一次性', '2025-06-08 13:52:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (23, '321', '123', '2025-06-20 00:00:37', '2025-06-06 12:02:00', 1, '个人', '一次性', '2025-06-06 03:52:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (24, '22', '22', '2025-06-20 00:01:14', '2025-06-05 22:22:00', 1, '团队', '一次性', '2025-06-05 14:12:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (25, '1', '11', '2025-06-20 00:01:48', '2025-06-04 11:01:00', 1, '个人', '一次性', '2025-06-20 00:28:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (26, '231', '1231', '2025-06-20 00:16:14', '2025-06-02 12:01:00', 1, '个人', '一次性', '2025-06-02 03:51:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (27, '1231', '21231', '2025-06-20 00:29:25', '2025-06-19 12:21:00', 1, '个人', '一次性', '2025-06-19 04:11:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (28, '231', '213', '2025-06-20 00:38:07', '2025-06-24 23:01:00', 1, '个人', '一次性', '2025-06-24 14:51:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (29, '21', '213', '2025-06-20 00:41:01', '2025-06-03 21:03:00', 1, '个人', '一次性', '2025-06-03 12:53:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (30, '12312', '23131', '2025-06-20 00:43:19', '2025-06-27 23:01:00', 1, '个人', '一次性', '2025-06-27 14:51:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (31, '11', '1', '2025-06-20 00:44:53', '2025-06-13 11:01:00', 1, '个人', '一次性', '2025-06-13 02:51:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (32, '213', '213', '2025-06-20 00:47:09', '2025-06-26 15:01:00', 1, '个人', '一次性', '2025-06-26 13:52:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (33, '1231', '121', '2025-06-20 00:50:32', '2025-06-15 12:01:00', 1, '个人', '一次性', '2025-06-15 03:51:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (34, '321', '123', '2025-06-20 00:51:58', '2025-06-22 21:12:00', 1, '个人', '一次性', '2025-06-22 13:02:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (35, '312', '123', '2025-06-20 00:54:21', '2025-06-30 12:12:00', 1, '个人', '一次性', '2025-06-30 12:02:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (37, '12312', '12312', '2025-06-20 01:07:48', '2025-06-20 01:17:00', 0, '个人', '一次性', '2025-06-20 01:07:00', 0, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (38, '231', '21312', '2025-06-20 01:14:03', '2025-06-20 01:25:00', 0, '个人', '一次性', '2025-06-20 01:15:00', 0, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (39, '213', '12312', '2025-06-20 01:52:20', '2025-06-20 02:02:00', 0, '个人', '一次性', '2025-06-20 01:52:00', 0, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (40, '222', '222', '2025-06-20 08:46:10', '2025-06-20 05:57:00', 1, '个人', '一次性', '2025-06-20 05:47:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (41, '22', '22', '2025-06-20 08:51:33', '2025-06-20 09:02:00', 0, '个人', '一次性', '2025-06-20 08:52:00', 0, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (42, '3242', '432', '2025-06-20 08:58:26', '2025-06-20 09:09:00', 0, '个人', '一次性', '2025-06-20 08:59:00', 0, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (43, '323', '13211', '2025-06-20 09:01:47', '2025-06-20 09:11:00', 0, '个人', '一次性', '2025-06-20 09:01:00', 0, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (44, '撒', '21', '2025-06-20 09:04:27', '2025-06-20 09:14:00', 0, '个人', '一次性', '2025-06-20 09:04:00', 0, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (45, '213', '2131', '2025-06-20 09:11:54', '2025-06-20 09:22:00', 0, '个人', '一次性', '2025-06-20 09:12:00', 0, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (47, '请为我创建一个日程', '请为我创建一个日程', '2025-06-20 09:37:47', '2025-06-20 09:56:00', 0, '个人', '一次性', '2025-06-20 09:46:00', 0, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (48, '今天答辩', '答辩', '2025-06-20 09:40:48', '2025-06-20 09:51:00', 0, '个人', '一次性', '2025-06-20 09:41:00', 0, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (49, '上厕所', '待会记得上厕所', '2025-06-20 13:11:07', '2025-06-21 17:39:00', 0, '个人', '一次性', '2025-06-21 17:29:00', 0, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (52, '123', '123', '2025-06-21 18:37:17', '2025-06-14 02:12:00', 1, '团队', '一次性', '2025-06-14 02:02:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (53, '1231', '213', '2025-06-21 18:37:23', '2025-07-04 22:02:00', 1, '团队', '一次性', '2025-07-04 21:52:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (54, '开会', '下个月下午5点开会，地点在明行喽', NULL, NULL, 1, '团队', NULL, '2025-06-17 20:03:18', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (55, '开会', '下个月下午5点开会，地点在明行喽', NULL, NULL, 1, '团队', NULL, '2025-06-17 20:03:18', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (56, '开会', '下个月下午5点开会，地点在明行喽', NULL, NULL, 1, '团队', NULL, '2025-06-17 20:03:18', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (57, '开会', '下个月下午5点开会，地点在明行喽', NULL, NULL, 1, '团队', NULL, '2025-06-17 20:03:18', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (58, '123', '213', NULL, '2025-06-28 12:33:00', 1, '个人', '一次性', '2025-06-28 12:23:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (59, '312', '1231', NULL, '2025-07-03 12:12:00', 1, '个人', '一次性', '2025-07-03 12:02:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (60, '12321', '123', NULL, '2025-07-02 12:12:00', 1, '团队', '一次性', '2025-07-02 12:02:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (61, '季度业绩回顾', '项目会议', NULL, '2023-11-29 10:00:00', 0, '团队', '一次性', '2023-11-29 09:50:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (62, '季度业绩回顾', '项目会议，主题为季度业绩回顾', NULL, '2023-11-29 10:00:00', 0, '团队', '一次性', '2023-11-29 09:50:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (63, '季度业绩回顾', '项目会议，主题为季度业绩回顾', NULL, '2023-11-29 10:00:00', 0, '团队', '一次性', '2023-11-29 09:50:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (64, '季度业绩回顾', '项目会议，主题为季度业绩回顾', NULL, '2023-11-29 10:00:00', 0, '团队', '一次性', '2023-11-29 09:50:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (65, '季度业绩回顾', '项目会议，主题为季度业绩回顾', NULL, '2023-11-29 10:00:00', 0, '团队', '一次性', '2023-11-29 09:50:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (66, '季度业绩回顾', '项目会议，主题是季度业绩回顾', NULL, '2023-11-29 10:00:00', 0, '团队', '一次性', '2023-11-29 09:50:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (67, '季度业绩回顾', '项目会议，主题是季度业绩回顾', NULL, '2025-06-27 10:00:00', 0, '团队', '一次性', '2025-06-27 09:50:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (68, '季度业绩回顾', '项目会议', '2025-06-26 16:20:11', '2025-06-27 10:00:00', 1, '团队', '一次性', '2025-06-27 09:50:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (69, '季度业绩回顾', '项目会议', '2025-06-26 16:22:13', '2025-06-27 10:00:00', 1, '团队', '一次性', '2025-06-27 09:50:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (70, '季度业绩回顾', '项目会议', '2025-06-26 16:24:29', '2025-06-27 10:00:00', 1, '团队', '一次性', '2025-06-27 09:50:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (71, '季度业绩回顾', '项目会议', '2025-06-26 16:26:39', '2025-06-28 22:00:00', 1, '团队', '一次性', '2025-06-28 21:50:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (72, '21', '1231', NULL, '2025-06-27 12:31:00', 1, '个人', '一次性', '2025-06-27 12:21:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (73, '开会', '到行政楼开会', '2025-06-27 22:24:22', '2025-06-28 15:00:00', 1, '个人', '一次性', '2025-06-28 14:50:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (74, '开会', '到行政楼开会', '2025-06-27 22:24:53', '2025-06-28 15:00:00', 1, '个人', '一次性', '2025-06-28 12:00:00', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (75, '明天下午日程', '明天下午的日程安排，具体时间待定', NULL, NULL, 0, NULL, NULL, NULL, 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (76, '明天下午的日程', '明天下午有日程安排，具体时间未定', NULL, NULL, 0, NULL, NULL, NULL, 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (77, '开会', '下个月下午5点开会，地点在明行喽', NULL, NULL, 1, '团队', NULL, '2025-06-17 20:03:18', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (78, '开会', '下个月下午5点开会，地点在明行喽', NULL, NULL, 1, '团队', NULL, '2025-06-17 20:03:18', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (79, '开会', '下个月下午5点开会，地点在明行喽', NULL, NULL, 1, '团队', NULL, '2025-06-17 20:03:18', 1, NULL);
INSERT INTO `wenshu_calendar_event` VALUES (80, '开会', '下个月下午5点开会，地点在明行喽', NULL, NULL, 1, '团队', NULL, '2025-06-17 20:03:18', 1, NULL);

-- ----------------------------
-- Table structure for wenshu_calendar_participant
-- ----------------------------
DROP TABLE IF EXISTS `wenshu_calendar_participant`;
CREATE TABLE `wenshu_calendar_participant`  (
  `event_id` int(0) NOT NULL COMMENT '日程ID',
  `user_id` int(0) DEFAULT NULL COMMENT '用户ID',
  `team_id` int(0) DEFAULT NULL COMMENT '团队ID',
  `id` int(0) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 78 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '日程参与者关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of wenshu_calendar_participant
-- ----------------------------
INSERT INTO `wenshu_calendar_participant` VALUES (2, 0, 0, 1);
INSERT INTO `wenshu_calendar_participant` VALUES (1, 0, 0, 2);
INSERT INTO `wenshu_calendar_participant` VALUES (3, 1, 0, 3);
INSERT INTO `wenshu_calendar_participant` VALUES (4, 1, 0, 4);
INSERT INTO `wenshu_calendar_participant` VALUES (5, 1, 0, 5);
INSERT INTO `wenshu_calendar_participant` VALUES (6, 1, 0, 6);
INSERT INTO `wenshu_calendar_participant` VALUES (7, 1, 0, 7);
INSERT INTO `wenshu_calendar_participant` VALUES (8, 1, 0, 8);
INSERT INTO `wenshu_calendar_participant` VALUES (9, 1, 0, 9);
INSERT INTO `wenshu_calendar_participant` VALUES (10, 1, 0, 10);
INSERT INTO `wenshu_calendar_participant` VALUES (11, 1, 0, 11);
INSERT INTO `wenshu_calendar_participant` VALUES (12, 1, 0, 12);
INSERT INTO `wenshu_calendar_participant` VALUES (13, 1, 0, 13);
INSERT INTO `wenshu_calendar_participant` VALUES (14, 1, 0, 14);
INSERT INTO `wenshu_calendar_participant` VALUES (15, 1, 0, 15);
INSERT INTO `wenshu_calendar_participant` VALUES (16, 1, 0, 16);
INSERT INTO `wenshu_calendar_participant` VALUES (17, 1, 0, 17);
INSERT INTO `wenshu_calendar_participant` VALUES (18, 1, 0, 18);
INSERT INTO `wenshu_calendar_participant` VALUES (19, 1, 0, 19);
INSERT INTO `wenshu_calendar_participant` VALUES (20, 1, 0, 20);
INSERT INTO `wenshu_calendar_participant` VALUES (21, 1, 0, 21);
INSERT INTO `wenshu_calendar_participant` VALUES (22, 1, 0, 22);
INSERT INTO `wenshu_calendar_participant` VALUES (23, 1, 0, 23);
INSERT INTO `wenshu_calendar_participant` VALUES (24, 1, 0, 24);
INSERT INTO `wenshu_calendar_participant` VALUES (25, 1, 0, 25);
INSERT INTO `wenshu_calendar_participant` VALUES (26, 1, 0, 26);
INSERT INTO `wenshu_calendar_participant` VALUES (27, 1, 0, 27);
INSERT INTO `wenshu_calendar_participant` VALUES (28, 1, 0, 28);
INSERT INTO `wenshu_calendar_participant` VALUES (29, 1, 0, 29);
INSERT INTO `wenshu_calendar_participant` VALUES (30, 1, 0, 30);
INSERT INTO `wenshu_calendar_participant` VALUES (31, 1, 0, 31);
INSERT INTO `wenshu_calendar_participant` VALUES (32, 1, 0, 32);
INSERT INTO `wenshu_calendar_participant` VALUES (33, 1, 0, 33);
INSERT INTO `wenshu_calendar_participant` VALUES (34, 1, 0, 34);
INSERT INTO `wenshu_calendar_participant` VALUES (35, 1, 0, 35);
INSERT INTO `wenshu_calendar_participant` VALUES (36, 1, 0, 36);
INSERT INTO `wenshu_calendar_participant` VALUES (37, 1, 0, 37);
INSERT INTO `wenshu_calendar_participant` VALUES (38, 1, 0, 38);
INSERT INTO `wenshu_calendar_participant` VALUES (39, 1, 0, 39);
INSERT INTO `wenshu_calendar_participant` VALUES (40, 1, 0, 40);
INSERT INTO `wenshu_calendar_participant` VALUES (41, 1, 0, 41);
INSERT INTO `wenshu_calendar_participant` VALUES (42, 1, 0, 42);
INSERT INTO `wenshu_calendar_participant` VALUES (43, 1, 0, 43);
INSERT INTO `wenshu_calendar_participant` VALUES (44, 1, 0, 44);
INSERT INTO `wenshu_calendar_participant` VALUES (45, 1, 0, 45);
INSERT INTO `wenshu_calendar_participant` VALUES (46, 1, 0, 46);
INSERT INTO `wenshu_calendar_participant` VALUES (47, 1, 0, 47);
INSERT INTO `wenshu_calendar_participant` VALUES (48, 1, 0, 48);
INSERT INTO `wenshu_calendar_participant` VALUES (49, 1, 0, 49);
INSERT INTO `wenshu_calendar_participant` VALUES (50, 1, 0, 50);
INSERT INTO `wenshu_calendar_participant` VALUES (51, 1, 0, 51);
INSERT INTO `wenshu_calendar_participant` VALUES (52, 1, 0, 52);
INSERT INTO `wenshu_calendar_participant` VALUES (53, 1, 0, 53);
INSERT INTO `wenshu_calendar_participant` VALUES (54, 1, 0, 54);
INSERT INTO `wenshu_calendar_participant` VALUES (57, 1, NULL, 55);
INSERT INTO `wenshu_calendar_participant` VALUES (58, 1, 0, 56);
INSERT INTO `wenshu_calendar_participant` VALUES (59, 1, 0, 57);
INSERT INTO `wenshu_calendar_participant` VALUES (60, 1, 0, 58);
INSERT INTO `wenshu_calendar_participant` VALUES (61, 1, 0, 59);
INSERT INTO `wenshu_calendar_participant` VALUES (62, 1, 0, 60);
INSERT INTO `wenshu_calendar_participant` VALUES (63, 1, 0, 61);
INSERT INTO `wenshu_calendar_participant` VALUES (64, 1, 0, 62);
INSERT INTO `wenshu_calendar_participant` VALUES (65, 1, 0, 63);
INSERT INTO `wenshu_calendar_participant` VALUES (66, 1, 0, 64);
INSERT INTO `wenshu_calendar_participant` VALUES (67, 1, 0, 65);
INSERT INTO `wenshu_calendar_participant` VALUES (68, 1, 0, 66);
INSERT INTO `wenshu_calendar_participant` VALUES (69, 1, 0, 67);
INSERT INTO `wenshu_calendar_participant` VALUES (70, 1, 0, 68);
INSERT INTO `wenshu_calendar_participant` VALUES (71, 1, 0, 69);
INSERT INTO `wenshu_calendar_participant` VALUES (72, 1, 0, 70);
INSERT INTO `wenshu_calendar_participant` VALUES (73, 1, 0, 71);
INSERT INTO `wenshu_calendar_participant` VALUES (74, 1, 0, 72);
INSERT INTO `wenshu_calendar_participant` VALUES (75, 1, 0, 73);
INSERT INTO `wenshu_calendar_participant` VALUES (76, 1, 0, 74);
INSERT INTO `wenshu_calendar_participant` VALUES (77, 1, 0, 75);
INSERT INTO `wenshu_calendar_participant` VALUES (78, 1, 0, 76);
INSERT INTO `wenshu_calendar_participant` VALUES (79, 1, 0, 77);
INSERT INTO `wenshu_calendar_participant` VALUES (80, 1, 0, 78);

-- ----------------------------
-- Table structure for wenshu_file_participant
-- ----------------------------
DROP TABLE IF EXISTS `wenshu_file_participant`;
CREATE TABLE `wenshu_file_participant`  (
  `file_id` int(0) NOT NULL,
  `user_id` int(0) NOT NULL COMMENT '用户ID',
  `team_id` int(0) NOT NULL COMMENT '团队ID',
  `role` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`file_id`, `user_id`, `team_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文件参与者关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of wenshu_file_participant
-- ----------------------------
INSERT INTO `wenshu_file_participant` VALUES (1, 2, 8, NULL);
INSERT INTO `wenshu_file_participant` VALUES (1, 8, 0, NULL);
INSERT INTO `wenshu_file_participant` VALUES (1, 16, 2, NULL);
INSERT INTO `wenshu_file_participant` VALUES (2, 3, 9, NULL);
INSERT INTO `wenshu_file_participant` VALUES (3, 8, 1, NULL);
INSERT INTO `wenshu_file_participant` VALUES (4, 3, 2, NULL);
INSERT INTO `wenshu_file_participant` VALUES (4, 12, 9, NULL);
INSERT INTO `wenshu_file_participant` VALUES (4, 14, 5, NULL);
INSERT INTO `wenshu_file_participant` VALUES (5, 7, 10, NULL);
INSERT INTO `wenshu_file_participant` VALUES (5, 14, 10, NULL);
INSERT INTO `wenshu_file_participant` VALUES (7, 1, 4, NULL);
INSERT INTO `wenshu_file_participant` VALUES (7, 1, 6, NULL);
INSERT INTO `wenshu_file_participant` VALUES (7, 13, 3, NULL);
INSERT INTO `wenshu_file_participant` VALUES (7, 18, 5, NULL);
INSERT INTO `wenshu_file_participant` VALUES (8, 2, 4, NULL);
INSERT INTO `wenshu_file_participant` VALUES (8, 7, 9, NULL);
INSERT INTO `wenshu_file_participant` VALUES (8, 10, 7, NULL);
INSERT INTO `wenshu_file_participant` VALUES (8, 19, 1, NULL);
INSERT INTO `wenshu_file_participant` VALUES (9, 15, 1, NULL);
INSERT INTO `wenshu_file_participant` VALUES (11, 4, 1, NULL);
INSERT INTO `wenshu_file_participant` VALUES (11, 12, 8, NULL);
INSERT INTO `wenshu_file_participant` VALUES (11, 18, 2, NULL);
INSERT INTO `wenshu_file_participant` VALUES (12, 4, 5, NULL);
INSERT INTO `wenshu_file_participant` VALUES (12, 15, 3, NULL);
INSERT INTO `wenshu_file_participant` VALUES (12, 17, 1, NULL);
INSERT INTO `wenshu_file_participant` VALUES (13, 19, 10, NULL);
INSERT INTO `wenshu_file_participant` VALUES (13, 20, 4, NULL);
INSERT INTO `wenshu_file_participant` VALUES (14, 2, 3, NULL);
INSERT INTO `wenshu_file_participant` VALUES (14, 7, 5, NULL);
INSERT INTO `wenshu_file_participant` VALUES (15, 5, 1, NULL);
INSERT INTO `wenshu_file_participant` VALUES (15, 6, 2, NULL);
INSERT INTO `wenshu_file_participant` VALUES (15, 15, 5, NULL);
INSERT INTO `wenshu_file_participant` VALUES (16, 1, 4, NULL);
INSERT INTO `wenshu_file_participant` VALUES (16, 2, 6, NULL);
INSERT INTO `wenshu_file_participant` VALUES (16, 4, 6, NULL);
INSERT INTO `wenshu_file_participant` VALUES (16, 19, 9, NULL);
INSERT INTO `wenshu_file_participant` VALUES (17, 12, 2, NULL);
INSERT INTO `wenshu_file_participant` VALUES (17, 12, 7, NULL);
INSERT INTO `wenshu_file_participant` VALUES (17, 14, 10, NULL);
INSERT INTO `wenshu_file_participant` VALUES (18, 4, 4, NULL);
INSERT INTO `wenshu_file_participant` VALUES (18, 6, 5, NULL);
INSERT INTO `wenshu_file_participant` VALUES (18, 12, 2, NULL);
INSERT INTO `wenshu_file_participant` VALUES (19, 4, 8, NULL);
INSERT INTO `wenshu_file_participant` VALUES (19, 8, 2, NULL);
INSERT INTO `wenshu_file_participant` VALUES (19, 8, 4, NULL);
INSERT INTO `wenshu_file_participant` VALUES (19, 12, 5, NULL);
INSERT INTO `wenshu_file_participant` VALUES (19, 15, 9, NULL);
INSERT INTO `wenshu_file_participant` VALUES (19, 17, 3, NULL);
INSERT INTO `wenshu_file_participant` VALUES (20, 1, 8, NULL);
INSERT INTO `wenshu_file_participant` VALUES (20, 4, 6, NULL);
INSERT INTO `wenshu_file_participant` VALUES (20, 8, 7, NULL);
INSERT INTO `wenshu_file_participant` VALUES (20, 17, 2, NULL);
INSERT INTO `wenshu_file_participant` VALUES (21, 12, 8, NULL);
INSERT INTO `wenshu_file_participant` VALUES (21, 13, 10, NULL);
INSERT INTO `wenshu_file_participant` VALUES (21, 17, 7, NULL);
INSERT INTO `wenshu_file_participant` VALUES (22, 5, 7, NULL);
INSERT INTO `wenshu_file_participant` VALUES (22, 8, 5, NULL);
INSERT INTO `wenshu_file_participant` VALUES (22, 18, 1, NULL);
INSERT INTO `wenshu_file_participant` VALUES (25, 4, 2, NULL);
INSERT INTO `wenshu_file_participant` VALUES (25, 7, 9, NULL);
INSERT INTO `wenshu_file_participant` VALUES (25, 15, 5, NULL);
INSERT INTO `wenshu_file_participant` VALUES (26, 4, 7, NULL);
INSERT INTO `wenshu_file_participant` VALUES (26, 8, 9, NULL);
INSERT INTO `wenshu_file_participant` VALUES (27, 1, 8, NULL);
INSERT INTO `wenshu_file_participant` VALUES (29, 1, 3, NULL);
INSERT INTO `wenshu_file_participant` VALUES (29, 6, 6, NULL);
INSERT INTO `wenshu_file_participant` VALUES (30, 7, 8, NULL);
INSERT INTO `wenshu_file_participant` VALUES (31, 18, 7, NULL);
INSERT INTO `wenshu_file_participant` VALUES (31, 39, 1, NULL);
INSERT INTO `wenshu_file_participant` VALUES (32, 8, 9, NULL);
INSERT INTO `wenshu_file_participant` VALUES (32, 10, 9, NULL);
INSERT INTO `wenshu_file_participant` VALUES (33, 1, 2, NULL);
INSERT INTO `wenshu_file_participant` VALUES (33, 6, 3, NULL);
INSERT INTO `wenshu_file_participant` VALUES (33, 10, 2, NULL);
INSERT INTO `wenshu_file_participant` VALUES (33, 12, 9, NULL);
INSERT INTO `wenshu_file_participant` VALUES (34, 11, 2, NULL);
INSERT INTO `wenshu_file_participant` VALUES (35, 8, 8, NULL);
INSERT INTO `wenshu_file_participant` VALUES (35, 9, 9, NULL);
INSERT INTO `wenshu_file_participant` VALUES (35, 11, 5, NULL);
INSERT INTO `wenshu_file_participant` VALUES (36, 5, 3, NULL);
INSERT INTO `wenshu_file_participant` VALUES (36, 6, 9, NULL);
INSERT INTO `wenshu_file_participant` VALUES (39, 1, 5, NULL);
INSERT INTO `wenshu_file_participant` VALUES (39, 18, 8, NULL);
INSERT INTO `wenshu_file_participant` VALUES (40, 13, 7, NULL);
INSERT INTO `wenshu_file_participant` VALUES (40, 13, 10, NULL);
INSERT INTO `wenshu_file_participant` VALUES (41, 11, 5, NULL);
INSERT INTO `wenshu_file_participant` VALUES (42, 1, 1, NULL);
INSERT INTO `wenshu_file_participant` VALUES (42, 11, 3, NULL);
INSERT INTO `wenshu_file_participant` VALUES (42, 17, 8, NULL);
INSERT INTO `wenshu_file_participant` VALUES (43, 10, 9, NULL);
INSERT INTO `wenshu_file_participant` VALUES (45, 16, 2, NULL);
INSERT INTO `wenshu_file_participant` VALUES (46, 3, 8, NULL);
INSERT INTO `wenshu_file_participant` VALUES (46, 13, 1, NULL);
INSERT INTO `wenshu_file_participant` VALUES (47, 7, 6, NULL);
INSERT INTO `wenshu_file_participant` VALUES (47, 17, 10, NULL);
INSERT INTO `wenshu_file_participant` VALUES (47, 18, 6, NULL);
INSERT INTO `wenshu_file_participant` VALUES (49, 2, 3, NULL);
INSERT INTO `wenshu_file_participant` VALUES (50, 7, 5, NULL);
INSERT INTO `wenshu_file_participant` VALUES (555, 554, 555, NULL);

-- ----------------------------
-- Table structure for wenshu_file_storage
-- ----------------------------
DROP TABLE IF EXISTS `wenshu_file_storage`;
CREATE TABLE `wenshu_file_storage`  (
  `file_id` int(0) NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件名称',
  `file_path` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '文件存储路径',
  `upload_time` datetime(6) DEFAULT NULL,
  `uploader_id` int(0) DEFAULT NULL COMMENT '上传者ID',
  `file_size` bigint(0) DEFAULT NULL COMMENT '文件大小（字节）',
  `file_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '文件状态（可读写/协作/不可读写）',
  `owner_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '所有者类型（个人/团队/分析）',
  PRIMARY KEY (`file_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 808 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文件存储主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of wenshu_file_storage
-- ----------------------------
INSERT INTO `wenshu_file_storage` VALUES (1, '手册_1.pptx', '/storage/2024/7/手册_1.pptx', '2024-09-01 16:21:57.000000', 5, 63957207, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (2, '报告_2.docx', '/storage/2024/2/报告_2.docx', '2024-07-23 16:21:57.000000', 50, 69410406, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (3, '手册_3.jpg', '/storage/2022/5/手册_3.jpg', '2025-02-06 16:21:57.000000', 20, 80793151, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (4, '记录_4.xlsx', '/storage/2024/1/记录_4.xlsx', '2025-02-07 16:21:58.000000', 2, 131933, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (5, '记录_5.docx', '/storage/2022/2/记录_5.docx', '2024-11-04 16:21:58.000000', 9, 73938790, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (6, '项目_6.jpg', '/storage/2023/3/项目_6.jpg', '2024-07-31 16:21:58.000000', 46, 2219291, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (7, '设计_7.txt', '/storage/2022/6/设计_7.txt', '2025-02-26 16:21:58.000000', 4, 57496095, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (8, '记录_8.pdf', '/storage/2024/9/记录_8.pdf', '2025-02-01 16:21:58.000000', 42, 89024212, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (9, '项目_9.pptx', '/storage/2024/2/项目_9.pptx', '2024-07-07 16:21:58.000000', 4, 77259131, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (10, '总结_10.pdf', '/storage/2023/6/总结_10.pdf', '2024-08-01 16:21:58.000000', 47, 41893554, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (11, '报告_11.pptx', '/storage/2022/9/报告_11.pptx', '2024-12-07 16:21:58.000000', 23, 72048699, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (12, '总结_12.pptx', '/storage/2023/12/总结_12.pptx', '2024-12-29 16:21:58.000000', 15, 87253827, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (13, '设计_13.pdf', '/storage/2023/6/设计_13.pdf', '2025-06-20 16:21:58.000000', 5, 101888663, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (14, '分析_14.jpg', '/storage/2022/12/分析_14.jpg', '2024-10-28 16:21:58.000000', 41, 39762396, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (15, '记录_15.pptx', '/storage/2024/1/记录_15.pptx', '2024-11-01 16:21:58.000000', 39, 83155864, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (16, '项目_16.pptx', '/storage/2022/11/项目_16.pptx', '2025-04-10 16:21:58.000000', 34, 59343158, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (17, '手册_17.pdf', '/storage/2024/5/手册_17.pdf', '2025-06-06 16:21:58.000000', 50, 85091587, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (18, '合同_18.pdf', '/storage/2024/11/合同_18.pdf', '2024-12-22 16:21:58.000000', 20, 68869462, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (19, '项目_19.pptx', '/storage/2022/9/项目_19.pptx', '2024-07-19 16:21:58.000000', 32, 65169078, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (20, '项目_20.txt', '/storage/2022/8/项目_20.txt', '2025-05-22 16:21:58.000000', 9, 3937760, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (21, '预算_21.pptx', '/storage/2022/12/预算_21.pptx', '2025-06-15 16:21:58.000000', 47, 87748338, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (22, '预算_22.xlsx', '/storage/2022/5/预算_22.xlsx', '2024-12-06 16:21:59.000000', 20, 29074402, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (23, '报告_23.pptx', '/storage/2024/10/报告_23.pptx', '2024-09-11 16:21:59.000000', 31, 98435481, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (24, '预算_24.pptx', '/storage/2024/4/预算_24.pptx', '2024-08-16 16:21:59.000000', 38, 21729373, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (25, '预算_25.docx', '/storage/2022/7/预算_25.docx', '2024-09-13 16:21:59.000000', 24, 33587661, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (26, '方案_26.docx', '/storage/2023/7/方案_26.docx', '2024-07-24 16:21:59.000000', 23, 77273535, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (27, '合同_27.pdf', '/storage/2023/7/合同_27.pdf', '2024-07-08 16:21:59.000000', 46, 41223756, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (28, '设计_28.txt', '/storage/2022/8/设计_28.txt', '2025-05-14 16:21:59.000000', 4, 2732744, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (29, '总结_29.jpg', '/storage/2024/7/总结_29.jpg', '2024-07-01 16:21:59.000000', 1, 46694385, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (30, '报告_30.docx', '/storage/2022/12/报告_30.docx', '2024-08-22 16:21:59.000000', 40, 71829038, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (31, '预算_31.pptx', '/storage/2024/3/预算_31.pptx', '2025-04-21 16:21:59.000000', 30, 6024149, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (32, '合同_32.jpg', '/storage/2024/1/合同_32.jpg', '2025-01-11 16:21:59.000000', 19, 804491, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (33, '手册_33.pdf', '/storage/2024/3/手册_33.pdf', '2025-04-15 16:21:59.000000', 26, 62600042, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (34, '报告_34.png', '/storage/2022/10/报告_34.png', '2024-12-29 16:21:59.000000', 24, 13258952, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (35, '预算_35.docx', '/storage/2023/10/预算_35.docx', '2025-02-02 16:21:59.000000', 50, 63178007, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (36, '总结_36.txt', '/storage/2024/6/总结_36.txt', '2024-12-14 16:21:59.000000', 18, 7556687, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (37, '合同_37.pptx', '/storage/2023/5/合同_37.pptx', '2024-08-18 16:21:59.000000', 40, 52590873, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (38, '记录_38.pdf', '/storage/2023/5/记录_38.pdf', '2025-01-23 16:21:59.000000', 8, 25560156, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (39, '分析_39.jpg', '/storage/2023/9/分析_39.jpg', '2025-04-20 16:22:00.000000', 16, 64342657, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (40, '记录_40.jpg', '/storage/2024/5/记录_40.jpg', '2024-11-13 16:22:00.000000', 30, 5607543, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (41, '手册_41.xlsx', '/storage/2024/6/手册_41.xlsx', '2025-06-01 16:22:00.000000', 42, 45767790, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (42, '合同_42.pptx', '/storage/2023/12/合同_42.pptx', '2024-08-13 16:22:00.000000', 5, 24942489, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (43, '方案_43.png', '/storage/2022/9/方案_43.png', '2025-04-20 16:22:00.000000', 17, 44395763, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (44, '合同_44.png', '/storage/2022/12/合同_44.png', '2025-03-12 16:22:00.000000', 4, 11624821, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (45, '方案_45.docx', '/storage/2022/4/方案_45.docx', '2025-04-09 16:22:00.000000', 48, 34357432, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (46, '总结_46.jpg', '/storage/2022/4/总结_46.jpg', '2025-01-27 16:22:00.000000', 21, 8635433, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (47, '合同_47.png', '/storage/2023/6/合同_47.png', '2024-10-06 16:22:00.000000', 42, 81181524, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (48, '手册_48.txt', '/storage/2022/9/手册_48.txt', '2024-09-22 16:22:00.000000', 22, 14749970, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (49, '手册_49.png', '/storage/2024/8/手册_49.png', '2025-05-22 16:22:00.000000', 17, 49716476, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (50, '设计_50.txt', '/storage/2024/1/设计_50.txt', '2025-05-26 16:22:00.000000', 23, 60299111, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (51, '项目_51.docx', '/storage/2024/10/项目_51.docx', '2024-12-12 16:22:00.000000', 49, 96395290, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (52, '方案_52.txt', '/storage/2022/4/方案_52.txt', '2024-10-06 16:22:00.000000', 20, 88046572, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (53, '总结_53.docx', '/storage/2023/8/总结_53.docx', '2025-04-10 16:22:00.000000', 36, 3501284, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (54, '记录_54.xlsx', '/storage/2023/6/记录_54.xlsx', '2024-11-16 16:22:00.000000', 40, 56291796, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (55, '方案_55.docx', '/storage/2024/2/方案_55.docx', '2025-03-26 16:22:00.000000', 12, 75858654, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (56, '预算_56.pptx', '/storage/2023/10/预算_56.pptx', '2024-12-03 16:22:01.000000', 19, 64637523, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (57, '项目_57.jpg', '/storage/2023/10/项目_57.jpg', '2024-08-04 16:22:01.000000', 41, 44157152, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (58, '记录_58.pdf', '/storage/2023/2/记录_58.pdf', '2025-01-17 16:22:01.000000', 43, 99155510, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (59, '报告_59.txt', '/storage/2023/2/报告_59.txt', '2025-04-13 16:22:01.000000', 6, 15167633, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (60, '方案_60.docx', '/storage/2024/6/方案_60.docx', '2025-05-21 16:22:01.000000', 35, 30198732, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (61, '预算_61.png', '/storage/2023/5/预算_61.png', '2025-05-17 16:22:01.000000', 32, 39875974, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (62, '报告_62.xlsx', '/storage/2024/10/报告_62.xlsx', '2024-07-06 16:22:01.000000', 14, 6307654, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (63, '报告_63.jpg', '/storage/2024/7/报告_63.jpg', '2024-07-11 16:22:01.000000', 9, 82147339, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (64, '设计_64.pptx', '/storage/2023/1/设计_64.pptx', '2024-09-08 16:22:01.000000', 50, 77250808, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (65, '合同_65.png', '/storage/2022/12/合同_65.png', '2024-11-15 16:22:01.000000', 5, 43491998, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (66, '项目_66.pdf', '/storage/2022/6/项目_66.pdf', '2024-07-09 16:22:01.000000', 42, 14693928, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (67, '手册_67.docx', '/storage/2024/2/手册_67.docx', '2024-07-12 16:22:01.000000', 14, 82135459, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (68, '方案_68.png', '/storage/2023/4/方案_68.png', '2025-06-07 16:22:01.000000', 30, 49000736, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (69, '手册_69.pdf', '/storage/2023/3/手册_69.pdf', '2025-02-26 16:22:01.000000', 45, 90835122, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (70, '设计_70.xlsx', '/storage/2022/1/设计_70.xlsx', '2025-01-15 16:22:01.000000', 26, 91548276, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (71, '合同_71.xlsx', '/storage/2023/9/合同_71.xlsx', '2025-01-03 16:22:01.000000', 43, 45422030, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (72, '手册_72.pdf', '/storage/2024/8/手册_72.pdf', '2024-09-08 16:22:01.000000', 14, 67259673, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (73, '分析_73.docx', '/storage/2023/9/分析_73.docx', '2024-07-30 16:22:02.000000', 25, 24028836, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (74, '设计_74.xlsx', '/storage/2024/8/设计_74.xlsx', '2024-09-03 16:22:02.000000', 41, 89443431, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (75, '预算_75.xlsx', '/storage/2024/11/预算_75.xlsx', '2025-02-28 16:22:02.000000', 15, 50010102, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (76, '记录_76.jpg', '/storage/2022/12/记录_76.jpg', '2025-06-06 16:22:02.000000', 8, 99467024, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (77, '合同_77.pdf', '/storage/2024/6/合同_77.pdf', '2024-09-23 16:22:02.000000', 21, 59508520, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (78, '分析_78.docx', '/storage/2024/9/分析_78.docx', '2024-07-25 16:22:02.000000', 47, 58611795, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (79, '分析_79.png', '/storage/2022/2/分析_79.png', '2024-12-04 16:22:02.000000', 50, 96920218, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (80, '记录_80.pptx', '/storage/2023/9/记录_80.pptx', '2024-10-01 16:22:02.000000', 16, 20840831, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (81, '报告_81.png', '/storage/2023/9/报告_81.png', '2024-12-21 16:22:02.000000', 21, 88506480, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (82, '合同_82.jpg', '/storage/2022/3/合同_82.jpg', '2024-09-07 16:22:02.000000', 45, 77905968, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (83, '项目_83.txt', '/storage/2022/6/项目_83.txt', '2025-03-07 16:22:02.000000', 5, 11505143, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (84, '预算_84.txt', '/storage/2024/11/预算_84.txt', '2025-03-15 16:22:02.000000', 34, 13102723, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (85, '方案_85.txt', '/storage/2023/9/方案_85.txt', '2025-05-23 16:22:02.000000', 41, 73163046, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (86, '记录_86.pptx', '/storage/2023/6/记录_86.pptx', '2024-08-26 16:22:02.000000', 32, 83788511, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (87, '方案_87.pptx', '/storage/2022/9/方案_87.pptx', '2025-02-07 16:22:02.000000', 45, 86017792, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (88, '报告_88.pdf', '/storage/2022/12/报告_88.pdf', '2024-06-23 16:22:02.000000', 42, 33482491, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (89, '手册_89.png', '/storage/2024/10/手册_89.png', '2025-01-03 16:22:02.000000', 35, 66796459, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (90, '分析_90.jpg', '/storage/2022/5/分析_90.jpg', '2025-06-13 16:22:03.000000', 48, 24302922, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (91, '手册_91.docx', '/storage/2024/1/手册_91.docx', '2025-04-19 16:22:03.000000', 15, 85555942, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (92, '预算_92.jpg', '/storage/2022/11/预算_92.jpg', '2025-06-01 16:22:03.000000', 36, 64937078, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (93, '方案_93.xlsx', '/storage/2022/10/方案_93.xlsx', '2025-01-14 16:22:03.000000', 40, 98436315, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (94, '方案_94.pdf', '/storage/2023/9/方案_94.pdf', '2024-08-30 16:22:03.000000', 23, 3307400, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (95, '分析_95.png', '/storage/2022/2/分析_95.png', '2024-12-13 16:22:03.000000', 18, 81027808, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (96, '报告_96.docx', '/storage/2024/2/报告_96.docx', '2024-10-02 16:22:03.000000', 12, 59005213, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (97, '分析_97.xlsx', '/storage/2022/5/分析_97.xlsx', '2025-04-11 16:22:03.000000', 26, 103481381, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (98, '方案_98.docx', '/storage/2023/6/方案_98.docx', '2024-10-25 16:22:03.000000', 29, 55810216, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (99, '分析_99.jpg', '/storage/2022/6/分析_99.jpg', '2025-06-03 16:22:03.000000', 46, 102902942, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (100, '记录_100.pdf', '/storage/2022/1/记录_100.pdf', '2024-07-21 16:22:03.000000', 45, 23311088, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (101, '方案_1.pptx', '/storage/2024/4/方案_1.pptx', '2024-08-31 16:25:41.000000', 25, 11096895, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (102, '分析_2.jpg', '/storage/2022/3/分析_2.jpg', '2024-07-04 16:25:41.000000', 39, 51711863, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (103, '合同_3.pptx', '/storage/2022/8/合同_3.pptx', '2025-04-20 16:25:41.000000', 12, 63121001, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (104, '设计_4.jpg', '/storage/2023/4/设计_4.jpg', '2025-02-02 16:25:41.000000', 44, 99722358, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (105, '项目_5.pptx', '/storage/2023/8/项目_5.pptx', '2024-10-24 16:25:41.000000', 4, 17434573, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (106, '报告_6.pptx', '/storage/2022/4/报告_6.pptx', '2025-06-12 16:25:41.000000', 5, 36162799, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (107, '设计_7.png', '/storage/2023/4/设计_7.png', '2025-06-22 16:25:41.000000', 16, 77769543, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (108, '预算_8.pptx', '/storage/2024/8/预算_8.pptx', '2024-08-24 16:25:42.000000', 6, 90891547, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (109, '记录_9.pptx', '/storage/2023/2/记录_9.pptx', '2025-05-05 16:25:42.000000', 8, 30799651, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (110, '预算_10.pptx', '/storage/2024/6/预算_10.pptx', '2024-11-08 16:25:42.000000', 41, 20771148, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (111, '方案_11.docx', '/storage/2023/1/方案_11.docx', '2025-01-29 16:25:42.000000', 26, 15131669, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (112, '手册_12.docx', '/storage/2022/4/手册_12.docx', '2025-03-21 16:25:42.000000', 16, 72877847, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (113, '预算_13.xlsx', '/storage/2023/6/预算_13.xlsx', '2025-01-27 16:25:42.000000', 46, 31818825, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (114, '手册_14.pptx', '/storage/2022/8/手册_14.pptx', '2024-07-20 16:25:42.000000', 32, 80832348, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (115, '总结_15.xlsx', '/storage/2023/8/总结_15.xlsx', '2024-11-02 16:25:42.000000', 21, 6005535, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (116, '预算_16.xlsx', '/storage/2023/11/预算_16.xlsx', '2024-07-31 16:25:42.000000', 33, 32633222, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (117, '分析_17.xlsx', '/storage/2024/5/分析_17.xlsx', '2024-12-20 16:25:42.000000', 27, 2446276, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (118, '分析_18.txt', '/storage/2023/8/分析_18.txt', '2025-01-09 16:25:42.000000', 3, 66748468, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (119, '合同_19.pdf', '/storage/2022/11/合同_19.pdf', '2025-03-23 16:25:42.000000', 10, 45125286, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (120, '记录_20.png', '/storage/2022/5/记录_20.png', '2024-07-28 16:25:42.000000', 39, 52847305, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (121, '分析_21.jpg', '/storage/2023/10/分析_21.jpg', '2024-06-29 16:25:42.000000', 3, 1426307, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (122, '分析_22.pdf', '/storage/2024/7/分析_22.pdf', '2024-11-05 16:25:42.000000', 17, 76754662, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (123, '总结_23.png', '/storage/2023/5/总结_23.png', '2025-02-06 16:25:43.000000', 6, 20838725, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (124, '记录_24.txt', '/storage/2024/1/记录_24.txt', '2024-07-22 16:25:43.000000', 45, 15914641, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (125, '报告_25.jpg', '/storage/2024/12/报告_25.jpg', '2024-07-17 16:25:43.000000', 30, 44502239, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (126, '合同_26.txt', '/storage/2024/4/合同_26.txt', '2024-11-21 16:25:43.000000', 28, 31660804, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (127, '手册_27.xlsx', '/storage/2023/11/手册_27.xlsx', '2024-07-09 16:25:43.000000', 16, 12408292, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (128, '手册_28.docx', '/storage/2024/3/手册_28.docx', '2024-09-01 16:25:43.000000', 36, 26421149, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (129, '记录_29.png', '/storage/2023/11/记录_29.png', '2025-06-15 16:25:43.000000', 8, 82472857, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (130, '记录_30.pptx', '/storage/2022/8/记录_30.pptx', '2024-11-01 16:25:43.000000', 31, 9041162, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (131, '记录_31.txt', '/storage/2023/6/记录_31.txt', '2025-03-11 16:25:43.000000', 41, 75544216, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (132, '预算_32.pptx', '/storage/2022/2/预算_32.pptx', '2025-03-19 16:25:43.000000', 23, 63849506, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (133, '项目_33.pptx', '/storage/2024/2/项目_33.pptx', '2024-09-21 16:25:43.000000', 49, 16096484, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (134, '预算_34.jpg', '/storage/2024/11/预算_34.jpg', '2025-01-10 16:25:43.000000', 4, 26467522, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (135, '预算_35.png', '/storage/2023/8/预算_35.png', '2025-04-06 16:25:43.000000', 30, 88629161, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (136, '设计_36.docx', '/storage/2024/10/设计_36.docx', '2024-08-06 16:25:43.000000', 13, 80142824, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (137, '设计_37.jpg', '/storage/2022/10/设计_37.jpg', '2025-06-04 16:25:43.000000', 50, 102588513, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (138, '分析_38.xlsx', '/storage/2024/7/分析_38.xlsx', '2025-02-04 16:25:44.000000', 34, 12340701, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (139, '报告_39.pdf', '/storage/2023/7/报告_39.pdf', '2025-03-15 16:25:44.000000', 40, 7422497, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (140, '报告_40.docx', '/storage/2024/12/报告_40.docx', '2024-08-17 16:25:44.000000', 43, 62992805, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (141, '报告_41.jpg', '/storage/2022/8/报告_41.jpg', '2024-09-08 16:25:44.000000', 28, 32181576, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (142, '记录_42.jpg', '/storage/2024/5/记录_42.jpg', '2024-06-30 16:25:44.000000', 33, 36453236, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (143, '设计_43.xlsx', '/storage/2023/8/设计_43.xlsx', '2024-07-10 16:25:44.000000', 27, 77905275, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (144, '合同_44.docx', '/storage/2022/3/合同_44.docx', '2024-08-12 16:25:44.000000', 34, 35738255, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (145, '报告_45.png', '/storage/2022/7/报告_45.png', '2024-12-14 16:25:44.000000', 47, 43590999, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (146, '分析_46.jpg', '/storage/2022/2/分析_46.jpg', '2025-05-29 16:25:44.000000', 13, 10550310, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (147, '设计_47.txt', '/storage/2022/6/设计_47.txt', '2024-08-12 16:25:44.000000', 29, 41224902, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (148, '方案_48.xlsx', '/storage/2022/10/方案_48.xlsx', '2024-09-21 16:25:44.000000', 36, 30463757, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (149, '预算_49.pptx', '/storage/2024/2/预算_49.pptx', '2025-02-11 16:25:44.000000', 15, 54317300, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (150, '报告_50.png', '/storage/2022/11/报告_50.png', '2024-08-07 16:25:44.000000', 17, 25766802, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (151, '方案_51.jpg', '/storage/2022/8/方案_51.jpg', '2025-03-22 16:25:44.000000', 28, 49188714, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (152, '报告_52.pptx', '/storage/2023/1/报告_52.pptx', '2025-04-17 16:25:44.000000', 40, 82459058, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (153, '记录_53.pdf', '/storage/2022/4/记录_53.pdf', '2025-02-19 16:25:45.000000', 9, 44032449, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (154, '记录_54.pdf', '/storage/2022/4/记录_54.pdf', '2024-12-01 16:25:45.000000', 28, 82898087, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (155, '报告_55.xlsx', '/storage/2023/11/报告_55.xlsx', '2025-01-29 16:25:45.000000', 46, 39062301, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (156, '手册_56.txt', '/storage/2022/12/手册_56.txt', '2025-04-26 16:25:45.000000', 47, 27916622, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (157, '报告_57.xlsx', '/storage/2024/6/报告_57.xlsx', '2024-08-18 16:25:45.000000', 30, 24836863, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (158, '总结_58.png', '/storage/2024/11/总结_58.png', '2024-10-02 16:25:45.000000', 27, 37545369, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (159, '分析_59.png', '/storage/2023/11/分析_59.png', '2024-07-18 16:25:45.000000', 46, 55479454, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (160, '手册_60.png', '/storage/2022/7/手册_60.png', '2024-08-11 16:25:45.000000', 30, 18112887, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (161, '方案_61.docx', '/storage/2022/10/方案_61.docx', '2024-11-12 16:25:45.000000', 16, 16755240, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (162, '项目_62.docx', '/storage/2023/6/项目_62.docx', '2024-06-27 16:25:45.000000', 13, 44125076, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (163, '手册_63.pdf', '/storage/2022/12/手册_63.pdf', '2025-06-19 16:25:45.000000', 7, 53615265, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (164, '合同_64.pdf', '/storage/2024/11/合同_64.pdf', '2024-07-08 16:25:45.000000', 3, 68316540, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (165, '总结_65.png', '/storage/2023/5/总结_65.png', '2025-03-07 16:25:45.000000', 46, 10811194, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (166, '分析_66.pdf', '/storage/2023/5/分析_66.pdf', '2024-09-20 16:25:45.000000', 41, 84342354, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (167, '总结_67.xlsx', '/storage/2024/12/总结_67.xlsx', '2024-08-15 16:25:45.000000', 1, 73754146, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (168, '项目_68.pptx', '/storage/2022/8/项目_68.pptx', '2024-12-13 16:25:46.000000', 5, 29683694, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (169, '项目_69.png', '/storage/2022/10/项目_69.png', '2025-01-10 16:25:46.000000', 39, 15967042, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (170, '记录_70.jpg', '/storage/2023/12/记录_70.jpg', '2025-03-25 16:25:46.000000', 14, 18585299, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (171, '设计_71.pptx', '/storage/2024/11/设计_71.pptx', '2025-04-10 16:25:46.000000', 38, 85625456, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (172, '总结_72.pptx', '/storage/2022/5/总结_72.pptx', '2024-07-22 16:25:46.000000', 49, 26255764, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (173, '手册_73.xlsx', '/storage/2024/12/手册_73.xlsx', '2025-01-01 16:25:46.000000', 36, 29454955, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (174, '方案_74.xlsx', '/storage/2024/6/方案_74.xlsx', '2025-03-04 16:25:46.000000', 6, 44753600, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (175, '报告_75.png', '/storage/2022/9/报告_75.png', '2025-06-19 16:25:46.000000', 11, 31016179, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (176, '合同_76.png', '/storage/2024/5/合同_76.png', '2025-03-30 16:25:46.000000', 49, 9760356, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (177, '设计_77.xlsx', '/storage/2024/8/设计_77.xlsx', '2025-05-19 16:25:46.000000', 2, 8332745, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (178, '报告_78.pdf', '/storage/2022/7/报告_78.pdf', '2025-02-13 16:25:46.000000', 47, 90497465, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (179, '项目_79.png', '/storage/2023/4/项目_79.png', '2024-10-03 16:25:46.000000', 10, 52540581, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (180, '设计_80.txt', '/storage/2023/4/设计_80.txt', '2025-02-12 16:25:46.000000', 17, 84679514, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (181, '合同_81.jpg', '/storage/2022/8/合同_81.jpg', '2025-03-30 16:25:46.000000', 41, 7699705, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (182, '方案_82.txt', '/storage/2024/3/方案_82.txt', '2025-01-14 16:25:46.000000', 49, 103351647, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (183, '记录_83.jpg', '/storage/2023/5/记录_83.jpg', '2025-04-10 16:25:47.000000', 19, 98106479, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (184, '总结_84.jpg', '/storage/2022/11/总结_84.jpg', '2024-12-04 16:25:47.000000', 36, 88410174, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (185, '预算_85.txt', '/storage/2024/7/预算_85.txt', '2024-08-18 16:25:47.000000', 50, 67714278, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (186, '项目_86.pdf', '/storage/2023/11/项目_86.pdf', '2025-05-15 16:25:47.000000', 44, 37392511, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (187, '合同_87.png', '/storage/2024/10/合同_87.png', '2025-05-09 16:25:47.000000', 22, 24078264, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (188, '手册_88.docx', '/storage/2023/11/手册_88.docx', '2025-05-22 16:25:47.000000', 1, 5719744, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (189, '记录_89.xlsx', '/storage/2024/6/记录_89.xlsx', '2025-05-06 16:25:47.000000', 14, 19276788, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (190, '方案_90.png', '/storage/2022/11/方案_90.png', '2024-08-18 16:25:47.000000', 28, 47201382, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (191, '分析_91.png', '/storage/2024/9/分析_91.png', '2024-12-30 16:25:47.000000', 13, 44648590, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (192, '手册_92.pdf', '/storage/2024/2/手册_92.pdf', '2024-11-27 16:25:47.000000', 34, 83738083, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (193, '项目_93.pdf', '/storage/2022/5/项目_93.pdf', '2025-02-04 16:25:47.000000', 2, 88142041, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (194, '设计_94.txt', '/storage/2024/12/设计_94.txt', '2024-12-08 16:25:47.000000', 1, 3557377, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (195, '总结_95.xlsx', '/storage/2023/1/总结_95.xlsx', '2025-05-05 16:25:47.000000', 20, 75611379, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (196, '预算_96.png', '/storage/2023/10/预算_96.png', '2024-11-10 16:25:47.000000', 20, 33244199, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (197, '报告_97.png', '/storage/2023/8/报告_97.png', '2025-04-29 16:25:47.000000', 14, 64494508, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (198, '合同_98.pdf', '/storage/2022/9/合同_98.pdf', '2025-05-11 16:25:48.000000', 12, 7663301, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (199, '方案_99.docx', '/storage/2023/4/方案_99.docx', '2024-09-23 16:25:48.000000', 11, 90320969, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (200, '总结_100.xlsx', '/storage/2024/7/总结_100.xlsx', '2025-05-26 16:25:48.000000', 45, 52124930, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (201, '合同_1.png', '/storage/2024/3/合同_1.png', '2025-02-06 16:26:47.000000', 30, 9057230, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (202, '手册_2.jpg', '/storage/2022/10/手册_2.jpg', '2025-03-18 16:26:47.000000', 47, 51975069, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (203, '项目_3.png', '/storage/2024/7/项目_3.png', '2024-06-29 16:26:47.000000', 46, 61379156, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (204, '设计_4.png', '/storage/2023/1/设计_4.png', '2024-06-24 16:26:47.000000', 47, 31370255, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (205, '手册_5.png', '/storage/2022/9/手册_5.png', '2025-01-07 16:26:47.000000', 18, 5158894, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (206, '报告_6.pdf', '/storage/2023/8/报告_6.pdf', '2025-05-09 16:26:47.000000', 48, 50942983, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (207, '项目_7.docx', '/storage/2024/11/项目_7.docx', '2025-06-13 16:26:47.000000', 31, 87250035, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (208, '总结_8.pdf', '/storage/2023/6/总结_8.pdf', '2024-09-12 16:26:48.000000', 46, 61607146, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (209, '报告_9.txt', '/storage/2023/5/报告_9.txt', '2024-06-26 16:26:48.000000', 50, 14789681, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (210, '分析_10.txt', '/storage/2022/4/分析_10.txt', '2024-12-13 16:26:48.000000', 4, 43815598, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (211, '手册_11.docx', '/storage/2024/1/手册_11.docx', '2024-09-06 16:26:48.000000', 38, 89873192, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (212, '分析_12.jpg', '/storage/2024/4/分析_12.jpg', '2024-09-10 16:26:48.000000', 47, 86555775, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (213, '预算_13.xlsx', '/storage/2024/12/预算_13.xlsx', '2024-09-15 16:26:48.000000', 39, 75140947, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (214, '设计_14.pdf', '/storage/2024/2/设计_14.pdf', '2025-03-18 16:26:48.000000', 42, 11258472, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (215, '报告_15.txt', '/storage/2023/2/报告_15.txt', '2025-05-12 16:26:48.000000', 32, 82118286, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (216, '设计_16.txt', '/storage/2023/2/设计_16.txt', '2024-09-29 16:26:48.000000', 44, 45813508, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (217, '记录_17.jpg', '/storage/2022/8/记录_17.jpg', '2024-08-08 16:26:48.000000', 11, 71819393, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (218, '设计_18.txt', '/storage/2023/7/设计_18.txt', '2024-10-28 16:26:48.000000', 17, 4218239, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (219, '设计_19.jpg', '/storage/2022/2/设计_19.jpg', '2025-02-16 16:26:48.000000', 35, 100416258, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (220, '总结_20.xlsx', '/storage/2023/6/总结_20.xlsx', '2024-11-12 16:26:48.000000', 42, 36364703, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (221, '设计_21.jpg', '/storage/2024/10/设计_21.jpg', '2025-01-09 16:26:48.000000', 41, 68292284, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (222, '预算_22.xlsx', '/storage/2022/2/预算_22.xlsx', '2025-06-15 16:26:48.000000', 42, 91671323, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (223, '总结_23.png', '/storage/2024/11/总结_23.png', '2024-09-19 16:26:48.000000', 42, 21178397, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (224, '设计_24.pptx', '/storage/2022/6/设计_24.pptx', '2025-01-27 16:26:49.000000', 10, 94820154, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (225, '手册_25.xlsx', '/storage/2023/2/手册_25.xlsx', '2025-02-16 16:26:49.000000', 36, 42339338, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (226, '合同_26.png', '/storage/2024/5/合同_26.png', '2024-09-29 16:26:49.000000', 38, 43196863, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (227, '合同_27.docx', '/storage/2022/10/合同_27.docx', '2025-01-31 16:26:49.000000', 36, 47184615, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (228, '分析_28.jpg', '/storage/2022/7/分析_28.jpg', '2025-03-23 16:26:49.000000', 49, 96248957, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (229, '总结_29.xlsx', '/storage/2024/11/总结_29.xlsx', '2024-12-16 16:26:49.000000', 49, 42932946, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (230, '报告_30.pptx', '/storage/2022/9/报告_30.pptx', '2025-03-03 16:26:49.000000', 16, 103700293, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (231, '预算_31.pptx', '/storage/2022/2/预算_31.pptx', '2024-10-07 16:26:49.000000', 3, 43551141, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (232, '分析_32.pdf', '/storage/2024/1/分析_32.pdf', '2025-06-06 16:26:49.000000', 30, 42458418, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (233, '记录_33.jpg', '/storage/2024/4/记录_33.jpg', '2025-05-18 16:26:49.000000', 29, 92386573, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (234, '项目_34.jpg', '/storage/2024/1/项目_34.jpg', '2024-09-21 16:26:49.000000', 45, 97224464, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (235, '方案_35.pptx', '/storage/2023/6/方案_35.pptx', '2024-10-02 16:26:49.000000', 23, 72295569, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (236, '总结_36.txt', '/storage/2023/4/总结_36.txt', '2025-04-10 16:26:49.000000', 6, 85412609, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (237, '记录_37.docx', '/storage/2022/10/记录_37.docx', '2025-01-21 16:26:49.000000', 46, 88797054, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (238, '手册_38.png', '/storage/2023/12/手册_38.png', '2024-11-29 16:26:49.000000', 21, 89678015, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (239, '记录_39.png', '/storage/2022/8/记录_39.png', '2025-01-28 16:26:50.000000', 25, 90787404, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (240, '手册_40.docx', '/storage/2022/3/手册_40.docx', '2025-04-01 16:26:50.000000', 45, 22892636, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (241, '预算_41.xlsx', '/storage/2023/8/预算_41.xlsx', '2025-01-18 16:26:50.000000', 20, 3289613, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (242, '方案_42.png', '/storage/2022/8/方案_42.png', '2024-09-04 16:26:50.000000', 27, 28707964, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (243, '方案_43.txt', '/storage/2024/11/方案_43.txt', '2024-12-29 16:26:50.000000', 11, 28706024, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (244, '记录_44.txt', '/storage/2023/2/记录_44.txt', '2024-12-25 16:26:50.000000', 3, 1306098, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (245, '手册_45.jpg', '/storage/2022/9/手册_45.jpg', '2024-09-05 16:26:50.000000', 26, 64896179, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (246, '总结_46.xlsx', '/storage/2024/2/总结_46.xlsx', '2025-02-27 16:26:50.000000', 19, 16305031, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (247, '预算_47.docx', '/storage/2024/5/预算_47.docx', '2025-01-17 16:26:50.000000', 39, 28745629, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (248, '方案_48.txt', '/storage/2023/8/方案_48.txt', '2024-11-14 16:26:50.000000', 10, 44183955, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (249, '项目_49.txt', '/storage/2024/5/项目_49.txt', '2024-07-14 16:26:50.000000', 48, 24600753, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (250, '报告_50.jpg', '/storage/2024/2/报告_50.jpg', '2025-06-01 16:26:50.000000', 33, 1299671, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (251, '报告_51.pdf', '/storage/2023/4/报告_51.pdf', '2025-03-02 16:26:50.000000', 44, 101791070, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (252, '合同_52.png', '/storage/2023/2/合同_52.png', '2025-04-10 16:26:50.000000', 36, 87590861, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (253, '记录_53.jpg', '/storage/2023/11/记录_53.jpg', '2024-10-13 16:26:50.000000', 20, 24082822, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (254, '预算_54.docx', '/storage/2022/5/预算_54.docx', '2024-12-09 16:26:50.000000', 45, 49942018, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (255, '记录_55.png', '/storage/2022/7/记录_55.png', '2025-04-13 16:26:51.000000', 26, 52642307, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (256, '记录_56.pptx', '/storage/2023/5/记录_56.pptx', '2025-05-27 16:26:51.000000', 22, 34364090, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (257, '预算_57.xlsx', '/storage/2024/9/预算_57.xlsx', '2025-01-29 16:26:51.000000', 21, 66306325, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (258, '报告_58.pptx', '/storage/2022/4/报告_58.pptx', '2025-05-11 16:26:51.000000', 28, 91981845, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (259, '报告_59.pptx', '/storage/2023/9/报告_59.pptx', '2024-11-15 16:26:51.000000', 1, 2159614, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (260, '方案_60.pptx', '/storage/2022/7/方案_60.pptx', '2024-11-13 16:26:51.000000', 39, 80613818, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (261, '预算_61.pdf', '/storage/2022/11/预算_61.pdf', '2025-03-12 16:26:51.000000', 8, 82686064, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (262, '报告_62.png', '/storage/2024/11/报告_62.png', '2024-09-08 16:26:51.000000', 16, 95990629, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (263, '方案_63.jpg', '/storage/2024/11/方案_63.jpg', '2024-12-20 16:26:51.000000', 18, 70367925, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (264, '分析_64.jpg', '/storage/2024/5/分析_64.jpg', '2024-08-17 16:26:51.000000', 33, 14344765, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (265, '预算_65.docx', '/storage/2023/5/预算_65.docx', '2024-09-25 16:26:51.000000', 27, 77012184, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (266, '记录_66.png', '/storage/2024/11/记录_66.png', '2024-12-15 16:26:51.000000', 25, 81338652, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (267, '方案_67.txt', '/storage/2023/10/方案_67.txt', '2025-06-06 16:26:51.000000', 8, 63351952, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (268, '方案_68.pdf', '/storage/2024/1/方案_68.pdf', '2025-01-29 16:26:51.000000', 26, 55772364, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (269, '合同_69.jpg', '/storage/2022/3/合同_69.jpg', '2024-11-12 16:26:51.000000', 15, 68408421, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (270, '记录_70.txt', '/storage/2023/8/记录_70.txt', '2024-12-12 16:26:52.000000', 23, 33794109, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (271, '方案_71.jpg', '/storage/2022/8/方案_71.jpg', '2024-10-25 16:26:52.000000', 18, 76536962, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (272, '方案_72.pdf', '/storage/2023/10/方案_72.pdf', '2025-05-10 16:26:52.000000', 11, 14901604, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (273, '记录_73.jpg', '/storage/2023/11/记录_73.jpg', '2025-01-29 16:26:52.000000', 34, 12151525, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (274, '设计_74.pdf', '/storage/2023/4/设计_74.pdf', '2024-07-06 16:26:52.000000', 38, 10181274, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (275, '设计_75.xlsx', '/storage/2022/12/设计_75.xlsx', '2024-12-02 16:26:52.000000', 11, 85614080, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (276, '预算_76.xlsx', '/storage/2022/2/预算_76.xlsx', '2024-06-27 16:26:52.000000', 43, 43991904, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (277, '总结_77.png', '/storage/2024/2/总结_77.png', '2024-12-13 16:26:52.000000', 10, 100298560, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (278, '总结_78.xlsx', '/storage/2024/7/总结_78.xlsx', '2025-03-15 16:26:52.000000', 11, 33062797, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (279, '总结_79.png', '/storage/2024/5/总结_79.png', '2025-03-18 16:26:52.000000', 24, 69257546, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (280, '报告_80.xlsx', '/storage/2022/11/报告_80.xlsx', '2025-02-07 16:26:52.000000', 3, 100440019, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (281, '预算_81.pdf', '/storage/2023/4/预算_81.pdf', '2024-11-13 16:26:52.000000', 32, 82955841, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (282, '分析_82.pptx', '/storage/2024/11/分析_82.pptx', '2024-09-21 16:26:52.000000', 14, 23004884, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (283, '总结_83.jpg', '/storage/2023/5/总结_83.jpg', '2024-08-04 16:26:52.000000', 3, 13517483, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (284, '记录_84.docx', '/storage/2023/11/记录_84.docx', '2024-09-21 16:26:52.000000', 46, 32873364, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (285, '记录_85.pptx', '/storage/2022/10/记录_85.pptx', '2025-01-31 16:26:52.000000', 23, 35032277, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (286, '记录_86.txt', '/storage/2023/12/记录_86.txt', '2025-03-01 16:26:53.000000', 1, 94050023, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (287, '总结_87.docx', '/storage/2023/7/总结_87.docx', '2025-01-05 16:26:53.000000', 3, 94252062, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (288, '记录_88.pptx', '/storage/2023/5/记录_88.pptx', '2025-05-19 16:26:53.000000', 40, 33545781, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (289, '记录_89.xlsx', '/storage/2022/3/记录_89.xlsx', '2024-11-22 16:26:53.000000', 25, 32571, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (290, '记录_90.xlsx', '/storage/2023/3/记录_90.xlsx', '2024-09-29 16:26:53.000000', 14, 32043248, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (291, '报告_91.pptx', '/storage/2022/1/报告_91.pptx', '2025-05-15 16:26:53.000000', 44, 82434086, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (292, '设计_92.pptx', '/storage/2023/6/设计_92.pptx', '2025-01-08 16:26:53.000000', 5, 101434796, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (293, '记录_93.pdf', '/storage/2024/1/记录_93.pdf', '2025-02-02 16:26:53.000000', 6, 54646016, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (294, '总结_94.pdf', '/storage/2022/12/总结_94.pdf', '2025-06-03 16:26:53.000000', 22, 10791490, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (295, '方案_95.pdf', '/storage/2022/10/方案_95.pdf', '2025-05-18 16:26:53.000000', 38, 24955893, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (296, '设计_96.png', '/storage/2023/3/设计_96.png', '2025-05-29 16:26:53.000000', 34, 100844280, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (297, '设计_97.docx', '/storage/2022/2/设计_97.docx', '2024-10-28 16:26:53.000000', 50, 100761686, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (298, '方案_98.docx', '/storage/2023/2/方案_98.docx', '2025-03-27 16:26:53.000000', 18, 84691942, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (299, '总结_99.jpg', '/storage/2023/4/总结_99.jpg', '2025-05-26 16:26:53.000000', 50, 19322761, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (300, '分析_100.pptx', '/storage/2022/5/分析_100.pptx', '2024-08-07 16:26:53.000000', 1, 27034806, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (301, '方案_1.txt', '/storage/2024/8/方案_1.txt', '2025-04-01 16:30:44.000000', 14, 57717553, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (302, '项目_2.png', '/storage/2022/10/项目_2.png', '2024-08-30 16:30:44.000000', 38, 92388295, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (303, '合同_3.xlsx', '/storage/2023/2/合同_3.xlsx', '2025-05-04 16:30:45.000000', 12, 39321280, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (304, '总结_4.txt', '/storage/2024/10/总结_4.txt', '2024-11-07 16:30:45.000000', 13, 28604260, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (305, '记录_5.png', '/storage/2024/12/记录_5.png', '2025-04-18 16:30:45.000000', 18, 58784913, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (306, '预算_6.pdf', '/storage/2023/10/预算_6.pdf', '2025-04-14 16:30:45.000000', 13, 40023556, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (307, '方案_7.docx', '/storage/2024/7/方案_7.docx', '2025-05-31 16:30:45.000000', 39, 35137472, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (308, '分析_8.pptx', '/storage/2022/4/分析_8.pptx', '2025-05-07 16:30:45.000000', 6, 89747131, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (309, '报告_9.pptx', '/storage/2023/7/报告_9.pptx', '2025-04-15 16:30:45.000000', 14, 19742229, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (310, '项目_10.pdf', '/storage/2023/5/项目_10.pdf', '2024-08-18 16:30:45.000000', 41, 13817752, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (311, '设计_11.docx', '/storage/2023/9/设计_11.docx', '2024-11-24 16:30:45.000000', 29, 91274, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (312, '预算_12.pdf', '/storage/2022/5/预算_12.pdf', '2024-07-19 16:30:45.000000', 36, 10443481, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (313, '设计_13.png', '/storage/2024/6/设计_13.png', '2024-08-07 16:30:45.000000', 15, 27527065, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (314, '分析_14.png', '/storage/2022/1/分析_14.png', '2025-02-16 16:30:45.000000', 39, 55218118, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (315, '预算_15.png', '/storage/2024/6/预算_15.png', '2024-10-21 16:30:45.000000', 37, 80121581, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (316, '合同_16.pptx', '/storage/2022/10/合同_16.pptx', '2025-05-01 16:30:45.000000', 42, 99904882, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (317, '预算_17.txt', '/storage/2023/2/预算_17.txt', '2024-10-15 16:30:45.000000', 13, 44921852, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (318, '方案_18.pdf', '/storage/2024/4/方案_18.pdf', '2024-08-08 16:30:45.000000', 39, 63997410, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (319, '报告_19.pdf', '/storage/2023/6/报告_19.pdf', '2025-05-22 16:30:45.000000', 39, 45173105, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (320, '设计_20.png', '/storage/2022/3/设计_20.png', '2024-09-22 16:30:46.000000', 1, 81131016, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (321, '合同_21.png', '/storage/2024/8/合同_21.png', '2024-08-16 16:30:46.000000', 37, 1268359, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (322, '合同_22.txt', '/storage/2024/5/合同_22.txt', '2024-12-28 16:30:46.000000', 27, 8798916, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (323, '预算_23.pptx', '/storage/2023/10/预算_23.pptx', '2025-03-02 16:30:46.000000', 42, 18407022, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (324, '方案_24.pdf', '/storage/2023/7/方案_24.pdf', '2024-10-31 16:30:46.000000', 39, 17203841, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (325, '设计_25.xlsx', '/storage/2024/9/设计_25.xlsx', '2025-02-04 16:30:46.000000', 21, 59396101, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (326, '合同_26.pdf', '/storage/2024/5/合同_26.pdf', '2025-01-03 16:30:46.000000', 28, 24174637, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (327, '手册_27.xlsx', '/storage/2022/3/手册_27.xlsx', '2024-11-12 16:30:46.000000', 45, 97965813, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (328, '记录_28.docx', '/storage/2023/5/记录_28.docx', '2025-06-11 16:30:46.000000', 26, 6517914, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (329, '总结_29.txt', '/storage/2022/6/总结_29.txt', '2024-10-03 16:30:46.000000', 16, 19355481, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (330, '合同_30.txt', '/storage/2023/4/合同_30.txt', '2024-11-06 16:30:46.000000', 48, 92001094, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (331, '总结_31.xlsx', '/storage/2024/12/总结_31.xlsx', '2024-12-26 16:30:46.000000', 22, 13583911, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (332, '手册_32.xlsx', '/storage/2022/4/手册_32.xlsx', '2025-03-24 16:30:46.000000', 19, 33308389, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (333, '总结_33.docx', '/storage/2024/5/总结_33.docx', '2024-11-07 16:30:46.000000', 38, 83430676, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (334, '预算_34.png', '/storage/2022/5/预算_34.png', '2024-10-05 16:30:46.000000', 50, 53946332, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (335, '报告_35.docx', '/storage/2024/8/报告_35.docx', '2024-08-18 16:30:46.000000', 50, 85453012, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (336, '设计_36.jpg', '/storage/2023/8/设计_36.jpg', '2024-08-29 16:30:47.000000', 17, 24923447, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (337, '报告_37.pdf', '/storage/2022/1/报告_37.pdf', '2024-12-06 16:30:47.000000', 12, 50520712, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (338, '合同_38.pptx', '/storage/2022/11/合同_38.pptx', '2025-06-07 16:30:47.000000', 12, 23252313, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (339, '方案_39.docx', '/storage/2023/4/方案_39.docx', '2025-02-26 16:30:47.000000', 28, 90543435, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (340, '方案_40.pptx', '/storage/2022/10/方案_40.pptx', '2024-11-01 16:30:47.000000', 50, 57962478, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (341, '设计_41.pptx', '/storage/2024/9/设计_41.pptx', '2024-11-09 16:30:47.000000', 36, 27252524, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (342, '分析_42.png', '/storage/2024/9/分析_42.png', '2025-05-13 16:30:47.000000', 31, 19884803, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (343, '预算_43.xlsx', '/storage/2022/5/预算_43.xlsx', '2024-11-10 16:30:47.000000', 14, 38709256, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (344, '报告_44.jpg', '/storage/2024/6/报告_44.jpg', '2025-06-05 16:30:47.000000', 21, 82376271, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (345, '记录_45.png', '/storage/2022/5/记录_45.png', '2025-06-13 16:30:47.000000', 22, 53250208, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (346, '分析_46.txt', '/storage/2023/1/分析_46.txt', '2025-02-10 16:30:47.000000', 30, 4866661, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (347, '方案_47.jpg', '/storage/2024/9/方案_47.jpg', '2025-02-03 16:30:47.000000', 18, 3321660, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (348, '方案_48.jpg', '/storage/2023/9/方案_48.jpg', '2024-08-18 16:30:47.000000', 2, 28607320, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (349, '手册_49.xlsx', '/storage/2022/7/手册_49.xlsx', '2024-06-26 16:30:47.000000', 21, 66106967, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (350, '设计_50.png', '/storage/2023/8/设计_50.png', '2024-08-27 16:30:47.000000', 40, 37096904, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (351, '方案_51.docx', '/storage/2024/3/方案_51.docx', '2024-10-04 16:30:47.000000', 16, 18210444, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (352, '分析_52.txt', '/storage/2023/7/分析_52.txt', '2024-11-23 16:30:47.000000', 40, 103491220, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (353, '记录_53.txt', '/storage/2022/3/记录_53.txt', '2024-11-23 16:30:48.000000', 46, 101216531, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (354, '设计_54.png', '/storage/2024/9/设计_54.png', '2025-01-25 16:30:48.000000', 17, 74341670, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (355, '记录_55.xlsx', '/storage/2023/4/记录_55.xlsx', '2024-10-09 16:30:48.000000', 7, 28167560, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (356, '报告_56.pdf', '/storage/2024/1/报告_56.pdf', '2024-07-12 16:30:48.000000', 20, 19725593, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (357, '方案_57.docx', '/storage/2024/3/方案_57.docx', '2024-09-26 16:30:48.000000', 47, 14010086, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (358, '预算_58.pdf', '/storage/2022/2/预算_58.pdf', '2025-03-16 16:30:48.000000', 48, 344430, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (359, '分析_59.pdf', '/storage/2024/12/分析_59.pdf', '2024-08-17 16:30:48.000000', 18, 89120716, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (360, '合同_60.docx', '/storage/2022/3/合同_60.docx', '2024-12-20 16:30:48.000000', 8, 49048446, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (361, '合同_61.png', '/storage/2023/2/合同_61.png', '2025-06-16 16:30:48.000000', 12, 78865235, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (362, '报告_62.png', '/storage/2024/11/报告_62.png', '2025-05-23 16:30:48.000000', 1, 90568363, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (363, '预算_63.pptx', '/storage/2023/12/预算_63.pptx', '2024-12-17 16:30:48.000000', 25, 59042431, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (364, '报告_64.docx', '/storage/2024/7/报告_64.docx', '2025-04-16 16:30:48.000000', 17, 7151409, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (365, '项目_65.pdf', '/storage/2023/10/项目_65.pdf', '2025-05-06 16:30:48.000000', 3, 65603619, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (366, '方案_66.jpg', '/storage/2023/10/方案_66.jpg', '2024-07-11 16:30:48.000000', 14, 100685670, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (367, '项目_67.png', '/storage/2023/6/项目_67.png', '2024-08-16 16:30:48.000000', 21, 102596054, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (368, '手册_68.jpg', '/storage/2023/1/手册_68.jpg', '2025-05-03 16:30:48.000000', 31, 15517618, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (369, '合同_69.txt', '/storage/2023/1/合同_69.txt', '2025-05-29 16:30:48.000000', 35, 7708914, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (370, '项目_70.pdf', '/storage/2023/3/项目_70.pdf', '2024-09-04 16:30:49.000000', 15, 56633962, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (371, '预算_71.txt', '/storage/2024/6/预算_71.txt', '2025-03-02 16:30:49.000000', 12, 22991218, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (372, '项目_72.jpg', '/storage/2024/1/项目_72.jpg', '2024-10-27 16:30:49.000000', 36, 66002937, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (373, '设计_73.pptx', '/storage/2023/8/设计_73.pptx', '2025-04-14 16:30:49.000000', 14, 51834096, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (374, '合同_74.png', '/storage/2023/3/合同_74.png', '2025-05-28 16:30:49.000000', 35, 30885560, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (375, '设计_75.jpg', '/storage/2023/5/设计_75.jpg', '2025-05-01 16:30:49.000000', 20, 45526097, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (376, '记录_76.pdf', '/storage/2024/7/记录_76.pdf', '2025-03-15 16:30:49.000000', 22, 79965383, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (377, '预算_77.pptx', '/storage/2022/5/预算_77.pptx', '2025-04-22 16:30:49.000000', 6, 14014433, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (378, '手册_78.pptx', '/storage/2024/7/手册_78.pptx', '2025-02-25 16:30:49.000000', 12, 88617667, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (379, '记录_79.xlsx', '/storage/2024/3/记录_79.xlsx', '2025-04-30 16:30:49.000000', 1, 10617874, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (380, '方案_80.txt', '/storage/2024/12/方案_80.txt', '2024-07-12 16:30:49.000000', 12, 23250258, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (381, '报告_81.txt', '/storage/2024/3/报告_81.txt', '2025-04-25 16:30:49.000000', 3, 7188833, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (382, '报告_82.xlsx', '/storage/2023/3/报告_82.xlsx', '2024-07-07 16:30:49.000000', 32, 77606352, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (383, '报告_83.pptx', '/storage/2022/7/报告_83.pptx', '2025-02-20 16:30:49.000000', 47, 11101961, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (384, '设计_84.pdf', '/storage/2022/1/设计_84.pdf', '2024-10-02 16:30:49.000000', 11, 42694444, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (385, '预算_85.xlsx', '/storage/2022/8/预算_85.xlsx', '2024-10-07 16:30:49.000000', 29, 59365325, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (386, '项目_86.pptx', '/storage/2023/5/项目_86.pptx', '2025-02-10 16:30:50.000000', 9, 88915823, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (387, '合同_87.docx', '/storage/2024/8/合同_87.docx', '2025-01-29 16:30:50.000000', 15, 1921311, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (388, '预算_88.png', '/storage/2023/11/预算_88.png', '2025-01-21 16:30:50.000000', 16, 62249465, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (389, '报告_89.pptx', '/storage/2023/2/报告_89.pptx', '2024-12-09 16:30:50.000000', 9, 50889702, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (390, '分析_90.png', '/storage/2023/1/分析_90.png', '2025-01-30 16:30:50.000000', 2, 10698170, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (391, '报告_91.xlsx', '/storage/2024/2/报告_91.xlsx', '2025-04-06 16:30:50.000000', 48, 67249988, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (392, '设计_92.xlsx', '/storage/2024/12/设计_92.xlsx', '2025-04-04 16:30:50.000000', 13, 73155122, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (393, '报告_93.txt', '/storage/2024/12/报告_93.txt', '2024-12-23 16:30:50.000000', 17, 83021156, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (394, '手册_94.pdf', '/storage/2022/7/手册_94.pdf', '2025-02-25 16:30:50.000000', 42, 2297891, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (395, '设计_95.docx', '/storage/2023/12/设计_95.docx', '2024-08-08 16:30:50.000000', 43, 40623414, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (396, '报告_96.docx', '/storage/2022/12/报告_96.docx', '2024-12-18 16:30:50.000000', 6, 32686576, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (397, '合同_97.txt', '/storage/2023/5/合同_97.txt', '2024-06-30 16:30:50.000000', 11, 20627124, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (398, '手册_98.xlsx', '/storage/2023/7/手册_98.xlsx', '2025-06-06 16:30:50.000000', 16, 15123217, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (399, '设计_99.jpg', '/storage/2024/2/设计_99.jpg', '2024-09-16 16:30:50.000000', 8, 79757765, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (400, '记录_100.docx', '/storage/2023/8/记录_100.docx', '2025-03-18 16:30:50.000000', 50, 13210828, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (401, '方案_1.jpg', '/storage/2022/3/方案_1.jpg', '2024-12-22 16:57:33.000000', 15, 6054022, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (402, '方案_2.jpg', '/storage/2023/4/方案_2.jpg', '2024-09-17 16:57:34.000000', 27, 81055055, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (403, '设计_3.docx', '/storage/2022/2/设计_3.docx', '2025-05-28 16:57:34.000000', 38, 6794543, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (404, '预算_4.jpg', '/storage/2024/9/预算_4.jpg', '2025-06-11 16:57:34.000000', 48, 70320429, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (405, '合同_5.pdf', '/storage/2022/2/合同_5.pdf', '2024-06-28 16:57:34.000000', 32, 5667353, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (406, '预算_6.jpg', '/storage/2023/1/预算_6.jpg', '2024-11-23 16:57:34.000000', 47, 50274021, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (407, '报告_7.jpg', '/storage/2022/8/报告_7.jpg', '2025-04-23 16:57:34.000000', 32, 96949393, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (408, '总结_8.txt', '/storage/2024/4/总结_8.txt', '2025-01-17 16:57:34.000000', 27, 65970518, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (409, '报告_9.xlsx', '/storage/2022/8/报告_9.xlsx', '2024-09-16 16:57:34.000000', 42, 89533750, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (410, '分析_10.docx', '/storage/2022/9/分析_10.docx', '2024-10-04 16:57:34.000000', 3, 8433707, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (411, '预算_11.pptx', '/storage/2022/6/预算_11.pptx', '2025-02-15 16:57:34.000000', 1, 5851674, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (412, '分析_12.pdf', '/storage/2022/2/分析_12.pdf', '2025-03-21 16:57:34.000000', 45, 4543755, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (413, '记录_13.pdf', '/storage/2022/7/记录_13.pdf', '2024-10-19 16:57:34.000000', 14, 31552951, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (414, '记录_14.jpg', '/storage/2022/5/记录_14.jpg', '2024-06-30 16:57:34.000000', 20, 42374650, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (415, '手册_15.pdf', '/storage/2023/10/手册_15.pdf', '2025-01-26 16:57:34.000000', 33, 72168646, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (416, '设计_16.png', '/storage/2024/2/设计_16.png', '2024-12-31 16:57:34.000000', 4, 37434242, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (417, '记录_17.xlsx', '/storage/2023/4/记录_17.xlsx', '2024-09-26 16:57:34.000000', 46, 16105531, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (418, '方案_18.jpg', '/storage/2022/9/方案_18.jpg', '2025-04-03 16:57:35.000000', 10, 69853595, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (419, '总结_19.jpg', '/storage/2022/1/总结_19.jpg', '2024-11-28 16:57:35.000000', 31, 80038514, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (420, '项目_20.txt', '/storage/2022/8/项目_20.txt', '2025-04-02 16:57:35.000000', 9, 68634386, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (421, '设计_21.docx', '/storage/2022/8/设计_21.docx', '2025-01-13 16:57:35.000000', 48, 11730455, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (422, '设计_22.docx', '/storage/2024/4/设计_22.docx', '2024-10-08 16:57:35.000000', 42, 16632679, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (423, '合同_23.jpg', '/storage/2024/7/合同_23.jpg', '2025-04-04 16:57:35.000000', 21, 63633657, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (424, '记录_24.jpg', '/storage/2024/5/记录_24.jpg', '2025-02-19 16:57:35.000000', 17, 103507081, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (425, '设计_25.txt', '/storage/2024/8/设计_25.txt', '2024-10-17 16:57:35.000000', 35, 24804115, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (426, '报告_26.txt', '/storage/2024/5/报告_26.txt', '2024-09-28 16:57:35.000000', 31, 24511536, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (427, '总结_27.txt', '/storage/2023/9/总结_27.txt', '2025-03-04 16:57:35.000000', 12, 32365296, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (428, '合同_28.xlsx', '/storage/2022/5/合同_28.xlsx', '2025-04-29 16:57:35.000000', 46, 95985480, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (429, '方案_29.pdf', '/storage/2022/2/方案_29.pdf', '2024-09-10 16:57:35.000000', 8, 55681128, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (430, '设计_30.jpg', '/storage/2023/8/设计_30.jpg', '2024-12-24 16:57:35.000000', 15, 102240235, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (431, '方案_31.pptx', '/storage/2022/2/方案_31.pptx', '2024-07-22 16:57:35.000000', 22, 38848275, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (432, '方案_32.txt', '/storage/2024/1/方案_32.txt', '2024-09-15 16:57:35.000000', 32, 87417332, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (433, '总结_33.pptx', '/storage/2024/2/总结_33.pptx', '2025-01-13 16:57:35.000000', 2, 29072099, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (434, '报告_34.pptx', '/storage/2022/3/报告_34.pptx', '2024-11-24 16:57:36.000000', 14, 57221160, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (435, '方案_35.jpg', '/storage/2023/10/方案_35.jpg', '2025-01-04 16:57:36.000000', 17, 53708245, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (436, '报告_36.pptx', '/storage/2022/10/报告_36.pptx', '2025-01-10 16:57:36.000000', 32, 39922196, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (437, '合同_37.png', '/storage/2023/6/合同_37.png', '2024-10-17 16:57:36.000000', 1, 63160127, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (438, '手册_38.pptx', '/storage/2022/8/手册_38.pptx', '2024-10-15 16:57:36.000000', 29, 74158927, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (439, '分析_39.pdf', '/storage/2022/6/分析_39.pdf', '2025-06-21 16:57:36.000000', 4, 64820180, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (440, '手册_40.pptx', '/storage/2022/2/手册_40.pptx', '2025-05-20 16:57:36.000000', 34, 6333320, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (441, '方案_41.docx', '/storage/2023/5/方案_41.docx', '2024-07-25 16:57:36.000000', 45, 89431766, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (442, '总结_42.txt', '/storage/2022/9/总结_42.txt', '2025-04-18 16:57:36.000000', 39, 12673894, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (443, '记录_43.pdf', '/storage/2024/8/记录_43.pdf', '2025-05-22 16:57:36.000000', 14, 56387088, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (444, '预算_44.txt', '/storage/2024/9/预算_44.txt', '2024-07-21 16:57:36.000000', 2, 41064867, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (445, '总结_45.jpg', '/storage/2024/5/总结_45.jpg', '2024-08-01 16:57:36.000000', 45, 83065841, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (446, '报告_46.png', '/storage/2023/11/报告_46.png', '2024-07-05 16:57:36.000000', 18, 43063268, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (447, '总结_47.jpg', '/storage/2022/8/总结_47.jpg', '2025-01-12 16:57:36.000000', 13, 38109562, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (448, '设计_48.pdf', '/storage/2023/6/设计_48.pdf', '2024-07-18 16:57:36.000000', 43, 34814472, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (449, '分析_49.txt', '/storage/2022/9/分析_49.txt', '2025-04-05 16:57:36.000000', 17, 15769459, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (450, '总结_50.pptx', '/storage/2023/10/总结_50.pptx', '2025-06-12 16:57:37.000000', 43, 39608588, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (451, '总结_51.pptx', '/storage/2023/4/总结_51.pptx', '2025-05-17 16:57:37.000000', 2, 41329619, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (452, '记录_52.docx', '/storage/2024/10/记录_52.docx', '2025-04-24 16:57:37.000000', 29, 33924786, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (453, '合同_53.jpg', '/storage/2023/12/合同_53.jpg', '2024-06-24 16:57:37.000000', 15, 1948185, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (454, '预算_54.jpg', '/storage/2024/1/预算_54.jpg', '2025-06-02 16:57:37.000000', 11, 21526771, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (455, '报告_55.docx', '/storage/2023/2/报告_55.docx', '2024-08-08 16:57:37.000000', 26, 74695474, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (456, '方案_56.png', '/storage/2022/3/方案_56.png', '2025-06-19 16:57:37.000000', 4, 37201819, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (457, '预算_57.pdf', '/storage/2024/2/预算_57.pdf', '2025-06-19 16:57:37.000000', 11, 26116186, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (458, '项目_58.docx', '/storage/2023/10/项目_58.docx', '2025-01-14 16:57:37.000000', 32, 65614198, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (459, '项目_59.txt', '/storage/2022/1/项目_59.txt', '2025-03-19 16:57:37.000000', 17, 20637983, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (460, '设计_60.png', '/storage/2022/11/设计_60.png', '2024-06-23 16:57:37.000000', 30, 38093373, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (461, '总结_61.txt', '/storage/2022/2/总结_61.txt', '2024-11-15 16:57:37.000000', 2, 10675628, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (462, '记录_62.png', '/storage/2024/4/记录_62.png', '2025-02-17 16:57:37.000000', 43, 77155945, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (463, '设计_63.pptx', '/storage/2023/2/设计_63.pptx', '2025-02-16 16:57:37.000000', 47, 79464359, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (464, '方案_64.pptx', '/storage/2023/11/方案_64.pptx', '2024-09-02 16:57:37.000000', 46, 55334150, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (465, '预算_65.docx', '/storage/2023/2/预算_65.docx', '2025-01-26 16:57:37.000000', 21, 91685510, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (466, '设计_66.pdf', '/storage/2022/8/设计_66.pdf', '2025-06-21 16:57:38.000000', 17, 37090950, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (467, '合同_67.docx', '/storage/2023/9/合同_67.docx', '2024-07-31 16:57:38.000000', 45, 81553576, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (468, '设计_68.xlsx', '/storage/2023/1/设计_68.xlsx', '2024-09-30 16:57:38.000000', 48, 43831551, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (469, '方案_69.txt', '/storage/2022/11/方案_69.txt', '2024-11-08 16:57:38.000000', 34, 7586123, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (470, '预算_70.xlsx', '/storage/2022/3/预算_70.xlsx', '2025-02-18 16:57:38.000000', 22, 74540100, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (471, '手册_71.xlsx', '/storage/2023/4/手册_71.xlsx', '2025-06-03 16:57:38.000000', 45, 67737089, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (472, '报告_72.pdf', '/storage/2022/6/报告_72.pdf', '2025-04-21 16:57:38.000000', 16, 28563343, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (473, '分析_73.png', '/storage/2022/4/分析_73.png', '2024-12-19 16:57:38.000000', 28, 79162549, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (474, '合同_74.docx', '/storage/2023/6/合同_74.docx', '2024-09-10 16:57:38.000000', 31, 80188821, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (475, '项目_75.pdf', '/storage/2024/8/项目_75.pdf', '2024-12-06 16:57:38.000000', 50, 45883557, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (476, '设计_76.xlsx', '/storage/2023/12/设计_76.xlsx', '2024-07-20 16:57:38.000000', 43, 97352738, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (477, '记录_77.pptx', '/storage/2022/11/记录_77.pptx', '2024-08-18 16:57:38.000000', 50, 48725247, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (478, '项目_78.docx', '/storage/2022/1/项目_78.docx', '2024-08-24 16:57:38.000000', 27, 53227629, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (479, '设计_79.txt', '/storage/2022/7/设计_79.txt', '2024-06-29 16:57:38.000000', 8, 93269208, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (480, '记录_80.jpg', '/storage/2024/6/记录_80.jpg', '2024-10-23 16:57:38.000000', 6, 92743287, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (481, '分析_81.xlsx', '/storage/2022/5/分析_81.xlsx', '2025-03-06 16:57:38.000000', 32, 44195348, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (482, '分析_82.jpg', '/storage/2022/3/分析_82.jpg', '2024-10-12 16:57:39.000000', 30, 70633218, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (483, '项目_83.jpg', '/storage/2023/9/项目_83.jpg', '2024-10-27 16:57:39.000000', 33, 21175444, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (484, '设计_84.docx', '/storage/2024/5/设计_84.docx', '2024-08-06 16:57:39.000000', 31, 37470856, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (485, '报告_85.png', '/storage/2024/9/报告_85.png', '2025-02-05 16:57:39.000000', 33, 88606581, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (486, '预算_86.png', '/storage/2024/5/预算_86.png', '2025-06-19 16:57:39.000000', 30, 9459191, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (487, '报告_87.xlsx', '/storage/2023/1/报告_87.xlsx', '2025-05-17 16:57:39.000000', 12, 46172228, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (488, '项目_88.docx', '/storage/2023/11/项目_88.docx', '2024-09-30 16:57:39.000000', 15, 2759085, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (489, '预算_89.xlsx', '/storage/2023/12/预算_89.xlsx', '2025-01-23 16:57:39.000000', 19, 88527721, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (490, '分析_90.pptx', '/storage/2024/3/分析_90.pptx', '2025-05-12 16:57:39.000000', 15, 74731026, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (491, '分析_91.txt', '/storage/2024/1/分析_91.txt', '2025-04-15 16:57:39.000000', 40, 80069303, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (492, '报告_92.jpg', '/storage/2024/10/报告_92.jpg', '2024-11-07 16:57:39.000000', 34, 25327284, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (493, '预算_93.pptx', '/storage/2024/7/预算_93.pptx', '2025-01-31 16:57:39.000000', 1, 17430757, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (494, '分析_94.png', '/storage/2022/6/分析_94.png', '2024-09-25 16:57:39.000000', 42, 91331234, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (495, '手册_95.txt', '/storage/2022/8/手册_95.txt', '2024-10-19 16:57:39.000000', 50, 77620630, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (496, '报告_96.docx', '/storage/2023/10/报告_96.docx', '2024-10-30 16:57:39.000000', 25, 97514155, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (497, '分析_97.docx', '/storage/2024/10/分析_97.docx', '2024-09-07 16:57:39.000000', 5, 37853579, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (498, '预算_98.xlsx', '/storage/2022/1/预算_98.xlsx', '2024-07-26 16:57:40.000000', 32, 50556112, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (499, '预算_99.docx', '/storage/2023/7/预算_99.docx', '2024-06-23 16:57:40.000000', 1, 86784475, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (500, '报告_100.jpg', '/storage/2022/8/报告_100.jpg', '2024-08-13 16:57:40.000000', 4, 7634943, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (501, '项目_1.pptx', '/storage/2024/9/项目_1.pptx', '2025-01-10 17:12:35.000000', 36, 68622041, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (502, '分析_2.png', '/storage/2022/2/分析_2.png', '2025-06-04 17:12:35.000000', 47, 67356860, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (503, '预算_3.jpg', '/storage/2022/8/预算_3.jpg', '2025-02-20 17:12:35.000000', 33, 73496292, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (504, '总结_4.txt', '/storage/2024/1/总结_4.txt', '2025-06-18 17:12:35.000000', 17, 71769463, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (505, '方案_5.jpg', '/storage/2022/10/方案_5.jpg', '2025-06-01 17:12:35.000000', 48, 68171142, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (506, '合同_6.pdf', '/storage/2024/9/合同_6.pdf', '2024-12-01 17:12:35.000000', 10, 77278388, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (507, '方案_7.jpg', '/storage/2024/12/方案_7.jpg', '2025-05-12 17:12:35.000000', 12, 18820739, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (508, '报告_8.txt', '/storage/2024/9/报告_8.txt', '2024-12-19 17:12:35.000000', 25, 98179792, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (509, '总结_9.pptx', '/storage/2023/5/总结_9.pptx', '2025-03-01 17:12:35.000000', 40, 7624412, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (510, '预算_10.txt', '/storage/2024/8/预算_10.txt', '2025-02-18 17:12:35.000000', 12, 49247247, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (511, '项目_11.docx', '/storage/2023/2/项目_11.docx', '2025-02-07 17:12:35.000000', 47, 43072387, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (512, '方案_12.docx', '/storage/2022/4/方案_12.docx', '2024-10-11 17:12:35.000000', 9, 42376799, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (513, '记录_13.png', '/storage/2024/12/记录_13.png', '2025-04-13 17:12:35.000000', 41, 64185796, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (514, '分析_14.xlsx', '/storage/2023/3/分析_14.xlsx', '2025-03-16 17:12:36.000000', 25, 37912725, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (515, '项目_15.docx', '/storage/2024/12/项目_15.docx', '2024-09-21 17:12:36.000000', 33, 19957409, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (516, '项目_16.png', '/storage/2024/8/项目_16.png', '2024-07-26 17:12:36.000000', 3, 41857025, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (517, '项目_17.docx', '/storage/2024/6/项目_17.docx', '2024-09-19 17:12:36.000000', 5, 54991283, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (518, '总结_18.pdf', '/storage/2023/9/总结_18.pdf', '2025-04-30 17:12:36.000000', 20, 16221719, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (519, '记录_19.xlsx', '/storage/2024/8/记录_19.xlsx', '2024-09-07 17:12:36.000000', 14, 95638339, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (520, '方案_20.txt', '/storage/2024/4/方案_20.txt', '2025-06-22 17:12:36.000000', 8, 21914364, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (521, '项目_21.xlsx', '/storage/2024/3/项目_21.xlsx', '2024-08-10 17:12:36.000000', 9, 72625555, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (522, '设计_22.pptx', '/storage/2023/11/设计_22.pptx', '2024-11-26 17:12:36.000000', 12, 101696896, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (523, '设计_23.pdf', '/storage/2022/2/设计_23.pdf', '2025-06-13 17:12:36.000000', 30, 37511803, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (524, '分析_24.jpg', '/storage/2024/9/分析_24.jpg', '2024-08-19 17:12:36.000000', 1, 4358632, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (525, '项目_25.jpg', '/storage/2022/9/项目_25.jpg', '2024-10-22 17:12:36.000000', 50, 27190916, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (526, '记录_26.png', '/storage/2023/4/记录_26.png', '2024-09-11 17:12:36.000000', 22, 88052691, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (527, '项目_27.xlsx', '/storage/2022/11/项目_27.xlsx', '2024-11-21 17:12:36.000000', 19, 67526705, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (528, '总结_28.png', '/storage/2024/2/总结_28.png', '2024-08-10 17:12:36.000000', 26, 14586676, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (529, '合同_29.txt', '/storage/2022/3/合同_29.txt', '2025-05-20 17:12:37.000000', 26, 71334658, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (530, '总结_30.pptx', '/storage/2023/7/总结_30.pptx', '2025-02-21 17:12:37.000000', 5, 46138009, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (531, '手册_31.pptx', '/storage/2023/10/手册_31.pptx', '2024-12-28 17:12:37.000000', 8, 72069150, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (532, '记录_32.pdf', '/storage/2022/6/记录_32.pdf', '2024-07-22 17:12:37.000000', 47, 59751817, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (533, '合同_33.png', '/storage/2024/9/合同_33.png', '2025-02-04 17:12:37.000000', 17, 19743049, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (534, '设计_34.docx', '/storage/2023/4/设计_34.docx', '2025-03-31 17:12:37.000000', 16, 6378124, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (535, '手册_35.xlsx', '/storage/2023/11/手册_35.xlsx', '2024-10-09 17:12:37.000000', 48, 55904279, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (536, '合同_36.txt', '/storage/2023/12/合同_36.txt', '2024-08-06 17:12:37.000000', 6, 22198337, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (537, '项目_37.txt', '/storage/2024/7/项目_37.txt', '2024-11-26 17:12:37.000000', 18, 91522462, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (538, '合同_38.docx', '/storage/2022/10/合同_38.docx', '2024-12-23 17:12:37.000000', 11, 56197530, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (539, '设计_39.xlsx', '/storage/2024/9/设计_39.xlsx', '2025-05-06 17:12:37.000000', 22, 33443309, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (540, '合同_40.pptx', '/storage/2022/11/合同_40.pptx', '2024-09-08 17:12:37.000000', 30, 81226486, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (541, '预算_41.jpg', '/storage/2023/7/预算_41.jpg', '2024-10-26 17:12:37.000000', 43, 41511637, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (542, '方案_42.pptx', '/storage/2023/12/方案_42.pptx', '2025-06-22 17:12:37.000000', 3, 28244891, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (543, '记录_43.jpg', '/storage/2023/10/记录_43.jpg', '2024-10-22 17:12:37.000000', 23, 97695426, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (544, '项目_44.xlsx', '/storage/2022/6/项目_44.xlsx', '2024-08-21 17:12:37.000000', 6, 48402951, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (545, '合同_45.pptx', '/storage/2022/7/合同_45.pptx', '2025-02-14 17:12:38.000000', 18, 74402697, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (546, '手册_46.png', '/storage/2024/5/手册_46.png', '2024-07-01 17:12:38.000000', 14, 74358009, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (547, '方案_47.pptx', '/storage/2024/12/方案_47.pptx', '2024-12-29 17:12:38.000000', 41, 53586584, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (548, '合同_48.docx', '/storage/2023/6/合同_48.docx', '2024-08-06 17:12:38.000000', 27, 76333616, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (549, '总结_49.pptx', '/storage/2022/10/总结_49.pptx', '2024-11-06 17:12:38.000000', 19, 55907633, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (550, '报告_50.jpg', '/storage/2022/2/报告_50.jpg', '2024-10-16 17:12:38.000000', 7, 56460489, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (551, '预算_51.docx', '/storage/2022/7/预算_51.docx', '2024-07-29 17:12:38.000000', 27, 33340693, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (552, '项目_52.docx', '/storage/2024/10/项目_52.docx', '2025-01-30 17:12:38.000000', 35, 82293558, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (553, '报告_53.jpg', '/storage/2022/2/报告_53.jpg', '2024-07-07 17:12:38.000000', 30, 89935560, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (554, '分析_54.jpg', '/storage/2023/4/分析_54.jpg', '2024-11-20 17:12:38.000000', 42, 67351757, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (555, '分析_55.txt', '/storage/2023/10/分析_55.txt', '2025-03-04 17:12:38.000000', 25, 47797433, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (556, '总结_56.pptx', '/storage/2024/2/总结_56.pptx', '2024-08-11 17:12:38.000000', 42, 102622701, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (557, '项目_57.txt', '/storage/2023/6/项目_57.txt', '2024-08-07 17:12:38.000000', 41, 86317593, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (558, '合同_58.pptx', '/storage/2024/8/合同_58.pptx', '2025-03-15 17:12:38.000000', 17, 23696910, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (559, '报告_59.pptx', '/storage/2024/2/报告_59.pptx', '2024-08-29 17:12:38.000000', 3, 19084229, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (560, '预算_60.png', '/storage/2024/8/预算_60.png', '2025-04-25 17:12:39.000000', 18, 42982797, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (561, '手册_61.pdf', '/storage/2024/9/手册_61.pdf', '2025-05-31 17:12:39.000000', 6, 86661598, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (562, '记录_62.pdf', '/storage/2023/3/记录_62.pdf', '2024-11-05 17:12:39.000000', 32, 17542814, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (563, '项目_63.pptx', '/storage/2023/7/项目_63.pptx', '2025-01-08 17:12:39.000000', 43, 22153267, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (564, '方案_64.txt', '/storage/2022/5/方案_64.txt', '2024-10-12 17:12:39.000000', 2, 92502034, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (565, '总结_65.jpg', '/storage/2023/10/总结_65.jpg', '2025-02-05 17:12:39.000000', 47, 49572224, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (566, '合同_66.jpg', '/storage/2024/9/合同_66.jpg', '2025-03-24 17:12:39.000000', 23, 40903000, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (567, '设计_67.jpg', '/storage/2023/2/设计_67.jpg', '2025-04-09 17:12:39.000000', 37, 33408854, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (568, '分析_68.xlsx', '/storage/2023/7/分析_68.xlsx', '2025-02-23 17:12:39.000000', 42, 6707565, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (569, '方案_69.txt', '/storage/2023/3/方案_69.txt', '2024-10-23 17:12:39.000000', 17, 49799739, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (570, '分析_70.jpg', '/storage/2022/9/分析_70.jpg', '2025-01-28 17:12:39.000000', 28, 24813496, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (571, '合同_71.jpg', '/storage/2024/10/合同_71.jpg', '2025-05-20 17:12:39.000000', 12, 21658522, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (572, '总结_72.xlsx', '/storage/2024/3/总结_72.xlsx', '2024-09-21 17:12:39.000000', 41, 74508149, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (573, '项目_73.docx', '/storage/2022/4/项目_73.docx', '2024-08-06 17:12:39.000000', 41, 73362414, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (574, '方案_74.docx', '/storage/2022/8/方案_74.docx', '2024-06-23 17:12:39.000000', 25, 59299386, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (575, '分析_75.jpg', '/storage/2022/7/分析_75.jpg', '2025-05-06 17:12:39.000000', 11, 64342471, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (576, '项目_76.xlsx', '/storage/2022/2/项目_76.xlsx', '2025-06-15 17:12:40.000000', 11, 67797597, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (577, '方案_77.jpg', '/storage/2024/3/方案_77.jpg', '2025-03-09 17:12:40.000000', 11, 41263369, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (578, '报告_78.pdf', '/storage/2023/5/报告_78.pdf', '2024-06-25 17:12:40.000000', 32, 70763807, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (579, '手册_79.png', '/storage/2024/11/手册_79.png', '2024-10-06 17:12:40.000000', 48, 43590008, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (580, '手册_80.pptx', '/storage/2023/11/手册_80.pptx', '2025-03-21 17:12:40.000000', 15, 54372145, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (581, '合同_81.pptx', '/storage/2023/7/合同_81.pptx', '2025-05-07 17:12:40.000000', 15, 53856436, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (582, '项目_82.jpg', '/storage/2023/12/项目_82.jpg', '2025-05-20 17:12:40.000000', 12, 90876128, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (583, '手册_83.pptx', '/storage/2023/9/手册_83.pptx', '2024-10-06 17:12:40.000000', 6, 49725499, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (584, '报告_84.txt', '/storage/2024/4/报告_84.txt', '2025-02-12 17:12:40.000000', 22, 84726245, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (585, '报告_85.xlsx', '/storage/2023/1/报告_85.xlsx', '2024-09-07 17:12:40.000000', 2, 70280525, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (586, '设计_86.txt', '/storage/2022/6/设计_86.txt', '2025-03-03 17:12:40.000000', 10, 80261245, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (587, '记录_87.pptx', '/storage/2022/11/记录_87.pptx', '2024-10-18 17:12:40.000000', 27, 59979056, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (588, '报告_88.txt', '/storage/2024/8/报告_88.txt', '2024-10-01 17:12:40.000000', 15, 31372495, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (589, '手册_89.pptx', '/storage/2024/2/手册_89.pptx', '2024-07-21 17:12:40.000000', 38, 22643443, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (590, '方案_90.png', '/storage/2022/4/方案_90.png', '2025-03-14 17:12:40.000000', 31, 70566165, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (591, '设计_91.png', '/storage/2023/5/设计_91.png', '2024-08-22 17:12:41.000000', 3, 39088670, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (592, '分析_92.jpg', '/storage/2022/1/分析_92.jpg', '2025-02-02 17:12:41.000000', 23, 55294992, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (593, '手册_93.txt', '/storage/2024/4/手册_93.txt', '2024-09-10 17:12:41.000000', 25, 59020513, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (594, '总结_94.xlsx', '/storage/2023/12/总结_94.xlsx', '2024-09-10 17:12:41.000000', 5, 25649415, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (595, '手册_95.pdf', '/storage/2023/7/手册_95.pdf', '2025-04-20 17:12:41.000000', 21, 97213747, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (596, '预算_96.xlsx', '/storage/2024/3/预算_96.xlsx', '2024-08-17 17:12:41.000000', 10, 41497660, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (597, '项目_97.pptx', '/storage/2023/5/项目_97.pptx', '2025-02-09 17:12:41.000000', 39, 42564504, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (598, '项目_98.pptx', '/storage/2024/1/项目_98.pptx', '2025-01-28 17:12:41.000000', 30, 21753813, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (599, '设计_99.jpg', '/storage/2024/7/设计_99.jpg', '2025-03-17 17:12:41.000000', 8, 47426357, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (600, '预算_100.txt', '/storage/2022/4/预算_100.txt', '2025-05-10 17:12:41.000000', 29, 64709452, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (601, '报告_1.jpg', '/storage/2023/7/报告_1.jpg', '2025-04-01 17:15:43.000000', 36, 15912863, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (602, '报告_2.pptx', '/storage/2024/12/报告_2.pptx', '2024-09-10 17:15:43.000000', 10, 29622213, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (603, '分析_3.xlsx', '/storage/2023/3/分析_3.xlsx', '2025-04-12 17:15:44.000000', 29, 89473441, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (604, '方案_4.txt', '/storage/2022/10/方案_4.txt', '2024-07-05 17:15:44.000000', 14, 24214158, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (605, '方案_5.txt', '/storage/2024/11/方案_5.txt', '2024-11-12 17:15:44.000000', 1, 86493912, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (606, '项目_6.jpg', '/storage/2022/8/项目_6.jpg', '2024-11-02 17:15:44.000000', 46, 87802081, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (607, '方案_7.pptx', '/storage/2023/3/方案_7.pptx', '2024-10-09 17:15:44.000000', 44, 21583815, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (608, '预算_8.docx', '/storage/2023/9/预算_8.docx', '2025-02-09 17:15:44.000000', 35, 34279414, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (609, '手册_9.png', '/storage/2024/1/手册_9.png', '2024-11-15 17:15:44.000000', 23, 55807462, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (610, '手册_10.pptx', '/storage/2022/1/手册_10.pptx', '2025-03-18 17:15:44.000000', 24, 22709905, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (611, '设计_11.xlsx', '/storage/2023/1/设计_11.xlsx', '2024-12-26 17:15:44.000000', 35, 29184853, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (612, '分析_12.txt', '/storage/2023/10/分析_12.txt', '2024-12-06 17:15:44.000000', 17, 17034563, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (613, '项目_13.xlsx', '/storage/2022/11/项目_13.xlsx', '2024-09-24 17:15:44.000000', 8, 94789469, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (614, '设计_14.pdf', '/storage/2022/12/设计_14.pdf', '2024-08-20 17:15:44.000000', 14, 51185791, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (615, '设计_15.xlsx', '/storage/2022/9/设计_15.xlsx', '2024-10-25 17:15:44.000000', 22, 36257558, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (616, '手册_16.png', '/storage/2023/1/手册_16.png', '2024-11-01 17:15:44.000000', 46, 66916452, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (617, '总结_17.pptx', '/storage/2023/5/总结_17.pptx', '2025-02-14 17:15:45.000000', 46, 28133551, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (618, '手册_18.docx', '/storage/2024/12/手册_18.docx', '2024-12-26 17:15:45.000000', 22, 83841969, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (619, '报告_19.pdf', '/storage/2024/10/报告_19.pdf', '2025-06-06 17:15:45.000000', 49, 8973648, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (620, '设计_20.pptx', '/storage/2023/6/设计_20.pptx', '2025-05-17 17:15:45.000000', 49, 85690797, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (621, '分析_21.pdf', '/storage/2022/9/分析_21.pdf', '2024-08-16 17:15:45.000000', 46, 29352938, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (622, '合同_22.pptx', '/storage/2023/9/合同_22.pptx', '2025-02-12 17:15:45.000000', 39, 20167509, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (623, '预算_23.png', '/storage/2022/1/预算_23.png', '2025-02-05 17:15:45.000000', 34, 41543359, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (624, '合同_24.jpg', '/storage/2024/3/合同_24.jpg', '2024-09-05 17:15:45.000000', 3, 56369190, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (625, '报告_25.xlsx', '/storage/2022/6/报告_25.xlsx', '2024-12-19 17:15:45.000000', 27, 36520911, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (626, '项目_26.xlsx', '/storage/2022/9/项目_26.xlsx', '2024-09-02 17:15:45.000000', 47, 32392523, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (627, '项目_27.png', '/storage/2024/7/项目_27.png', '2025-06-02 17:15:45.000000', 23, 38157954, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (628, '手册_28.png', '/storage/2023/9/手册_28.png', '2025-05-21 17:15:45.000000', 42, 39058775, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (629, '分析_29.txt', '/storage/2023/12/分析_29.txt', '2025-04-05 17:15:45.000000', 41, 10042562, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (630, '设计_30.docx', '/storage/2023/4/设计_30.docx', '2024-06-29 17:15:45.000000', 27, 92682363, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (631, '手册_31.pptx', '/storage/2022/8/手册_31.pptx', '2025-02-10 17:15:46.000000', 42, 39967435, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (632, '项目_32.docx', '/storage/2024/5/项目_32.docx', '2025-02-10 17:15:46.000000', 19, 7924060, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (633, '设计_33.pdf', '/storage/2022/10/设计_33.pdf', '2025-01-01 17:15:46.000000', 40, 22304817, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (634, '项目_34.xlsx', '/storage/2024/3/项目_34.xlsx', '2024-07-28 17:15:46.000000', 12, 85192135, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (635, '设计_35.xlsx', '/storage/2024/6/设计_35.xlsx', '2024-08-13 17:15:46.000000', 49, 73082620, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (636, '预算_36.docx', '/storage/2022/6/预算_36.docx', '2024-06-25 17:15:46.000000', 6, 95048274, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (637, '合同_37.pdf', '/storage/2022/12/合同_37.pdf', '2025-03-03 17:15:46.000000', 49, 32257725, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (638, '合同_38.png', '/storage/2022/3/合同_38.png', '2025-05-07 17:15:46.000000', 44, 95692604, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (639, '报告_39.jpg', '/storage/2024/4/报告_39.jpg', '2025-04-13 17:15:46.000000', 5, 77044002, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (640, '合同_40.docx', '/storage/2023/3/合同_40.docx', '2024-12-06 17:15:46.000000', 12, 96550436, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (641, '项目_41.png', '/storage/2024/11/项目_41.png', '2024-10-15 17:15:46.000000', 15, 54905161, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (642, '记录_42.txt', '/storage/2023/5/记录_42.txt', '2024-09-16 17:15:46.000000', 38, 37184597, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (643, '预算_43.png', '/storage/2022/3/预算_43.png', '2025-05-12 17:15:46.000000', 14, 87256791, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (644, '分析_44.txt', '/storage/2022/2/分析_44.txt', '2024-11-28 17:15:46.000000', 12, 8488855, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (645, '设计_45.xlsx', '/storage/2024/3/设计_45.xlsx', '2025-06-02 17:15:46.000000', 33, 92840364, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (646, '总结_46.png', '/storage/2024/8/总结_46.png', '2024-07-21 17:15:47.000000', 8, 2534162, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (647, '预算_47.pptx', '/storage/2024/8/预算_47.pptx', '2025-03-27 17:15:47.000000', 50, 79800120, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (648, '分析_48.png', '/storage/2023/3/分析_48.png', '2025-04-24 17:15:47.000000', 20, 59181062, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (649, '方案_49.pdf', '/storage/2023/9/方案_49.pdf', '2024-12-19 17:15:47.000000', 10, 33357333, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (650, '分析_50.png', '/storage/2022/4/分析_50.png', '2025-02-10 17:15:47.000000', 5, 45623547, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (651, '记录_51.jpg', '/storage/2022/5/记录_51.jpg', '2025-06-08 17:15:47.000000', 26, 70376257, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (652, '总结_52.xlsx', '/storage/2022/7/总结_52.xlsx', '2024-06-23 17:15:47.000000', 4, 56539909, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (653, '合同_53.xlsx', '/storage/2023/9/合同_53.xlsx', '2024-10-19 17:15:47.000000', 10, 50288495, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (654, '合同_54.xlsx', '/storage/2023/10/合同_54.xlsx', '2024-12-05 17:15:47.000000', 17, 33007751, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (655, '项目_55.xlsx', '/storage/2023/11/项目_55.xlsx', '2025-05-06 17:15:47.000000', 29, 4166545, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (656, '报告_56.png', '/storage/2022/7/报告_56.png', '2024-09-08 17:15:47.000000', 29, 88251440, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (657, '手册_57.jpg', '/storage/2023/4/手册_57.jpg', '2024-08-06 17:15:47.000000', 30, 84798985, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (658, '合同_58.png', '/storage/2023/4/合同_58.png', '2024-08-29 17:15:47.000000', 25, 33966508, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (659, '手册_59.txt', '/storage/2023/9/手册_59.txt', '2024-10-27 17:15:47.000000', 50, 75943726, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (660, '方案_60.txt', '/storage/2024/12/方案_60.txt', '2024-11-02 17:15:48.000000', 30, 86035945, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (661, '合同_61.pptx', '/storage/2022/9/合同_61.pptx', '2024-09-04 17:15:48.000000', 36, 83228269, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (662, '记录_62.txt', '/storage/2022/5/记录_62.txt', '2025-04-15 17:15:48.000000', 30, 57498883, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (663, '设计_63.docx', '/storage/2024/12/设计_63.docx', '2024-09-21 17:15:48.000000', 47, 99326233, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (664, '报告_64.jpg', '/storage/2024/1/报告_64.jpg', '2024-11-28 17:15:48.000000', 48, 51918451, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (665, '记录_65.pptx', '/storage/2024/11/记录_65.pptx', '2024-06-26 17:15:48.000000', 28, 51339302, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (666, '总结_66.pptx', '/storage/2022/5/总结_66.pptx', '2024-11-13 17:15:48.000000', 33, 85222288, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (667, '报告_67.txt', '/storage/2022/10/报告_67.txt', '2025-04-23 17:15:48.000000', 6, 50519863, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (668, '分析_68.png', '/storage/2023/10/分析_68.png', '2024-09-19 17:15:48.000000', 36, 13477920, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (669, '分析_69.txt', '/storage/2022/10/分析_69.txt', '2024-08-21 17:15:48.000000', 44, 28439493, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (670, '合同_70.txt', '/storage/2022/9/合同_70.txt', '2024-07-04 17:15:48.000000', 49, 13839989, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (671, '项目_71.pdf', '/storage/2022/4/项目_71.pdf', '2025-06-12 17:15:48.000000', 13, 47140862, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (672, '预算_72.pptx', '/storage/2023/1/预算_72.pptx', '2024-10-03 17:15:48.000000', 25, 82783553, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (673, '方案_73.png', '/storage/2023/1/方案_73.png', '2025-02-05 17:15:48.000000', 39, 54545299, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (674, '报告_74.txt', '/storage/2022/2/报告_74.txt', '2024-10-20 17:15:49.000000', 34, 35136090, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (675, '记录_75.png', '/storage/2022/8/记录_75.png', '2025-04-12 17:15:49.000000', 15, 19645092, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (676, '报告_76.png', '/storage/2022/1/报告_76.png', '2024-09-16 17:15:49.000000', 48, 27124712, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (677, '项目_77.pptx', '/storage/2024/2/项目_77.pptx', '2025-06-10 17:15:49.000000', 21, 92774202, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (678, '设计_78.jpg', '/storage/2022/8/设计_78.jpg', '2025-02-14 17:15:49.000000', 45, 46861354, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (679, '报告_79.pptx', '/storage/2023/3/报告_79.pptx', '2025-03-10 17:15:49.000000', 29, 34349335, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (680, '分析_80.png', '/storage/2024/7/分析_80.png', '2025-01-29 17:15:49.000000', 43, 51200578, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (681, '方案_81.pdf', '/storage/2023/4/方案_81.pdf', '2024-11-10 17:15:49.000000', 45, 38474491, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (682, '合同_82.png', '/storage/2022/7/合同_82.png', '2025-04-04 17:15:49.000000', 5, 87963912, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (683, '方案_83.jpg', '/storage/2022/2/方案_83.jpg', '2024-10-02 17:15:49.000000', 36, 79226531, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (684, '设计_84.jpg', '/storage/2024/11/设计_84.jpg', '2025-05-25 17:15:49.000000', 49, 96927868, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (685, '设计_85.xlsx', '/storage/2023/6/设计_85.xlsx', '2024-08-10 17:15:49.000000', 44, 10593873, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (686, '报告_86.png', '/storage/2023/1/报告_86.png', '2024-08-11 17:15:49.000000', 38, 30107701, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (687, '项目_87.jpg', '/storage/2024/1/项目_87.jpg', '2024-11-15 17:15:49.000000', 32, 52105199, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (688, '报告_88.png', '/storage/2023/11/报告_88.png', '2024-08-12 17:15:49.000000', 42, 48056518, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (689, '合同_89.xlsx', '/storage/2024/4/合同_89.xlsx', '2024-06-28 17:15:50.000000', 38, 101979589, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (690, '报告_90.txt', '/storage/2024/3/报告_90.txt', '2025-06-22 17:15:50.000000', 48, 33464755, '不可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (691, '项目_91.txt', '/storage/2023/5/项目_91.txt', '2024-08-13 17:15:50.000000', 35, 89274455, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (692, '方案_92.pdf', '/storage/2024/8/方案_92.pdf', '2025-03-31 17:15:50.000000', 8, 41461423, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (693, '手册_93.pdf', '/storage/2023/8/手册_93.pdf', '2024-11-24 17:15:50.000000', 43, 22871031, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (694, '预算_94.xlsx', '/storage/2023/1/预算_94.xlsx', '2025-02-18 17:15:50.000000', 46, 76633284, '协作', '团队');
INSERT INTO `wenshu_file_storage` VALUES (695, '手册_95.docx', '/storage/2022/5/手册_95.docx', '2024-10-15 17:15:50.000000', 46, 45373650, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (696, '项目_96.txt', '/storage/2024/11/项目_96.txt', '2025-01-14 17:15:50.000000', 42, 8225450, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (697, '方案_97.pptx', '/storage/2024/12/方案_97.pptx', '2025-05-22 17:15:50.000000', 22, 45759510, '协作', '个人');
INSERT INTO `wenshu_file_storage` VALUES (698, '总结_98.pptx', '/storage/2022/9/总结_98.pptx', '2025-06-06 17:15:50.000000', 31, 18191249, '不可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (699, '手册_99.png', '/storage/2024/4/手册_99.png', '2025-04-26 17:15:50.000000', 39, 72729183, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (700, '报告_100.txt', '/storage/2022/7/报告_100.txt', '2024-12-02 17:15:50.000000', 42, 83232426, '可读写', '团队');
INSERT INTO `wenshu_file_storage` VALUES (701, 'AI写的测试页面.html', 'users/1/2025/06/28/a81dd11cb8fe463ab176fecee08307b6.html', '2025-06-28 10:45:31.000000', 1, 39340, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (702, 'AI写的测试页面.html', 'users/1/2025/06/28/bd16feae61ad47b88359eac87979eeaa.html', '2025-06-28 10:52:07.000000', 1, 39340, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (703, '教师考核系统智能体需求.doc', 'users/1/2025/06/28/886a613722b4406aabb2ee91bff494f5.doc', '2025-06-28 21:33:46.000000', 1, 423936, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (704, '教师考核系统智能体需求.doc', 'users/1/2025/06/28/d3c1311e70b747e4831dd11c87fc2901.doc', '2025-06-28 21:34:54.000000', 1, 423936, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (705, '教师考核系统智能体需求.doc', 'users/1/2025/06/28/db7b7de77dad423c8c4c9ab19522276e.doc', '2025-06-28 21:56:41.000000', 1, 423936, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (706, '教师考核系统智能体需求.doc', 'users/1/2025/06/28/efaad0e7dd7f4402b9f15f1cc56419c2.doc', '2025-06-28 21:58:44.000000', 1, 423936, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (707, '教师考核系统智能体需求.doc', 'users/1/2025/06/30/701d8162179a4a3db585be7f9c3db3d1.doc', '2025-06-30 09:42:12.000000', 1, 423936, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (708, '教师考核系统智能体需求.doc', 'users/1/2025/06/30/2f4942dcc4704e09a5019b5b2837de9e.doc', '2025-06-30 10:19:11.000000', 1, 423936, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (709, '新建 文本文档.txt', 'users/1/2025/06/30/d06e9b423545488e8dd91d5f5c407329.txt', '2025-06-30 10:22:02.000000', 1, 5142, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (710, 'QQ2025629-17573.mp4', 'users/1/2025/07/01/6b025fdf87e94885b4f073d53fee17a5.mp4', '2025-07-01 11:45:54.000000', 1, 2668665, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (711, 'QQ2025629-17573.mp4', 'users/1/2025/07/01/5912528ead5a4f60b8e43c5e73cd57e2.mp4', '2025-07-01 12:06:51.000000', 1, 2668665, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (712, 'QQ2025629-17573.mp4', 'users/1/2025/07/01/d5666399a1b84700a8f9426cad0e31f8.mp4', '2025-07-01 12:07:44.000000', 1, 2668665, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (713, 'QQ2025629-17573.mp4', 'users/1/2025/07/01/0d1ba3e8d4b243d3b769db445038610d.mp4', '2025-07-01 12:08:55.000000', 1, 2668665, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (714, 'QQ2025629-17573.mp4', 'users/1/2025/07/01/15d4ecea04ab4504b519ff9f9a221e06.mp4', '2025-07-01 12:11:25.000000', 1, 2668665, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (715, 'QQ2025629-17573.mp4', 'users/1/2025/07/01/93adace43f2e49f08016519b5f7a33f8.mp4', '2025-07-01 13:22:53.000000', 1, 2668665, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (716, '谢志华_1606000662.jpg', 'users/1/2025/07/01/b84cb8ddfadb40adaf2af0872ab537d9.jpg', '2025-07-01 13:56:34.000000', 1, 1210445, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (717, '谢志华_1606000662.jpg', 'users/1/2025/07/01/249bcdb7ace94d0bba7b445147c20cd0.jpg', '2025-07-01 13:57:03.000000', 1, 1210445, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (718, '谢志华_1606000662.jpg', 'users/1/2025/07/01/9a2eb844d60b492d8f03c51bf109a214.jpg', '2025-07-01 13:59:24.000000', 1, 1210445, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (719, '谢志华_1606000662.jpg', 'users/1/2025/07/01/8eda707476e34e7b8f789d3539c5a834.jpg', '2025-07-01 14:00:35.000000', 1, 1210445, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (720, '谢志华_1606000662.jpg', 'users/1/2025/07/01/8b51cd5aa14141249636793f1c9c677c.jpg', '2025-07-01 14:01:41.000000', 1, 1210445, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (721, '谢志华_1606000662.jpg', 'users/1/2025/07/01/4c2a9fd0978142968b5a8faab45be1a5.jpg', '2025-07-01 14:03:15.000000', 1, 1210445, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (722, '谢志华_1606000662.jpg', 'users/1/2025/07/01/d4ea0e488f8749f7a205ee95e0f46163.jpg', '2025-07-01 14:03:34.000000', 1, 1210445, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (723, 'QQ20250701-113933.jpg', 'users/1/2025/07/01/1c82f7205d714497bd100f1078317dd3.jpg', '2025-07-01 14:04:06.000000', 1, 96350, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (724, 'QQ20250701-113933.jpg', 'users/1/2025/07/01/f6dee14483754edc87507f9ec1cb04f1.jpg', '2025-07-01 14:07:29.000000', 1, 96350, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (725, 'QQ20250701-113933.jpg', 'users/1/2025/07/01/3261b79ef1ad43b2b162b6a972aef53f.jpg', '2025-07-01 14:10:08.000000', 1, 96350, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (726, 'QQ20250701-113933.jpg', 'users/1/2025/07/01/a6fe5fb4a8a347af9680a0c73cb9d9f5.jpg', '2025-07-01 14:18:42.000000', 1, 96350, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (727, 'QQ20250701-113933.jpg', 'users/1/2025/07/01/5d4826da2fd34ce09131cd6ea7784d1f.jpg', '2025-07-01 14:19:29.000000', 1, 96350, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (728, 'QQ20250701-113933.jpg', 'users/1/2025/07/01/02a97f204af144e7b5465e5669a5ecd3.jpg', '2025-07-01 14:25:40.000000', 1, 96350, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (729, 'QQ20250701-113933.jpg', 'users/1/2025/07/01/fce7d5cd3e8749b9b38f483a14c81087.jpg', '2025-07-01 14:37:01.000000', 1, 96350, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (730, 'QQ20250701-113933.jpg', 'users/1/2025/07/01/a302aebbfac747369f04e2526c518c7a.jpg', '2025-07-01 14:43:32.000000', 1, 96350, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (731, 'QQ20250701-113933.jpg', 'users/1/2025/07/01/532806bfe5654c46a6e70f3dbfd42998.jpg', '2025-07-01 14:46:22.000000', 1, 96350, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (732, 'QQ20250701-113933.jpg', 'users/1/2025/07/01/1856d4bd5cb14a4a9fa6d4e5f4b298f7.jpg', '2025-07-01 14:48:52.000000', 1, 96350, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (733, 'QQ20250701-113933.jpg', 'users/1/2025/07/01/0d4ec23503ad4e39ab2847719f96cabb.jpg', '2025-07-01 14:49:22.000000', 1, 96350, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (734, 'b1需求分析.docx', 'users/1/2025/07/01/b90bde841aab4e248c0aee783dc36b8c.docx', '2025-07-01 15:30:46.000000', 1, 18910, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (735, '多模态实现方案.docx', 'users/1/2025/07/01/ad3295fc3ced4e9fa88d03f81d2ac3d9.docx', '2025-07-01 15:33:21.000000', 1, 11076, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (736, '多模态实现方案.docx', 'users/1/2025/07/01/e646f32e818d4433b00ad960cb7e700f.docx', '2025-07-01 15:35:00.000000', 1, 11076, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (737, '多模态实现方案.docx', 'users/1/2025/07/01/e6b2e4328cbe46c1b539e29054d5eaaa.docx', '2025-07-01 15:38:51.000000', 1, 11076, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (738, '多模态实现方案.docx', 'users/1/2025/07/01/c5560c3863164f42b587e3eb4828266e.docx', '2025-07-01 15:47:48.000000', 1, 11076, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (739, '多模态实现方案.docx', 'users/1/2025/07/01/5dbeb2a76fa44fbdb802b9832bcf70ec.docx', '2025-07-01 15:59:13.000000', 1, 11076, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (740, '多模态实现方案.docx', 'users/1/2025/07/01/cfc43bd35cee4478b8fe247d92b87df2.docx', '2025-07-01 16:00:48.000000', 1, 11076, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (741, '多模态实现方案.docx', 'users/1/2025/07/01/6fb0d78150c441aba8d7d9190ec6d4e7.docx', '2025-07-01 16:01:20.000000', 1, 11076, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (742, '多模态实现方案.docx', 'users/1/2025/07/01/b93b87c15b0444bb80696631ead88dd9.docx', '2025-07-01 16:01:23.000000', 1, 11076, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (743, '多模态实现方案.docx', 'users/1/2025/07/01/3407596b4214489883576f658c80ee67.docx', '2025-07-01 16:02:25.000000', 1, 11076, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (745, 'python考试.txt', 'users/1/2025/07/01/f8df08653caf4ab8b1c19a88017cb86a.txt', '2025-07-01 16:21:28.000000', 1, 30, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (746, 'python考试.txt', 'users/1/2025/07/01/a39c10c580d944e6b5aa87ee556cea94.txt', '2025-07-01 16:23:19.000000', 1, 30, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (747, 'python考试.txt', 'users/1/2025/07/01/335a3786d3d04fb0b718f743da5404f3.txt', '2025-07-01 16:29:11.000000', 1, 30, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (749, 'b1需求分析.docx', 'users/1/2025/07/01/58c91803dd17494fb720c8bdfabb8495.docx', '2025-07-01 16:57:05.000000', 1, 18910, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (750, 'b1需求分析.docx', 'users/1/2025/07/01/145e4edbae934ef98d6b82c30f2d1e94.docx', '2025-07-01 16:58:19.000000', 1, 18910, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (754, 'a3a69e25-e1c2-4491-9387-7bce57f1ad37.pdf', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\a3a69e25-e1c2-4491-9387-7bce57f1ad37.pdf', '2025-07-08 21:19:43.000000', 1, 2593, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (755, 'e0e35aee-c7c9-4808-a36f-5747939c3d26.docx', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\e0e35aee-c7c9-4808-a36f-5747939c3d26.docx', '2025-07-08 21:20:37.000000', 1, 4807, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (756, '4bb1b8d1-c1ef-4d83-9a55-ea6d5aa12c84.docx', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\4bb1b8d1-c1ef-4d83-9a55-ea6d5aa12c84.docx', '2025-07-08 21:38:19.000000', 39, 4812, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (757, '03bc8a29-1067-47ca-ae56-6fe3d1088455.docx', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\03bc8a29-1067-47ca-ae56-6fe3d1088455.docx', '2025-07-08 21:41:16.000000', 39, 4807, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (758, '63d468b1-8688-4d02-8a80-3509b07b94ef.docx', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\63d468b1-8688-4d02-8a80-3509b07b94ef.docx', '2025-07-08 21:41:39.000000', 39, 3058, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (759, 'auto_file_1752027744013.txt', '/uploads/auto/2025/07/09/auto_file_1752027744013.txt', '2025-07-09 10:22:24.000000', 1000, 6039439, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (760, '5c5ea238-f074-4aa7-87cb-d3040e8bc2fa.docx', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\5c5ea238-f074-4aa7-87cb-d3040e8bc2fa.docx', '2025-07-09 10:34:33.000000', 39, 4253, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (761, '8a868154-1b83-48e7-87b2-e18b01c450b7.docx', 'users/39/2025/07/09/6bdb51d386a74805b83ee7e7eb29cf64.docx', '2025-07-09 10:58:19.000000', 39, 2616, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (762, '9d48578e-4ffe-478f-8865-20a5d594ac43.docx', 'users/277/2025/07/09/0339dc6b79e544c4924caeef3fe09834.docx', '2025-07-09 11:03:19.000000', 277, 4292, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (763, '45891647-d8d4-47c1-9f3c-9a53f5f3bfae.docx', 'users/277/2025/07/09/03ead266d22b44a984bb225e08924bb2.docx', '2025-07-09 11:07:44.000000', 277, 4340, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (764, '669863d0-1303-42dd-a1c0-2094ffe17d55.docx', 'users/277/2025/07/09/f4809d0613f6467f8ec949b1c4da0d13.docx', '2025-07-09 11:11:13.000000', 277, 4500, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (765, 'b3887a26-468f-45e2-b213-b68314189d0f.docx', 'users/277/2025/07/09/21397b1920304c22a8815400d35f1edd.docx', '2025-07-09 11:19:11.000000', 277, 4729, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (766, '微信图片_20250629010040.png', 'anonymous/2025/07/10/24ca687af8cb405da645d17739302a15.png', '2025-07-10 21:26:16.000000', NULL, 1465286, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (767, '1.xlsx', 'anonymous/2025/07/10/98e197ecf7904101ac1e67e21208e3de.xlsx', '2025-07-10 21:26:32.000000', NULL, 10964, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (768, '微信图片_20250629010040.png', 'anonymous/2025/07/11/912f40a5c34c4a32886a4babe6799b8b.png', '2025-07-11 14:55:30.000000', NULL, 1465286, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (769, '屏幕截图 2023-02-03 143250.png', 'anonymous/2025/07/11/656d7ccbfff5456fb8560be7c547a233.png', '2025-07-11 15:05:38.000000', NULL, 155414, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (770, 'aa', 'D:\\workspace\\WenshuIntelligentComputing-back\\.\\editfiles\\770_1752394358752.txt', '2025-07-13 16:12:38.461890', NULL, NULL, 'ACTIVE', NULL);
INSERT INTO `wenshu_file_storage` VALUES (771, 'aa', 'D:\\workspace\\WenshuIntelligentComputing-back\\.\\editfiles\\771_1752394372106.txt', '2025-07-13 16:12:51.940509', NULL, NULL, 'ACTIVE', NULL);
INSERT INTO `wenshu_file_storage` VALUES (772, '11', 'D:\\workspace\\WenshuIntelligentComputing-back\\.\\editfiles\\772_1752409306975.txt', '2025-07-13 20:21:46.704206', NULL, NULL, 'ACTIVE', NULL);
INSERT INTO `wenshu_file_storage` VALUES (773, '0519808f-a7d1-45e7-8fb7-10dd9262609a.docx', 'users/277/2025/07/13/3989f6ef65ef40adb6786e7a1199f3db.docx', '2025-07-13 20:49:56.395489', 277, 2617, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (774, '3ff848cf-e550-4ccd-8e1f-a31c284f7a87.docx', 'users/277/2025/07/13/9919199c99eb47b38cc3ba314258e352.docx', '2025-07-13 21:03:26.134445', 277, 2616, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (775, 'd679965c-1601-4601-ac15-cc261e91b566.md', 'users/1/2025/07/13/e0efa8c357654557bd3581449aa69058.md', '2025-07-13 21:03:56.559754', 1, 403, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (776, '9ec74746-6d81-4ef3-bbce-2470b62096e1.docx', 'users/277/2025/07/13/a34af5b001e7483983dc952a5699a026.docx', '2025-07-13 21:06:07.445207', 277, 2614, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (777, '未命名项目-图层 1 (3).png', 'users/1/2025/07/13/13fe74dd51b64e4f950b297fc3e5c847.png', '2025-07-13 21:11:45.839330', 1, 24923, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (778, '未命名项目-图层 1 (3).png', 'users/1/2025/07/13/9b8dd3afc57242e292710cabaf98e61b.png', '2025-07-13 21:11:56.473459', 1, 24923, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (779, '微信图片_20250629010040.png', 'users/1/2025/07/13/f85da7829a804982a7f75b20f2359cd7.png', '2025-07-13 21:12:13.867483', 1, 1465286, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (780, '微信图片_20250629010040.png', 'users/1/2025/07/13/ded4bfcf1c944175b82f9bcb1605a68b.png', '2025-07-13 21:13:17.532810', 1, 1465286, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (781, '微信图片_20250629010040.png', 'users/1/2025/07/13/c7fdfa87a75c4e1c9b0ce4d8165488fa.png', '2025-07-13 21:13:42.605973', 1, 1465286, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (782, 'b12fd7ab-e600-460b-b870-d28a2323aaae.md', 'users/1/2025/07/13/2b1d70c1212646ef9b66e0a118d04f99.md', '2025-07-13 21:22:01.219561', 1, 403, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (783, '1218fab7-ebba-4c04-9cd3-3d1ca314480f.html', 'users/1/2025/07/13/36100551c23f4b099dfa1c5633a3c844.html', '2025-07-13 21:22:28.686766', 1, 464, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (784, '屏幕截图 2023-02-01 135328.png', 'users/1/2025/07/13/a54f2a7e385c47e495be60cee860ffc0.png', '2025-07-13 21:26:32.202949', 1, 1085964, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (785, 'ec1771c4-9939-498e-b7b6-e1513c8ec604.html', 'users/1/2025/07/13/4054c02f02ad4c22a00cc220675076e0.html', '2025-07-13 21:26:58.731365', 1, 440, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (786, '12745374-2a74-4694-a9e0-5e3f0fd72a9e.md', 'users/1/2025/07/13/ad73000b8aee4bf6a64eab373cf70d88.md', '2025-07-13 21:49:28.568867', 1, 403, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (787, 'c58c5cbb-f764-4872-a1b0-81f9329907d9.docx', 'users/277/2025/07/13/ef47bc1e5cdc44a5bd41d91897bc16f5.docx', '2025-07-13 22:07:13.954045', 277, 4237, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (788, '9e5b7d9c-8b25-405d-9f89-1aa094ff482f.docx', 'users/277/2025/07/13/818fa177d1a04cf9ba5a943221f17528.docx', '2025-07-13 22:09:32.542624', 277, 4745, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (789, '6060b241-bacf-48ce-9285-cf1818c00e41.docx', 'users/277/2025/07/13/f2c2fa180b8c45efb760d3503e89efea.docx', '2025-07-13 22:11:28.881623', 277, 4487, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (790, '656ac1b9-bca5-4f32-acb5-1a3a0474264c.docx', 'users/277/2025/07/13/61d1d8905ce4488891d06da0eac88473.docx', '2025-07-13 22:25:26.311798', 277, 4310, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (791, 'cfcb8112-2e04-4b3a-8d9c-de65738d71c6.docx', 'users/277/2025/07/13/a99adc7c0f14401eb91ed7ea02194335.docx', '2025-07-13 23:35:41.343872', 277, 4150, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (792, 'a737f327-2a77-4b57-8159-f71239476b77.docx', 'users/277/2025/07/13/aadcac6944ed48bb993d95ec53c81d22.docx', '2025-07-13 23:48:58.707743', 277, 2244, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (793, '0ba8d6a5-34bb-4ac6-951c-942e4f18347a.docx', 'users/277/2025/07/13/6c5c18afc41b43a6b2d263ee8ac2c661.docx', '2025-07-13 23:55:46.412355', 277, 4701, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (794, 'c13af460-1879-47c1-8462-6d9e66afe327.docx', 'users/277/2025/07/14/c798e0cd0c7948c9ad7522d30a8efb95.docx', '2025-07-14 20:52:30.567793', 277, 4408, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (795, 'ed550f93-abfb-4620-9fc4-11dc202a0f31.md', 'users/1/2025/07/14/71c5171e22704774be093f362587f4c8.md', '2025-07-14 22:52:35.207960', 1, 3162, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (796, 'ef624507-1d38-4603-bd10-9e0cbf77db63.md', 'users/1/2025/07/14/e3fede7f4c4d48e99a8878be4392aae0.md', '2025-07-14 22:59:33.149544', 1, 3450, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (797, '新建 文本文档.txt', 'anonymous/2025/07/14/e4d91a4e7c744086b35e28232c104bbb.txt', '2025-07-14 23:17:35.999928', NULL, 1998, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (798, 'b5585b28-3db8-40fd-a8c9-bdafc4838be4.md', 'users/1/2025/07/14/95e369ae4ba24ab2bf2768507c83e997.md', '2025-07-14 23:36:16.566059', 1, 2475, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (799, 'c9ee55f6-2349-4440-85bc-e447b9879a47.html', 'users/1/2025/07/15/cb2cd18190ad4b9a89cc9bab0985c9b8.html', '2025-07-15 09:58:18.717480', 1, 1035, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (800, '微信图片_20250629010040.png', 'users/1/2025/07/15/73536c4ec92e4dba9b200fc9b45667f8.png', '2025-07-15 14:52:26.032728', 1, 1465286, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (801, '92abad43d563f79301a001f796f4472b.mp4', 'users/1/2025/07/16/d7c3897bc1c14626b01668fb89cc71fa.mp4', '2025-07-16 20:31:54.425687', 1, 5386710, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (802, '92abad43d563f79301a001f796f4472b.mp4', 'users/1/2025/07/16/3b76e43f4cc84efdb30581d4e2ec9166.mp4', '2025-07-16 20:32:01.121765', 1, 5386710, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (803, '92abad43d563f79301a001f796f4472b.mp4', 'users/1/2025/07/16/5f3415fe9b2f4485b01b1af37e6324a7.mp4', '2025-07-16 20:56:29.441325', 1, 5386710, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (804, '92abad43d563f79301a001f796f4472b.mp4', 'users/1/2025/07/16/d0b03a4259304f1d9025c576ff528b8d.mp4', '2025-07-16 21:31:23.161333', 1, 5386710, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (805, '92abad43d563f79301a001f796f4472b.mp4', 'users/1/2025/07/16/1b0313cdc32a4f149311b41c9522303c.mp4', '2025-07-16 21:31:45.668600', 1, 5386710, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (806, '92abad43d563f79301a001f796f4472b.mp4', 'users/1/2025/07/16/8fc86988c056488a88ba20aa14d581da.mp4', '2025-07-16 21:54:42.806360', 1, 5386710, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (807, '92abad43d563f79301a001f796f4472b.mp4', 'users/1/2025/07/16/6d0994fe0ef94ff690da908ed7afae08.mp4', '2025-07-16 22:47:25.304470', 1, 5386710, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (808, 'b6f802dd-ba5e-4c9c-a081-9ed6e5ca2f04.md', 'users/1/2025/07/18/a0fd11d66c034c7a89f1a46d340a3067.md', '2025-07-18 09:49:37.579754', 1, 415, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (809, '22f6bafd-bf18-4ece-988f-29fbb7ea3f49.md', 'users/1/2025/07/18/53e55fb3a4dc4d1ebf7543e98eeccb66.md', '2025-07-18 09:51:06.409198', 1, 415, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (810, '08ec97c7-7a78-46f9-a287-2f225a91c719.html', 'users/1/2025/07/18/5fc58e01c4734dc6bdbe0dc23be245d0.html', '2025-07-18 09:52:05.518653', 1, 1061, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (811, '0997c33f-7afe-4d56-b1e3-35c36439bd33.docx', 'users/1/2025/07/18/a412f6aff96e4cfcbda83c27772cc8db.docx', '2025-07-18 09:52:14.961467', 1, 3142, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (812, 'ed534b77-0d8e-4650-88ab-9e1e0d142f21.docx', 'users/1/2025/07/18/f5200023e5c8483cba9c84147e584c32.docx', '2025-07-18 10:00:46.365514', 1, 3141, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (813, '03fc87f6-5cfb-4446-afff-6fde47145281.md', 'users/1/2025/07/18/1321a73e0ba04eca9746ee4c3abe1a6e.md', '2025-07-18 10:01:11.090384', 1, 415, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (814, 'f4e91d75-5be5-47b6-8d2f-69c788435618.md', 'users/1/2025/07/18/0f7c312cda8c4da59d204d40cbd1d6ba.md', '2025-07-18 10:12:17.515741', 1, 415, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (815, '7aaad034-1dfc-45cf-8a6d-5ce1fc3b14ac.md', 'users/1/2025/07/18/b70f9ef177b94cfd83ed6abdee7cd251.md', '2025-07-18 10:13:13.377788', 1, 415, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (816, 'f456c719-0aea-4ccc-9ae3-13958e3b19d5.md', 'users/1/2025/07/18/537f4f1399c74157b6e78cf1dd3fe48d.md', '2025-07-18 10:21:36.406956', 1, 415, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (818, 'QQ2025714-233952.mp4', 'users/1/2025/07/18/a0353e5f3ecf4733961ce670a601e5bc.mp4', '2025-07-18 10:29:09.459006', 1, 2299045, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (819, '793e5404-f743-4ac1-83ee-7f954748b39e.md', 'users/1/2025/07/18/b39694970bce47e894a0c695d3455305.md', '2025-07-18 11:51:45.841106', 1, 415, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (820, '75f91390-6324-4c3a-8cf1-d8db16446b4d.md', 'users/1/2025/07/18/ea1ea2a7d9fa4aba8eb52bf42fd93602.md', '2025-07-18 11:52:13.678099', 1, 415, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (821, 'ce8be6eb-4fa0-4870-8056-329cf2b7d23f.docx', 'users/1/2025/07/18/921fe6e30eb34ca2b5c53fcce6580f6c.docx', '2025-07-18 11:57:49.042819', 1, 3142, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (822, '07bac95a-de98-4d32-a824-52f79e310522.md', 'users/1/2025/07/18/93be10ab62034b8c997127856ce24dcd.md', '2025-07-18 13:05:04.033388', 1, 415, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (823, 'f81e7bd1-fe22-4678-9148-b9dd2d758bba.docx', 'users/1/2025/07/18/7e7db687869848e3904d26f58a263787.docx', '2025-07-18 13:05:16.197334', 1, 3141, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (824, 'bb927c72-cb87-48e9-98c7-1036a3405f2a.docx', 'users/1/2025/07/18/fb4c0028a94648d0903f6ef4806405ea.docx', '2025-07-18 14:11:10.341768', 1, 3141, '可读写', '个人');
INSERT INTO `wenshu_file_storage` VALUES (825, '825a72c7-a8f4-4c7a-baa6-9077cd81b238.docx', 'users/1/2025/07/18/6a4a0f8517ac495a82f38148a54001ec.docx', '2025-07-18 14:12:09.116125', 1, 3139, '可读写', '个人');

-- ----------------------------
-- Table structure for wenshu_team_audit
-- ----------------------------
DROP TABLE IF EXISTS `wenshu_team_audit`;
CREATE TABLE `wenshu_team_audit`  (
  `audit_id` int(0) NOT NULL AUTO_INCREMENT COMMENT '审核ID',
  `apply_team_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '申请团队名称',
  `apply_leader` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '申请负责人',
  `apply_creator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '申请创建者',
  `apply_time` datetime(0) DEFAULT NULL COMMENT '申请时间',
  `apply_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '申请描述',
  `audit_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '审核状态（已通过/已驳回/已取消/待审核）',
  PRIMARY KEY (`audit_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '团队创建审核表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of wenshu_team_audit
-- ----------------------------
INSERT INTO `wenshu_team_audit` VALUES (21, '财务四组1', '廖飞翔', '廖飞翔', '2025-06-22 20:15:53', '我想上厕所', '未审核');

-- ----------------------------
-- Table structure for wenshu_team_info
-- ----------------------------
DROP TABLE IF EXISTS `wenshu_team_info`;
CREATE TABLE `wenshu_team_info`  (
  `team_id` int(0) NOT NULL AUTO_INCREMENT COMMENT '团队ID',
  `team_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '团队名称',
  `leader_id` int(0) DEFAULT NULL COMMENT '团队负责人ID',
  `creator_id` int(0) DEFAULT NULL COMMENT '团队创建者ID',
  `create_time` datetime(0) DEFAULT NULL COMMENT '团队创建时间',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '团队描述',
  `status` tinyint(0) DEFAULT 1 COMMENT '状态（0-禁用,1-启用）',
  PRIMARY KEY (`team_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 112 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '团队信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of wenshu_team_info
-- ----------------------------
INSERT INTO `wenshu_team_info` VALUES (1, '财务四组1', 1, 1, NULL, '提供优质的客户服务', 0);
INSERT INTO `wenshu_team_info` VALUES (2, '设计中心2', 2, 72, '2024-11-18 18:22:28', '专注于技术研发和创新', 1);
INSERT INTO `wenshu_team_info` VALUES (3, '市场部门3', 31, 93, '2024-08-10 18:22:28', '确保产品质量和稳定性', 0);
INSERT INTO `wenshu_team_info` VALUES (4, '设计小组4', 30, 25, '2025-02-22 18:22:28', '负责产品销售和客户关系', 1);
INSERT INTO `wenshu_team_info` VALUES (5, '产品五组5', 61, 9, '2024-07-25 18:22:28', '管理公司财务和预算', 1);
INSERT INTO `wenshu_team_info` VALUES (6, '人事小组6', 44, 71, '2024-07-20 18:22:29', '负责人才招聘和团队建设', 1);
INSERT INTO `wenshu_team_info` VALUES (7, '客服小组7', 10, 36, '2024-09-05 18:22:29', '维护系统稳定运行', 1);
INSERT INTO `wenshu_team_info` VALUES (8, '设计中心8', 44, 93, '2024-07-07 18:22:29', '提供优质的客户服务', 1);
INSERT INTO `wenshu_team_info` VALUES (9, '运维二组9', 20, 84, '2024-12-05 18:22:29', '负责人才招聘和团队建设', 1);
INSERT INTO `wenshu_team_info` VALUES (10, '产品部门10', 44, 13, '2025-06-09 18:22:29', '开拓市场推广产品', 1);
INSERT INTO `wenshu_team_info` VALUES (11, '设计二组11', 71, 16, '2024-10-03 18:22:30', '负责产品销售和客户关系', 1);
INSERT INTO `wenshu_team_info` VALUES (12, '产品三组12', 94, 34, '2025-05-17 18:22:30', '开拓市场推广产品', 1);
INSERT INTO `wenshu_team_info` VALUES (13, '设计四组13', 44, 38, '2024-06-26 18:22:30', '负责人才招聘和团队建设', 1);
INSERT INTO `wenshu_team_info` VALUES (14, '测试团队14', 52, 70, '2025-04-30 18:22:30', '负责人才招聘和团队建设', 1);
INSERT INTO `wenshu_team_info` VALUES (15, '产品三组15', 56, 66, '2024-07-25 18:22:30', '管理公司财务和预算', 0);
INSERT INTO `wenshu_team_info` VALUES (16, '市场小组16', 12, 92, '2024-10-20 18:22:31', '开拓市场推广产品', 1);
INSERT INTO `wenshu_team_info` VALUES (17, '设计二组17', 50, 11, '2024-11-24 18:22:31', '维护系统稳定运行', 1);
INSERT INTO `wenshu_team_info` VALUES (18, '市场团队18', 29, 66, '2025-04-16 18:22:31', '确保产品质量和稳定性', 1);
INSERT INTO `wenshu_team_info` VALUES (19, '市场部门19', 71, 69, '2024-07-31 18:22:31', '负责人才招聘和团队建设', 1);
INSERT INTO `wenshu_team_info` VALUES (20, '研发团队20', 1, 74, '2025-01-15 18:22:32', '提供高质量的设计方案', 1);
INSERT INTO `wenshu_team_info` VALUES (21, '产品部门21', 26, 18, '2025-01-23 18:22:32', '管理公司财务和预算', 1);
INSERT INTO `wenshu_team_info` VALUES (22, '市场一组22', 26, 83, '2024-10-07 18:22:32', '负责人才招聘和团队建设', 1);
INSERT INTO `wenshu_team_info` VALUES (23, '财务五组23', 28, 69, '2024-09-22 18:22:32', '维护系统稳定运行', 1);
INSERT INTO `wenshu_team_info` VALUES (24, '财务二组24', 39, 38, '2024-11-05 18:22:32', '开拓市场推广产品', 0);
INSERT INTO `wenshu_team_info` VALUES (25, '运维五组25', 84, 31, '2025-02-20 18:22:33', '提供优质的客户服务', 1);
INSERT INTO `wenshu_team_info` VALUES (26, '市场四组26', 66, 71, '2025-02-27 18:22:33', '提供高质量的设计方案', 1);
INSERT INTO `wenshu_team_info` VALUES (27, '研发一组27', 76, 24, '2025-01-28 18:22:33', '提供优质的客户服务', 0);
INSERT INTO `wenshu_team_info` VALUES (28, '销售分队28', 53, 95, '2024-11-28 18:22:33', '确保产品质量和稳定性', 0);
INSERT INTO `wenshu_team_info` VALUES (29, '设计分队29', 91, 46, '2024-09-15 18:22:33', '维护系统稳定运行', 1);
INSERT INTO `wenshu_team_info` VALUES (30, '运维中心30', 91, 17, '2025-06-22 18:22:34', '管理公司财务和预算', 1);
INSERT INTO `wenshu_team_info` VALUES (31, '运维五组31', 59, 55, '2025-05-16 18:22:34', '开拓市场推广产品', 1);
INSERT INTO `wenshu_team_info` VALUES (32, '人事三组32', 8, 27, '2024-11-13 18:22:34', '维护系统稳定运行', 0);
INSERT INTO `wenshu_team_info` VALUES (33, '财务三组33', 54, 69, '2024-07-06 18:22:34', '负责人才招聘和团队建设', 1);
INSERT INTO `wenshu_team_info` VALUES (34, '设计三组34', 47, 64, '2024-09-01 18:22:35', '维护系统稳定运行', 1);
INSERT INTO `wenshu_team_info` VALUES (35, '客服分队35', 50, 57, '2024-11-21 18:22:35', '开拓市场推广产品', 1);
INSERT INTO `wenshu_team_info` VALUES (36, '客服部门36', 4, 60, '2025-05-25 18:22:35', '管理公司财务和预算', 1);
INSERT INTO `wenshu_team_info` VALUES (37, '财务四组37', 76, 26, '2024-11-07 18:22:35', '负责人才招聘和团队建设', 1);
INSERT INTO `wenshu_team_info` VALUES (38, '设计一组38', 100, 91, '2025-06-05 18:22:35', '确保产品质量和稳定性', 1);
INSERT INTO `wenshu_team_info` VALUES (39, '运维小组39', 20, 80, '2024-10-15 18:22:36', '确保产品质量和稳定性', 1);
INSERT INTO `wenshu_team_info` VALUES (40, '研发二组40', 64, 23, '2024-11-13 18:22:36', '负责产品销售和客户关系', 1);
INSERT INTO `wenshu_team_info` VALUES (41, '运维五组41', 45, 36, '2025-03-11 18:22:36', '负责人才招聘和团队建设', 1);
INSERT INTO `wenshu_team_info` VALUES (42, '人事四组42', 52, 54, '2024-11-11 18:22:36', '确保产品质量和稳定性', 1);
INSERT INTO `wenshu_team_info` VALUES (43, '人事小组43', 10, 53, '2024-11-08 18:22:36', '确保产品质量和稳定性', 1);
INSERT INTO `wenshu_team_info` VALUES (44, '客服分队44', 75, 55, '2024-07-13 18:22:37', '管理公司财务和预算', 0);
INSERT INTO `wenshu_team_info` VALUES (45, '人事中心45', 40, 96, '2025-03-14 18:22:37', '专注于技术研发和创新', 1);
INSERT INTO `wenshu_team_info` VALUES (46, '客服三组46', 39, 91, '2025-05-13 18:22:37', '负责产品设计和用户体验', 1);
INSERT INTO `wenshu_team_info` VALUES (47, '运维五组47', 75, 87, '2025-05-16 18:22:37', '开拓市场推广产品', 1);
INSERT INTO `wenshu_team_info` VALUES (48, '研发三组48', 47, 53, '2025-01-05 18:22:37', '负责产品销售和客户关系', 1);
INSERT INTO `wenshu_team_info` VALUES (49, '客服一组49', 77, 44, '2024-10-01 18:22:38', '提供高质量的设计方案', 1);
INSERT INTO `wenshu_team_info` VALUES (50, '测试一组50', 34, 9, '2025-05-04 18:22:38', '负责人才招聘和团队建设', 1);
INSERT INTO `wenshu_team_info` VALUES (51, '研发部门51', 82, 27, '2024-07-02 18:22:38', '负责产品设计和用户体验', 1);
INSERT INTO `wenshu_team_info` VALUES (52, '测试一组52', 46, 41, '2024-07-12 18:22:38', '维护系统稳定运行', 1);
INSERT INTO `wenshu_team_info` VALUES (53, '运维分队53', 55, 80, '2025-02-20 18:22:39', '负责产品设计和用户体验', 1);
INSERT INTO `wenshu_team_info` VALUES (54, '产品小组54', 100, 77, '2025-02-04 18:22:39', '开拓市场推广产品', 1);
INSERT INTO `wenshu_team_info` VALUES (55, '财务分队55', 8, 11, '2025-02-07 18:22:39', '维护系统稳定运行', 1);
INSERT INTO `wenshu_team_info` VALUES (56, '产品中心56', 90, 27, '2025-05-13 18:22:39', '负责人才招聘和团队建设', 1);
INSERT INTO `wenshu_team_info` VALUES (57, '研发一组57', 43, 57, '2025-02-11 18:22:39', '管理公司财务和预算', 1);
INSERT INTO `wenshu_team_info` VALUES (58, '设计一组58', 8, 82, '2024-08-17 18:22:40', '开拓市场推广产品', 1);
INSERT INTO `wenshu_team_info` VALUES (59, '设计小组59', 42, 32, '2024-11-25 18:22:40', '负责产品设计和用户体验', 1);
INSERT INTO `wenshu_team_info` VALUES (60, '人事分队60', 21, 72, '2025-01-07 18:22:40', '负责人才招聘和团队建设', 1);
INSERT INTO `wenshu_team_info` VALUES (61, '产品分队61', 36, 39, '2024-07-27 18:22:40', '维护系统稳定运行', 0);
INSERT INTO `wenshu_team_info` VALUES (62, '销售分队62', 86, 75, '2024-12-27 18:22:40', '提供高质量的设计方案', 1);
INSERT INTO `wenshu_team_info` VALUES (63, '财务中心63', 3, 79, '2025-01-28 18:22:41', '专注于技术研发和创新', 1);
INSERT INTO `wenshu_team_info` VALUES (64, '市场团队64', 88, 30, '2025-02-13 18:22:41', '负责人才招聘和团队建设', 0);
INSERT INTO `wenshu_team_info` VALUES (65, '设计三组65', 79, 74, '2024-07-15 18:22:41', '开拓市场推广产品', 1);
INSERT INTO `wenshu_team_info` VALUES (66, '产品五组66', 11, 89, '2025-04-24 18:22:41', '负责产品设计和用户体验', 1);
INSERT INTO `wenshu_team_info` VALUES (67, '研发部门67', 59, 16, '2025-02-12 18:22:41', '开拓市场推广产品', 1);
INSERT INTO `wenshu_team_info` VALUES (68, '测试一组68', 71, 90, '2024-11-08 18:22:42', '负责人才招聘和团队建设', 1);
INSERT INTO `wenshu_team_info` VALUES (69, '销售一组69', 51, 81, '2025-03-21 18:22:42', '提供优质的客户服务', 1);
INSERT INTO `wenshu_team_info` VALUES (70, '销售三组70', 92, 24, '2024-10-29 18:22:42', '提供高质量的设计方案', 0);
INSERT INTO `wenshu_team_info` VALUES (71, '研发三组71', 60, 70, '2025-01-03 18:22:42', '维护系统稳定运行', 1);
INSERT INTO `wenshu_team_info` VALUES (72, '运维团队72', 91, 17, '2024-08-11 18:22:42', '维护系统稳定运行', 1);
INSERT INTO `wenshu_team_info` VALUES (73, '客服小组73', 81, 99, '2024-11-11 18:22:43', '专注于技术研发和创新', 1);
INSERT INTO `wenshu_team_info` VALUES (74, '客服三组74', 78, 6, '2024-12-27 18:22:43', '管理公司财务和预算', 1);
INSERT INTO `wenshu_team_info` VALUES (75, '市场一组75', 50, 44, '2024-12-14 18:22:43', '负责人才招聘和团队建设', 1);
INSERT INTO `wenshu_team_info` VALUES (76, '财务部门76', 71, 27, '2024-12-26 18:22:43', '负责产品销售和客户关系', 1);
INSERT INTO `wenshu_team_info` VALUES (77, '人事中心77', 42, 91, '2025-06-08 18:22:44', '负责人才招聘和团队建设', 1);
INSERT INTO `wenshu_team_info` VALUES (78, '产品团队78', 90, 10, '2025-03-22 18:22:44', '负责产品设计和用户体验', 1);
INSERT INTO `wenshu_team_info` VALUES (79, '产品部门79', 19, 56, '2024-11-22 18:22:44', '负责产品设计和用户体验', 0);
INSERT INTO `wenshu_team_info` VALUES (80, '客服团队80', 99, 13, '2024-10-16 18:22:44', '提供高质量的设计方案', 0);
INSERT INTO `wenshu_team_info` VALUES (81, '财务团队81', 14, 36, '2024-09-09 18:22:44', '负责人才招聘和团队建设', 1);
INSERT INTO `wenshu_team_info` VALUES (82, '财务三组82', 7, 83, '2025-04-01 18:22:45', '负责产品设计和用户体验', 1);
INSERT INTO `wenshu_team_info` VALUES (83, '设计四组83', 9, 67, '2025-01-07 18:22:45', '维护系统稳定运行', 1);
INSERT INTO `wenshu_team_info` VALUES (84, '设计中心84', 85, 54, '2025-04-09 18:22:45', '负责人才招聘和团队建设', 1);
INSERT INTO `wenshu_team_info` VALUES (85, '客服中心85', 90, 78, '2025-06-04 18:22:45', '管理公司财务和预算', 0);
INSERT INTO `wenshu_team_info` VALUES (86, '客服一组86', 1, 62, '2025-02-13 18:22:45', '负责人才招聘和团队建设', 1);
INSERT INTO `wenshu_team_info` VALUES (87, '产品分队87', 95, 20, '2025-01-06 18:22:46', '开拓市场推广产品', 1);
INSERT INTO `wenshu_team_info` VALUES (88, '销售五组88', 19, 64, '2025-03-24 18:22:46', '确保产品质量和稳定性', 1);
INSERT INTO `wenshu_team_info` VALUES (89, '市场中心89', 27, 40, '2024-07-29 18:22:46', '提供高质量的设计方案', 0);
INSERT INTO `wenshu_team_info` VALUES (90, '客服一组90', 61, 35, '2024-11-22 18:22:46', '负责产品设计和用户体验', 1);
INSERT INTO `wenshu_team_info` VALUES (91, '研发一组91', 76, 6, '2025-01-04 18:22:46', '负责产品设计和用户体验', 1);
INSERT INTO `wenshu_team_info` VALUES (92, '销售小组92', 68, 73, '2024-08-03 18:22:47', '维护系统稳定运行', 1);
INSERT INTO `wenshu_team_info` VALUES (93, '人事四组93', 33, 31, '2025-03-02 18:22:47', '负责产品设计和用户体验', 1);
INSERT INTO `wenshu_team_info` VALUES (94, '人事小组94', 83, 79, '2024-10-25 18:22:47', '提供优质的客户服务', 0);
INSERT INTO `wenshu_team_info` VALUES (95, '测试部门95', 14, 84, '2024-09-16 18:22:47', '负责产品设计和用户体验', 0);
INSERT INTO `wenshu_team_info` VALUES (96, '财务部门96', 39, 49, '2025-03-13 18:22:47', '维护系统稳定运行', 0);
INSERT INTO `wenshu_team_info` VALUES (97, '客服分队97', 23, 27, '2025-04-20 18:22:48', '负责人才招聘和团队建设', 0);
INSERT INTO `wenshu_team_info` VALUES (98, '客服中心98', 33, 63, '2024-07-19 18:22:48', '负责产品设计和用户体验', 1);
INSERT INTO `wenshu_team_info` VALUES (99, '研发三组99', 39, 44, '2025-04-16 18:22:48', '开拓市场推广产品', 1);
INSERT INTO `wenshu_team_info` VALUES (100, '客服分队100', 47, 21, '2024-10-18 18:22:48', '负责产品设计和用户体验', 1);
INSERT INTO `wenshu_team_info` VALUES (101, '123', NULL, NULL, NULL, '123', 1);
INSERT INTO `wenshu_team_info` VALUES (102, '21为', NULL, NULL, NULL, '123', 1);
INSERT INTO `wenshu_team_info` VALUES (103, '211', 1, 1, NULL, '123', 1);
INSERT INTO `wenshu_team_info` VALUES (104, '去问驱蚊器', 1, 1, NULL, '1', 1);
INSERT INTO `wenshu_team_info` VALUES (105, '士大夫', 1, 1, NULL, '2', 1);
INSERT INTO `wenshu_team_info` VALUES (106, '231123', 1, 1, '2025-07-15 14:36:37', '123123', 1);
INSERT INTO `wenshu_team_info` VALUES (107, '123123123', 1, 1, '2025-07-15 15:19:18', '123', 1);
INSERT INTO `wenshu_team_info` VALUES (108, '123123123123', 1, 1, NULL, '123', 1);
INSERT INTO `wenshu_team_info` VALUES (109, '111212', 1, 1, '2025-07-15 15:36:46', '', 1);
INSERT INTO `wenshu_team_info` VALUES (110, '222', 1, 1, '2025-07-15 15:37:11', '', 1);
INSERT INTO `wenshu_team_info` VALUES (111, '9080', 1, 1, '2025-07-15 23:51:45', '90', 1);

-- ----------------------------
-- Table structure for wenshu_team_member
-- ----------------------------
DROP TABLE IF EXISTS `wenshu_team_member`;
CREATE TABLE `wenshu_team_member`  (
  `team_id` int(0) NOT NULL COMMENT '团队ID',
  `user_id` int(0) NOT NULL COMMENT '用户ID',
  PRIMARY KEY (`team_id`, `user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '团队成员关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of wenshu_team_member
-- ----------------------------
INSERT INTO `wenshu_team_member` VALUES (1, 1);
INSERT INTO `wenshu_team_member` VALUES (1, 2);
INSERT INTO `wenshu_team_member` VALUES (1, 39);
INSERT INTO `wenshu_team_member` VALUES (1, 45);
INSERT INTO `wenshu_team_member` VALUES (1, 47);
INSERT INTO `wenshu_team_member` VALUES (1, 74);
INSERT INTO `wenshu_team_member` VALUES (1, 97);
INSERT INTO `wenshu_team_member` VALUES (2, 1);
INSERT INTO `wenshu_team_member` VALUES (2, 2);
INSERT INTO `wenshu_team_member` VALUES (2, 45);
INSERT INTO `wenshu_team_member` VALUES (2, 79);
INSERT INTO `wenshu_team_member` VALUES (2, 93);
INSERT INTO `wenshu_team_member` VALUES (2, 95);
INSERT INTO `wenshu_team_member` VALUES (3, 5);
INSERT INTO `wenshu_team_member` VALUES (3, 19);
INSERT INTO `wenshu_team_member` VALUES (3, 45);
INSERT INTO `wenshu_team_member` VALUES (3, 47);
INSERT INTO `wenshu_team_member` VALUES (3, 86);
INSERT INTO `wenshu_team_member` VALUES (3, 92);
INSERT INTO `wenshu_team_member` VALUES (4, 11);
INSERT INTO `wenshu_team_member` VALUES (4, 17);
INSERT INTO `wenshu_team_member` VALUES (4, 67);
INSERT INTO `wenshu_team_member` VALUES (4, 69);
INSERT INTO `wenshu_team_member` VALUES (4, 97);
INSERT INTO `wenshu_team_member` VALUES (5, 4);
INSERT INTO `wenshu_team_member` VALUES (5, 18);
INSERT INTO `wenshu_team_member` VALUES (5, 36);
INSERT INTO `wenshu_team_member` VALUES (5, 52);
INSERT INTO `wenshu_team_member` VALUES (5, 78);
INSERT INTO `wenshu_team_member` VALUES (6, 16);
INSERT INTO `wenshu_team_member` VALUES (6, 40);
INSERT INTO `wenshu_team_member` VALUES (6, 44);
INSERT INTO `wenshu_team_member` VALUES (6, 47);
INSERT INTO `wenshu_team_member` VALUES (6, 57);
INSERT INTO `wenshu_team_member` VALUES (6, 63);
INSERT INTO `wenshu_team_member` VALUES (7, 3);
INSERT INTO `wenshu_team_member` VALUES (7, 37);
INSERT INTO `wenshu_team_member` VALUES (7, 65);
INSERT INTO `wenshu_team_member` VALUES (7, 68);
INSERT INTO `wenshu_team_member` VALUES (7, 73);
INSERT INTO `wenshu_team_member` VALUES (7, 80);
INSERT INTO `wenshu_team_member` VALUES (8, 12);
INSERT INTO `wenshu_team_member` VALUES (8, 41);
INSERT INTO `wenshu_team_member` VALUES (8, 57);
INSERT INTO `wenshu_team_member` VALUES (8, 62);
INSERT INTO `wenshu_team_member` VALUES (8, 100);
INSERT INTO `wenshu_team_member` VALUES (9, 21);
INSERT INTO `wenshu_team_member` VALUES (9, 23);
INSERT INTO `wenshu_team_member` VALUES (9, 48);
INSERT INTO `wenshu_team_member` VALUES (9, 51);
INSERT INTO `wenshu_team_member` VALUES (9, 94);
INSERT INTO `wenshu_team_member` VALUES (10, 38);
INSERT INTO `wenshu_team_member` VALUES (10, 42);
INSERT INTO `wenshu_team_member` VALUES (10, 48);
INSERT INTO `wenshu_team_member` VALUES (10, 67);
INSERT INTO `wenshu_team_member` VALUES (10, 89);
INSERT INTO `wenshu_team_member` VALUES (10, 90);
INSERT INTO `wenshu_team_member` VALUES (11, 11);
INSERT INTO `wenshu_team_member` VALUES (11, 21);
INSERT INTO `wenshu_team_member` VALUES (11, 35);
INSERT INTO `wenshu_team_member` VALUES (11, 56);
INSERT INTO `wenshu_team_member` VALUES (11, 90);
INSERT INTO `wenshu_team_member` VALUES (12, 1);
INSERT INTO `wenshu_team_member` VALUES (12, 28);
INSERT INTO `wenshu_team_member` VALUES (12, 48);
INSERT INTO `wenshu_team_member` VALUES (12, 84);
INSERT INTO `wenshu_team_member` VALUES (12, 92);
INSERT INTO `wenshu_team_member` VALUES (13, 2);
INSERT INTO `wenshu_team_member` VALUES (13, 9);
INSERT INTO `wenshu_team_member` VALUES (13, 51);
INSERT INTO `wenshu_team_member` VALUES (13, 61);
INSERT INTO `wenshu_team_member` VALUES (13, 89);
INSERT INTO `wenshu_team_member` VALUES (13, 93);
INSERT INTO `wenshu_team_member` VALUES (14, 14);
INSERT INTO `wenshu_team_member` VALUES (14, 35);
INSERT INTO `wenshu_team_member` VALUES (14, 37);
INSERT INTO `wenshu_team_member` VALUES (14, 69);
INSERT INTO `wenshu_team_member` VALUES (14, 91);
INSERT INTO `wenshu_team_member` VALUES (15, 22);
INSERT INTO `wenshu_team_member` VALUES (15, 33);
INSERT INTO `wenshu_team_member` VALUES (15, 55);
INSERT INTO `wenshu_team_member` VALUES (15, 58);
INSERT INTO `wenshu_team_member` VALUES (15, 72);
INSERT INTO `wenshu_team_member` VALUES (15, 82);
INSERT INTO `wenshu_team_member` VALUES (16, 11);
INSERT INTO `wenshu_team_member` VALUES (16, 17);
INSERT INTO `wenshu_team_member` VALUES (16, 29);
INSERT INTO `wenshu_team_member` VALUES (16, 69);
INSERT INTO `wenshu_team_member` VALUES (16, 86);
INSERT INTO `wenshu_team_member` VALUES (17, 6);
INSERT INTO `wenshu_team_member` VALUES (17, 8);
INSERT INTO `wenshu_team_member` VALUES (17, 21);
INSERT INTO `wenshu_team_member` VALUES (17, 23);
INSERT INTO `wenshu_team_member` VALUES (17, 42);
INSERT INTO `wenshu_team_member` VALUES (17, 60);
INSERT INTO `wenshu_team_member` VALUES (18, 14);
INSERT INTO `wenshu_team_member` VALUES (18, 31);
INSERT INTO `wenshu_team_member` VALUES (18, 44);
INSERT INTO `wenshu_team_member` VALUES (18, 63);
INSERT INTO `wenshu_team_member` VALUES (18, 96);
INSERT INTO `wenshu_team_member` VALUES (19, 2);
INSERT INTO `wenshu_team_member` VALUES (19, 3);
INSERT INTO `wenshu_team_member` VALUES (19, 38);
INSERT INTO `wenshu_team_member` VALUES (19, 46);
INSERT INTO `wenshu_team_member` VALUES (19, 64);
INSERT INTO `wenshu_team_member` VALUES (19, 100);
INSERT INTO `wenshu_team_member` VALUES (20, 6);
INSERT INTO `wenshu_team_member` VALUES (20, 11);
INSERT INTO `wenshu_team_member` VALUES (20, 14);
INSERT INTO `wenshu_team_member` VALUES (20, 40);
INSERT INTO `wenshu_team_member` VALUES (20, 52);
INSERT INTO `wenshu_team_member` VALUES (20, 82);
INSERT INTO `wenshu_team_member` VALUES (21, 15);
INSERT INTO `wenshu_team_member` VALUES (21, 31);
INSERT INTO `wenshu_team_member` VALUES (21, 51);
INSERT INTO `wenshu_team_member` VALUES (21, 53);
INSERT INTO `wenshu_team_member` VALUES (21, 57);
INSERT INTO `wenshu_team_member` VALUES (21, 66);
INSERT INTO `wenshu_team_member` VALUES (22, 17);
INSERT INTO `wenshu_team_member` VALUES (22, 25);
INSERT INTO `wenshu_team_member` VALUES (22, 29);
INSERT INTO `wenshu_team_member` VALUES (22, 44);
INSERT INTO `wenshu_team_member` VALUES (22, 52);
INSERT INTO `wenshu_team_member` VALUES (22, 68);
INSERT INTO `wenshu_team_member` VALUES (23, 7);
INSERT INTO `wenshu_team_member` VALUES (23, 31);
INSERT INTO `wenshu_team_member` VALUES (23, 45);
INSERT INTO `wenshu_team_member` VALUES (23, 47);
INSERT INTO `wenshu_team_member` VALUES (23, 59);
INSERT INTO `wenshu_team_member` VALUES (23, 61);
INSERT INTO `wenshu_team_member` VALUES (24, 2);
INSERT INTO `wenshu_team_member` VALUES (24, 52);
INSERT INTO `wenshu_team_member` VALUES (24, 85);
INSERT INTO `wenshu_team_member` VALUES (24, 88);
INSERT INTO `wenshu_team_member` VALUES (24, 91);
INSERT INTO `wenshu_team_member` VALUES (25, 41);
INSERT INTO `wenshu_team_member` VALUES (25, 43);
INSERT INTO `wenshu_team_member` VALUES (25, 58);
INSERT INTO `wenshu_team_member` VALUES (25, 70);
INSERT INTO `wenshu_team_member` VALUES (25, 95);
INSERT INTO `wenshu_team_member` VALUES (26, 19);
INSERT INTO `wenshu_team_member` VALUES (26, 30);
INSERT INTO `wenshu_team_member` VALUES (26, 62);
INSERT INTO `wenshu_team_member` VALUES (26, 65);
INSERT INTO `wenshu_team_member` VALUES (26, 70);
INSERT INTO `wenshu_team_member` VALUES (27, 38);
INSERT INTO `wenshu_team_member` VALUES (27, 52);
INSERT INTO `wenshu_team_member` VALUES (27, 55);
INSERT INTO `wenshu_team_member` VALUES (27, 71);
INSERT INTO `wenshu_team_member` VALUES (27, 84);
INSERT INTO `wenshu_team_member` VALUES (27, 90);
INSERT INTO `wenshu_team_member` VALUES (28, 26);
INSERT INTO `wenshu_team_member` VALUES (28, 54);
INSERT INTO `wenshu_team_member` VALUES (28, 64);
INSERT INTO `wenshu_team_member` VALUES (28, 69);
INSERT INTO `wenshu_team_member` VALUES (28, 98);
INSERT INTO `wenshu_team_member` VALUES (29, 2);
INSERT INTO `wenshu_team_member` VALUES (29, 23);
INSERT INTO `wenshu_team_member` VALUES (29, 34);
INSERT INTO `wenshu_team_member` VALUES (29, 47);
INSERT INTO `wenshu_team_member` VALUES (29, 55);
INSERT INTO `wenshu_team_member` VALUES (30, 2);
INSERT INTO `wenshu_team_member` VALUES (30, 41);
INSERT INTO `wenshu_team_member` VALUES (30, 48);
INSERT INTO `wenshu_team_member` VALUES (30, 54);
INSERT INTO `wenshu_team_member` VALUES (30, 80);
INSERT INTO `wenshu_team_member` VALUES (30, 83);
INSERT INTO `wenshu_team_member` VALUES (31, 12);
INSERT INTO `wenshu_team_member` VALUES (31, 26);
INSERT INTO `wenshu_team_member` VALUES (31, 49);
INSERT INTO `wenshu_team_member` VALUES (31, 52);
INSERT INTO `wenshu_team_member` VALUES (31, 63);
INSERT INTO `wenshu_team_member` VALUES (31, 97);
INSERT INTO `wenshu_team_member` VALUES (32, 6);
INSERT INTO `wenshu_team_member` VALUES (32, 22);
INSERT INTO `wenshu_team_member` VALUES (32, 35);
INSERT INTO `wenshu_team_member` VALUES (32, 80);
INSERT INTO `wenshu_team_member` VALUES (32, 86);
INSERT INTO `wenshu_team_member` VALUES (32, 90);
INSERT INTO `wenshu_team_member` VALUES (33, 9);
INSERT INTO `wenshu_team_member` VALUES (33, 13);
INSERT INTO `wenshu_team_member` VALUES (33, 32);
INSERT INTO `wenshu_team_member` VALUES (33, 46);
INSERT INTO `wenshu_team_member` VALUES (33, 59);
INSERT INTO `wenshu_team_member` VALUES (34, 21);
INSERT INTO `wenshu_team_member` VALUES (34, 50);
INSERT INTO `wenshu_team_member` VALUES (34, 54);
INSERT INTO `wenshu_team_member` VALUES (34, 66);
INSERT INTO `wenshu_team_member` VALUES (34, 77);
INSERT INTO `wenshu_team_member` VALUES (34, 97);
INSERT INTO `wenshu_team_member` VALUES (35, 7);
INSERT INTO `wenshu_team_member` VALUES (35, 25);
INSERT INTO `wenshu_team_member` VALUES (35, 64);
INSERT INTO `wenshu_team_member` VALUES (35, 72);
INSERT INTO `wenshu_team_member` VALUES (35, 100);
INSERT INTO `wenshu_team_member` VALUES (36, 13);
INSERT INTO `wenshu_team_member` VALUES (36, 54);
INSERT INTO `wenshu_team_member` VALUES (36, 56);
INSERT INTO `wenshu_team_member` VALUES (36, 66);
INSERT INTO `wenshu_team_member` VALUES (36, 67);
INSERT INTO `wenshu_team_member` VALUES (36, 87);
INSERT INTO `wenshu_team_member` VALUES (37, 15);
INSERT INTO `wenshu_team_member` VALUES (37, 16);
INSERT INTO `wenshu_team_member` VALUES (37, 27);
INSERT INTO `wenshu_team_member` VALUES (37, 61);
INSERT INTO `wenshu_team_member` VALUES (37, 62);
INSERT INTO `wenshu_team_member` VALUES (37, 70);
INSERT INTO `wenshu_team_member` VALUES (38, 16);
INSERT INTO `wenshu_team_member` VALUES (38, 49);
INSERT INTO `wenshu_team_member` VALUES (38, 54);
INSERT INTO `wenshu_team_member` VALUES (38, 72);
INSERT INTO `wenshu_team_member` VALUES (38, 83);
INSERT INTO `wenshu_team_member` VALUES (38, 89);
INSERT INTO `wenshu_team_member` VALUES (39, 9);
INSERT INTO `wenshu_team_member` VALUES (39, 11);
INSERT INTO `wenshu_team_member` VALUES (39, 86);
INSERT INTO `wenshu_team_member` VALUES (39, 96);
INSERT INTO `wenshu_team_member` VALUES (39, 98);
INSERT INTO `wenshu_team_member` VALUES (40, 1);
INSERT INTO `wenshu_team_member` VALUES (40, 7);
INSERT INTO `wenshu_team_member` VALUES (40, 17);
INSERT INTO `wenshu_team_member` VALUES (40, 57);
INSERT INTO `wenshu_team_member` VALUES (40, 63);
INSERT INTO `wenshu_team_member` VALUES (41, 2);
INSERT INTO `wenshu_team_member` VALUES (41, 23);
INSERT INTO `wenshu_team_member` VALUES (41, 27);
INSERT INTO `wenshu_team_member` VALUES (41, 49);
INSERT INTO `wenshu_team_member` VALUES (41, 52);
INSERT INTO `wenshu_team_member` VALUES (41, 85);
INSERT INTO `wenshu_team_member` VALUES (42, 9);
INSERT INTO `wenshu_team_member` VALUES (42, 56);
INSERT INTO `wenshu_team_member` VALUES (42, 60);
INSERT INTO `wenshu_team_member` VALUES (42, 64);
INSERT INTO `wenshu_team_member` VALUES (42, 81);
INSERT INTO `wenshu_team_member` VALUES (42, 96);
INSERT INTO `wenshu_team_member` VALUES (43, 15);
INSERT INTO `wenshu_team_member` VALUES (43, 44);
INSERT INTO `wenshu_team_member` VALUES (43, 50);
INSERT INTO `wenshu_team_member` VALUES (43, 73);
INSERT INTO `wenshu_team_member` VALUES (43, 82);
INSERT INTO `wenshu_team_member` VALUES (44, 40);
INSERT INTO `wenshu_team_member` VALUES (44, 48);
INSERT INTO `wenshu_team_member` VALUES (44, 49);
INSERT INTO `wenshu_team_member` VALUES (44, 72);
INSERT INTO `wenshu_team_member` VALUES (44, 74);
INSERT INTO `wenshu_team_member` VALUES (44, 96);
INSERT INTO `wenshu_team_member` VALUES (45, 14);
INSERT INTO `wenshu_team_member` VALUES (45, 32);
INSERT INTO `wenshu_team_member` VALUES (45, 33);
INSERT INTO `wenshu_team_member` VALUES (45, 38);
INSERT INTO `wenshu_team_member` VALUES (45, 40);
INSERT INTO `wenshu_team_member` VALUES (45, 68);
INSERT INTO `wenshu_team_member` VALUES (46, 6);
INSERT INTO `wenshu_team_member` VALUES (46, 41);
INSERT INTO `wenshu_team_member` VALUES (46, 66);
INSERT INTO `wenshu_team_member` VALUES (46, 86);
INSERT INTO `wenshu_team_member` VALUES (46, 89);
INSERT INTO `wenshu_team_member` VALUES (47, 12);
INSERT INTO `wenshu_team_member` VALUES (47, 62);
INSERT INTO `wenshu_team_member` VALUES (47, 92);
INSERT INTO `wenshu_team_member` VALUES (47, 94);
INSERT INTO `wenshu_team_member` VALUES (47, 99);
INSERT INTO `wenshu_team_member` VALUES (48, 8);
INSERT INTO `wenshu_team_member` VALUES (48, 24);
INSERT INTO `wenshu_team_member` VALUES (48, 42);
INSERT INTO `wenshu_team_member` VALUES (48, 55);
INSERT INTO `wenshu_team_member` VALUES (48, 84);
INSERT INTO `wenshu_team_member` VALUES (48, 96);
INSERT INTO `wenshu_team_member` VALUES (49, 23);
INSERT INTO `wenshu_team_member` VALUES (49, 32);
INSERT INTO `wenshu_team_member` VALUES (49, 53);
INSERT INTO `wenshu_team_member` VALUES (49, 67);
INSERT INTO `wenshu_team_member` VALUES (49, 82);
INSERT INTO `wenshu_team_member` VALUES (49, 83);
INSERT INTO `wenshu_team_member` VALUES (50, 18);
INSERT INTO `wenshu_team_member` VALUES (50, 30);
INSERT INTO `wenshu_team_member` VALUES (50, 46);
INSERT INTO `wenshu_team_member` VALUES (50, 77);
INSERT INTO `wenshu_team_member` VALUES (50, 88);
INSERT INTO `wenshu_team_member` VALUES (50, 99);
INSERT INTO `wenshu_team_member` VALUES (51, 11);
INSERT INTO `wenshu_team_member` VALUES (51, 14);
INSERT INTO `wenshu_team_member` VALUES (51, 33);
INSERT INTO `wenshu_team_member` VALUES (51, 40);
INSERT INTO `wenshu_team_member` VALUES (51, 100);
INSERT INTO `wenshu_team_member` VALUES (52, 7);
INSERT INTO `wenshu_team_member` VALUES (52, 32);
INSERT INTO `wenshu_team_member` VALUES (52, 53);
INSERT INTO `wenshu_team_member` VALUES (52, 54);
INSERT INTO `wenshu_team_member` VALUES (52, 61);
INSERT INTO `wenshu_team_member` VALUES (52, 62);
INSERT INTO `wenshu_team_member` VALUES (53, 4);
INSERT INTO `wenshu_team_member` VALUES (53, 53);
INSERT INTO `wenshu_team_member` VALUES (53, 66);
INSERT INTO `wenshu_team_member` VALUES (53, 75);
INSERT INTO `wenshu_team_member` VALUES (53, 95);
INSERT INTO `wenshu_team_member` VALUES (54, 22);
INSERT INTO `wenshu_team_member` VALUES (54, 46);
INSERT INTO `wenshu_team_member` VALUES (54, 70);
INSERT INTO `wenshu_team_member` VALUES (54, 71);
INSERT INTO `wenshu_team_member` VALUES (54, 85);
INSERT INTO `wenshu_team_member` VALUES (54, 91);
INSERT INTO `wenshu_team_member` VALUES (55, 3);
INSERT INTO `wenshu_team_member` VALUES (55, 12);
INSERT INTO `wenshu_team_member` VALUES (55, 13);
INSERT INTO `wenshu_team_member` VALUES (55, 24);
INSERT INTO `wenshu_team_member` VALUES (55, 36);
INSERT INTO `wenshu_team_member` VALUES (55, 48);
INSERT INTO `wenshu_team_member` VALUES (56, 26);
INSERT INTO `wenshu_team_member` VALUES (56, 47);
INSERT INTO `wenshu_team_member` VALUES (56, 64);
INSERT INTO `wenshu_team_member` VALUES (56, 70);
INSERT INTO `wenshu_team_member` VALUES (56, 91);
INSERT INTO `wenshu_team_member` VALUES (57, 12);
INSERT INTO `wenshu_team_member` VALUES (57, 42);
INSERT INTO `wenshu_team_member` VALUES (57, 45);
INSERT INTO `wenshu_team_member` VALUES (57, 51);
INSERT INTO `wenshu_team_member` VALUES (57, 68);
INSERT INTO `wenshu_team_member` VALUES (58, 9);
INSERT INTO `wenshu_team_member` VALUES (58, 17);
INSERT INTO `wenshu_team_member` VALUES (58, 42);
INSERT INTO `wenshu_team_member` VALUES (58, 54);
INSERT INTO `wenshu_team_member` VALUES (58, 69);
INSERT INTO `wenshu_team_member` VALUES (59, 43);
INSERT INTO `wenshu_team_member` VALUES (59, 52);
INSERT INTO `wenshu_team_member` VALUES (59, 56);
INSERT INTO `wenshu_team_member` VALUES (59, 87);
INSERT INTO `wenshu_team_member` VALUES (59, 94);
INSERT INTO `wenshu_team_member` VALUES (60, 3);
INSERT INTO `wenshu_team_member` VALUES (60, 37);
INSERT INTO `wenshu_team_member` VALUES (60, 38);
INSERT INTO `wenshu_team_member` VALUES (60, 52);
INSERT INTO `wenshu_team_member` VALUES (60, 63);
INSERT INTO `wenshu_team_member` VALUES (60, 87);
INSERT INTO `wenshu_team_member` VALUES (61, 6);
INSERT INTO `wenshu_team_member` VALUES (61, 12);
INSERT INTO `wenshu_team_member` VALUES (61, 59);
INSERT INTO `wenshu_team_member` VALUES (61, 71);
INSERT INTO `wenshu_team_member` VALUES (61, 80);
INSERT INTO `wenshu_team_member` VALUES (62, 14);
INSERT INTO `wenshu_team_member` VALUES (62, 41);
INSERT INTO `wenshu_team_member` VALUES (62, 55);
INSERT INTO `wenshu_team_member` VALUES (62, 59);
INSERT INTO `wenshu_team_member` VALUES (62, 72);
INSERT INTO `wenshu_team_member` VALUES (62, 81);
INSERT INTO `wenshu_team_member` VALUES (63, 3);
INSERT INTO `wenshu_team_member` VALUES (63, 20);
INSERT INTO `wenshu_team_member` VALUES (63, 67);
INSERT INTO `wenshu_team_member` VALUES (63, 93);
INSERT INTO `wenshu_team_member` VALUES (63, 97);
INSERT INTO `wenshu_team_member` VALUES (64, 43);
INSERT INTO `wenshu_team_member` VALUES (64, 61);
INSERT INTO `wenshu_team_member` VALUES (64, 64);
INSERT INTO `wenshu_team_member` VALUES (64, 77);
INSERT INTO `wenshu_team_member` VALUES (64, 81);
INSERT INTO `wenshu_team_member` VALUES (64, 95);
INSERT INTO `wenshu_team_member` VALUES (65, 6);
INSERT INTO `wenshu_team_member` VALUES (65, 49);
INSERT INTO `wenshu_team_member` VALUES (65, 52);
INSERT INTO `wenshu_team_member` VALUES (65, 54);
INSERT INTO `wenshu_team_member` VALUES (65, 61);
INSERT INTO `wenshu_team_member` VALUES (66, 8);
INSERT INTO `wenshu_team_member` VALUES (66, 39);
INSERT INTO `wenshu_team_member` VALUES (66, 44);
INSERT INTO `wenshu_team_member` VALUES (66, 74);
INSERT INTO `wenshu_team_member` VALUES (66, 78);
INSERT INTO `wenshu_team_member` VALUES (67, 26);
INSERT INTO `wenshu_team_member` VALUES (67, 42);
INSERT INTO `wenshu_team_member` VALUES (67, 50);
INSERT INTO `wenshu_team_member` VALUES (67, 79);
INSERT INTO `wenshu_team_member` VALUES (67, 98);
INSERT INTO `wenshu_team_member` VALUES (68, 41);
INSERT INTO `wenshu_team_member` VALUES (68, 50);
INSERT INTO `wenshu_team_member` VALUES (68, 54);
INSERT INTO `wenshu_team_member` VALUES (68, 58);
INSERT INTO `wenshu_team_member` VALUES (68, 69);
INSERT INTO `wenshu_team_member` VALUES (68, 74);
INSERT INTO `wenshu_team_member` VALUES (69, 58);
INSERT INTO `wenshu_team_member` VALUES (69, 65);
INSERT INTO `wenshu_team_member` VALUES (69, 79);
INSERT INTO `wenshu_team_member` VALUES (69, 80);
INSERT INTO `wenshu_team_member` VALUES (69, 97);
INSERT INTO `wenshu_team_member` VALUES (70, 4);
INSERT INTO `wenshu_team_member` VALUES (70, 18);
INSERT INTO `wenshu_team_member` VALUES (70, 45);
INSERT INTO `wenshu_team_member` VALUES (70, 49);
INSERT INTO `wenshu_team_member` VALUES (70, 91);
INSERT INTO `wenshu_team_member` VALUES (70, 95);
INSERT INTO `wenshu_team_member` VALUES (71, 2);
INSERT INTO `wenshu_team_member` VALUES (71, 23);
INSERT INTO `wenshu_team_member` VALUES (71, 42);
INSERT INTO `wenshu_team_member` VALUES (71, 51);
INSERT INTO `wenshu_team_member` VALUES (71, 71);
INSERT INTO `wenshu_team_member` VALUES (71, 91);
INSERT INTO `wenshu_team_member` VALUES (72, 17);
INSERT INTO `wenshu_team_member` VALUES (72, 27);
INSERT INTO `wenshu_team_member` VALUES (72, 29);
INSERT INTO `wenshu_team_member` VALUES (72, 50);
INSERT INTO `wenshu_team_member` VALUES (72, 97);
INSERT INTO `wenshu_team_member` VALUES (73, 6);
INSERT INTO `wenshu_team_member` VALUES (73, 43);
INSERT INTO `wenshu_team_member` VALUES (73, 73);
INSERT INTO `wenshu_team_member` VALUES (73, 95);
INSERT INTO `wenshu_team_member` VALUES (73, 99);
INSERT INTO `wenshu_team_member` VALUES (74, 45);
INSERT INTO `wenshu_team_member` VALUES (74, 53);
INSERT INTO `wenshu_team_member` VALUES (74, 60);
INSERT INTO `wenshu_team_member` VALUES (74, 65);
INSERT INTO `wenshu_team_member` VALUES (74, 67);
INSERT INTO `wenshu_team_member` VALUES (74, 90);
INSERT INTO `wenshu_team_member` VALUES (75, 7);
INSERT INTO `wenshu_team_member` VALUES (75, 48);
INSERT INTO `wenshu_team_member` VALUES (75, 77);
INSERT INTO `wenshu_team_member` VALUES (75, 79);
INSERT INTO `wenshu_team_member` VALUES (75, 81);
INSERT INTO `wenshu_team_member` VALUES (75, 99);
INSERT INTO `wenshu_team_member` VALUES (76, 1);
INSERT INTO `wenshu_team_member` VALUES (76, 27);
INSERT INTO `wenshu_team_member` VALUES (76, 52);
INSERT INTO `wenshu_team_member` VALUES (76, 60);
INSERT INTO `wenshu_team_member` VALUES (76, 63);
INSERT INTO `wenshu_team_member` VALUES (76, 74);
INSERT INTO `wenshu_team_member` VALUES (77, 2);
INSERT INTO `wenshu_team_member` VALUES (77, 39);
INSERT INTO `wenshu_team_member` VALUES (77, 46);
INSERT INTO `wenshu_team_member` VALUES (77, 59);
INSERT INTO `wenshu_team_member` VALUES (77, 64);
INSERT INTO `wenshu_team_member` VALUES (77, 73);
INSERT INTO `wenshu_team_member` VALUES (78, 50);
INSERT INTO `wenshu_team_member` VALUES (78, 59);
INSERT INTO `wenshu_team_member` VALUES (78, 64);
INSERT INTO `wenshu_team_member` VALUES (78, 79);
INSERT INTO `wenshu_team_member` VALUES (78, 86);
INSERT INTO `wenshu_team_member` VALUES (79, 5);
INSERT INTO `wenshu_team_member` VALUES (79, 36);
INSERT INTO `wenshu_team_member` VALUES (79, 47);
INSERT INTO `wenshu_team_member` VALUES (79, 75);
INSERT INTO `wenshu_team_member` VALUES (79, 92);
INSERT INTO `wenshu_team_member` VALUES (80, 37);
INSERT INTO `wenshu_team_member` VALUES (80, 38);
INSERT INTO `wenshu_team_member` VALUES (80, 53);
INSERT INTO `wenshu_team_member` VALUES (80, 61);
INSERT INTO `wenshu_team_member` VALUES (80, 70);
INSERT INTO `wenshu_team_member` VALUES (81, 5);
INSERT INTO `wenshu_team_member` VALUES (81, 27);
INSERT INTO `wenshu_team_member` VALUES (81, 42);
INSERT INTO `wenshu_team_member` VALUES (81, 51);
INSERT INTO `wenshu_team_member` VALUES (81, 64);
INSERT INTO `wenshu_team_member` VALUES (81, 82);
INSERT INTO `wenshu_team_member` VALUES (82, 2);
INSERT INTO `wenshu_team_member` VALUES (82, 19);
INSERT INTO `wenshu_team_member` VALUES (82, 72);
INSERT INTO `wenshu_team_member` VALUES (82, 88);
INSERT INTO `wenshu_team_member` VALUES (82, 94);
INSERT INTO `wenshu_team_member` VALUES (83, 4);
INSERT INTO `wenshu_team_member` VALUES (83, 17);
INSERT INTO `wenshu_team_member` VALUES (83, 45);
INSERT INTO `wenshu_team_member` VALUES (83, 61);
INSERT INTO `wenshu_team_member` VALUES (83, 70);
INSERT INTO `wenshu_team_member` VALUES (83, 86);
INSERT INTO `wenshu_team_member` VALUES (84, 16);
INSERT INTO `wenshu_team_member` VALUES (84, 32);
INSERT INTO `wenshu_team_member` VALUES (84, 45);
INSERT INTO `wenshu_team_member` VALUES (84, 74);
INSERT INTO `wenshu_team_member` VALUES (84, 85);
INSERT INTO `wenshu_team_member` VALUES (85, 21);
INSERT INTO `wenshu_team_member` VALUES (85, 55);
INSERT INTO `wenshu_team_member` VALUES (85, 74);
INSERT INTO `wenshu_team_member` VALUES (85, 78);
INSERT INTO `wenshu_team_member` VALUES (85, 91);
INSERT INTO `wenshu_team_member` VALUES (86, 17);
INSERT INTO `wenshu_team_member` VALUES (86, 18);
INSERT INTO `wenshu_team_member` VALUES (86, 26);
INSERT INTO `wenshu_team_member` VALUES (86, 42);
INSERT INTO `wenshu_team_member` VALUES (86, 46);
INSERT INTO `wenshu_team_member` VALUES (87, 12);
INSERT INTO `wenshu_team_member` VALUES (87, 17);
INSERT INTO `wenshu_team_member` VALUES (87, 23);
INSERT INTO `wenshu_team_member` VALUES (87, 47);
INSERT INTO `wenshu_team_member` VALUES (87, 71);
INSERT INTO `wenshu_team_member` VALUES (88, 34);
INSERT INTO `wenshu_team_member` VALUES (88, 52);
INSERT INTO `wenshu_team_member` VALUES (88, 63);
INSERT INTO `wenshu_team_member` VALUES (88, 76);
INSERT INTO `wenshu_team_member` VALUES (88, 85);
INSERT INTO `wenshu_team_member` VALUES (89, 2);
INSERT INTO `wenshu_team_member` VALUES (89, 27);
INSERT INTO `wenshu_team_member` VALUES (89, 56);
INSERT INTO `wenshu_team_member` VALUES (89, 69);
INSERT INTO `wenshu_team_member` VALUES (89, 91);
INSERT INTO `wenshu_team_member` VALUES (89, 92);
INSERT INTO `wenshu_team_member` VALUES (90, 1);
INSERT INTO `wenshu_team_member` VALUES (90, 2);
INSERT INTO `wenshu_team_member` VALUES (90, 37);
INSERT INTO `wenshu_team_member` VALUES (90, 38);
INSERT INTO `wenshu_team_member` VALUES (90, 63);
INSERT INTO `wenshu_team_member` VALUES (90, 79);
INSERT INTO `wenshu_team_member` VALUES (91, 8);
INSERT INTO `wenshu_team_member` VALUES (91, 36);
INSERT INTO `wenshu_team_member` VALUES (91, 56);
INSERT INTO `wenshu_team_member` VALUES (91, 67);
INSERT INTO `wenshu_team_member` VALUES (91, 83);
INSERT INTO `wenshu_team_member` VALUES (91, 98);
INSERT INTO `wenshu_team_member` VALUES (92, 6);
INSERT INTO `wenshu_team_member` VALUES (92, 41);
INSERT INTO `wenshu_team_member` VALUES (92, 48);
INSERT INTO `wenshu_team_member` VALUES (92, 70);
INSERT INTO `wenshu_team_member` VALUES (92, 75);
INSERT INTO `wenshu_team_member` VALUES (92, 86);
INSERT INTO `wenshu_team_member` VALUES (93, 12);
INSERT INTO `wenshu_team_member` VALUES (93, 22);
INSERT INTO `wenshu_team_member` VALUES (93, 40);
INSERT INTO `wenshu_team_member` VALUES (93, 68);
INSERT INTO `wenshu_team_member` VALUES (93, 78);
INSERT INTO `wenshu_team_member` VALUES (94, 21);
INSERT INTO `wenshu_team_member` VALUES (94, 32);
INSERT INTO `wenshu_team_member` VALUES (94, 38);
INSERT INTO `wenshu_team_member` VALUES (94, 46);
INSERT INTO `wenshu_team_member` VALUES (94, 94);
INSERT INTO `wenshu_team_member` VALUES (94, 99);
INSERT INTO `wenshu_team_member` VALUES (95, 18);
INSERT INTO `wenshu_team_member` VALUES (95, 43);
INSERT INTO `wenshu_team_member` VALUES (95, 62);
INSERT INTO `wenshu_team_member` VALUES (95, 79);
INSERT INTO `wenshu_team_member` VALUES (95, 84);
INSERT INTO `wenshu_team_member` VALUES (95, 100);
INSERT INTO `wenshu_team_member` VALUES (96, 6);
INSERT INTO `wenshu_team_member` VALUES (96, 36);
INSERT INTO `wenshu_team_member` VALUES (96, 39);
INSERT INTO `wenshu_team_member` VALUES (96, 76);
INSERT INTO `wenshu_team_member` VALUES (96, 95);
INSERT INTO `wenshu_team_member` VALUES (97, 62);
INSERT INTO `wenshu_team_member` VALUES (97, 76);
INSERT INTO `wenshu_team_member` VALUES (97, 84);
INSERT INTO `wenshu_team_member` VALUES (97, 89);
INSERT INTO `wenshu_team_member` VALUES (97, 100);
INSERT INTO `wenshu_team_member` VALUES (98, 1);
INSERT INTO `wenshu_team_member` VALUES (98, 22);
INSERT INTO `wenshu_team_member` VALUES (98, 41);
INSERT INTO `wenshu_team_member` VALUES (98, 47);
INSERT INTO `wenshu_team_member` VALUES (98, 70);
INSERT INTO `wenshu_team_member` VALUES (98, 84);
INSERT INTO `wenshu_team_member` VALUES (99, 10);
INSERT INTO `wenshu_team_member` VALUES (99, 70);
INSERT INTO `wenshu_team_member` VALUES (99, 71);
INSERT INTO `wenshu_team_member` VALUES (99, 81);
INSERT INTO `wenshu_team_member` VALUES (99, 87);
INSERT INTO `wenshu_team_member` VALUES (100, 27);
INSERT INTO `wenshu_team_member` VALUES (100, 35);
INSERT INTO `wenshu_team_member` VALUES (100, 58);
INSERT INTO `wenshu_team_member` VALUES (100, 61);
INSERT INTO `wenshu_team_member` VALUES (100, 66);
INSERT INTO `wenshu_team_member` VALUES (100, 71);
INSERT INTO `wenshu_team_member` VALUES (105, 1);
INSERT INTO `wenshu_team_member` VALUES (107, 1);
INSERT INTO `wenshu_team_member` VALUES (108, 1);
INSERT INTO `wenshu_team_member` VALUES (109, 1);
INSERT INTO `wenshu_team_member` VALUES (110, 1);
INSERT INTO `wenshu_team_member` VALUES (111, 1);

SET FOREIGN_KEY_CHECKS = 1;
