spring:
  application:
    name: wenshu-base
  main:
    allow-bean-definition-overriding: true
  cloud:
    openfeign:
      client:
        config:
          default:
            connectTimeout: 10000
            readTimeout: 10000
            loggerLevel: FULL
      okhttp:
        enabled: true
      httpclient:
        enabled: false
      circuitbreaker:
        enabled: true
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************
    username: root
    password: 2313147023

# 微服务配置
server:
  port: 8604

# Feign配置
feign:
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
      wenshu-calebdar:
        connectTimeout: 30000
        readTimeout: 30000
  compression:
    request:
      enabled: true
    response:
      enabled: true
  circuitbreaker:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.ruoyi.wenshubase.entity
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false

# 日志配置
logging:
  level:
    root: INFO
    com.ruoyi.wenshubase: DEBUG
    org.springframework.cloud.openfeign: DEBUG
    com.ruoyi.programmemanage.client.ProgrammeFeignClient: DEBUG 