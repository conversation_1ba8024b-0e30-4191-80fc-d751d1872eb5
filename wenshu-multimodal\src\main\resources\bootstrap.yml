server:
  port: 8702
spring:
  application:
    name: wenshu-multimodal
  ai:
    dashscope:
      api-key: sk-574f46304e5c4405aa5bbe26af6489b0
      chat:
        model: qwen-max
      embedding:
        options:
          model: text-embedding-v1
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************
    username: root
    password: 2313147023
  servlet:
    multipart:
      max-file-size: 10MB       # 单个文件最大10MB
      max-request-size: 10MB    # 整个请求最大10MB
      enabled: true
  cloud:
    openfeign:
      client:
        config:
          default:
            connectTimeout: 5000  # 默认连接超时5秒
            readTimeout: 60000    # 默认读取超时60秒
          wenshu-chat:           # 为wenshu-chat特别配置
            connectTimeout: 5000
            readTimeout: 120000  # 读取超时2分钟
      compression:
        request:
          enabled: true        # 开启请求压缩
        response:
          enabled: true        # 开启响应压缩
      circuitbreaker:
        enabled: true          # 启用断路器支持
    nacos:
      discovery:
        # 服务注册地址
        server-addr: ************:8848
      config:
        # 配置中心地址
        server-addr: ************:8848
        # 配置文件格式
        file-extension: yml
