package com.ruoyi.wenshufile.service.impl;

import com.ruoyi.wenshuapi.pojo.file.FileUserTeamPojo;
import com.ruoyi.wenshufile.dao.FileUserTeamDao;

import com.ruoyi.wenshufile.service.FileUserTeamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

/**
 * 文件-用户-团队关联服务实现类
 * 实现FileUserTeamService接口，提供关联关系的CRUD操作
 */
@Service  // 标识为Spring服务组件
@Transactional  // 声明事务性操作，确保数据一致性
public class FileUserTeamServiceImpl implements FileUserTeamService {

    private final FileUserTeamDao fileUserTeamDao;

    /**
     * 构造函数注入DAO依赖
     * @param fileUserTeamDao 数据访问对象
     */
    @Autowired
    public FileUserTeamServiceImpl(FileUserTeamDao fileUserTeamDao) {
        this.fileUserTeamDao = fileUserTeamDao;
    }

    /**
     * 插入新的关联关系
     * @param pojo 包含fileId、userId、teamId的实体对象
     * @return 插入操作影响的行数（1-成功，0-失败）
     * @throws IllegalArgumentException 如果参数不合法（如ID为负值）
     */
    @Override
    public int insert(FileUserTeamPojo pojo) {
        validateIds(pojo.getFileId(), pojo.getUserId(), pojo.getTeamId());
        return fileUserTeamDao.insert(pojo);
    }

    /**
     * 通过复合主键删除关联关系
     * @param fileId 文件ID
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 删除操作影响的行数
     * @throws IllegalArgumentException 如果任意ID为无效值
     */
    @Override
    public int deleteByPrimaryKey(int fileId, int userId, int teamId) {
        validateIds(fileId, userId, teamId);
        return fileUserTeamDao.deleteByPrimaryKey(fileId, userId, teamId);
    }

    /**
     * 通过文件ID删除所有相关关联
     * @param fileId 要删除的文件ID
     * @return 删除的记录数量
     * @throws IllegalArgumentException 如果文件ID无效
     */
    @Override
    public int deleteByFileId(int fileId) {
        validateId(fileId, "文件ID");
        return fileUserTeamDao.deleteByFileId(fileId);
    }

    /**
     * 通过用户ID删除所有相关关联
     * @param userId 要删除的用户ID
     * @return 删除的记录数量
     * @throws IllegalArgumentException 如果用户ID无效
     */
    @Override
    public int deleteByUserId(int userId) {
        validateId(userId, "用户ID");
        return fileUserTeamDao.deleteByUserId(userId);
    }

    /**
     * 通过团队ID删除所有相关关联
     * @param teamId 要删除的团队ID
     * @return 删除的记录数量
     * @throws IllegalArgumentException 如果团队ID无效
     */
    @Override
    public int deleteByTeamId(int teamId) {
        validateId(teamId, "团队ID");
        return fileUserTeamDao.deleteByTeamId(teamId);
    }

    /**
     * 通过复合主键查询关联关系
     * @param fileId 文件ID
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 对应的关联实体，不存在时返回null
     * @throws IllegalArgumentException 如果任意ID为无效值
     */
    @Override
    public FileUserTeamPojo selectByPrimaryKey(int fileId, int userId, int teamId) {
        validateIds(fileId, userId, teamId);
        return fileUserTeamDao.selectByPrimaryKey(fileId, userId, teamId);
    }

    /**
     * 通过文件ID查询所有相关关联
     * @param fileId 要查询的文件ID
     * @return 关联实体列表（可能为空列表）
     * @throws IllegalArgumentException 如果文件ID无效
     */
    @Override
    public List<FileUserTeamPojo> selectByFileId(int fileId) {
        validateId(fileId, "文件ID");
        return fileUserTeamDao.selectByFileId(fileId);
    }

    /**
     * 通过用户ID查询所有相关关联
     * @param userId 要查询的用户ID
     * @return 关联实体列表（可能为空列表）
     * @throws IllegalArgumentException 如果用户ID无效
     */
    @Override
    public List<FileUserTeamPojo> selectByUserId(int userId) {
        validateId(userId, "用户ID");
        return fileUserTeamDao.selectByUserId(userId);
    }

    /**
     * 通过团队ID查询所有相关关联
     * @param teamId 要查询的团队ID
     * @return 关联实体列表（可能为空列表）
     * @throws IllegalArgumentException 如果团队ID无效
     */
    @Override
    public List<FileUserTeamPojo> selectByTeamId(int teamId) {
        validateId(teamId, "团队ID");
        return fileUserTeamDao.selectByTeamId(teamId);
    }

    /**
     * 更新关联关系
     * @param oldFileId 原文件ID
     * @param oldUserId 原用户ID
     * @param oldTeamId 原团队ID
     * @param newPojo 新关联实体（包含新的fileId/userId/teamId）
     * @return 更新操作影响的行数
     * @throws IllegalArgumentException 如果任意ID无效或新实体为空
     */
    @Override
    public int updateByPrimaryKey(int oldFileId, int oldUserId, int oldTeamId, FileUserTeamPojo newPojo) {
        // 验证旧ID
        validateIds(oldFileId, oldUserId, oldTeamId);
        
        // 验证新实体及新ID
        if (newPojo == null) {
            throw new IllegalArgumentException("新的关联实体不能为空");
        }
        validateIds(newPojo.getFileId(), newPojo.getUserId(), newPojo.getTeamId());
        
        return fileUserTeamDao.updateByPrimaryKey(oldFileId, oldUserId, oldTeamId, newPojo);
    }

    // ===================== 私有验证方法 =====================
    
    /**
     * 验证单个ID的有效性
     * @param id 要验证的ID值
     * @param idName ID名称（用于错误信息）
     * @throws IllegalArgumentException 如果ID无效
     */
    private void validateId(int id, String idName) {
        if (id <= 0) {
            throw new IllegalArgumentException(idName + "必须为正整数");
        }
    }

    /**
     * 验证复合主键所有ID的有效性
     * @param fileId 文件ID
     * @param userId 用户ID
     * @param teamId 团队ID
     * @throws IllegalArgumentException 如果任意ID无效
     */
    private void validateIds(int fileId, int userId, int teamId) {
        validateId(fileId, "文件ID");
        validateId(userId, "用户ID");
        validateId(teamId, "团队ID");
    }
}