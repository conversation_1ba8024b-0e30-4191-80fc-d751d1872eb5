// 包声明：定义当前类所属的包路径，属于WebSocket工具包
package com.ruoyi.wenshumeeting.websocket.util;

// 导入阿里巴巴FastJSON库的JSONObject类，用于Java对象与JSON字符串的转换
import com.alibaba.fastjson.JSONObject;
// 导入自定义的信号实体类，用于封装WebSocket通信数据
import com.ruoyi.wenshumeeting.controller.SignalEntity;
// 导入WebSocket编码异常类，当编码过程出错时抛出
import jakarta.websocket.EncodeException;
// 导入WebSocket编码器接口，用于将Java对象编码为文本消息
import jakarta.websocket.Encoder;
// 导入WebSocket端点配置接口，用于编码器的初始化配置
import jakarta.websocket.EndpointConfig;

/**
 * WebSocket消息编码器工具类
 * 实现Encoder.Text接口，用于将SignalEntity对象编码为JSON字符串消息发送给客户端
 * 在WebSocket通信中，服务端需要将Java对象转换为客户端可识别的文本消息
 *
 * 泛型参数说明：
 * - Encoder.Text<SignalEntity>: 表示这是一个文本编码器，将SignalEntity类型编码为文本
 *
 * 作者信息：
 * @Author: wu.shaoya
 * @Description: WebSocket消息编码器
 * @Date: 10:14 2019/10/31
 */
public class EncoderUtil implements Encoder.Text<SignalEntity> {

    /**
     * 编码方法实现
     * 将SignalEntity对象编码为JSON字符串消息
     * 这是编码器的核心方法，WebSocket框架会自动调用此方法进行消息转换
     *
     * @param message 要编码的SignalEntity对象，包含消息类型、内容和数据
     * @return String 编码后的JSON格式字符串，可以直接发送给WebSocket客户端
     * @throws EncodeException 当对象序列化失败或转换出错时抛出编码异常
     */
    @Override
    public String encode(SignalEntity message) throws EncodeException {
        // 使用FastJSON库将SignalEntity对象转换为JSON字符串
        // JSONObject.toJSONString方法会自动将对象的所有字段序列化为JSON格式
        // 生成的JSON字符串包含type、msg、status、data等字段
        return JSONObject.toJSONString(message);
    }

    /**
     * 编码器初始化方法
     * 在WebSocket端点创建编码器实例时调用
     * 可以在此方法中进行编码器的初始化配置
     *
     * @param ec WebSocket端点配置对象，包含端点的配置信息
     */
    @Override
    public void init(EndpointConfig ec) {
        // 初始化逻辑（当前为空实现）
        // 如果需要可以在此处添加编码器的初始化代码
        // 例如：配置JSON序列化的特殊设置、初始化格式化器等
        // System.out.println("MessageEncoder - init method called");
    }

    /**
     * 编码器销毁方法
     * 在WebSocket端点关闭或编码器不再使用时调用
     * 可以在此方法中进行资源清理工作
     */
    @Override
    public void destroy() {
        // 清理逻辑（当前为空实现）
        // 如果需要可以在此处添加资源清理代码
        // 例如：清理缓存、释放资源、关闭连接等
        // System.out.println("MessageEncoder - destroy method called");
    }
}

