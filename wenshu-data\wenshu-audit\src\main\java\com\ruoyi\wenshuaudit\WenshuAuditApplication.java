package com.ruoyi.wenshuaudit;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Configuration;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

// Generated by https://start.springboot.io
// 优质的 spring/boot/data/security/cloud 框架中文文档尽在 => https://springdoc.cn
@SpringBootApplication
@MapperScan("com.ruoyi.wenshuaudit.dao")
@EnableFeignClients
public class WenshuAuditApplication {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static void printStartupBanner() {
        System.out.println();
        System.out.println("██╗    ██╗███████╗███╗   ██╗███████╗██╗  ██╗██╗   ██╗");
        System.out.println("██║    ██║██╔════╝████╗  ██║██╔════╝██║  ██║██║   ██║");
        System.out.println("██║ █╗ ██║█████╗  ██╔██╗ ██║███████╗███████║██║   ██║");
        System.out.println("██║███╗██║██╔══╝  ██║╚██╗██║╚════██║██╔══██║██║   ██║");
        System.out.println("╚███╔███╔╝███████╗██║ ╚████║███████║██║  ██║╚██████╔╝");
        System.out.println(" ╚══╝╚══╝ ╚══════╝╚═╝  ╚═══╝╚══════╝╚═╝  ╚═╝ ╚═════╝ ");
        System.out.println();
        System.out.println("WENSHU-AUDIT 审计服务");
        System.out.println("启动时间: " + LocalDateTime.now().format(FORMATTER));
        System.out.println("服务端口: 8603");
        System.out.println("服务功能: 系统审计管理");
        System.out.println();
    }

    /**
     * 额外配置，禁用DashScope自动配置
     */
    @Configuration
    @ConditionalOnClass(name = {
        "com.alibaba.cloud.ai.autoconfigure.dashscope.DashScopeAutoConfiguration",
        "com.alibaba.cloud.ai.autoconfigure.dashscope.embedding.DashScopeEmbeddingAutoConfiguration",
        "com.alibaba.cloud.ai.autoconfigure.dashscope.generation.DashScopeGenerationAutoConfiguration",
        "com.alibaba.cloud.ai.autoconfigure.dashscope.transcription.DashScopeTranscriptionAutoConfiguration"
    })
    static class DashScopeExclusionConfig {
        // 空配置类，仅用于条件性排除DashScope自动配置
    }

    public static void main(String[] args) {
        printStartupBanner();
        System.setProperty("spring.cloud.bootstrap.location", "classpath:bootstrap.yml");
        System.setProperty("spring.config.location", "classpath:bootstrap.yml");
        SpringApplication.run(WenshuAuditApplication.class, args);
    }

}
