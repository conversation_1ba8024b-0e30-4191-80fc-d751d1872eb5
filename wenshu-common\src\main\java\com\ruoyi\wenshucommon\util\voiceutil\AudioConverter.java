package com.ruoyi.wenshucommon.util.voiceutil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sound.sampled.*;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 音频转换工具类
 * 用于处理音频格式转换，特别是双声道转单声道
 */
public class AudioConverter {
    private static final Logger logger = LoggerFactory.getLogger(AudioConverter.class);

    /**
     * 将双声道音频转换为单声道
     * @param inputPath 输入音频文件路径
     * @return 转换后的单声道音频文件路径
     * @throws Exception 转换过程中的异常
     */
    public static Path convertToMono(Path inputPath) throws Exception {
        logger.info("开始音频转换: {} -> 单声道", inputPath);

        try {
            // 检查输入文件是否存在
            if (!Files.exists(inputPath)) {
                throw new FileNotFoundException("输入音频文件不存在: " + inputPath);
            }

            // 首先尝试使用FFmpeg转换（支持更多格式）
            if (FFmpegUtils.isFFmpegAvailable()) {
                try {
                    return convertToMonoWithFFmpeg(inputPath);
                } catch (Exception e) {
                    logger.warn("FFmpeg转换失败，尝试使用Java AudioSystem: {}", e.getMessage());
                }
            } else {
                logger.info("FFmpeg不可用，直接使用Java AudioSystem进行转换");
            }

            // 如果FFmpeg失败，尝试使用Java AudioSystem
            try {
                return convertToMonoWithAudioSystem(inputPath);
            } catch (UnsupportedAudioFileException e) {
                logger.error("Java AudioSystem不支持该音频格式");
                if (!FFmpegUtils.isFFmpegAvailable()) {
                    String advice = FFmpegUtils.getFFmpegInstallationAdvice();
                    throw new Exception("音频格式不支持。建议: " + advice, e);
                } else {
                    throw new Exception("音频格式转换失败，请检查音频文件是否损坏", e);
                }
            }

        } catch (Exception e) {
            logger.error("音频转换失败: {}", e.getMessage(), e);
            throw new Exception("音频转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用FFmpeg将音频转换为单声道
     * @param inputPath 输入文件路径
     * @return 转换后的文件路径
     * @throws Exception 转换异常
     */
    private static Path convertToMonoWithFFmpeg(Path inputPath) throws Exception {
        logger.info("使用FFmpeg进行音频转换");

        // 生成输出文件路径
        String fileName = inputPath.getFileName().toString();
        String nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
        Path outputPath = inputPath.getParent().resolve(nameWithoutExt + "_mono.wav");

        try {
            // 构建FFmpeg命令：转换为单声道16kHz WAV格式（阿里云API要求）
            ProcessBuilder pb = new ProcessBuilder(
                "ffmpeg",
                "-i", inputPath.toString(),
                "-ac", "1",           // 单声道
                "-ar", "16000",       // 16kHz采样率（阿里云API要求）
                "-acodec", "pcm_s16le", // PCM 16位编码
                "-y",                 // 覆盖输出文件
                outputPath.toString()
            );

            pb.redirectErrorStream(true);
            Process process = pb.start();

            // 读取输出（用于调试）
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                StringBuilder output = new StringBuilder();
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
                logger.debug("FFmpeg输出: {}", output.toString());
            }

            int exitCode = process.waitFor();
            if (exitCode != 0) {
                throw new Exception("FFmpeg转换失败，退出码: " + exitCode);
            }

            if (!Files.exists(outputPath)) {
                throw new Exception("FFmpeg转换完成但输出文件不存在");
            }

            logger.info("FFmpeg音频转换完成: {}", outputPath);
            return outputPath;

        } catch (Exception e) {
            // 清理可能创建的文件
            try {
                Files.deleteIfExists(outputPath);
            } catch (IOException ignored) {}

            throw new Exception("FFmpeg音频转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用Java AudioSystem进行音频转换
     * @param inputPath 输入文件路径
     * @return 转换后的文件路径
     * @throws Exception 转换异常
     */
    private static Path convertToMonoWithAudioSystem(Path inputPath) throws Exception {
        logger.info("使用Java AudioSystem进行音频转换");

        // 读取音频文件
        AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(inputPath.toFile());
        AudioFormat originalFormat = audioInputStream.getFormat();

        logger.info("原始音频格式: 声道数={}, 采样率={}, 位深={}",
                   originalFormat.getChannels(),
                   originalFormat.getSampleRate(),
                   originalFormat.getSampleSizeInBits());

        // 如果已经是单声道，直接返回原文件
        if (originalFormat.getChannels() == 1) {
            logger.info("音频已经是单声道，无需转换");
            audioInputStream.close();
            return inputPath;
        }

        // 创建单声道格式
        AudioFormat monoFormat = new AudioFormat(
            originalFormat.getEncoding(),
            originalFormat.getSampleRate(),
            originalFormat.getSampleSizeInBits(),
            1, // 单声道
            originalFormat.getFrameSize() / originalFormat.getChannels(),
            originalFormat.getFrameRate(),
            originalFormat.isBigEndian()
        );

        logger.info("目标音频格式: 声道数={}, 采样率={}, 位深={}",
                   monoFormat.getChannels(),
                   monoFormat.getSampleRate(),
                   monoFormat.getSampleSizeInBits());

        // 检查是否支持转换
        if (!AudioSystem.isConversionSupported(monoFormat, originalFormat)) {
            logger.warn("系统不支持直接转换，尝试手动转换");
            audioInputStream.close();
            return convertToMonoManually(inputPath, originalFormat);
        }

        // 执行转换
        AudioInputStream monoStream = AudioSystem.getAudioInputStream(monoFormat, audioInputStream);

        // 生成输出文件路径
        String fileName = inputPath.getFileName().toString();
        String nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
        Path outputPath = inputPath.getParent().resolve(nameWithoutExt + "_mono.wav");

        // 写入转换后的音频
        AudioSystem.write(monoStream, AudioFileFormat.Type.WAVE, outputPath.toFile());

        // 关闭流
        monoStream.close();
        audioInputStream.close();

        logger.info("Java AudioSystem音频转换完成: {}", outputPath);
        return outputPath;
    }
    
    /**
     * 手动转换双声道为单声道（当系统不支持直接转换时）
     * @param inputPath 输入文件路径
     * @param originalFormat 原始音频格式
     * @return 转换后的文件路径
     * @throws Exception 转换异常
     */
    private static Path convertToMonoManually(Path inputPath, AudioFormat originalFormat) throws Exception {
        logger.info("开始手动音频转换");
        
        try (AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(inputPath.toFile())) {
            
            int channels = originalFormat.getChannels();
            int sampleSizeInBytes = originalFormat.getSampleSizeInBits() / 8;
            int frameSize = originalFormat.getFrameSize();
            
            // 读取所有音频数据
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = audioInputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            
            byte[] audioData = baos.toByteArray();
            
            // 转换为单声道（取左声道或混合）
            byte[] monoData = new byte[audioData.length / channels];
            
            for (int i = 0; i < audioData.length; i += frameSize) {
                // 只取左声道数据
                System.arraycopy(audioData, i, monoData, i / channels, sampleSizeInBytes);
            }
            
            // 创建单声道格式
            AudioFormat monoFormat = new AudioFormat(
                originalFormat.getEncoding(),
                originalFormat.getSampleRate(),
                originalFormat.getSampleSizeInBits(),
                1, // 单声道
                sampleSizeInBytes,
                originalFormat.getFrameRate(),
                originalFormat.isBigEndian()
            );
            
            // 创建单声道音频流
            ByteArrayInputStream bais = new ByteArrayInputStream(monoData);
            AudioInputStream monoStream = new AudioInputStream(bais, monoFormat, monoData.length / monoFormat.getFrameSize());
            
            // 生成输出文件路径
            String fileName = inputPath.getFileName().toString();
            String nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
            String extension = fileName.substring(fileName.lastIndexOf('.'));
            Path outputPath = inputPath.getParent().resolve(nameWithoutExt + "_mono" + extension);
            
            // 写入文件
            AudioSystem.write(monoStream, AudioFileFormat.Type.WAVE, outputPath.toFile());
            
            monoStream.close();
            
            logger.info("手动音频转换完成: {}", outputPath);
            return outputPath;
            
        } catch (Exception e) {
            logger.error("手动音频转换失败: {}", e.getMessage(), e);
            throw new Exception("手动音频转换失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 检查音频文件的声道数
     * @param audioPath 音频文件路径
     * @return 声道数
     * @throws Exception 检查异常
     */
    public static int getChannelCount(Path audioPath) throws Exception {
        try {
            // 首先尝试使用AudioSystem（支持WAV等格式）
            try (AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(audioPath.toFile())) {
                int channels = audioInputStream.getFormat().getChannels();
                logger.info("通过AudioSystem获取声道数: {}", channels);
                return channels;
            }
        } catch (UnsupportedAudioFileException e) {
            logger.warn("AudioSystem不支持该音频格式，尝试使用FFmpeg检查: {}", e.getMessage());

            // 如果AudioSystem不支持，尝试使用FFmpeg命令行工具
            if (FFmpegUtils.isFFmpegAvailable()) {
                return getChannelCountWithFFmpeg(audioPath);
            } else {
                logger.warn("FFmpeg不可用，无法检测音频格式，假设为双声道");
                return 2; // 假设是双声道
            }
        } catch (Exception e) {
            logger.error("获取音频声道数失败: {}", e.getMessage());
            throw new Exception("获取音频声道数失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用FFmpeg获取音频声道数
     * @param audioPath 音频文件路径
     * @return 声道数
     * @throws Exception 检查异常
     */
    private static int getChannelCountWithFFmpeg(Path audioPath) throws Exception {
        try {
            // 构建FFmpeg命令
            ProcessBuilder pb = new ProcessBuilder(
                "ffprobe",
                "-v", "quiet",
                "-show_entries", "stream=channels",
                "-of", "csv=p=0",
                audioPath.toString()
            );

            Process process = pb.start();

            // 读取输出
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line = reader.readLine();
                if (line != null && !line.trim().isEmpty()) {
                    int channels = Integer.parseInt(line.trim());
                    logger.info("通过FFmpeg获取声道数: {}", channels);
                    return channels;
                }
            }

            int exitCode = process.waitFor();
            if (exitCode != 0) {
                throw new Exception("FFmpeg执行失败，退出码: " + exitCode);
            }

            throw new Exception("无法从FFmpeg输出中解析声道数");

        } catch (Exception e) {
            logger.warn("FFmpeg检查失败: {}", e.getMessage());
            // 如果FFmpeg也失败，假设是双声道（常见情况）
            logger.info("无法确定声道数，假设为双声道");
            return 2;
        }
    }
    
    /**
     * 获取音频文件的采样率
     * @param audioPath 音频文件路径
     * @return 采样率（Hz）
     * @throws Exception 检查异常
     */
    public static int getSampleRate(Path audioPath) throws Exception {
        try {
            // 首先尝试使用AudioSystem
            try (AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(audioPath.toFile())) {
                float sampleRate = audioInputStream.getFormat().getSampleRate();
                logger.info("通过AudioSystem获取采样率: {} Hz", sampleRate);
                return (int) sampleRate;
            }
        } catch (UnsupportedAudioFileException e) {
            logger.warn("AudioSystem不支持该音频格式，尝试使用FFmpeg检查采样率: {}", e.getMessage());

            // 如果AudioSystem不支持，尝试使用FFmpeg
            if (FFmpegUtils.isFFmpegAvailable()) {
                return getSampleRateWithFFmpeg(audioPath);
            } else {
                logger.warn("FFmpeg不可用，无法检测音频采样率，使用默认值16000Hz");
                return 16000; // 默认采样率
            }
        } catch (Exception e) {
            logger.error("获取音频采样率失败: {}", e.getMessage());
            throw new Exception("获取音频采样率失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用FFmpeg获取音频采样率
     * @param audioPath 音频文件路径
     * @return 采样率（Hz）
     * @throws Exception 检查异常
     */
    private static int getSampleRateWithFFmpeg(Path audioPath) throws Exception {
        try {
            // 构建FFmpeg命令
            ProcessBuilder pb = new ProcessBuilder(
                "ffprobe",
                "-v", "quiet",
                "-show_entries", "stream=sample_rate",
                "-of", "csv=p=0",
                audioPath.toString()
            );

            Process process = pb.start();

            // 读取输出
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line = reader.readLine();
                if (line != null && !line.trim().isEmpty()) {
                    int sampleRate = Integer.parseInt(line.trim());
                    logger.info("通过FFmpeg获取采样率: {} Hz", sampleRate);
                    return sampleRate;
                }
            }

            int exitCode = process.waitFor();
            if (exitCode != 0) {
                throw new Exception("FFmpeg执行失败，退出码: " + exitCode);
            }

            throw new Exception("无法从FFmpeg输出中解析采样率");

        } catch (Exception e) {
            logger.warn("FFmpeg检查采样率失败: {}", e.getMessage());
            // 如果FFmpeg也失败，使用默认采样率
            logger.info("无法确定采样率，使用默认值16000Hz");
            return 16000;
        }
    }

    /**
     * 清理临时转换文件
     * @param convertedPath 转换后的文件路径
     */
    public static void cleanupConvertedFile(Path convertedPath) {
        try {
            if (convertedPath != null && Files.exists(convertedPath)) {
                String fileName = convertedPath.getFileName().toString();
                // 检查是否是转换后的文件（包含_mono标识）
                if (fileName.contains("_mono")) {
                    Files.delete(convertedPath);
                    logger.info("已清理临时转换文件: {}", convertedPath);
                }
            }
        } catch (IOException e) {
            logger.warn("清理临时转换文件失败: {}", e.getMessage());
        }
    }
}
