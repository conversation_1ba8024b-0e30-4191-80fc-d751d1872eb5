@echo off
chcp 65001 >nul
echo === 更新Redis到v7.4.4版本 ===
echo.

REM 检查Docker是否运行
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: Docker未运行，请先启动Docker
    pause
    exit /b 1
)
echo ✅ Docker运行正常

REM 1. 停止现有的Redis服务
echo.
echo 1. 停止现有Redis服务...
docker compose -f docker-compose.env.yml down redis >nul 2>&1

REM 2. 拉取新的Redis镜像
echo.
echo 2. 拉取Redis v7.4.4镜像...
docker pull lcr.loongnix.cn/library/redis:7.4.4

if %errorlevel% equ 0 (
    echo ✅ Redis v7.4.4镜像拉取成功
) else (
    echo ❌ Redis镜像拉取失败，请检查网络连接
    pause
    exit /b 1
)

REM 3. 删除旧的Redis镜像（可选）
echo.
echo 3. 清理旧的Redis镜像...
docker rmi lcr.loongnix.cn/library/redis:7.0.0-alpine >nul 2>&1

REM 4. 备份Redis数据（可选）
echo.
set /p backup="是否备份Redis数据？(y/N): "
if /i "%backup%"=="y" (
    echo    备份Redis数据...
    for /f "tokens=2 delims==" %%i in ('wmic OS Get localdatetime /value') do set datetime=%%i
    set timestamp=%datetime:~0,8%_%datetime:~8,6%
    docker run --rm -v wenshuintelligentcomputing-back_redis_data:/data -v %cd%:/backup lcr.loongnix.cn/library/redis:7.4.4 tar czf /backup/redis_backup_%timestamp%.tar.gz -C /data .
    if %errorlevel% equ 0 (
        echo ✅ Redis数据备份完成: redis_backup_%timestamp%.tar.gz
    ) else (
        echo ⚠️  Redis数据备份失败，但继续更新
    )
)

REM 5. 启动新版本的Redis
echo.
echo 4. 启动Redis v7.4.4...
docker compose -f docker-compose.env.yml up -d redis

REM 6. 等待Redis启动完成
echo.
echo 5. 等待Redis启动完成...
set /a count=0
:wait_redis
set /a count+=1
docker exec wenshu-redis redis-cli ping >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Redis启动成功
    goto redis_ready
)
if %count% geq 30 (
    echo ❌ Redis启动失败，请检查日志：
    echo    docker compose -f docker-compose.env.yml logs redis
    pause
    exit /b 1
)
echo    等待Redis启动... (%count%/30)
timeout /t 2 /nobreak >nul
goto wait_redis

:redis_ready
REM 7. 验证Redis功能
echo.
echo 6. 验证Redis功能...

REM 测试基本功能
echo    测试基本读写功能...
docker exec wenshu-redis redis-cli set test_key "Redis 7.4.4 works!" >nul
for /f "delims=" %%i in ('docker exec wenshu-redis redis-cli get test_key') do set test_value=%%i
if "%test_value%"=="Redis 7.4.4 works!" (
    echo ✅ Redis读写功能正常
    docker exec wenshu-redis redis-cli del test_key >nul
) else (
    echo ❌ Redis读写功能异常
)

REM 检查Redis版本
echo    检查Redis版本...
for /f "tokens=2 delims=:" %%i in ('docker exec wenshu-redis redis-cli info server ^| findstr redis_version') do (
    set redis_version=%%i
    set redis_version=!redis_version: =!
)
echo ✅ 当前Redis版本: %redis_version%

REM 检查内存使用
echo    检查内存配置...
for /f "skip=1 delims=" %%i in ('docker exec wenshu-redis redis-cli config get maxmemory') do (
    set max_memory=%%i
    goto :break_memory
)
:break_memory
set /a max_memory_mb=%max_memory%/1024/1024
echo ✅ 最大内存限制: %max_memory%字节 (%max_memory_mb%MB)

REM 8. 重启依赖Redis的服务
echo.
echo 7. 重启依赖Redis的服务...

docker ps --filter "name=wenshu-auth" --format "{{.Names}}" | findstr wenshu-auth >nul
if %errorlevel% equ 0 (
    echo    重启 auth 服务...
    docker compose -f docker-compose.app.yml restart auth
    timeout /t 5 /nobreak >nul
)

docker ps --filter "name=wenshu-system" --format "{{.Names}}" | findstr wenshu-system >nul
if %errorlevel% equ 0 (
    echo    重启 system 服务...
    docker compose -f docker-compose.app.yml restart system
    timeout /t 5 /nobreak >nul
)

docker ps --filter "name=wenshu-gateway" --format "{{.Names}}" | findstr wenshu-gateway >nul
if %errorlevel% equ 0 (
    echo    重启 gateway 服务...
    docker compose -f docker-compose.app.yml restart gateway
    timeout /t 5 /nobreak >nul
)

REM 9. 最终检查
echo.
echo 8. 最终状态检查...
echo === Redis容器状态 ===
docker ps --filter "name=wenshu-redis" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo.
echo === Redis健康检查 ===
docker exec wenshu-redis redis-cli ping >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Redis服务正常
) else (
    echo ❌ Redis服务异常
)

echo.
echo === Redis配置信息 ===
echo 端口: 6379
echo 数据持久化: AOF + RDB
echo 最大内存: 96MB
echo 内存策略: allkeys-lru
echo 最大客户端: 50
echo 数据库数量: 8

echo.
echo === 更新完成 ===
echo 🎉 Redis已成功更新到v7.4.4版本
echo.
echo Redis v7.4.4新特性：
echo   - 性能优化和bug修复
echo   - 更好的内存管理
echo   - 增强的安全性
echo   - 改进的集群支持
echo.
echo 访问Redis：
echo   docker exec -it wenshu-redis redis-cli
echo.
echo 查看Redis日志：
echo   docker compose -f docker-compose.env.yml logs redis
echo.
pause
