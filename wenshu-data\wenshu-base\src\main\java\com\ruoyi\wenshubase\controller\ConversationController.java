package com.ruoyi.wenshubase.controller;

import com.ruoyi.wenshuapi.common.RestResult;
import com.ruoyi.wenshubase.entity.BaseConversation;
import com.ruoyi.wenshubase.service.ChatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 聊天API控制器
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/conversation")
@Slf4j
public class ConversationController {
    final ChatService chatService;


    @PostMapping("/create")
    public RestResult<BaseConversation> create(@RequestParam("userId") int userId) {
        return RestResult.buildSuccessResult(chatService.create(userId));
    }

    @PutMapping("/edit")
    public RestResult<Void> edit(@RequestBody BaseConversation conversation, @RequestParam("userId") int userId) {
        chatService.updateConversation(conversation, userId);
        return RestResult.buildSuccessResult();
    }

    @DeleteMapping("/del")
    public RestResult<Void> del(@RequestParam(value = "conversationId") String conversationId) {
        chatService.delete(conversationId);
        return RestResult.buildSuccessResult();
    }

    @GetMapping("/list")
    public RestResult<List<BaseConversation>> list(@RequestParam("userId") int userId) {
        return RestResult.buildSuccessResult(chatService.list(userId));
    }

    @GetMapping("/get")
    public RestResult<BaseConversation> get(@RequestParam(value = "conversationId") String conversationId, @RequestParam("userId") int userId) {
        return RestResult.buildSuccessResult(chatService.get(conversationId, userId));
    }
}