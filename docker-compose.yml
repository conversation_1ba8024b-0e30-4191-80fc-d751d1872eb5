version: '3.8'
services:
  # 注释掉本地服务，改为使用外网************的服务
  # mysql:
  #   image: lcr.loongnix.cn/library/mysql:8.4.3
  #   container_name: wenshu-mysql
  #   restart: always
  #   environment:
  #     MYSQL_ROOT_PASSWORD: **********
  #     MYSQL_DATABASE: ry-cloud
  #     TZ: Asia/Shanghai
  #     MYSQL_INITDB_SKIP_TZINFO: 1
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #     - ./sql:/docker-entrypoint-initdb.d
  #   ports:
  #     - "3306:3306"
  #   command:
  #     - --character-set-server=utf8mb4
  #     - --collation-server=utf8mb4_unicode_ci
  #     - --mysql-native-password=ON
  #     # 内存优化配置（移除不支持的参数）
  #     - --innodb-buffer-pool-size=256M
  #     - --innodb-log-file-size=64M
  #     - --innodb-log-buffer-size=8M
  #     - --key-buffer-size=32M
  #     - --table-open-cache=256
  #     - --sort-buffer-size=1M
  #     - --read-buffer-size=1M
  #     - --read-rnd-buffer-size=1M
  #     - --myisam-sort-buffer-size=32M
  #     - --thread-cache-size=8
  #     - --tmp-table-size=32M
  #     - --max-heap-table-size=32M
  #     - --max-connections=100
  #   healthcheck:
  #     test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p**********"]
  #     interval: 60s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 30s
  #   security_opt:
  #     - apparmor=unconfined
  #     - seccomp=unconfined
  #   # 内存限制
  #   mem_limit: 512m
  #   memswap_limit: 512m
  #   networks:
  #     - wenshu-network

  # redis:
  #   image: lcr.loongnix.cn/library/redis:7.4.4
  #   container_name: wenshu-redis
  #   restart: always
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   command:
  #     - redis-server
  #     - --appendonly yes
  #     - --maxmemory 128mb
  #     - --maxmemory-policy allkeys-lru
  #     - --save 900 1
  #     - --save 300 10
  #     - --save 60 10000
  #     - --tcp-keepalive 60
  #     - --timeout 300
  #   healthcheck:
  #     test: ["CMD", "redis-cli", "ping"]
  #     interval: 60s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 15s
  #   security_opt:
  #     - apparmor=unconfined
  #     - seccomp=unconfined
  #   # 内存限制
  #   mem_limit: 256m
  #   memswap_limit: 256m
  #   networks:
  #     - wenshu-network

  # nacos:
  #   image: lcr.loongnix.cn/nacos/nacos-server:v2.2.0
  #   container_name: wenshu-nacos
  #   restart: always
  #   environment:
  #     MODE: standalone
  #     SPRING_DATASOURCE_PLATFORM: mysql
  #     MYSQL_SERVICE_HOST: mysql
  #     MYSQL_SERVICE_PORT: 3306
  #     MYSQL_SERVICE_USER: root
  #     MYSQL_SERVICE_PASSWORD: **********
  #     MYSQL_SERVICE_DB_NAME: ry-config
  #     MYSQL_SERVICE_DB_PARAM: characterEncoding=utf8&connectTimeout=10000&socketTimeout=30000&autoReconnect=true&useUnicode=true
  #     # 禁用认证
  #     NACOS_AUTH_ENABLE: false
  #     # 强制使用HTTP协议，禁用gRPC
  #     NACOS_SERVER_GRPC_PORT_OFFSET: -1
  #     # JVM 内存优化
  #     JVM_XMS: 256m
  #     JVM_XMX: 512m
  #     JVM_XMN: 128m
  #     JVM_MS: 64m
  #     JVM_MMS: 128m
  #     NACOS_DEBUG: n
  #     TOMCAT_ACCESSLOG_ENABLED: false
  #   volumes:
  #     - nacos_data:/home/<USER>/data
  #   ports:
  #     - "8848:8848"
  #   depends_on:
  #     - mysql
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:8848/nacos/actuator/health"]
  #     interval: 60s
  #     timeout: 15s
  #     retries: 3
  #     start_period: 90s
  #   security_opt:
  #     - apparmor=unconfined
  #     - seccomp=unconfined
  #   # 内存限制
  #   mem_limit: 768m
  #   memswap_limit: 768m
  #   networks:
  #     - wenshu-network

  # etcd:
  #   image: lcr.loongnix.cn/kubernetes/etcd:3.5.10-0
  #   container_name: wenshu-etcd
  #   restart: always
  #   environment:
  #     - ETCD_AUTO_COMPACTION_MODE=revision
  #     - ETCD_AUTO_COMPACTION_RETENTION=1000
  #     - ETCD_QUOTA_BACKEND_BYTES=**********  # 1GB instead of 4GB
  #     - ETCD_SNAPSHOT_COUNT=10000  # Reduced from 50000
  #     - ETCD_MAX_REQUEST_BYTES=1572864  # 1.5MB
  #     - ETCD_GRPC_KEEPALIVE_MIN_TIME=5s
  #     - ETCD_GRPC_KEEPALIVE_INTERVAL=2h
  #     - ETCD_GRPC_KEEPALIVE_TIMEOUT=20s
  #   volumes:
  #     - etcd_data:/etcd
  #   command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
  #   healthcheck:
  #     test: ["CMD", "etcdctl", "endpoint", "health"]
  #     interval: 60s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 30s
  #   security_opt:
  #     - apparmor=unconfined
  #     - seccomp=unconfined
  #   # 内存限制
  #   mem_limit: 256m
  #   memswap_limit: 256m
  #   networks:
  #     - wenshu-network

  # minio:
  #   image: lcr.loongnix.cn/minio/minio:RELEASE.2025-03-12T18-04-18Z
  #   container_name: wenshu-minio
  #   restart: always
  #   environment:
  #     MINIO_ACCESS_KEY: minioadmin
  #     MINIO_SECRET_KEY: minioadmin
  #     # 内存优化配置
  #     MINIO_CACHE_DRIVES: ""
  #     MINIO_CACHE_EXCLUDE: ""
  #     MINIO_CACHE_QUOTA: 50
  #     MINIO_CACHE_AFTER: 0
  #     MINIO_CACHE_WATERMARK_LOW: 70
  #     MINIO_CACHE_WATERMARK_HIGH: 90
  #   volumes:
  #     - minio_data:/minio_data
  #   command: minio server /minio_data --console-address ":9001"
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/ready"]
  #     interval: 60s
  #     timeout: 20s
  #     retries: 3
  #     start_period: 45s
  #   security_opt:
  #     - apparmor=unconfined
  #     - seccomp=unconfined
  #   # 内存限制
  #   mem_limit: 256m
  #   memswap_limit: 256m
  #   networks:
  #     - wenshu-network

  # milvus:
  #   image: lcr.loongnix.cn/milvusdb/milvus:2.5.7
  #   container_name: wenshu-milvus
  #   restart: "no"  # 临时禁用自动重启以便调试
  #   environment:
  #     ETCD_ENDPOINTS: etcd:2379
  #     MINIO_ADDRESS: minio:9000
  #     # 内存优化配置
  #     CACHE_SIZE: 256MB
  #     INSERT_BUFFER_SIZE: 64MB
  #     PRELOAD_COLLECTION: false
  #     # 直接通过环境变量配置
  #     MILVUS_ETCD_ENDPOINTS: etcd:2379
  #     MILVUS_MINIO_ADDRESS: minio:9000
  #     MILVUS_MINIO_ACCESS_KEY_ID: minioadmin
  #     MILVUS_MINIO_SECRET_ACCESS_KEY: minioadmin
  #   command: ["milvus", "run", "standalone"]
  #   volumes:
  #     - milvus_data:/var/lib/milvus
  #   ports:
  #     - "19530:19530"
  #     - "9091:9091"
  #   depends_on:
  #     - etcd
  #     - minio
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:9091/api/v1/health"]
  #     interval: 60s
  #     timeout: 15s
  #     retries: 5
  #     start_period: 120s
  #   security_opt:
  #     - apparmor=unconfined
  #     - seccomp=unconfined
  #   # 内存限制
  #   mem_limit: 1g
  #   memswap_limit: 1g
  #   networks:
  #     - wenshu-network

  # attu:
  #   image: lcr.loongnix.cn/library/attu:2.5.6
  #   container_name: wenshu-attu
  #   restart: always
  #   environment:
  #     MILVUS_URL: milvus:19530
  #     NODE_ENV: production
  #   ports:
  #     - "3000:3000"
  #   depends_on:
  #     - milvus
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:3000"]
  #     interval: 60s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 30s
  #   security_opt:
  #     - apparmor=unconfined
  #     - seccomp=unconfined
  #   # 内存限制
  #   mem_limit: 128m
  #   memswap_limit: 128m
  #   networks:
  #     - wenshu-network

  nginx:
    image: lcr.loongnix.cn/library/nginx:1.24.0
    container_name: wenshu-nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/html:/usr/local/nginx-1.24.0/html
      - ./nginx/logs:/usr/nginx-1.24.0/logs
      - ./nginx/ssl:/etc/nginx/ssl
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 30s
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined
    # 内存限制
    mem_limit: 64m
    memswap_limit: 64m
    networks:
      - wenshu-network

  gateway:
    build:
      context: .
      dockerfile: ruoyi-gateway/Dockerfile
      args:
        JAR_FILE: ruoyi-gateway/target/ruoyi-gateway-*.jar
        SERVER_PORT: 8080
        SERVICE_NAME: gateway
    container_name: wenshu-gateway
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: ************:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: ************:8848
      JAVA_OPTS: "-Xmx768m -Xms384m -Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    ports:
      - "8080:8080"
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined
    networks:
      - wenshu-network

  auth:
    build:
      context: .
      dockerfile: ruoyi-auth/Dockerfile
      args:
        JAR_FILE: ruoyi-auth/target/ruoyi-auth-*.jar
        SERVER_PORT: 9200
        SERVICE_NAME: auth
    container_name: wenshu-auth
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: ************:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: ************:8848
      SPRING_DATASOURCE_URL: ***********************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: **********
      SPRING_DATA_REDIS_HOST: ************
      SPRING_DATA_REDIS_PORT: 6379
      JAVA_OPTS: "-Xmx768m -Xms384m -Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    volumes:
      - ./logs/ruoyi-auth:/app/logs
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined
    networks:
      - wenshu-network

  system:
    build:
      context: .
      dockerfile: ruoyi-modules/ruoyi-system/Dockerfile
      args:
        JAR_FILE: ruoyi-modules/ruoyi-system/target/ruoyi-system-*.jar
        SERVER_PORT: 9201
        SERVICE_NAME: system
    container_name: wenshu-system
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: ************:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: ************:8848
      SPRING_DATASOURCE_URL: ***********************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: **********
      SPRING_DATA_REDIS_HOST: ************
      SPRING_DATA_REDIS_PORT: 6379
      JAVA_OPTS: "-Xmx512m -Xms256m -Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    volumes:
      - ./logs/ruoyi-system:/app/logs
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined
    networks:
      - wenshu-network

  wenshu-wchat:
    build:
      context: .
      dockerfile: wenshu-data/wenshu-wchat/Dockerfile
    container_name: wenshu-wchat
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: ************:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: ************:8848
      SPRING_DATASOURCE_URL: ***********************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: **********
      JAVA_OPTS: "-Xmx256m -Xms128m -Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
    volumes:
      - ./logs/wenshu-wchat:/app/logs
    ports:
      - "1019:1019"
    security_opt:
      - apparmor=unconfined
      - seccomp=unconfined
    networks:
      - wenshu-network

volumes:
  mysql_data:
  redis_data:
  nacos_data:
  milvus_data:
  etcd_data:
  minio_data: 

networks:
  wenshu-network:
    driver: bridge 