package com.ruoyi.wenshulivechat.controller;

import com.ruoyi.wenshulivechat.model.NewMessageRequest;
import com.ruoyi.wenshulivechat.model.NewMessageResponse;
import com.ruoyi.wenshulivechat.service.LiveChatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket消息控制器
 * 处理实时聊天相关的WebSocket消息
 * 
 * 统一接口路径前缀：/wenshu/livechat
 * WebSocket消息路由：
 * - 客户端发送：/send/{userId}/query-new-messages
 * - 服务端响应：/recv/{userId}/new-messages
 */
@Controller
public class LiveChatController {

    @Autowired
    private LiveChatService liveChatService;

    /**
     * 处理用户查询新消息请求
     *
     * 客户端发送路径：/send/{userId}/query-new-messages
     * 服务端响应路径：/recv/{userId}/new-messages
     *
     * @param userId 用户ID（从路径中获取）
     */
    @MessageMapping("/{userId}/query-new-messages")
    @SendTo("/recv/{userId}/new-messages")
    public NewMessageResponse queryNewMessages(@DestinationVariable("userId") Long userId) {
        try {
            System.out.println("收到用户" + userId + "的新消息查询请求");

            // 创建查询请求，直接使用路径中的用户ID
            NewMessageRequest request = new NewMessageRequest();
            request.setUserId(userId);
            request.setUnreadOnly(true);  // 默认只查询未读消息
            request.setLimit(50);         // 默认限制50条

            // 查询新消息
            NewMessageResponse response = liveChatService.queryNewMessages(request);

            System.out.println("已向用户" + userId + "发送新消息查询结果，有新消息: " + response.getHasNewMessages() +
                             ", 未读数量: " + response.getUnreadCount());

            return response;

        } catch (Exception e) {
            System.out.println("处理用户" + userId + "新消息查询请求异常: " + e.getMessage());
            e.printStackTrace();

            // 返回错误响应
            return NewMessageResponse.error("处理请求异常: " + e.getMessage(), userId, "notification");
        }
    }

    /**
     * 处理用户心跳请求
     *
     * 客户端发送路径：/send/{userId}/heartbeat
     * 服务端响应路径：/recv/{userId}/heartbeat
     *
     * @param userId 用户ID（从路径中获取）
     */
    @MessageMapping("/{userId}/heartbeat")
    @SendTo("/recv/{userId}/heartbeat")
    public Map<String, Object> handleHeartbeat(@DestinationVariable("userId") Long userId) {
        try {
            System.out.println("收到用户" + userId + "的心跳请求");

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "心跳响应");
            response.put("timestamp", System.currentTimeMillis());
            response.put("userId", userId);

            System.out.println("已向用户" + userId + "发送心跳响应");

            return response;

        } catch (Exception e) {
            System.out.println("处理用户" + userId + "心跳请求异常: " + e.getMessage());
            e.printStackTrace();

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "心跳处理异常: " + e.getMessage());
            errorResponse.put("timestamp", System.currentTimeMillis());
            errorResponse.put("userId", userId);

            return errorResponse;
        }
    }

    /**
     * 处理用户订阅所有未读消息请求
     *
     * 客户端发送路径：/send/{userId}/subscribe-all-unread
     * 服务端响应路径：/recv/{userId}/all-unread-messages
     *
     * @param userId 用户ID（从路径中获取）
     */
    @MessageMapping("/{userId}/subscribe-all-unread")
    @SendTo("/recv/{userId}/all-unread-messages")
    public NewMessageResponse subscribeAllUnreadMessages(@DestinationVariable("userId") Long userId) {
        try {
            System.out.println("收到用户" + userId + "的订阅所有未读消息请求");

            // 订阅所有未读消息
            NewMessageResponse response = liveChatService.subscribeAllUnreadMessages(userId);

            System.out.println("已向用户" + userId + "发送所有未读消息，消息数量: " +
                             (response.getNewMessages() != null ? response.getNewMessages().size() : 0) +
                             ", 总未读数量: " + response.getUnreadCount());

            return response;

        } catch (Exception e) {
            System.out.println("处理用户" + userId + "订阅所有未读消息请求异常: " + e.getMessage());
            e.printStackTrace();

            // 返回错误响应
            return NewMessageResponse.error("处理订阅请求异常: " + e.getMessage(), userId, "subscription");
        }
    }
}
