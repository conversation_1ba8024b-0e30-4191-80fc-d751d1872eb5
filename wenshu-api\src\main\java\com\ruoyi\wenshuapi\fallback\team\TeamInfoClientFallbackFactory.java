package com.ruoyi.wenshuapi.fallback.team;

import com.ruoyi.wenshuapi.client.team.TeamInfoClient;
import com.ruoyi.wenshuapi.pojo.team.TeamInfo;
import com.ruoyi.wenshuapi.util.file.ApiResponse;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * TeamInfoClient 的降级处理工厂
 *
 * <p><strong>功能说明：</strong>
 * 当团队服务(wenshu-team-service)不可用或调用超时时，触发熔断降级机制，
 * 避免服务雪崩，同时提供可读性强的错误提示和详细的日志记录。</p>
 *
 * <p><strong>降级策略：</strong>
 * 1. 返回友好的错误响应（HTTP 503状态）
 * 2. 记录完整的异常堆栈信息
 * 3. 空操作保护（如删除/更新操作不执行实际动作）
 * 4. 查询操作返回空数据集
 * </p>
 */
@Slf4j
@Component
public class TeamInfoClientFallbackFactory implements FallbackFactory<TeamInfoClient> {

    @Override
    public TeamInfoClient create(Throwable cause) {
        // 记录原始异常信息（包含服务名和异常详情）
        log.error("团队服务调用失败，触发降级处理。服务名称: wenshu-team-service, 异常信息: ", cause);

        return new TeamInfoClient() {
            // 统一错误消息模板（避免硬编码）
            private final String SERVICE_UNAVAILABLE_MSG = "团队服务暂不可用，请稍后重试。触发原因: "
                    + cause.getClass().getSimpleName();

            @Override
            public ApiResponse<Integer> createTeam(TeamInfo teamInfo) {
                /*
                 * 创建团队降级逻辑：
                 * - 返回错误码503（服务不可用）
                 * - 记录团队创建失败的关键信息
                 * - 返回-1作为无效团队ID
                 */
                log.warn("团队创建降级触发 >> 团队名称: {}, 负责人: {}",
                        teamInfo.getTeamName(), teamInfo.getLeaderId());
                return ApiResponse.failed(SERVICE_UNAVAILABLE_MSG, 503);
            }

            @Override
            public ApiResponse<Void> deleteTeam(Integer teamId) {
                /*
                 * 删除操作降级逻辑：
                 * - 空操作保护（不执行实际删除）
                 * - 记录尝试删除的团队ID
                 * - 返回操作中止提示
                 */
                log.warn("团队删除降级触发 >> 尝试删除团队ID: {}", teamId);
                return ApiResponse.failed("删除操作已中止。" + SERVICE_UNAVAILABLE_MSG, 503);
            }

            @Override
            public ApiResponse<Void> updateTeam(TeamInfo teamInfo) {
                /*
                 * 更新操作降级逻辑：
                 * - 空操作保护
                 * - 记录关键更新字段
                 */
                if (teamInfo != null) {
                    log.warn("团队更新降级触发 >> 团队ID: {}, 新状态: {}",
                            teamInfo.getTeamId(), teamInfo.getStatus());
                }
                return ApiResponse.failed("更新操作已回滚。" + SERVICE_UNAVAILABLE_MSG, 503);
            }

            @Override
            public ApiResponse<TeamInfo> getTeamById(Integer teamId) {
                /*
                 * 单体查询降级逻辑：
                 * - 返回空数据对象（非null）
                 * - 添加服务不可用提示
                 * - 记录缺失的团队ID
                 */
                log.warn("团队详情查询降级触发 >> 请求团队ID: {}", teamId);
                return ApiResponse.failed("无法获取团队详情，" + SERVICE_UNAVAILABLE_MSG, 503);
            }

            @Override
            public ApiResponse<List<TeamInfo>> getAllTeams() {
                /*
                 * 全量查询降级逻辑：
                 * - 返回空集合（避免NPE）
                 * - 添加降级标记到消息
                 */
                log.warn("全量团队查询降级触发");
                return ApiResponse.success(new ArrayList<>(), "（降级数据）" + SERVICE_UNAVAILABLE_MSG);
            }

            @Override
            public ApiResponse<List<TeamInfo>> getTeamsByStatus(Byte status) {
                /*
                 * 状态查询降级逻辑：
                 * - 返回空集合
                 * - 记录查询状态值
                 */
                log.warn("团队状态查询降级触发 >> 状态码: {}", status);
                return ApiResponse.success(new ArrayList<>(), "（降级数据）" + SERVICE_UNAVAILABLE_MSG);
            }

            @Override
            public ApiResponse<List<TeamInfo>> searchTeamsByName(String name) {
                /*
                 * 搜索操作降级逻辑：
                 * - 返回空集合
                 * - 记录搜索关键词
                 */
                log.warn("团队搜索降级触发 >> 关键词: {}", name);
                return ApiResponse.success(new ArrayList<>(), "（降级数据）" + SERVICE_UNAVAILABLE_MSG);
            }
        };
    }
}