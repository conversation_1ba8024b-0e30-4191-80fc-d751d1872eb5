# Spring
spring:
  application:
    # 应用名称
    name: programme-manage
  main:
    # 允许同名Bean覆盖
    allow-bean-definition-overriding: true
  profiles:
    # 环境配置
    active: dev
  cloud:
    openfeign:
      client:
        config:
          default:
            connectTimeout: 10000
            readTimeout: 10000
            loggerLevel: FULL
      okhttp:
        enabled: true
      httpclient:
        enabled: false
      circuitbreaker:
        enabled: true
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 8.155.31.217:8848
      config:
        # 配置中心地址
        server-addr: 8.155.31.217:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
server:
  port: 8605
# Feign配置
feign:
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
      wenshu-calebdar:
        connectTimeout: 30000
        readTimeout: 30000
  compression:
    request:
      enabled: true
    response:
      enabled: true
  circuitbreaker:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false

# 日志配置
logging:
  level:
    root: INFO
    com.ruoyi.programmemanage: DEBUG
    org.springframework.cloud.openfeign: DEBUG
    com.ruoyi.programmemanage.client.ProgrammeFeignClient: DEBUG