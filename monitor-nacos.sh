#!/bin/bash

# Nacos监控脚本
echo "=== Nacos实时监控 ==="
echo "按 Ctrl+C 退出监控"
echo ""

# 监控函数
monitor_nacos() {
    while true; do
        clear
        echo "=== Nacos实时监控 $(date) ==="
        echo ""
        
        # 1. 容器状态
        echo "📦 容器状态:"
        docker ps --filter "name=wenshu-nacos" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || echo "   Nacos容器未运行"
        echo ""
        
        # 2. 资源使用情况
        echo "💾 资源使用:"
        if docker ps --filter "name=wenshu-nacos" --format "{{.Names}}" | grep -q "wenshu-nacos"; then
            docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" wenshu-nacos 2>/dev/null || echo "   无法获取资源信息"
        else
            echo "   Nacos容器未运行"
        fi
        echo ""
        
        # 3. 健康检查
        echo "🏥 健康状态:"
        if curl -f http://localhost:8848/nacos/actuator/health > /dev/null 2>&1; then
            echo "   ✅ Nacos健康检查: 正常"
        else
            echo "   ❌ Nacos健康检查: 异常"
        fi
        
        # 4. 服务注册情况
        echo ""
        echo "🔗 服务注册状态:"
        
        # 检查各个服务的健康状态
        services=("auth:9200" "system:9201" "gateway:8080")
        for service in "${services[@]}"; do
            name=$(echo $service | cut -d: -f1)
            port=$(echo $service | cut -d: -f2)
            
            if curl -f http://localhost:$port/actuator/health > /dev/null 2>&1; then
                echo "   ✅ $name: 正常"
            else
                echo "   ❌ $name: 异常"
            fi
        done
        
        # 5. 最近的日志（最后10行）
        echo ""
        echo "📋 最近日志 (最后5行):"
        if docker ps --filter "name=wenshu-nacos" --format "{{.Names}}" | grep -q "wenshu-nacos"; then
            docker logs --tail 5 wenshu-nacos 2>/dev/null | sed 's/^/   /' || echo "   无法获取日志"
        else
            echo "   Nacos容器未运行"
        fi
        
        # 6. 系统资源
        echo ""
        echo "🖥️  系统资源:"
        free -h | grep -E "Mem|Swap" | sed 's/^/   /'
        
        echo ""
        echo "刷新间隔: 5秒 | 按 Ctrl+C 退出"
        echo "=================================="
        
        sleep 5
    done
}

# 捕获Ctrl+C信号
trap 'echo -e "\n\n👋 监控已停止"; exit 0' INT

# 开始监控
monitor_nacos
