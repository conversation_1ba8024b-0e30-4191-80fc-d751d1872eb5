//package com.ruoyi.wenshuwchat.controller;
//
//import com.ruoyi.wenshuwchat.service.ChatFileService;
//import com.ruoyi.wenshuwchat.service.ChatRecordService;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.http.MediaType;
//import org.springframework.mock.web.MockMultipartFile;
//import org.springframework.test.web.servlet.MockMvc;
//import org.springframework.test.web.servlet.setup.MockMvcBuilders;
//
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.ArgumentMatchers.anyString;
//import static org.mockito.Mockito.when;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
//import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
//
///**
// * 聊天记录控制器测试类
// */
//@ExtendWith(MockitoExtension.class)
//class ChatRecordControllerTest {
//
//    @Mock
//    private ChatRecordService chatRecordService;
//
//    @Mock
//    private ChatFileService chatFileService;
//
//    private MockMvc mockMvc;
//    private ChatRecordController chatRecordController;
//
//    @BeforeEach
//    void setUp() {
//        chatRecordController = new ChatRecordController(chatRecordService, chatFileService);
//        mockMvc = MockMvcBuilders.standaloneSetup(chatRecordController).build();
//    }
//
//    @Test
//    void testSendTextMessage() throws Exception {
//        // 模拟服务返回
//        when(chatRecordService.sendMessage(any())).thenReturn(123L);
//
//        mockMvc.perform(multipart("/wenshu/wchat/chat/send")
//                .param("senderId", "1001")
//                .param("receiverId", "1002")
//                .param("contentType", "text")
//                .param("textContent", "Hello, World!")
//                .contentType(MediaType.MULTIPART_FORM_DATA))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(true))
//                .andExpect(jsonPath("$.message").value("文本消息发送成功"))
//                .andExpect(jsonPath("$.data.messageId").value(123))
//                .andExpect(jsonPath("$.data.contentType").value("text"))
//                .andExpect(jsonPath("$.data.content").value("Hello, World!"));
//    }
//
//    @Test
//    void testSendImageMessage() throws Exception {
//        // 创建模拟文件
//        MockMultipartFile file = new MockMultipartFile(
//                "file", "test.jpg", "image/jpeg", "test image content".getBytes());
//
//        // 模拟服务返回
//        when(chatFileService.isFileTypeAllowed(any(), anyString())).thenReturn(true);
//        when(chatFileService.uploadChatFile(any(), anyString())).thenReturn("image/2024/01/15/test.jpg");
//        when(chatFileService.getFileAccessUrl(anyString())).thenReturn("http://localhost:8083/chat-files/image/2024/01/15/test.jpg");
//        when(chatRecordService.sendMessage(any())).thenReturn(124L);
//
//        mockMvc.perform(multipart("/wenshu/wchat/chat/send")
//                .file(file)
//                .param("senderId", "1001")
//                .param("receiverId", "1002")
//                .param("contentType", "image")
//                .contentType(MediaType.MULTIPART_FORM_DATA))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(true))
//                .andExpect(jsonPath("$.message").value("image消息发送成功"))
//                .andExpect(jsonPath("$.data.messageId").value(124))
//                .andExpect(jsonPath("$.data.contentType").value("image"))
//                .andExpect(jsonPath("$.data.filePath").value("image/2024/01/15/test.jpg"))
//                .andExpect(jsonPath("$.data.fileName").value("test.jpg"));
//    }
//
//    @Test
//    void testSendTextMessageWithMissingContent() throws Exception {
//        mockMvc.perform(multipart("/wenshu/wchat/chat/send")
//                .param("senderId", "1001")
//                .param("receiverId", "1002")
//                .param("contentType", "text")
//                .contentType(MediaType.MULTIPART_FORM_DATA))
//                .andExpect(status().isBadRequest())
//                .andExpect(jsonPath("$.success").value(false))
//                .andExpect(jsonPath("$.message").value("文本内容不能为空"));
//    }
//
//    @Test
//    void testSendImageMessageWithMissingFile() throws Exception {
//        mockMvc.perform(multipart("/wenshu/wchat/chat/send")
//                .param("senderId", "1001")
//                .param("receiverId", "1002")
//                .param("contentType", "image")
//                .contentType(MediaType.MULTIPART_FORM_DATA))
//                .andExpect(status().isBadRequest())
//                .andExpect(jsonPath("$.success").value(false))
//                .andExpect(jsonPath("$.message").value("文件不能为空"));
//    }
//
//    @Test
//    void testSendMessageWithInvalidContentType() throws Exception {
//        mockMvc.perform(multipart("/wenshu/wchat/chat/send")
//                .param("senderId", "1001")
//                .param("receiverId", "1002")
//                .param("contentType", "invalid")
//                .param("textContent", "Hello")
//                .contentType(MediaType.MULTIPART_FORM_DATA))
//                .andExpect(status().isBadRequest())
//                .andExpect(jsonPath("$.success").value(false))
//                .andExpect(jsonPath("$.message").value("不支持的内容类型，仅支持: text, image, video, file"));
//    }
//
//    @Test
//    void testSendTextMessageUsingSpecificEndpoint() throws Exception {
//        when(chatRecordService.sendMessage(any())).thenReturn(125L);
//
//        mockMvc.perform(multipart("/wenshu/wchat/chat/send/text")
//                .param("senderId", "1001")
//                .param("receiverId", "1002")
//                .param("content", "Hello from specific endpoint!")
//                .contentType(MediaType.MULTIPART_FORM_DATA))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(true))
//                .andExpect(jsonPath("$.message").value("文本消息发送成功"))
//                .andExpect(jsonPath("$.data.messageId").value(125))
//                .andExpect(jsonPath("$.data.contentType").value("text"))
//                .andExpect(jsonPath("$.data.content").value("Hello from specific endpoint!"));
//    }
//
//    @Test
//    void testSendImageMessageUsingSpecificEndpoint() throws Exception {
//        MockMultipartFile file = new MockMultipartFile(
//                "file", "test2.png", "image/png", "test image content 2".getBytes());
//
//        when(chatFileService.isFileTypeAllowed(any(), anyString())).thenReturn(true);
//        when(chatFileService.uploadChatFile(any(), anyString())).thenReturn("image/2024/01/15/test2.png");
//        when(chatFileService.getFileAccessUrl(anyString())).thenReturn("http://localhost:8083/chat-files/image/2024/01/15/test2.png");
//        when(chatRecordService.sendMessage(any())).thenReturn(126L);
//
//        mockMvc.perform(multipart("/wenshu/wchat/chat/send/image")
//                .file(file)
//                .param("senderId", "1001")
//                .param("receiverId", "1002")
//                .contentType(MediaType.MULTIPART_FORM_DATA))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.success").value(true))
//                .andExpect(jsonPath("$.message").value("image消息发送成功"))
//                .andExpect(jsonPath("$.data.messageId").value(126))
//                .andExpect(jsonPath("$.data.contentType").value("image"))
//                .andExpect(jsonPath("$.data.filePath").value("image/2024/01/15/test2.png"))
//                .andExpect(jsonPath("$.data.fileName").value("test2.png"));
//    }
//}
