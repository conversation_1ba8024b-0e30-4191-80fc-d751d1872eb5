package com.ruoyi.wenshuapi.fallback.file;

import com.ruoyi.wenshuapi.client.file.FileStorageClient;
import com.ruoyi.wenshuapi.util.file.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * FileStorageClient的降级处理工厂
 * 当文件存储服务不可用或调用失败时提供容错响应
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-28
 */
@Component
public class FileStorageClientFallback implements FallbackFactory<FileStorageClient> {

    private static final Logger log = LoggerFactory.getLogger(FileStorageClientFallback.class);

    @Override
    public FileStorageClient create(Throwable cause) {
        return new FileStorageClient() {

            // 统一错误码：503表示服务不可用
            private static final int SERVICE_UNAVAILABLE_CODE = 503;

            // 通用错误信息前缀
            private static final String ERROR_PREFIX = "文件存储服务不可用，请稍后重试。原因：";

            @Override
            public ApiResponse<Map<String, Object>> uploadFile(MultipartFile file, Integer uploaderId,
                                                               String ownerType, String fileStatus) {
                logError("文件上传", file != null ? file.getOriginalFilename() : "null", cause);
                return ApiResponse.failed(
                        buildErrorMessage("文件上传", file != null ? file.getOriginalFilename() : "未知文件"),
                        SERVICE_UNAVAILABLE_CODE
                );
            }

            @Override
            public ApiResponse<Map<String, Object>> batchUploadFiles(MultipartFile[] files, Integer uploaderId,
                                                                     String ownerType, String fileStatus) {
                int fileCount = files != null ? files.length : 0;
                logError("批量文件上传", fileCount + "个文件", cause);

                // 返回降级的批量上传结果
                Map<String, Object> fallbackResult = new HashMap<>();
                fallbackResult.put("totalCount", fileCount);
                fallbackResult.put("successCount", 0);
                fallbackResult.put("failedCount", fileCount);
                fallbackResult.put("uploadedFiles", new HashMap<>());

                return ApiResponse.failed(
                        buildErrorMessage("批量文件上传", fileCount + "个文件"),
                        SERVICE_UNAVAILABLE_CODE,
                        fallbackResult
                );
            }

            @Override
            public ApiResponse<String> getDownloadUrl(int fileId) {
                logError("获取下载链接", "文件ID=" + fileId, cause);
                return ApiResponse.failed(
                        buildErrorMessage("获取下载链接", "文件ID=" + fileId),
                        SERVICE_UNAVAILABLE_CODE
                );
            }

            @Override
            public ApiResponse<Boolean> fileExists(int fileId) {
                logError("检查文件存在性", "文件ID=" + fileId, cause);
                // 返回false确保安全（假设文件不存在）
                return ApiResponse.success(false, buildWarningMessage("文件存在性检查"));
            }

            @Override
            public ApiResponse<String> getFileAccessUrl(int fileId) {
                logError("获取文件访问URL", "文件ID=" + fileId, cause);
                return ApiResponse.failed(
                        buildErrorMessage("获取文件访问URL", "文件ID=" + fileId),
                        SERVICE_UNAVAILABLE_CODE
                );
            }

            @Override
            public ApiResponse<Map<String, Object>> validateFileIntegrity(int fileId) {
                logError("验证文件完整性", "文件ID=" + fileId, cause);

                // 返回降级的验证结果
                Map<String, Object> fallbackResult = new HashMap<>();
                fallbackResult.put("fileId", fileId);
                fallbackResult.put("isValid", false);
                fallbackResult.put("message", "服务不可用，无法验证");

                return ApiResponse.success(fallbackResult, buildWarningMessage("文件完整性验证"));
            }

            @Override
            public ApiResponse<Map<String, Object>> getStorageStats(Integer uploaderId) {
                logError("获取存储统计", "上传者ID=" + uploaderId, cause);

                // 返回降级的统计结果
                Map<String, Object> fallbackStats = new HashMap<>();
                fallbackStats.put("totalFiles", 0);
                fallbackStats.put("totalSize", 0L);
                fallbackStats.put("usedSpace", "0 B");
                fallbackStats.put("fileTypes", new HashMap<>());

                return ApiResponse.success(fallbackStats, buildWarningMessage("存储统计"));
            }

            @Override
            public ApiResponse<String> cleanupTempFiles() {
                logError("清理临时文件", "系统清理", cause);
                return ApiResponse.failed(
                        buildErrorMessage("清理临时文件", "系统清理"),
                        SERVICE_UNAVAILABLE_CODE
                );
            }

            // 构建完整的错误信息
            private String buildErrorMessage(String action, String target) {
                return String.format("%s失败[%s]。%s%s",
                        action,
                        target,
                        ERROR_PREFIX,
                        cause != null ? cause.getMessage() : "未知错误"
                );
            }

            // 构建警告信息（用于返回成功但实际降级的场景）
            private String buildWarningMessage(String action) {
                return String.format("警告：%s操作已降级返回默认值（服务不可用：%s）",
                        action,
                        cause != null ? cause.getMessage() : "服务超时"
                );
            }

            // 统一错误日志记录
            private void logError(String action, Object target, Throwable ex) {
                log.error("[文件存储服务] {} 操作失败 - 目标: {} | 原因: {} | 错误堆栈: ",
                        action,
                        target,
                        ex != null ? ex.getMessage() : "未知",
                        ex
                );
            }
        };
    }
}
