//package com.ruoyi.wenshuvoice.controller;
//
//import com.ruoyi.wenshuapi.client.file.FileInfoClient;
//import com.ruoyi.wenshuapi.pojo.file.FileInfoPojo;
//import com.ruoyi.wenshuapi.util.file.ApiResponse;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//import java.util.Map;
//
///**
// * 文件服务代理控制器
// * 通过Feign客户端调用远程文件服务
// * 顶层路径：/client/fileinfo
// *
// * 注意：此Controller作为网关层，将请求转发到实际的文件服务
// */
//@RestController
//@RequestMapping("/client/fileinfo")
//public class FileInfoProxyController {
//
//    private final FileInfoClient fileInfoClient;
//
//    @Autowired
//    public FileInfoProxyController(FileInfoClient fileInfoClient) {
//        this.fileInfoClient = fileInfoClient;
//    }
//
//    /**
//     * 代理添加文件请求
//     * @param fileInfo 文件信息
//     * @return 远程操作结果
//     */
//    @PostMapping("/add")
//    public ApiResponse<Integer> addFile(@RequestBody FileInfoPojo fileInfo) {
//        return fileInfoClient.addFile(fileInfo);
//    }
//
//    /**
//     * 代理删除文件请求
//     * @param fileId 文件ID
//     * @return 远程操作结果
//     */
//    @DeleteMapping("/delete/{fileId}")
//    public ApiResponse<Integer> deleteFile(@PathVariable("fileId") int fileId) {
//        return fileInfoClient.deleteFile(fileId);
//    }
//
//    /**
//     * 代理更新文件请求
//     * @param fileInfo 文件信息
//     * @return 远程操作结果
//     */
//    @PutMapping("/update")
//    public ApiResponse<Integer> updateFile(@RequestBody FileInfoPojo fileInfo) {
//        return fileInfoClient.updateFile(fileInfo);
//    }
//
//    /**
//     * 代理获取文件详情
//     * @param fileId 文件ID
//     * @return 文件详情
//     */
//    @GetMapping("/get/{fileId}")
//    public ApiResponse<FileInfoPojo> getFileById(@PathVariable("fileId") int fileId) {
//        return fileInfoClient.getFileById(fileId);
//    }
//
//    /**
//     * 代理条件查询文件
//     * @param condition 查询条件
//     * @return 文件列表
//     */
//    @GetMapping("/list")
//    public ApiResponse<List<FileInfoPojo>> getFilesByCondition(FileInfoPojo condition) {
//        return fileInfoClient.getFilesByCondition(condition);
//    }
//
//    /**
//     * 代理路径查询文件
//     * @param filePath 文件路径
//     * @return 文件信息
//     */
//    @GetMapping("/byPath")
//    public ApiResponse<FileInfoPojo> getFileByPath(@RequestParam("filePath") String filePath) {
//        return fileInfoClient.getFileByPath(filePath);
//    }
//
//    /**
//     * 代理更新文件状态
//     * @param fileId 文件ID
//     * @param status 新状态
//     * @return 操作结果
//     */
//    @PutMapping("/updateStatus")
//    public ApiResponse<Integer> changeFileStatus(
//            @RequestParam("fileId") int fileId,
//            @RequestParam("status") String status) {
//        return fileInfoClient.changeFileStatus(fileId, status);
//    }
//
//    /**
//     * 代理统计用户文件
//     * @param uploaderId 上传者ID
//     * @return 文件数量
//     */
//    @GetMapping("/countByUser")
//    public ApiResponse<Integer> countFilesByUploader(@RequestParam("uploaderId") int uploaderId) {
//        return fileInfoClient.countFilesByUploader(uploaderId);
//    }
//
//    /**
//     * 代理批量更新状态
//     * @param fileIds 文件ID列表
//     * @param status 新状态
//     * @return 操作结果
//     */
//    @PutMapping("/batchUpdateStatus")
//    public ApiResponse<Integer> batchUpdateStatus(
//            @RequestParam("fileIds") List<Integer> fileIds,
//            @RequestParam("status") String status) {
//        return fileInfoClient.batchUpdateStatus(fileIds, status);
//    }
//
//    /**
//     * 代理验证文件归属
//     * @param fileId 文件ID
//     * @param userId 用户ID
//     * @return 验证结果
//     */
//    @GetMapping("/isOwned")
//    public ApiResponse<Boolean> isFileOwnedByUser(
//            @RequestParam("fileId") int fileId,
//            @RequestParam("userId") int userId) {
//        return fileInfoClient.isFileOwnedByUser(fileId, userId);
//    }
//
//    /**
//     * 代理批量删除文件
//     * 同时删除数据库记录和服务器上的物理文件
//     * @param fileIds 文件ID列表
//     * @return 删除结果，包含详细的删除统计信息
//     */
//    @DeleteMapping("/batchDelete")
//    public ApiResponse<Map<String, Object>> batchDeleteFiles(@RequestParam("fileIds") List<Integer> fileIds) {
//        return fileInfoClient.batchDeleteFiles(fileIds);
//    }
//}