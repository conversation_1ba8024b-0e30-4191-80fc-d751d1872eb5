// 包声明：定义当前类所属的包路径，属于配置包
package com.ruoyi.wenshumeeting.config;

// 导入Spring Bean注解，用于声明Spring管理的Bean
import org.springframework.context.annotation.Bean;
// 导入Spring配置注解，标识这是一个配置类
import org.springframework.context.annotation.Configuration;
// 导入WebSocket服务端点导出器，用于自动注册WebSocket端点
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

/**
 * WebSocket配置类
 * 用于配置WebSocket相关的Bean和设置
 * @Configuration 标识这是一个Spring配置类，会被Spring容器扫描并加载
 */
@Configuration
public class WebSocketConfig {

    /**
     * 配置WebSocket服务端点导出器Bean
     * ServerEndpointExporter用于自动检测和注册使用@ServerEndpoint注解的WebSocket端点
     * 在Spring Boot内嵌容器环境下，需要显式声明此Bean来启用WebSocket功能
     *
     * @return ServerEndpointExporter实例，用于导出WebSocket服务端点
     */
    @Bean
    public ServerEndpointExporter serverEndpointExporter(){
        // 创建并返回ServerEndpointExporter实例
        // 该实例会自动扫描项目中所有标注了@ServerEndpoint注解的类
        // 并将它们注册为WebSocket端点，使客户端可以连接到这些端点
        return new ServerEndpointExporter();
    }
}
