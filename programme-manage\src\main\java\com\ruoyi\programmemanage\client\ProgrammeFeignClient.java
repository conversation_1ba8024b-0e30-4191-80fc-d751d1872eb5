package com.ruoyi.programmemanage.client;

import com.ruoyi.programmemanage.common.ApiResponse;
import com.ruoyi.programmemanage.common.CalendarParticipant;
import com.ruoyi.programmemanage.common.Programme;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 日程服务Feign客户端接口
 * 用于调用wenshu-calebdar微服务
 */
@FeignClient(name = "wenshu-calebdar", contextId = "programmeManageFeignClient", path = "/programme")
public interface ProgrammeFeignClient {

    /**
     * 添加日程
     */
    @PostMapping("/add/{userId}")
    ApiResponse<?> addProgramme(@PathVariable("userId") Long userId, @RequestBody Programme programme);

    /**
     * 删除日程
     */
    @DeleteMapping("/delete/{event_id}")
    ApiResponse<?> deleteProgramme(@PathVariable("event_id") int event_id);

    /**
     * 根据时间范围查询日程
     */
    @GetMapping("/getByTime")
    ApiResponse<?> listProgramme(
            @RequestParam(required = false, value = "userId") List<Integer> eventIds,
            @RequestParam(required = false, value = "startTime") String startTime,
            @RequestParam(required = false, value = "endTime") String endTime);

    /**
     * 根据ID查询日程
     */
    @GetMapping("/getById/{event_id}")
    ApiResponse<?> getById(@PathVariable("event_id") int event_id);

    /**
     * 更新日程
     */
    @PutMapping("/update/{event_id}")
    ApiResponse<?> updateProgramme(@PathVariable("event_id") int event_id, @RequestBody Programme programme);

    /**
     * 获取用户所有日程
     */
    @GetMapping("/getAll")
    ApiResponse<?> getAll(@RequestParam(required = false, value = "userId") int userId);
    
    /**
     * 获取用户参与的日程ID列表
     */
    @GetMapping("/list")
    List<CalendarParticipant> list(@RequestParam(required = false, value = "userId") int userId);
    
    /**
     * 获取用户所有日程（返回原始Programme列表）
     */
    @GetMapping("/getAllProgrammes")
    List<Programme> ProgetAll(@RequestParam(required = false, value = "userId") Long userId);
} 