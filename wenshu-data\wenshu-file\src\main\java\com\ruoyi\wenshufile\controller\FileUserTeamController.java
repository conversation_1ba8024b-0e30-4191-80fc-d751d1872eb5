package com.ruoyi.wenshufile.controller;


import com.ruoyi.wenshuapi.pojo.file.FileUserTeamPojo;
import com.ruoyi.wenshuapi.util.file.ApiResponse;
import com.ruoyi.wenshufile.service.FileUserTeamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 文件-用户-团队关联关系控制器
 * 提供对关联关系的RESTful API管理
 *
 * 注意：数据库无外键约束，需在应用层维护数据一致性
 */
@RestController
@RequestMapping("/wenshu/file")  // 统一API前缀
public class FileUserTeamController {

    private final FileUserTeamService fileUserTeamService;

    /**
     * 构造函数注入Service依赖
     * @param fileUserTeamService 关联关系服务
     */
    @Autowired
    public FileUserTeamController(FileUserTeamService fileUserTeamService) {
        this.fileUserTeamService = fileUserTeamService;
    }

    /**
     * 创建新的关联关系
     * @param pojo 关联实体数据（JSON格式）
     * @return 创建成功的关联实体
     *
     * 注意：由于无外键约束，需确保关联的文件/用户/团队存在
     */
    @PostMapping
    public ApiResponse<FileUserTeamPojo> createAssociation(@RequestBody FileUserTeamPojo pojo) {
        fileUserTeamService.insert(pojo);
        return ApiResponse.success(pojo, "关联关系创建成功");
    }

    /**
     * 删除特定关联关系
     * @param fileId 文件ID
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 操作结果消息
     */
    @DeleteMapping("/{fileId}/{userId}/{teamId}")
    public ApiResponse<String> deleteAssociation(
            @PathVariable("fileId") int fileId,  // 显式指定名称
            @PathVariable("userId") int userId,  // 显式指定名称
            @PathVariable("teamId") int teamId) {
        int result = fileUserTeamService.deleteByPrimaryKey(fileId, userId, teamId);
        if (result > 0) {
            return ApiResponse.success("关联关系删除成功");
        } else {
            return ApiResponse.failed("未找到对应的关联关系", 404);
        }
    }

    /**
     * 删除文件相关的所有关联
     * @param fileId 文件ID
     * @return 操作结果消息
     *
     * 使用场景：删除文件前清理关联关系
     */
    @DeleteMapping("/file/{fileId}")
    public ApiResponse<String> deleteByFile(@PathVariable("fileId") int fileId) {
        int count = fileUserTeamService.deleteByFileId(fileId);
        return ApiResponse.success("成功删除 " + count + " 条文件关联");
    }

    /**
     * 删除用户相关的所有关联
     * @param userId 用户ID
     * @return 操作结果消息
     *
     * 使用场景：删除用户前清理关联关系
     */
    @DeleteMapping("/user/{userId}")
    public ApiResponse<String> deleteByUser(@PathVariable("userId") int userId) {
        int count = fileUserTeamService.deleteByUserId(userId);
        return ApiResponse.success("成功删除 " + count + " 条用户关联");
    }

    /**
     * 删除团队相关的所有关联
     * @param teamId 团队ID
     * @return 操作结果消息
     *
     * 使用场景：删除团队前清理关联关系
     */
    @DeleteMapping("/team/{teamId}")
    public ApiResponse<String> deleteByTeam(@PathVariable("teamId") int teamId) {
        int count = fileUserTeamService.deleteByTeamId(teamId);
        return ApiResponse.success("成功删除 " + count + " 条团队关联");
    }

    /**
     * 获取特定关联关系详情
     * @param fileId 文件ID
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 关联实体或错误消息
     */
    @GetMapping("/{fileId}/{userId}/{teamId}")
    public ApiResponse<FileUserTeamPojo> getAssociation(
            @PathVariable("fileId") int fileId,
            @PathVariable("userId") int userId,
            @PathVariable("teamId") int teamId) {
        FileUserTeamPojo pojo = fileUserTeamService.selectByPrimaryKey(fileId, userId, teamId);
        if (pojo != null) {
            return ApiResponse.success(pojo);
        } else {
            return ApiResponse.failed("未找到对应的关联关系", 404);
        }
    }

    /**
     * 获取文件相关的所有关联
     * @param fileId 文件ID
     * @return 关联列表
     */
    @GetMapping("/file/{fileId}")
    public ApiResponse<List<FileUserTeamPojo>> getByFile(@PathVariable("fileId") int fileId) {
        List<FileUserTeamPojo> list = fileUserTeamService.selectByFileId(fileId);
        return ApiResponse.success(list, "文件关联查询成功");
    }

    /**
     * 获取用户相关的所有关联
     * @param userId 用户ID
     * @return 关联列表
     */
    @GetMapping("/user/{userId}")
    public ApiResponse<List<FileUserTeamPojo>> getByUser(@PathVariable("userId") int userId) {
        List<FileUserTeamPojo> list = fileUserTeamService.selectByUserId(userId);
        return ApiResponse.success(list, "用户关联查询成功");
    }

    /**
     * 获取团队相关的所有关联
     * @param teamId 团队ID
     * @return 关联列表
     */
    @GetMapping("/team/{teamId}")
    public ApiResponse<List<FileUserTeamPojo>> getByTeam(@PathVariable("teamId") int teamId) {
        List<FileUserTeamPojo> list = fileUserTeamService.selectByTeamId(teamId);
        return ApiResponse.success(list, "团队关联查询成功");
    }

    /**
     * 更新关联关系
     * @param oldFileId 原文件ID
     * @param oldUserId 原用户ID
     * @param oldTeamId 原团队ID
     * @param newPojo 新关联数据（JSON格式）
     * @return 更新后的关联实体
     *
     * 注意：由于无外键约束，需确保新关联的文件/用户/团队存在
     */
    @PutMapping("/{oldFileId}/{oldUserId}/{oldTeamId}")
    public ApiResponse<FileUserTeamPojo> updateAssociation(
            @PathVariable("oldFileId") int oldFileId,
            @PathVariable("oldUserId") int oldUserId,
            @PathVariable("oldTeamId") int oldTeamId,
            @RequestBody FileUserTeamPojo newPojo) {
        int result = fileUserTeamService.updateByPrimaryKey(oldFileId, oldUserId, oldTeamId, newPojo);
        if (result > 0) {
            return ApiResponse.success(newPojo, "关联关系更新成功");
        } else {
            return ApiResponse.failed("关联关系更新失败，原始记录未找到", 404);
        }
    }
}