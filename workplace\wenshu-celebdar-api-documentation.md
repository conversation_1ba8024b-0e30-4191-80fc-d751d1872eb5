# Wenshu-Celebdar 智能日程管理模块 - 详细API接口文档

## 📋 模块概述

**服务名称**: wenshu-celebdar  
**服务端口**: 8703  
**版本**: v1.0.0  
**最后更新**: 2024-12-28  

**核心功能**: 智能日程管理服务，提供日程创建、查询、提醒、冲突检测、智能推荐等功能

**技术栈**:
- Spring Boot 3.x
- Spring Cloud 2023.x
- Spring Data JPA (数据持久化)
- Redis (缓存和提醒队列)
- Quartz (定时任务调度)
- WebSocket (实时提醒)
- iCal4j (日历标准支持)

---

## 🏗️ 服务架构

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                Wenshu-Celebdar 智能日程管理                  │
│                        (Port: 8703)                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│   日程管理     │ │智能提醒  │ │ 冲突检测   │
│ (Schedule)    │ │(Remind) │ │(Conflict) │
└───────────────┘ └────────┘ └───────────┘
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│ 日历同步       │ │定时任务  │ │ 智能推荐   │
│ (Calendar)    │ │(Quartz) │ │(AI Suggest)│
└───────────────┘ └────────┘ └───────────┘
```

### 服务依赖关系
- **wenshu-chat**: AI智能分析和推荐
- **wenshu-base**: 用户信息和权限管理
- **MySQL**: 日程数据存储
- **Redis**: 缓存和消息队列
- **WebSocket**: 实时提醒推送

---

## 🌐 REST API接口

### 基础信息
- **Base URL**: `http://localhost:8703`
- **Content-Type**: `application/json`
- **鉴权方式**: JWT Token (Header: `Authorization: Bearer <token>`)

---

## 📅 日程管理API

### 1. 日程基础操作

#### 1.1 创建日程
**接口路径**: `POST /api/schedule/create`

**功能描述**: 创建新的日程安排

**请求参数**:
```http
POST /api/schedule/create
Content-Type: application/json

{
  "title": "项目评审会议",
  "description": "讨论Q1项目进展和下阶段计划",
  "startTime": "2024-12-29T14:00:00",
  "endTime": "2024-12-29T16:00:00",
  "location": "会议室A",
  "attendees": ["1001", "1002", "1003"],
  "remindBefore": [15, 60],
  "priority": "high",
  "category": "meeting",
  "isRecurring": false,
  "tags": ["项目", "评审", "重要"]
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "日程创建成功",
  "data": {
    "scheduleId": "schedule_12345",
    "title": "项目评审会议",
    "description": "讨论Q1项目进展和下阶段计划",
    "startTime": "2024-12-29T14:00:00",
    "endTime": "2024-12-29T16:00:00",
    "location": "会议室A",
    "creator": {
      "userId": "1001",
      "userName": "张三"
    },
    "attendees": [
      {
        "userId": "1001",
        "userName": "张三",
        "status": "accepted"
      },
      {
        "userId": "1002", 
        "userName": "李四",
        "status": "pending"
      }
    ],
    "remindBefore": [15, 60],
    "priority": "high",
    "category": "meeting",
    "status": "confirmed",
    "createTime": "2024-12-28T15:30:00",
    "conflictCheck": {
      "hasConflict": false,
      "conflicts": []
    }
  },
  "timestamp": 1640995200000
}
```

#### 1.2 查询日程列表
**接口路径**: `GET /api/schedule/list`

**功能描述**: 查询用户的日程列表

**请求参数**:
```http
GET /api/schedule/list?userId=1001&startDate=2024-12-28&endDate=2024-12-31&category=meeting&status=confirmed
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "schedules": [
      {
        "scheduleId": "schedule_12345",
        "title": "项目评审会议",
        "startTime": "2024-12-29T14:00:00",
        "endTime": "2024-12-29T16:00:00",
        "location": "会议室A",
        "priority": "high",
        "category": "meeting",
        "status": "confirmed",
        "attendeeCount": 3,
        "hasReminder": true
      }
    ],
    "summary": {
      "totalCount": 15,
      "todayCount": 3,
      "upcomingCount": 8,
      "overdueCount": 1
    },
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 15,
      "totalPages": 1
    }
  },
  "timestamp": 1640995200000
}
```

#### 1.3 获取日程详情
**接口路径**: `GET /api/schedule/{scheduleId}`

**功能描述**: 获取指定日程的详细信息

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "scheduleId": "schedule_12345",
    "title": "项目评审会议",
    "description": "讨论Q1项目进展和下阶段计划",
    "startTime": "2024-12-29T14:00:00",
    "endTime": "2024-12-29T16:00:00",
    "location": "会议室A",
    "creator": {
      "userId": "1001",
      "userName": "张三",
      "email": "<EMAIL>"
    },
    "attendees": [
      {
        "userId": "1001",
        "userName": "张三",
        "email": "<EMAIL>",
        "status": "accepted",
        "responseTime": "2024-12-28T15:30:00"
      },
      {
        "userId": "1002",
        "userName": "李四", 
        "email": "<EMAIL>",
        "status": "pending",
        "responseTime": null
      }
    ],
    "reminders": [
      {
        "remindBefore": 15,
        "remindTime": "2024-12-29T13:45:00",
        "status": "pending"
      },
      {
        "remindBefore": 60,
        "remindTime": "2024-12-29T13:00:00", 
        "status": "pending"
      }
    ],
    "priority": "high",
    "category": "meeting",
    "tags": ["项目", "评审", "重要"],
    "status": "confirmed",
    "isRecurring": false,
    "createTime": "2024-12-28T15:30:00",
    "updateTime": "2024-12-28T15:30:00",
    "attachments": [
      {
        "fileName": "项目进展报告.pdf",
        "fileUrl": "/files/project_report.pdf",
        "fileSize": 2048576
      }
    ]
  },
  "timestamp": 1640995200000
}
```

#### 1.4 更新日程
**接口路径**: `PUT /api/schedule/{scheduleId}`

**功能描述**: 更新日程信息

**请求参数**:
```http
PUT /api/schedule/schedule_12345
Content-Type: application/json

{
  "title": "项目评审会议(修改)",
  "startTime": "2024-12-29T15:00:00",
  "endTime": "2024-12-29T17:00:00",
  "location": "会议室B",
  "description": "讨论Q1项目进展和下阶段计划(已更新议程)"
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "日程更新成功",
  "data": {
    "scheduleId": "schedule_12345",
    "title": "项目评审会议(修改)",
    "startTime": "2024-12-29T15:00:00",
    "endTime": "2024-12-29T17:00:00",
    "location": "会议室B",
    "updateTime": "2024-12-28T16:00:00",
    "conflictCheck": {
      "hasConflict": true,
      "conflicts": [
        {
          "conflictScheduleId": "schedule_67890",
          "conflictTitle": "部门例会",
          "conflictTime": "2024-12-29T15:30:00 - 2024-12-29T16:30:00"
        }
      ]
    },
    "notificationSent": true
  },
  "timestamp": 1640995200000
}
```

#### 1.5 删除日程
**接口路径**: `DELETE /api/schedule/{scheduleId}`

**功能描述**: 删除指定日程

**响应格式**:
```json
{
  "code": 200,
  "msg": "日程删除成功",
  "data": {
    "scheduleId": "schedule_12345",
    "title": "项目评审会议",
    "deleteTime": "2024-12-28T16:30:00",
    "notificationSent": true
  },
  "timestamp": 1640995200000
}
```

---

## ⏰ 提醒管理API

### 2. 智能提醒功能

#### 2.1 设置提醒
**接口路径**: `POST /api/reminders/set`

**功能描述**: 为日程设置提醒

**请求参数**:
```http
POST /api/reminders/set
Content-Type: application/json

{
  "scheduleId": "schedule_12345",
  "reminders": [
    {
      "remindBefore": 15,
      "remindType": "popup",
      "message": "会议即将开始，请准备相关材料"
    },
    {
      "remindBefore": 60,
      "remindType": "email",
      "message": "一小时后有重要会议"
    }
  ]
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "提醒设置成功",
  "data": {
    "scheduleId": "schedule_12345",
    "reminders": [
      {
        "reminderId": "reminder_001",
        "remindBefore": 15,
        "remindTime": "2024-12-29T13:45:00",
        "remindType": "popup",
        "status": "scheduled"
      },
      {
        "reminderId": "reminder_002", 
        "remindBefore": 60,
        "remindTime": "2024-12-29T13:00:00",
        "remindType": "email",
        "status": "scheduled"
      }
    ]
  },
  "timestamp": 1640995200000
}
```

#### 2.2 获取待提醒列表
**接口路径**: `GET /api/reminders/pending`

**功能描述**: 获取用户的待提醒列表

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "pendingReminders": [
      {
        "reminderId": "reminder_001",
        "scheduleId": "schedule_12345",
        "scheduleTitle": "项目评审会议",
        "remindTime": "2024-12-29T13:45:00",
        "remindType": "popup",
        "message": "会议即将开始，请准备相关材料",
        "priority": "high"
      }
    ],
    "summary": {
      "totalPending": 5,
      "todayReminders": 3,
      "upcomingReminders": 8
    }
  },
  "timestamp": 1640995200000
}
```

---

*文档版本: v1.0.0 | 最后更新: 2024-12-28 | Wenshu-Celebdar智能日程管理模块*
