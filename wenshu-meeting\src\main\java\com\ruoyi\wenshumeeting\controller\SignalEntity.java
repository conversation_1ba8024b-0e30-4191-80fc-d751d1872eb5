// 包声明：定义当前类所属的包路径，属于控制器包
package com.ruoyi.wenshumeeting.controller;

// 导入Lombok的Data注解，自动生成getter、setter、toString、equals、hashCode等方法
import lombok.Data;

/**
 * WebSocket信号实体类
 * 用于封装WebSocket通信中传输的信号数据
 * @Data 是Lombok注解，会自动生成以下方法：
 * - 所有字段的getter和setter方法
 * - toString()方法
 * - equals()和hashCode()方法
 * - 无参构造函数（如果没有显式定义构造函数）
 */
@Data
public class SignalEntity {

    /**
     * 信号类型字段
     * 用于标识WebSocket消息的类型，如：
     * - "join": 用户加入会议
     * - "leave": 用户离开会议
     * - "offer": WebRTC连接提议
     * - "answer": WebRTC连接应答
     * - "candidate": ICE候选信息
     */
    private String type;

    /**
     * 消息内容字段
     * 用于存储具体的消息文本或描述信息
     * 可以是用户操作的描述、错误信息、状态说明等
     */
    private String msg;

    /**
     * 状态码字段
     * 用于标识操作的执行状态，类似HTTP状态码：
     * - 200: 操作成功
     * - 400: 客户端请求错误
     * - 500: 服务器内部错误
     * - 自定义状态码用于特定业务逻辑
     */
    private int status;

    /**
     * 数据载荷字段
     * 用于存储具体的业务数据，使用Object类型以支持各种数据结构：
     * - 用户信息对象
     * - WebRTC会话描述信息
     * - 会议室信息
     * - 其他业务相关数据
     */
    private Object data;

    /**
     * 无参构造函数
     * 用于创建空的SignalEntity实例，通常用于：
     * - JSON反序列化
     * - 框架实例化
     * - 后续通过setter方法设置属性值
     */
    public SignalEntity() {
    }

    /**
     * 全参构造函数
     * 用于快速创建包含所有必要信息的SignalEntity实例
     *
     * @param type 信号类型，标识消息的业务类型
     * @param msg 消息内容，描述具体的操作或状态信息
     * @param status 状态码，标识操作的执行结果
     * @param data 数据载荷，包含具体的业务数据
     */
    public SignalEntity(String type, String msg, int status, Object data) {
        // 设置信号类型
        this.type = type;
        // 设置消息内容
        this.msg = msg;
        // 设置状态码
        this.status = status;
        // 设置数据载荷
        this.data = data;
    }
}
