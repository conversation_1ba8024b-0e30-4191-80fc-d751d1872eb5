package com.ruoyi.wenshubase.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.wenshubase.entity.BaseConversation;

import java.util.List;

/**
 * 对话服务接口
 */
public interface ChatService extends IService<BaseConversation> {

    /**
     * 创建对话
     */
    BaseConversation create(int userId);

    /**
     * 编辑对话名称
     */
    void edit(String conversationId, String name, int userId);
    
    /**
     * 更新整个会话对象
     * @param conversation 会话对象
     */
    void updateConversation(BaseConversation conversation,int userId);

    /**
     * 获取对话列表
     */
    List<BaseConversation> list(int userId);

    /**
     * 删除对话
     */
    void delete(String conversationId);

    /**
     * 获取单个对话
     */
    BaseConversation get(String conversationId,int userId);
} 