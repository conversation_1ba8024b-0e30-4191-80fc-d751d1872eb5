# 文书智能计算系统 - 优化版Nginx配置
# 根据Docker部署文件优化的配置

# 上游服务定义 - 所有微服务
upstream gateway_main {
    server wenshu-gateway:8080 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

upstream auth_service {
    server wenshu-auth:9200 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

upstream system_service {
    server wenshu-system:9201 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

upstream nacos_service {
    server wenshu-nacos:8848 max_fails=3 fail_timeout=30s;
    keepalive 16;
}

upstream chat_service {
    server wenshu-chat:8701 max_fails=3 fail_timeout=30s;
    keepalive 8;
}

upstream meeting_service {
    server wenshu-meeting:8703 max_fails=3 fail_timeout=30s;
    keepalive 8;
}

upstream multimodal_service {
    server wenshu-multimodal:8702 max_fails=3 fail_timeout=30s;
    keepalive 8;
}

upstream base_service {
    server wenshu-base:8601 max_fails=3 fail_timeout=30s;
    keepalive 8;
}

upstream team_service {
    server wenshu-team:1017 max_fails=3 fail_timeout=30s;
    keepalive 8;
}

upstream api_service {
    server wenshu-api:8704 max_fails=3 fail_timeout=30s;
    keepalive 8;
}

upstream attu_service {
    server wenshu-attu:3000 max_fails=3 fail_timeout=30s;
    keepalive 8;
}

# 主服务器配置
server {
    listen 80;
    server_name localhost;
    
    # 根目录
    root /usr/local/nginx-1.24.0/html;
    index index.html index.htm;

    # 日志配置
    access_log /usr/nginx-1.24.0/logs/wenshu-optimized.access.log main;
    error_log /usr/nginx-1.24.0/logs/wenshu-optimized.error.log;

    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # 静态文件缓存和压缩
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        access_log off;
        
        # 跨域支持
        add_header Access-Control-Allow-Origin *;
    }

    # API网关 - 主要入口
    location /api/ {
        proxy_pass http://gateway_main/;
        include /etc/nginx/proxy_params;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        # 跨域支持
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
        
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }

    # 认证服务直接访问
    location /auth/ {
        proxy_pass http://auth_service/;
        include /etc/nginx/proxy_params;

        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 跨域支持
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }

    # 系统服务直接访问
    location /system/ {
        proxy_pass http://system_service/;
        include /etc/nginx/proxy_params;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 聊天服务
    location /chat/ {
        proxy_pass http://chat_service/;
        include /etc/nginx/proxy_params;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }

    # 会议服务
    location /meeting/ {
        proxy_pass http://meeting_service/;
        include /etc/nginx/proxy_params;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }

    # 多模态服务
    location /multimodal/ {
        proxy_pass http://multimodal_service/;
        include /etc/nginx/proxy_params;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }

    # 基础数据服务
    location /base/ {
        proxy_pass http://base_service/;
        include /etc/nginx/proxy_params;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 团队服务
    location /team/ {
        proxy_pass http://team_service/;
        include /etc/nginx/proxy_params;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # API服务
    location /wenshu-api/ {
        proxy_pass http://api_service/;
        include /etc/nginx/proxy_params;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Nacos控制台
    location /nacos/ {
        proxy_pass http://nacos_service/nacos/;
        include /etc/nginx/proxy_params;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Attu (Milvus管理界面)
    location /attu/ {
        proxy_pass http://attu_service/;
        include /etc/nginx/proxy_params;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # WebSocket支持
    location /ws/ {
        proxy_pass http://gateway_main/ws/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        include /etc/nginx/proxy_params;
        
        proxy_connect_timeout 7d;
        proxy_send_timeout 7d;
        proxy_read_timeout 7d;
    }

    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # Vue Router历史模式支持
    location / {
        try_files $uri $uri/ /index.html;
        
        # 禁用HTML缓存
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # 文件上传大小限制
    client_max_body_size 100M;

    # 错误页面
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /50x.html {
        root /usr/local/nginx-1.24.0/html;
    }

    # 安全配置
    server_tokens off;
    
    # 禁止访问隐藏文件和敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(htaccess|htpasswd|ini|log|sh|sql|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}
