package com.ruoyi.wenshuchat.util;

import lombok.RequiredArgsConstructor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.stereotype.Component;
import org.springframework.ai.chat.messages.AssistantMessage;

import java.util.ArrayList;
import java.util.List;

/**
 * 聊天消息保存工具，用于将用户消息和AI回复保存到聊天历史中
 */
@Component
@RequiredArgsConstructor
public class ChatMessageSaver {
    private final ChatMemory chatMemory;

    /**
     * 保存用户消息和AI回复到指定的会话ID
     * @param conversationId 会话ID
     * @param userMessage 用户消息
     * @param aiResponse AI回复内容
     */
    public void saveMessage(String conversationId, String userMessage, String aiResponse) {
        // 创建消息列表
        List<Message> messages = new ArrayList<>();
        
        // 添加用户消息
        messages.add(new UserMessage(userMessage));
        
        // 添加AI回复
        messages.add(new AssistantMessage(aiResponse));
        
        // 保存消息到会话记忆中
        chatMemory.add(conversationId, messages);
    }
} 