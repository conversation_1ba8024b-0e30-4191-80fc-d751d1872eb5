# Nacos故障排除指南

## 问题：Nacos在多服务连接时自动重启

### 🔍 问题分析

当启动多个服务（auth、system、gateway）连接到Nacos时，Nacos容器自动重启，这通常是由以下原因造成的：

1. **内存不足** - JVM堆内存或容器内存限制太小
2. **连接数过多** - Tomcat连接池配置不足
3. **数据库连接池不足** - 数据库连接数限制
4. **GC压力过大** - 垃圾收集器配置不当

### ✅ 解决方案

#### 1. 使用优化脚本（推荐）

```bash
# Linux/macOS
chmod +x optimize-nacos.sh
./optimize-nacos.sh

# Windows
optimize-nacos.bat
```

#### 2. 手动优化配置

已经在 `docker-compose.env.yml` 中进行了以下优化：

**内存配置优化：**
- JVM堆内存：512MB → 1024MB
- 容器内存限制：512MB → 1280MB
- 新生代内存：96MB → 256MB

**连接池优化：**
- Tomcat最大线程数：400
- Tomcat最小线程数：50
- 最大连接数：20000
- 数据库连接池：50

**GC优化：**
- 使用G1垃圾收集器
- 最大GC暂停时间：200ms

### 📊 监控和诊断

#### 1. 实时监控
```bash
# 启动监控脚本
chmod +x monitor-nacos.sh
./monitor-nacos.sh
```

#### 2. 手动检查命令

**检查容器状态：**
```bash
docker ps --filter "name=wenshu-nacos"
```

**检查资源使用：**
```bash
docker stats wenshu-nacos
```

**检查日志：**
```bash
docker compose -f docker-compose.env.yml logs -f nacos
```

**检查健康状态：**
```bash
curl http://localhost:8848/nacos/actuator/health
```

### 🚨 常见错误和解决方法

#### 错误1: OutOfMemoryError
```
java.lang.OutOfMemoryError: Java heap space
```

**解决方法：**
1. 增加JVM堆内存
2. 增加容器内存限制
3. 优化GC配置

#### 错误2: Too many connections
```
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure
```

**解决方法：**
1. 增加数据库连接池大小
2. 检查MySQL最大连接数配置
3. 优化连接超时设置

#### 错误3: Port already in use
```
Address already in use: bind
```

**解决方法：**
```bash
# 检查端口占用
netstat -tlnp | grep 8848
# 或
ss -tlnp | grep 8848

# 停止占用端口的进程
sudo kill -9 <PID>
```

### 🔧 进一步优化建议

#### 1. 系统级优化

**增加系统内存：**
- 推荐至少4GB可用内存
- 如果运行多个服务，建议8GB+

**优化Docker配置：**
```bash
# 增加Docker内存限制
# 在Docker Desktop中设置更大的内存限制
```

#### 2. 应用级优化

**分批启动服务：**
```bash
# 不要同时启动所有服务，分批启动
docker compose -f docker-compose.app.yml up -d auth
sleep 30
docker compose -f docker-compose.app.yml up -d system
sleep 30
docker compose -f docker-compose.app.yml up -d gateway
```

**调整服务启动顺序：**
1. 基础设施服务（MySQL, Redis）
2. Nacos
3. 核心服务（auth）
4. 业务服务（system）
5. 网关服务（gateway）

#### 3. 配置文件优化

如果问题仍然存在，可以进一步调整配置：

```yaml
# 在docker-compose.env.yml中进一步增加内存
JVM_XMS: 1024m
JVM_XMX: 2048m
mem_limit: 2560m
```

### 📈 性能监控

#### 1. 关键指标监控

- **内存使用率** < 80%
- **CPU使用率** < 70%
- **GC频率** < 每分钟5次
- **响应时间** < 100ms

#### 2. 监控命令

```bash
# 持续监控资源使用
watch -n 5 'docker stats wenshu-nacos --no-stream'

# 监控GC情况
docker exec wenshu-nacos jstat -gc 1 5s

# 监控线程情况
docker exec wenshu-nacos jstack 1
```

### 🆘 紧急恢复

如果Nacos完全无法启动：

```bash
# 1. 完全重置
docker compose -f docker-compose.env.yml down nacos
docker volume rm wenshuintelligentcomputing-back_nacos_data

# 2. 重新初始化数据库
docker exec -i wenshu-mysql mysql -uroot -p2313147023 < fix-nacos-db.sql

# 3. 使用最小配置启动
# 临时降低内存配置，确保能启动
JVM_XMS: 256m
JVM_XMX: 512m

# 4. 逐步增加配置
# 启动成功后再逐步增加内存配置
```

### 📞 获取帮助

如果问题仍然存在：

1. **收集信息：**
   - 系统内存信息：`free -h`
   - Docker版本：`docker --version`
   - 容器日志：`docker logs wenshu-nacos`
   - 系统日志：`dmesg | tail -50`

2. **提供详细错误信息：**
   - 完整的错误堆栈
   - 重启前后的日志对比
   - 资源使用情况截图

3. **尝试最小化复现：**
   - 只启动一个服务测试
   - 逐个增加服务数量
   - 记录在哪个服务启动时出现问题
