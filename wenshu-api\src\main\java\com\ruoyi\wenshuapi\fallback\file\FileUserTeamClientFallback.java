package com.ruoyi.wenshuapi.fallback.file;

import com.ruoyi.wenshuapi.client.file.FileUserTeamClient;
import com.ruoyi.wenshuapi.pojo.file.FileUserTeamPojo;
import com.ruoyi.wenshuapi.util.file.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * FileUserTeamClient 的 Fallback 实现
 * 当 wenshu-file 服务不可用时，提供降级响应
 */
@Slf4j
@Component
public class FileUserTeamClientFallback implements FallbackFactory<FileUserTeamClient> {

    private static final String SERVICE_UNAVAILABLE_MSG = "文件关联服务不可用，请稍后重试";

    @Override
    public FileUserTeamClient create(Throwable cause) {
        log.error("文件关联服务调用失败", cause);

        return new FileUserTeamClient() {
            @Override
            public ApiResponse<FileUserTeamPojo> createAssociation(FileUserTeamPojo pojo) {
                log.warn("文件关联服务不可用 - 创建关联失败: fileId={}, userId={}, teamId={}",
                        pojo.getFileId(), pojo.getUserId(), pojo.getTeamId());
                return ApiResponse.failed(SERVICE_UNAVAILABLE_MSG, 503);
            }

            @Override
            public ApiResponse<String> deleteAssociation(int fileId, int userId, int teamId) {
                log.warn("文件关联服务不可用 - 删除关联失败: fileId={}, userId={}, teamId={}", fileId, userId, teamId);
                return ApiResponse.failed(SERVICE_UNAVAILABLE_MSG, 503);
            }

            @Override
            public ApiResponse<String> deleteByFile(int fileId) {
                log.warn("文件关联服务不可用 - 删除文件所有关联失败: fileId={}", fileId);
                return ApiResponse.failed(SERVICE_UNAVAILABLE_MSG, 503);
            }

            @Override
            public ApiResponse<String> deleteByUser(int userId) {
                log.warn("文件关联服务不可用 - 删除用户所有关联失败: userId={}", userId);
                return ApiResponse.failed(SERVICE_UNAVAILABLE_MSG, 503);
            }

            @Override
            public ApiResponse<String> deleteByTeam(int teamId) {
                log.warn("文件关联服务不可用 - 删除团队所有关联失败: teamId={}", teamId);
                return ApiResponse.failed(SERVICE_UNAVAILABLE_MSG, 503);
            }

            @Override
            public ApiResponse<FileUserTeamPojo> getAssociation(int fileId, int userId, int teamId) {
                log.warn("文件关联服务不可用 - 获取关联详情失败: fileId={}, userId={}, teamId={}", fileId, userId, teamId);
                return ApiResponse.failed(SERVICE_UNAVAILABLE_MSG, 503);
            }

            @Override
            public ApiResponse<List<FileUserTeamPojo>> getByFile(int fileId) {
                log.warn("文件关联服务不可用 - 获取文件关联列表失败: fileId={}", fileId);
                // 返回空列表而不是错误，避免影响主流程
                return ApiResponse.success(Collections.emptyList(), SERVICE_UNAVAILABLE_MSG);
            }

            @Override
            public ApiResponse<List<FileUserTeamPojo>> getByUser(int userId) {
                log.warn("文件关联服务不可用 - 获取用户关联列表失败: userId={}", userId);
                // 返回空列表而不是错误，避免影响主流程
                return ApiResponse.success(Collections.emptyList(), SERVICE_UNAVAILABLE_MSG);
            }

            @Override
            public ApiResponse<List<FileUserTeamPojo>> getByTeam(int teamId) {
                log.warn("文件关联服务不可用 - 获取团队关联列表失败: teamId={}", teamId);
                // 返回空列表而不是错误，避免影响主流程
                return ApiResponse.success(Collections.emptyList(), SERVICE_UNAVAILABLE_MSG);
            }

            @Override
            public ApiResponse<FileUserTeamPojo> updateAssociation(int oldFileId, int oldUserId, int oldTeamId, FileUserTeamPojo newPojo) {
                log.warn("文件关联服务不可用 - 更新关联失败: oldFileId={}, oldUserId={}, oldTeamId={}",
                        oldFileId, oldUserId, oldTeamId);
                return ApiResponse.failed(SERVICE_UNAVAILABLE_MSG, 503);
            }
        };
    }
}