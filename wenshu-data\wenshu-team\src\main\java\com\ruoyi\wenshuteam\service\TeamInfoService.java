package com.ruoyi.wenshuteam.service;

import com.ruoyi.wenshuapi.pojo.team.TeamInfo;
import java.util.List;

/**
 * 团队信息服务层接口
 * 提供团队信息的业务逻辑操作
 */
public interface TeamInfoService {

    /**
     * 创建团队
     * 
     * @param teamInfo 团队信息实体
     * @return 创建成功的团队ID
     */
    int createTeam(TeamInfo teamInfo);

    /**
     * 删除团队
     * 
     * @param teamId 团队ID
     * @return 删除操作影响的行数
     */
    int deleteTeam(Integer teamId);

    /**
     * 更新团队信息
     * 
     * @param teamInfo 团队信息实体
     * @return 更新操作影响的行数
     */
    int updateTeam(TeamInfo teamInfo);

    /**
     * 根据ID获取团队详情
     * 
     * @param teamId 团队ID
     * @return 团队信息实体
     */
    TeamInfo getTeamById(Integer teamId);

    /**
     * 获取所有团队列表
     * 
     * @return 团队信息列表
     */
    List<TeamInfo> getAllTeams();

    /**
     * 根据状态获取团队列表
     * 
     * @param status 团队状态 (0-禁用, 1-启用)
     * @return 符合条件的团队列表
     */
    List<TeamInfo> getTeamsByStatus(Byte status);

    /**
     * 根据团队名称搜索团队
     * 
     * @param teamName 团队名称关键词
     * @return 匹配的团队列表
     */
    List<TeamInfo> searchTeamsByName(String teamName);
}