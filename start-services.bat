@echo off
chcp 65001 >nul
echo === 启动微服务系统 ===

REM 检查Docker是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo 错误: Docker未运行，请先启动Docker
    pause
    exit /b 1
)

REM 停止所有现有服务
echo 1. 停止现有服务...
sudo docker compose -f docker-compose.app.yml down

REM 清理网络
echo 2. 清理Docker网络...
sudo docker network prune -f

REM 等待清理完成
timeout /t 2 /nobreak >nul

REM 启动基础设施服务
echo 3. 启动基础设施服务...
echo    启动 Nacos...
sudo docker compose -f docker-compose.yml up -d wenshu-nacos 2>nul
timeout /t 10 /nobreak >nul

echo    启动 MySQL...
sudo docker compose -f docker-compose.yml up -d wenshu-mysql 2>nul
timeout /t 5 /nobreak >nul

echo    启动 Redis...
sudo docker compose -f docker-compose.yml up -d wenshu-redis 2>nul
timeout /t 5 /nobreak >nul

REM 等待基础服务完全启动
echo 4. 等待基础服务启动完成...
timeout /t 15 /nobreak >nul

REM 启动认证服务
echo 5. 启动认证服务...
sudo docker compose -f docker-compose.app.yml up -d auth
echo    等待认证服务启动...
timeout /t 20 /nobreak >nul

REM 检查认证服务状态
echo 6. 检查认证服务状态...
sudo docker compose -f docker-compose.app.yml ps auth | findstr "Up" >nul
if errorlevel 1 (
    echo    ✗ 认证服务启动失败，查看日志:
    sudo docker compose -f docker-compose.app.yml logs auth --tail=20
    pause
    exit /b 1
) else (
    echo    ✓ 认证服务启动成功
)

REM 启动网关服务
echo 7. 启动网关服务...
sudo docker compose -f docker-compose.app.yml up -d gateway
echo    等待网关服务启动...
timeout /t 20 /nobreak >nul

REM 检查网关服务状态
echo 8. 检查网关服务状态...
sudo docker compose -f docker-compose.app.yml ps gateway | findstr "Up" >nul
if errorlevel 1 (
    echo    ✗ 网关服务启动失败，查看日志:
    sudo docker compose -f docker-compose.app.yml logs gateway --tail=20
    pause
    exit /b 1
) else (
    echo    ✓ 网关服务启动成功
)

REM 启动其他服务
echo 9. 启动其他服务...
sudo docker compose -f docker-compose.app.yml up -d

echo === 启动完成 ===
echo.
echo 服务状态:
sudo docker compose -f docker-compose.app.yml ps

echo.
echo 如果有服务启动失败，可以使用以下命令查看日志:
echo sudo docker compose -f docker-compose.app.yml logs [服务名] --tail=50
echo.
echo 访问地址:
echo - 网关: http://localhost:8080
echo - 认证服务: http://localhost:9200

pause
