package com.ruoyi.wenshumultimodal.model;

import lombok.Data;

/**
 * 文档生成请求
 */
@Data
public class DocumentRequest {

    /**
     * 提示词
     */
    private String prompt;
    
    /**
     * 文档格式 (md, html, docx, pdf)
     */
    private String format;
    
    /**
     * 文档标题
     */
    private String title;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 输出路径 (可选)
     */
    private String outputPath;
    
    /**
     * 文件ID
     */
    private int fileId;
    
    /**
     * 文档风格 (formal-正式, creative-创意, academic-学术, concise-简洁)
     */
    private String style = "formal";
    
    /**
     * 文档结构类型 (report-报告, article-文章, note-笔记, proposal-提案)
     */
    private String structureType = "article";
    
    /**
     * 是否包含目录
     */
    private boolean includeTableOfContents = false;
    
    /**
     * 主题颜色
     */
    private String theme = "default";
    
    /**
     * 字体大小
     */
    private int fontSize = 12;
    
    /**
     * 行间距
     */
    private float lineSpacing = 1.5f;
    
    /**
     * 页边距（单位：mm）
     */
    private int pageMargin = 20;
    
    /**
     * 字体类型
     */
    private String fontFamily = "默认字体";
    
    /**
     * 是否自动生成摘要
     */
    private boolean generateAbstract = false;
    
    /**
     * 是否自动生成参考文献
     */
    private boolean generateReferences = false;
    
    /**
     * 文档语言 (zh-中文, en-英文)
     */
    private String language = "zh";
} 