package com.ruoyi.wenshucommon.service;

import com.ruoyi.wenshucommon.response.ConversationVO;

import java.util.List;

/**
 * 对话服务接口
 */
public interface ChatService {

    /**
     * 创建对话
     */
    ConversationVO create();

    /**
     * 编辑对话
     */
    void edit(String conversationId, String name);

    /**
     * 获取对话列表
     */
    List<ConversationVO> list();

    /**
     * 删除对话
     */
    void delete(String conversationId);

    /**
     * 获取单个对话
     */
    ConversationVO get(String conversationId);
} 