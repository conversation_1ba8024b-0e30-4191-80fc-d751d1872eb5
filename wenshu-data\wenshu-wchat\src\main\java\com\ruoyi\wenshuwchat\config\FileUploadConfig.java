package com.ruoyi.wenshuwchat.config;

import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.support.StandardServletMultipartResolver;

import jakarta.servlet.MultipartConfigElement;

/**
 * 文件上传配置类
 * 配置文件上传的各种参数，解决大文件上传问题
 */
@Configuration
public class FileUploadConfig {

    /**
     * 配置文件上传解析器
     * 支持大文件上传
     */
    @Bean
    public MultipartResolver multipartResolver() {
        StandardServletMultipartResolver resolver = new StandardServletMultipartResolver();
        return resolver;
    }

    /**
     * 配置文件上传参数
     * 设置最大文件大小为10GB
     */
    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        
        // 设置单个文件最大大小为10GB
        factory.setMaxFileSize(DataSize.ofGigabytes(10));

        // 设置总上传数据最大大小为10GB
        factory.setMaxRequestSize(DataSize.ofGigabytes(10));
        
        // 设置内存临界值，超过此值将写入临时文件
        factory.setFileSizeThreshold(DataSize.ofKilobytes(2));
        
        // 设置临时文件存储位置
        factory.setLocation(System.getProperty("java.io.tmpdir"));
        
        return factory.createMultipartConfig();
    }
}
