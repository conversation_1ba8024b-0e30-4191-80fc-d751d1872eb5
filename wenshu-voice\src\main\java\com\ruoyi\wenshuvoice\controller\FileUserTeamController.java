//package com.ruoyi.wenshuvoice.controller;
//
//import com.ruoyi.wenshuapi.client.file.FileUserTeamClient;
//import com.ruoyi.wenshuapi.pojo.file.FileUserTeamPojo;
//import com.ruoyi.wenshuapi.util.file.ApiResponse;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
///**
// * 文件-用户-团队关联关系 Controller
// * 实现所有关联关系的CRUD操作
// */
//@RestController
//@RequestMapping("/wenshu/file")
//public class FileUserTeamController {
//
//    private final FileUserTeamClient fileUserTeamClient;
//
//    @Autowired
//    public FileUserTeamController(FileUserTeamClient fileUserTeamClient) {
//        this.fileUserTeamClient = fileUserTeamClient;
//    }
//
//    /**
//     * 创建新的关联关系
//     * @param pojo 关联实体数据
//     * @return 创建结果
//     */
//    @PostMapping
//    public ApiResponse<FileUserTeamPojo> createAssociation(@RequestBody FileUserTeamPojo pojo) {
//        return fileUserTeamClient.createAssociation(pojo);
//    }
//
//    /**
//     * 删除特定关联关系
//     * @param fileId 文件ID
//     * @param userId 用户ID
//     * @param teamId 团队ID
//     * @return 删除结果
//     */
//    @DeleteMapping("/{fileId}/{userId}/{teamId}")
//    public ApiResponse<String> deleteAssociation(
//            @PathVariable("fileId") int fileId,
//            @PathVariable("userId") int userId,
//            @PathVariable("teamId") int teamId) {
//        return fileUserTeamClient.deleteAssociation(fileId, userId, teamId);
//    }
//
//    /**
//     * 删除文件相关的所有关联
//     * @param fileId 文件ID
//     * @return 删除结果
//     */
//    @DeleteMapping("/file/{fileId}")
//    public ApiResponse<String> deleteByFile(@PathVariable("fileId") int fileId) {
//        return fileUserTeamClient.deleteByFile(fileId);
//    }
//
//    /**
//     * 删除用户相关的所有关联
//     * @param userId 用户ID
//     * @return 删除结果
//     */
//    @DeleteMapping("/user/{userId}")
//    public ApiResponse<String> deleteByUser(@PathVariable("userId") int userId) {
//        return fileUserTeamClient.deleteByUser(userId);
//    }
//
//    /**
//     * 删除团队相关的所有关联
//     * @param teamId 团队ID
//     * @return 删除结果
//     */
//    @DeleteMapping("/team/{teamId}")
//    public ApiResponse<String> deleteByTeam(@PathVariable("teamId") int teamId) {
//        return fileUserTeamClient.deleteByTeam(teamId);
//    }
//
//    /**
//     * 获取特定关联关系详情
//     * @param fileId 文件ID
//     * @param userId 用户ID
//     * @param teamId 团队ID
//     * @return 关联实体详情
//     */
//    @GetMapping("/{fileId}/{userId}/{teamId}")
//    public ApiResponse<FileUserTeamPojo> getAssociation(
//            @PathVariable("fileId") int fileId,
//            @PathVariable("userId") int userId,
//            @PathVariable("teamId") int teamId) {
//        return fileUserTeamClient.getAssociation(fileId, userId, teamId);
//    }
//
//    /**
//     * 获取文件相关的所有关联
//     * @param fileId 文件ID
//     * @return 关联列表
//     */
//    @GetMapping("/file/{fileId}")
//    public ApiResponse<List<FileUserTeamPojo>> getByFile(@PathVariable("fileId") int fileId) {
//        return fileUserTeamClient.getByFile(fileId);
//    }
//
//    /**
//     * 获取用户相关的所有关联
//     * @param userId 用户ID
//     * @return 关联列表
//     */
//    @GetMapping("/user/{userId}")
//    public ApiResponse<List<FileUserTeamPojo>> getByUser(@PathVariable("userId") int userId) {
//        return fileUserTeamClient.getByUser(userId);
//    }
//
//    /**
//     * 获取团队相关的所有关联
//     * @param teamId 团队ID
//     * @return 关联列表
//     */
//    @GetMapping("/team/{teamId}")
//    public ApiResponse<List<FileUserTeamPojo>> getByTeam(@PathVariable("teamId") int teamId) {
//        return fileUserTeamClient.getByTeam(teamId);
//    }
//
//    /**
//     * 更新关联关系
//     * @param oldFileId 原文件ID
//     * @param oldUserId 原用户ID
//     * @param oldTeamId 原团队ID
//     * @param newPojo 新关联数据
//     * @return 更新结果
//     */
//    @PutMapping("/{oldFileId}/{oldUserId}/{oldTeamId}")
//    public ApiResponse<FileUserTeamPojo> updateAssociation(
//            @PathVariable("oldFileId") int oldFileId,
//            @PathVariable("oldUserId") int oldUserId,
//            @PathVariable("oldTeamId") int oldTeamId,
//            @RequestBody FileUserTeamPojo newPojo) {
//        return fileUserTeamClient.updateAssociation(oldFileId, oldUserId, oldTeamId, newPojo);
//    }
//}