# Spring
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************
    username: root
    password: 2313147023
  application:
    # 应用名称
    name: wenshu-base
  main:
    # 允许同名Bean覆盖
    allow-bean-definition-overriding: true
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 8.155.31.217:8848
      config:
        # 配置中心地址
        server-addr: 8.155.31.217:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
server:
  port: 8601
# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.ruoyi.wenshubase.entity
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false

# 日志配置
logging:
  level:
    root: INFO
    com.ruoyi.wenshubase: DEBUG
    org.springframework.cloud.openfeign: DEBUG
    com.ruoyi.programmemanage.client.ProgrammeFeignClient: DEBUG
# Feign配置
feign:
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
      wenshu-calebdar:
        connectTimeout: 30000
        readTimeout: 30000
  compression:
    request:
      enabled: true
    response:
      enabled: true
  circuitbreaker:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false

