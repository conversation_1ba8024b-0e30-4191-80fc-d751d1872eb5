package com.ruoyi.wenshuapi.pojo.file;
public class FileUserTeamPojo {
    private int fileId;
    private int userId;
    private int teamId;

    // 无参构造方法
    public FileUserTeamPojo() {
    }

    // 全参构造方法（可选）
    public FileUserTeamPojo(int fileId, int userId, int teamId) {
        this.fileId = fileId;
        this.userId = userId;
        this.teamId = teamId;
    }

    // Getter和Setter方法
    public int getFileId() {
        return fileId;
    }

    public void setFileId(int fileId) {
        this.fileId = fileId;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public int getTeamId() {
        return teamId;
    }

    public void setTeamId(int teamId) {
        this.teamId = teamId;
    }

    // 可选：toString()方法
    @Override
    public String toString() {
        return "FileUserTeam{" +
               "fileId=" + fileId +
               ", userId=" + userId +
               ", teamId=" + teamId +
               '}';
    }
}