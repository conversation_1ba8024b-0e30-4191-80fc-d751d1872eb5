package com.ruoyi.wenshuapi.client.voice;

import feign.Request;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * VoiceClient专用配置
 * 为语音识别服务设置专门的超时配置
 */
@Configuration
public class VoiceClientConfig {

    /**
     * 为VoiceClient配置专门的超时时间
     * 语音处理需要更长的时间，设置为1小时
     */
    @Bean
    public Request.Options requestOptions() {
        return new Request.Options(
                30, TimeUnit.SECONDS,    // 连接超时：30秒
                3600, TimeUnit.SECONDS,  // 读取超时：1小时
                true                     // 跟随重定向
        );
    }
}
