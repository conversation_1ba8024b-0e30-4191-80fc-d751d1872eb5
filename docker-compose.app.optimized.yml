# 优化版本的 Docker Compose 配置
# 通过公共配置模板减少重复，添加资源限制，优化部署空间

# 公共配置模板
x-common-service: &common-service
  restart: always
  networks:
    - wenshu_wenshu-network
  security_opt:
    - apparmor=unconfined
    - seccomp=unconfined

x-common-environment: &common-environment
  SPRING_PROFILES_ACTIVE: prod
  SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: nacos:8848
  SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR: nacos:8848
  JAVA_OPTS: "-Xmx512m -Xms256m -Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"

x-database-environment: &database-environment
  <<: *common-environment
  SPRING_DATASOURCE_URL: ****************************************************************************************************************************************************
  SPRING_DATASOURCE_USERNAME: root
  SPRING_DATASOURCE_PASSWORD: 2313147023

x-redis-environment: &redis-environment
  <<: *database-environment
  SPRING_DATA_REDIS_HOST: redis
  SPRING_DATA_REDIS_PORT: 6379

# 资源限制模板
x-core-service-limits: &core-service-limits
  deploy:
    resources:
      limits:
        memory: 768M
        cpus: '0.8'
      reservations:
        memory: 384M
        cpus: '0.4'

x-standard-service-limits: &standard-service-limits
  deploy:
    resources:
      limits:
        memory: 512M
        cpus: '0.5'
      reservations:
        memory: 256M
        cpus: '0.25'

x-lightweight-service-limits: &lightweight-service-limits
  deploy:
    resources:
      limits:
        memory: 256M
        cpus: '0.3'
      reservations:
        memory: 128M
        cpus: '0.15'

services:
  # 核心基础服务
  gateway:
    <<: *common-service
    build:
      context: .
      dockerfile: ruoyi-gateway/Dockerfile
      args:
        JAR_FILE: ruoyi-gateway/target/ruoyi-gateway-*.jar
        SERVER_PORT: 8080
        SERVICE_NAME: gateway
    container_name: wenshu-gateway
    environment:
      <<: *common-environment
    ports:
      - "8080:8080"
    <<: *core-service-limits

  auth:
    <<: *common-service
    build:
      context: .
      dockerfile: ruoyi-auth/Dockerfile
      args:
        JAR_FILE: ruoyi-auth/target/ruoyi-auth-*.jar
        SERVER_PORT: 9200
        SERVICE_NAME: auth
    container_name: wenshu-auth
    environment:
      <<: *redis-environment
    volumes:
      - ./logs/ruoyi-auth:/app/logs
    <<: *core-service-limits

  system:
    <<: *common-service
    build:
      context: .
      dockerfile: ruoyi-modules/ruoyi-system/Dockerfile
      args:
        JAR_FILE: ruoyi-modules/ruoyi-system/target/ruoyi-system-*.jar
        SERVER_PORT: 9201
        SERVICE_NAME: system
    container_name: wenshu-system
    environment:
      <<: *redis-environment
    volumes:
      - ./logs/ruoyi-system:/app/logs
    <<: *standard-service-limits

  # 系统功能模块（轻量级）
  file:
    <<: *common-service
    build:
      context: .
      dockerfile: ruoyi-modules/ruoyi-file/Dockerfile
    container_name: ruoyi-file
    privileged: true
    environment:
      <<: *redis-environment
    volumes:
      - ./logs/ruoyi-file:/app/logs
      - ./uploadPath:/app/uploadPath
    <<: *lightweight-service-limits

  job:
    <<: *common-service
    build:
      context: .
      dockerfile: ruoyi-modules/ruoyi-job/Dockerfile
    container_name: wenshu-job
    environment:
      <<: *redis-environment
    volumes:
      - ./logs/ruoyi-job:/app/logs
    <<: *lightweight-service-limits

  # 监控服务（仅在需要时启用）
  monitor:
    <<: *common-service
    build:
      context: .
      dockerfile: ruoyi-visual/ruoyi-monitor/Dockerfile
    container_name: wenshu-monitor
    environment:
      <<: *common-environment
    ports:
      - "9100:9100"
    profiles:
      - monitoring
    <<: *lightweight-service-limits

  # 代码生成服务（仅在开发时启用）
  gen:
    <<: *common-service
    build:
      context: .
      dockerfile: ruoyi-modules/ruoyi-gen/Dockerfile
    container_name: wenshu-gen
    environment:
      <<: *redis-environment
    volumes:
      - ./logs/ruoyi-gen:/app/logs
    profiles:
      - development
    <<: *lightweight-service-limits

  # 业务核心服务
  chat:
    <<: *common-service
    build:
      context: .
      dockerfile: wenshu-chat/Dockerfile
    container_name: wenshu-chat
    environment:
      <<: *redis-environment
      SPRING_AI_DASHSCOPE_API-KEY: sk-574f46304e5c4405aa5bbe26af6489b0
    volumes:
      - ./logs/wenshu-chat:/app/logs
    ports:
      - "8701:8701"
    <<: *standard-service-limits

  voice:
    <<: *common-service
    build:
      context: .
      dockerfile: wenshu-voice/Dockerfile
    container_name: wenshu-voice
    environment:
      <<: *database-environment
      SPRING_AI_DASHSCOPE_API-KEY: sk-a4b80017093447aab1688acad39d24b6
    volumes:
      - ./logs/wenshu-voice:/app/logs
    ports:
      - "1014:1014"
    <<: *standard-service-limits

  meeting:
    <<: *common-service
    build:
      context: .
      dockerfile: wenshu-meeting/Dockerfile
    container_name: wenshu-meeting
    environment:
      <<: *redis-environment
    volumes:
      - ./logs/wenshu-meeting:/app/logs
      - ./temp:/app/temp
      - ./uploads:/app/uploads
      - /dev/shm:/dev/shm
    ports:
      - "1021:1021"
    <<: *standard-service-limits

  remind:
    <<: *common-service
    build:
      context: .
      dockerfile: wenshu-remind/Dockerfile
    container_name: wenshu-remind
    environment:
      <<: *database-environment
    volumes:
      - ./logs/wenshu-remind:/app/logs
    ports:
      - "1015:1015"
    <<: *lightweight-service-limits

  multimodal:
    <<: *common-service
    build:
      context: .
      dockerfile: wenshu-multimodal/Dockerfile
    container_name: wenshu-multimodal
    environment:
      <<: *database-environment
    volumes:
      - ./logs/wenshu-multimodal:/app/logs
    ports:
      - "8702:8702"
    <<: *standard-service-limits

  # 数据服务模块
  wenshu-base:
    <<: *common-service
    build:
      context: .
      dockerfile: wenshu-data/wenshu-base/Dockerfile
    container_name: wenshu-base
    environment:
      <<: *database-environment
    ports:
      - "8601:8601"
    <<: *lightweight-service-limits

  wenshu-team:
    <<: *common-service
    build:
      context: .
      dockerfile: wenshu-data/wenshu-team/Dockerfile
    container_name: wenshu-team
    environment:
      <<: *database-environment
    ports:
      - "1017:1017"
    <<: *lightweight-service-limits

  wenshu-calebdar:
    <<: *common-service
    build:
      context: .
      dockerfile: wenshu-data/wenshu-calebdar/Dockerfile
    container_name: wenshu-calebdar
    environment:
      <<: *database-environment
    ports:
      - "8705:8705"
    <<: *lightweight-service-limits

  programme-manage:
    <<: *common-service
    build:
      context: .
      dockerfile: programme-manage/Dockerfile
    container_name: programme-manage
    environment:
      <<: *database-environment
    ports:
      - "8605:8605"
    <<: *lightweight-service-limits

  wenshu-api:
    <<: *common-service
    build:
      context: .
      dockerfile: wenshu-api/Dockerfile
    container_name: wenshu-api
    environment:
      <<: *database-environment
    ports:
      - "8704:8704"
    <<: *lightweight-service-limits

  wenshu-audit:
    <<: *common-service
    build:
      context: .
      dockerfile: wenshu-data/wenshu-audit/Dockerfile
    container_name: wenshu-audit
    environment:
      <<: *database-environment
    volumes:
      - ./logs/wenshu-audit:/app/logs
    ports:
      - "8603:8603"
    <<: *lightweight-service-limits

  wenshu-file:
    <<: *common-service
    build:
      context: .
      dockerfile: wenshu-data/wenshu-file/Dockerfile
    container_name: wenshu-file
    environment:
      <<: *database-environment
    volumes:
      - ./logs/wenshu-file:/app/logs
      - ./uploadPath:/app/uploadPath
    ports:
      - "1016:1016"
    <<: *lightweight-service-limits

networks:
  wenshu_wenshu-network:
    external: true
