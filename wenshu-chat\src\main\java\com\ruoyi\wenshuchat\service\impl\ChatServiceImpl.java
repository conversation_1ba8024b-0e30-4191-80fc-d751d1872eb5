package com.ruoyi.wenshuchat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.wenshucommon.common.MessageWrapper;
import com.ruoyi.wenshucommon.entity.BaseConversation;
import com.ruoyi.wenshucommon.response.ConversationVO;
import com.ruoyi.wenshucommon.service.ChatService;
import com.ruoyi.wenshuchat.service.ConversationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ChatServiceImpl implements ChatService {

    private final ConversationService conversationService;

    @Override
    public ConversationVO create() {
        // Generate a shorter ID (12 characters)
        String conversationId = generateShortUuid();
        
        BaseConversation conversation = BaseConversation.builder()
                .conversationId(conversationId)
                .title("新对话")
                .content("")
                .build();
        
        boolean saved = conversationService.save(conversation);
        if (!saved) {
            throw new RuntimeException("创建对话失败");
        }
        
        return ConversationVO.builder()
                .conversationId(conversationId)
                .title("新对话")
                .messages(new ArrayList<>())
                .build();
    }

    @Override
    public void edit(String conversationId, String name) {
        BaseConversation conversation = getConversationById(conversationId);
        conversation.setTitle(name);
        
        boolean updated = conversationService.updateById(conversation);
        if (!updated) {
            throw new RuntimeException("更新对话失败");
        }
    }

    @Override
    public List<ConversationVO> list() {
        LambdaQueryWrapper<BaseConversation> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(BaseConversation::getCreatedTime);
        
        return conversationService.list(wrapper)
                .stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public void delete(String conversationId) {
        LambdaQueryWrapper<BaseConversation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseConversation::getConversationId, conversationId);
        
        boolean removed = conversationService.remove(wrapper);
        if (!removed) {
            log.warn("删除对话失败，可能对话不存在: {}", conversationId);
        }
    }

    @Override
    public ConversationVO get(String conversationId) {
        return convertToVO(getConversationById(conversationId));
    }
    
    private BaseConversation getConversationById(String conversationId) {
        LambdaQueryWrapper<BaseConversation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseConversation::getConversationId, conversationId);
        
        return conversationService.getOneOpt(wrapper)
                .orElseThrow(() -> new RuntimeException("对话不存在: " + conversationId));
    }
    
    private ConversationVO convertToVO(BaseConversation conversation) {
        List<MessageWrapper> messages = new ArrayList<>();
        if (conversation.getContent() != null && !conversation.getContent().isEmpty()) {
            messages = MessageWrapper.fromConversationStr(conversation.getContent());
        }
        
        return ConversationVO.builder()
                .conversationId(conversation.getConversationId())
                .title(conversation.getTitle())
                .messages(messages)
                .build();
    }
    
    /**
     * 生成短UUID (12个字符)
     */
    private String generateShortUuid() {
        UUID uuid = UUID.randomUUID();
        long mostSigBits = uuid.getMostSignificantBits();
        long leastSigBits = uuid.getLeastSignificantBits();
        
        return (Long.toHexString(mostSigBits) + Long.toHexString(leastSigBits))
                .substring(0, 12);
    }
}
