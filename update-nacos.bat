@echo off
chcp 65001 >nul
echo === 更新Nacos到v2.2.0版本 ===
echo.

REM 检查Docker是否运行
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: Docker未运行，请先启动Docker
    pause
    exit /b 1
)
echo ✅ Docker运行正常

REM 1. 停止现有的Nacos相关服务
echo.
echo 1. 停止现有服务...
docker compose -f docker-compose.env.yml down nacos >nul 2>&1
docker compose -f docker-compose.app.yml down auth gateway system >nul 2>&1

REM 2. 拉取新的Nacos镜像
echo.
echo 2. 拉取Nacos v2.2.0镜像...
docker pull lcr.loongnix.cn/nacos/nacos-server:v2.2.0

if %errorlevel% equ 0 (
    echo ✅ Nacos v2.2.0镜像拉取成功
) else (
    echo ❌ Nacos镜像拉取失败，请检查网络连接
    pause
    exit /b 1
)

REM 3. 删除旧的Nacos镜像（可选）
echo.
echo 3. 清理旧的Nacos镜像...
docker rmi lcr.loongnix.cn/nacos/nacos-server:v2.2.0-BETA >nul 2>&1

REM 4. 修复数据库表结构（如果需要）
echo.
echo 4. 检查并修复数据库表结构...
if exist "fix-nacos-db.sql" (
    REM 检查MySQL容器是否运行
    docker ps --filter "name=wenshu-mysql" --format "{{.Names}}" | findstr wenshu-mysql >nul
    if %errorlevel% equ 0 (
        echo    执行数据库修复...
        docker exec -i wenshu-mysql mysql -uroot -p2313147023 < fix-nacos-db.sql
        if %errorlevel% equ 0 (
            echo ✅ 数据库表结构修复成功
        ) else (
            echo ⚠️  数据库修复失败，但继续启动服务
        )
    ) else (
        echo    MySQL容器未运行，跳过数据库修复
    )
) else (
    echo    修复脚本不存在，跳过数据库修复
)

REM 5. 启动基础设施服务
echo.
echo 5. 启动基础设施服务...

REM 启动MySQL（如果未运行）
docker ps --filter "name=wenshu-mysql" --format "{{.Names}}" | findstr wenshu-mysql >nul
if %errorlevel% neq 0 (
    echo    启动MySQL...
    docker compose -f docker-compose.env.yml up -d mysql
    timeout /t 10 /nobreak >nul
)

REM 启动Redis（如果未运行）
docker ps --filter "name=wenshu-redis" --format "{{.Names}}" | findstr wenshu-redis >nul
if %errorlevel% neq 0 (
    echo    启动Redis...
    docker compose -f docker-compose.env.yml up -d redis
    timeout /t 5 /nobreak >nul
)

REM 启动新版本的Nacos
echo    启动Nacos v2.2.0...
docker compose -f docker-compose.env.yml up -d nacos

REM 6. 等待Nacos启动完成
echo.
echo 6. 等待Nacos启动完成...
set /a count=0
:wait_nacos
set /a count+=1
curl -f http://localhost:8848/nacos/actuator/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Nacos启动成功
    goto nacos_ready
)
if %count% geq 30 (
    echo ❌ Nacos启动超时
    goto nacos_failed
)
echo    等待Nacos启动... (%count%/30)
timeout /t 5 /nobreak >nul
goto wait_nacos

:nacos_failed
echo ❌ Nacos启动失败，请检查日志：
echo    docker compose -f docker-compose.env.yml logs nacos
pause
exit /b 1

:nacos_ready
REM 7. 启动应用服务
echo.
echo 7. 启动应用服务...
echo    启动认证服务...
docker compose -f docker-compose.app.yml up -d auth
timeout /t 15 /nobreak >nul

echo    启动系统服务...
docker compose -f docker-compose.app.yml up -d system
timeout /t 10 /nobreak >nul

echo    启动网关服务...
docker compose -f docker-compose.app.yml up -d gateway
timeout /t 10 /nobreak >nul

REM 8. 验证服务状态
echo.
echo 8. 验证服务状态...
echo === 容器状态 ===
docker ps --filter "name=wenshu-" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo.
echo === 服务健康检查 ===
curl -f http://localhost:8848/nacos/actuator/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ nacos 服务正常
) else (
    echo ❌ nacos 服务异常
)

curl -f http://localhost:9200/actuator/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ auth 服务正常
) else (
    echo ❌ auth 服务异常
)

curl -f http://localhost:8080/actuator/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ gateway 服务正常
) else (
    echo ❌ gateway 服务异常
)

echo.
echo === 更新完成 ===
echo 🎉 Nacos已成功更新到v2.2.0版本
echo.
echo 访问地址：
echo   - Nacos控制台: http://localhost:8848/nacos
echo   - 网关服务: http://localhost:8080
echo.
echo 如果服务异常，请查看日志：
echo   docker compose -f docker-compose.env.yml logs nacos
echo   docker compose -f docker-compose.app.yml logs auth
echo   docker compose -f docker-compose.app.yml logs gateway
echo.
pause
