package com.ruoyi.wenshuapi.fallback.team;

import com.ruoyi.wenshuapi.client.team.TeamMemberClient;
import com.ruoyi.wenshuapi.pojo.team.TeamUserRelation;
import com.ruoyi.wenshuapi.util.file.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 团队-成员关系 Feign 客户端降级处理类
 * 当远程服务调用失败时提供优雅的降级响应
 */
@Slf4j
@Component
public class TeamMemberClientFallback implements TeamMemberClient {

    private static final String SERVICE_ERROR_MSG = "团队服务不可用，请稍后重试";
    private static final int SERVICE_UNAVAILABLE_CODE = 503;

    @Override
    public ApiResponse<Boolean> addMemberToTeam(TeamUserRelation relation) {
        logError("addMemberToTeam", relation);
        return ApiResponse.failed(SERVICE_ERROR_MSG, SERVICE_UNAVAILABLE_CODE);
    }

    @Override
    public ApiResponse<Boolean> removeMemberFromTeam(int teamId, int userId) {
        logError("removeMemberFromTeam", "teamId=" + teamId + ", userId=" + userId);
        return ApiResponse.failed(SERVICE_ERROR_MSG, SERVICE_UNAVAILABLE_CODE);
    }

    @Override
    public ApiResponse<Integer> disbandTeam(int teamId) {
        logError("disbandTeam", "teamId=" + teamId);
        return ApiResponse.failed(SERVICE_ERROR_MSG, SERVICE_UNAVAILABLE_CODE);
    }

    @Override
    public ApiResponse<Integer> removeUserFromAllTeams(int userId) {
        logError("removeUserFromAllTeams", "userId=" + userId);
        return ApiResponse.failed(SERVICE_ERROR_MSG, SERVICE_UNAVAILABLE_CODE);
    }

    @Override
    public ApiResponse<List<Integer>> getTeamMembers(int teamId) {
        logError("getTeamMembers", "teamId=" + teamId);
        return ApiResponse.success(Collections.emptyList(), SERVICE_ERROR_MSG);
    }

    @Override
    public ApiResponse<List<Integer>> getUserTeams(int userId) {
        logError("getUserTeams", "userId=" + userId);
        return ApiResponse.success(Collections.emptyList(), SERVICE_ERROR_MSG);
    }

    @Override
    public ApiResponse<Boolean> isUserInTeam(int teamId, int userId) {
        logError("isUserInTeam", "teamId=" + teamId + ", userId=" + userId);
        return ApiResponse.success(false, SERVICE_ERROR_MSG);
    }

    @Override
    public ApiResponse<Boolean> transferUserToNewTeam(int userId, int oldTeamId, int newTeamId) {
        logError("transferUserToNewTeam",
                "userId=" + userId + ", oldTeamId=" + oldTeamId + ", newTeamId=" + newTeamId);
        return ApiResponse.success(false, SERVICE_ERROR_MSG);
    }

    @Override
    public ApiResponse<Integer> countTeamMembers(int teamId) {
        logError("countTeamMembers", "teamId=" + teamId);
        return ApiResponse.success(0, SERVICE_ERROR_MSG);
    }

    @Override
    public ApiResponse<Integer> countUserTeams(int userId) {
        logError("countUserTeams", "userId=" + userId);
        return ApiResponse.success(0, SERVICE_ERROR_MSG);
    }

    @Override
    public ApiResponse<Integer> batchAddMembersToTeam(int teamId, List<Integer> userIds) {
        logError("batchAddMembersToTeam", "teamId=" + teamId + ", userIds=" + userIds);
        return ApiResponse.success(0, SERVICE_ERROR_MSG);
    }

    @Override
    public ApiResponse<Integer> batchRemoveMembersFromTeam(int teamId, List<Integer> userIds) {
        logError("batchRemoveMembersFromTeam", "teamId=" + teamId + ", userIds=" + userIds);
        return ApiResponse.success(0, SERVICE_ERROR_MSG);
    }

    /**
     * 记录错误日志
     *
     * @param method 方法名
     * @param params 方法参数
     */
    private void logError(String method, Object params) {
        log.error("[团队服务降级] 调用 {} 失败，参数: {}，原因: 团队服务不可用或响应超时", method, params);
    }
}