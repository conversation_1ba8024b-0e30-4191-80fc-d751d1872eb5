package com.ruoyi.wenshuchat.advisor;

import com.ruoyi.wenshuapi.client.base.BaseFeignClientInter;
import com.ruoyi.wenshuapi.pojo.base.BaseConversation;
import com.ruoyi.wenshuapi.common.MessageWrapper;
import com.ruoyi.wenshuapi.common.RestResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class DbChatMemory implements ChatMemory {

    private final BaseFeignClientInter clientInter;
    
    // 会话缓存，确保同一ID不重复创建会话
    private final Map<String, Boolean> sessionCache = new ConcurrentHashMap<>();
    
    // 使用ThreadLocal存储当前用户ID
    private static final ThreadLocal<Integer> currentUserId = new ThreadLocal<>();
    
    /**
     * 设置当前用户ID
     * @param userId 用户ID
     */
    public static void setCurrentUserId(Integer userId) {
        currentUserId.set(userId);
    }
    
    /**
     * 获取当前用户ID，如果未设置则返回默认值1
     * @return 当前用户ID
     */
    public static Integer getCurrentUserId() {
        Integer userId = currentUserId.get();
        return userId != null ? userId : 1; // 默认用户ID为1
    }
    
    /**
     * 清除当前用户ID
     */
    public static void clearCurrentUserId() {
        currentUserId.remove();
    }

    @Override
    public void add(String conversationId, List<Message> messages) {
        if (conversationId == null || messages == null || messages.isEmpty()) {
            log.warn("会话ID或消息为空，跳过添加");
            return;
        }
        
        try {
            // 从Feign客户端获取的BaseConversation
            BaseConversation apiConversation = null;
            
            // 1. 尝试获取现有会话
            try {
                RestResult<BaseConversation> result = clientInter.get(conversationId, getCurrentUserId());
                if (result != null && result.isSuccess() && result.getContent() != null) {
                    apiConversation = result.getContent();
                    log.debug("成功获取现有会话: {}", conversationId);
                } else {
                    log.info("会话不存在，需要创建: {}", conversationId);
                }
            } catch (Exception e) {
                log.error("获取会话异常: {}", e.getMessage());
            }
            
            // 2. 如果会话不存在，创建新会话
            if (apiConversation == null) {
                // 检查会话是否已缓存，避免重复创建
                if (!isInCache(conversationId)) {
                    try {
                        // 创建新会话，通过API调用
                        RestResult<BaseConversation> createResult = clientInter.create(getCurrentUserId()); // 使用当前用户ID
                        BaseConversation newConversation = null;
                        
                        if (createResult != null && createResult.isSuccess() && createResult.getContent() != null) {
                            newConversation = createResult.getContent();
                            newConversation.setConversationId(conversationId);
                            newConversation.setTitle("新会话-" + conversationId.substring(0, Math.min(8, conversationId.length())));
                            newConversation.setContent("");
                            newConversation.setUpdatedTime(new Date());
                        } else {
                            // 创建失败，使用默认对象
                            newConversation = BaseConversation.builder()
                                    .conversationId(conversationId)
                                    .title("新会话-" + conversationId.substring(0, Math.min(8, conversationId.length())))
                                    .content("")
                                    .userId(getCurrentUserId())  // 使用当前用户ID
                                    .createdTime(new Date())
                                    .updatedTime(new Date())
                                    .build();
                        }
                                
                        // 保存到数据库
                        RestResult<Void> editResult = clientInter.edit(newConversation, newConversation.getUserId());
                        if (editResult != null && editResult.isSuccess()) {
                            log.info("直接创建新会话成功: {}", conversationId);
                            
                            // 添加到缓存
                            addToCache(conversationId);
                            
                            // 再次尝试获取，确保有ID
                            try {
                                RestResult<BaseConversation> getResult = clientInter.get(conversationId, newConversation.getUserId());
                                if (getResult != null && getResult.isSuccess() && getResult.getContent() != null) {
                                    apiConversation = getResult.getContent();
                                    log.info("获取新创建会话成功，ID: {}", apiConversation.getId());
                                }
                            } catch (Exception e) {
                                log.error("获取新创建会话异常: {}", e.getMessage());
                            }
                        }
                    } catch (Exception e) {
                        log.error("直接创建会话异常: {}", e.getMessage());
                    }
                } else {
                    log.info("会话ID已在缓存中，跳过创建: {}", conversationId);
                }
                
                // 如果仍然为null，则使用临时对象
                if (apiConversation == null) {
                    apiConversation = BaseConversation.builder()
                            .conversationId(conversationId)
                            .title("临时会话-" + conversationId.substring(0, Math.min(8, conversationId.length())))
                            .content("")
                            .userId(getCurrentUserId())  // 使用当前用户ID
                            .createdTime(new Date())
                            .updatedTime(new Date())
                            .build();
                    log.info("使用临时会话对象: {}", conversationId);
                }
            }
            
            // 3. 处理会话内容
            List<Message> existingMessages = new ArrayList<>();
            
            // 解析现有消息
            if (StringUtils.hasText(apiConversation.getContent())) {
                try {
                    List<MessageWrapper> wrappers = MessageWrapper.fromConversationStr(apiConversation.getContent());
                    if (wrappers != null && !wrappers.isEmpty()) {
                        for (MessageWrapper wrapper : wrappers) {
                            Message msg = convertToSpringAiMessage(wrapper);
                            if (msg != null) {
                                existingMessages.add(msg);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("解析现有消息异常: {}", e.getMessage());
                }
            }
            
            // 4. 合并新消息
            existingMessages.addAll(messages);
            
            // 5. 转换为API消息格式
            List<MessageWrapper> allWrappers = existingMessages.stream()
                    .map(this::convertToApiMessageWrapper)
                    .filter(w -> w != null)
                    .collect(Collectors.toList());
            
            // 6. 更新会话内容
            String conversationStr = MessageWrapper.toConversationStr(allWrappers);
            apiConversation.setContent(conversationStr);
            
            // 7. 保存会话
            try {
                // 确保ID字段正确
                if (apiConversation.getId() == null) {
                    log.warn("会话对象ID为空，尝试通过conversationId查询: {}", conversationId);
                    RestResult<BaseConversation> idResult = clientInter.get(conversationId, apiConversation.getUserId());
                    if (idResult != null && idResult.isSuccess() && idResult.getContent() != null) {
                        apiConversation.setId(idResult.getContent().getId());
                        apiConversation.setUserId(idResult.getContent().getUserId());
                        // 保留创建时间
                        if (idResult.getContent().getCreatedTime() != null) {
                            apiConversation.setCreatedTime(idResult.getContent().getCreatedTime());
                        }
                    }
                }
                
                // 设置更新时间
                apiConversation.setUpdatedTime(new Date());
                
                // 确保用户ID有值
                if (apiConversation.getUserId() <= 0) {
                    apiConversation.setUserId(getCurrentUserId()); // 使用当前用户ID
                }
                
                RestResult<Void> saveResult = clientInter.edit(apiConversation, apiConversation.getUserId());
                if (saveResult != null && saveResult.isSuccess()) {
                    log.debug("保存会话成功: {}, 消息数: {}", conversationId, allWrappers.size());
                } else {
                    log.error("保存会话失败: {}, 消息数: {}", saveResult, allWrappers.size());
                }
            } catch (Exception e) {
                log.error("保存会话异常: {}", e.getMessage());
            }
        } catch (Exception e) {
            log.error("处理对话记忆异常: {}", e.getMessage(), e);
        }
    }

    @Override
    public List<Message> get(String conversationId, int lastN) {
        if (conversationId == null || conversationId.trim().isEmpty()) {
            log.warn("会话ID为空，无法获取消息");
            return List.of();
        }
        
        try {
            // 尝试获取会话
            RestResult<BaseConversation> result = clientInter.get(conversationId, getCurrentUserId());  // 使用当前用户ID
            if (result == null || !result.isSuccess()) {
                log.error("获取会话失败: {}", result);
                return List.of();
            }
            
            BaseConversation conversation = result.getContent();
            if (conversation == null) {
                log.warn("会话内容为空，返回空消息列表: {}", conversationId);
                return List.of();
            }
            
            // 如果内容为空，返回空列表
            if (!StringUtils.hasText(conversation.getContent())) {
                log.debug("会话没有消息内容: {}", conversationId);
                return List.of();
            }
            
            // 解析消息
            List<Message> messages = new ArrayList<>();
            try {
                List<MessageWrapper> wrappers = MessageWrapper.fromConversationStr(conversation.getContent());
                if (wrappers != null) {
                    for (MessageWrapper wrapper : wrappers) {
                        Message msg = convertToSpringAiMessage(wrapper);
                        if (msg != null) {
                            messages.add(msg);
                        }
                    }
                    log.debug("成功获取会话消息: {}, 消息数: {}", conversationId, messages.size());
                }
            } catch (Exception e) {
                log.error("解析会话消息异常: {}", e.getMessage());
            }
            
            // 返回最后N条消息
            if (lastN <= 0 || lastN >= messages.size()) {
                return messages;
            } else {
                return messages.subList(messages.size() - lastN, messages.size());
            }
        } catch (Exception e) {
            log.error("获取对话记忆异常: {}", e.getMessage(), e);
            return List.of();
        }
    }

    @Override
    public void clear(String conversationId) {
        if (conversationId == null || conversationId.trim().isEmpty()) {
            log.warn("会话ID为空，无法清除");
            return;
        }
        
        try {
            // 删除会话
            RestResult<Void> result = clientInter.del(conversationId);
            
            // 无论是否成功，都从缓存中移除
            sessionCache.remove(conversationId);
            
            if (result != null && result.isSuccess()) {
                log.info("成功清除会话: {}", conversationId);
            } else {
                log.error("API调用清除会话失败，但已从本地缓存移除: {}", result);
            }
        } catch (Exception e) {
            // 即使调用失败，也尝试从缓存中移除
            sessionCache.remove(conversationId);
            log.error("清除对话记忆异常: {}", e.getMessage(), e);
        }
    }
    
    // Spring AI Message 转换为 wenshuapi MessageWrapper
    private MessageWrapper convertToApiMessageWrapper(Message message) {
        if (message == null) {
            return null;
        }
        
        try {
            MessageWrapper wrapper = new MessageWrapper();
            wrapper.setContent(message.getContent());
            
            if (message instanceof AssistantMessage) {
                wrapper.setType(MessageWrapper.MessageType.ASSISTANT);
            } else if (message instanceof UserMessage) {
                wrapper.setType(MessageWrapper.MessageType.USER);
            } else if (message instanceof SystemMessage) {
                wrapper.setType(MessageWrapper.MessageType.SYSTEM);
            } else {
                wrapper.setType(MessageWrapper.MessageType.TOOL);
            }
            
            return wrapper;
        } catch (Exception e) {
            log.error("转换消息异常: {}", e.getMessage());
            return null;
        }
    }
    
    // wenshuapi MessageWrapper 转换为 Spring AI Message
    private Message convertToSpringAiMessage(MessageWrapper wrapper) {
        if (wrapper == null) {
            return null;
        }
        
        try {
            if (wrapper.getType() == null) {
                // 默认作为用户消息处理
                return new UserMessage(wrapper.getContent());
            }
            
            switch (wrapper.getType()) {
                case ASSISTANT:
                    return new AssistantMessage(wrapper.getContent());
                case USER:
                    return new UserMessage(wrapper.getContent());
                case SYSTEM:
                    return new SystemMessage(wrapper.getContent());
                case TOOL:
                default:
                    return new UserMessage(wrapper.getContent());
            }
        } catch (Exception e) {
            log.error("转换消息类型异常: {}", e.getMessage());
            return null;
        }
    }

    // 添加对话ID到缓存
    private void addToCache(String conversationId) {
        if (conversationId != null && !conversationId.trim().isEmpty()) {
            sessionCache.put(conversationId, true);
        }
    }
    
    // 检查缓存中是否存在会话
    private boolean isInCache(String conversationId) {
        return conversationId != null && sessionCache.containsKey(conversationId);
    }
}

