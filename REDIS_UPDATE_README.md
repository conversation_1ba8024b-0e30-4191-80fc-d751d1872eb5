# Redis版本更新指南

## 更新内容

已将Redis版本从 `7.0.0-alpine` 更新到 `7.4.4`

### 📋 修改的文件

1. **docker-compose.env.yml** - 主要的Redis配置文件
2. **docker-compose.yml** - 备用配置文件（已注释）

### 🚀 版本更新的好处

#### Redis 7.4.4新特性和改进：

1. **性能优化**
   - 更快的内存分配和释放
   - 优化的网络I/O处理
   - 改进的命令执行效率

2. **内存管理**
   - 更精确的内存使用统计
   - 优化的内存碎片整理
   - 改进的过期键清理机制

3. **安全性增强**
   - 修复了多个安全漏洞
   - 增强的访问控制
   - 改进的SSL/TLS支持

4. **稳定性提升**
   - 修复了崩溃和内存泄漏问题
   - 更好的错误处理
   - 改进的持久化机制

5. **功能增强**
   - 新的Redis命令和选项
   - 改进的集群支持
   - 更好的监控和诊断工具

## 🛠️ 更新步骤

### 方法1: 使用自动化脚本（推荐）

#### Linux/macOS:
```bash
chmod +x update-redis.sh
./update-redis.sh
```

#### Windows:
```cmd
update-redis.bat
```

### 方法2: 手动更新

#### 1. 停止现有服务
```bash
docker compose -f docker-compose.env.yml down redis
```

#### 2. 拉取新镜像
```bash
docker pull lcr.loongnix.cn/library/redis:7.4.4
```

#### 3. 备份数据（可选但推荐）
```bash
# 创建数据备份
timestamp=$(date +%Y%m%d_%H%M%S)
docker run --rm -v wenshuintelligentcomputing-back_redis_data:/data -v $(pwd):/backup \
  lcr.loongnix.cn/library/redis:7.4.4 \
  tar czf /backup/redis_backup_${timestamp}.tar.gz -C /data .
```

#### 4. 启动新版本
```bash
docker compose -f docker-compose.env.yml up -d redis
```

#### 5. 重启依赖服务
```bash
docker compose -f docker-compose.app.yml restart auth system gateway
```

## ✅ 验证更新

### 1. 检查Redis版本
```bash
docker exec wenshu-redis redis-cli info server | grep redis_version
```

### 2. 测试基本功能
```bash
# 测试读写
docker exec wenshu-redis redis-cli set test_key "Hello Redis 7.4.4"
docker exec wenshu-redis redis-cli get test_key
docker exec wenshu-redis redis-cli del test_key
```

### 3. 检查配置
```bash
# 查看内存配置
docker exec wenshu-redis redis-cli config get maxmemory

# 查看持久化配置
docker exec wenshu-redis redis-cli config get save
```

### 4. 监控状态
```bash
# 查看Redis状态
docker exec wenshu-redis redis-cli info

# 查看连接的客户端
docker exec wenshu-redis redis-cli client list
```

## 📊 当前Redis配置

### 基本配置
- **版本**: 7.4.4
- **端口**: 6379
- **最大内存**: 96MB
- **内存策略**: allkeys-lru
- **数据库数量**: 8
- **最大客户端**: 50

### 持久化配置
- **AOF**: 启用 (appendonly yes)
- **RDB快照**: 
  - 900秒内至少1个键变化
  - 300秒内至少10个键变化
  - 60秒内至少1000个键变化

### 网络配置
- **TCP Keep-Alive**: 60秒
- **客户端超时**: 300秒

## 🔧 性能优化建议

### 1. 内存优化
如果需要更多内存，可以调整配置：
```yaml
# 在docker-compose.env.yml中修改
command:
  - --maxmemory 128mb  # 增加到128MB
```

### 2. 连接数优化
如果需要更多连接：
```yaml
command:
  - --maxclients 100   # 增加到100个客户端
```

### 3. 持久化优化
根据需求调整持久化策略：
```yaml
command:
  - --save 60 1000     # 更频繁的快照
  - --appendfsync everysec  # AOF同步策略
```

## 🚨 故障排除

### 问题1: Redis启动失败
```bash
# 查看日志
docker compose -f docker-compose.env.yml logs redis

# 常见原因：
# - 内存不足
# - 端口被占用
# - 数据文件损坏
```

### 问题2: 连接被拒绝
```bash
# 检查Redis是否运行
docker ps --filter "name=wenshu-redis"

# 检查端口
netstat -tlnp | grep 6379

# 测试连接
docker exec wenshu-redis redis-cli ping
```

### 问题3: 内存不足
```bash
# 查看内存使用
docker exec wenshu-redis redis-cli info memory

# 清理过期键
docker exec wenshu-redis redis-cli flushdb
```

### 问题4: 数据丢失
```bash
# 恢复备份（如果有）
docker run --rm -v wenshuintelligentcomputing-back_redis_data:/data -v $(pwd):/backup \
  lcr.loongnix.cn/library/redis:7.4.4 \
  tar xzf /backup/redis_backup_TIMESTAMP.tar.gz -C /data
```

## 📈 监控和维护

### 1. 定期监控
```bash
# 监控内存使用
docker exec wenshu-redis redis-cli info memory

# 监控连接数
docker exec wenshu-redis redis-cli info clients

# 监控命令统计
docker exec wenshu-redis redis-cli info commandstats
```

### 2. 定期备份
建议设置定期备份任务：
```bash
# 创建备份脚本
#!/bin/bash
timestamp=$(date +%Y%m%d_%H%M%S)
docker run --rm -v wenshuintelligentcomputing-back_redis_data:/data -v /backup:/backup \
  lcr.loongnix.cn/library/redis:7.4.4 \
  tar czf /backup/redis_backup_${timestamp}.tar.gz -C /data .

# 删除7天前的备份
find /backup -name "redis_backup_*.tar.gz" -mtime +7 -delete
```

### 3. 性能调优
```bash
# 查看慢查询
docker exec wenshu-redis redis-cli slowlog get 10

# 查看键空间统计
docker exec wenshu-redis redis-cli info keyspace
```

## 🔄 回滚方案

如果更新后出现问题，可以回滚：

```bash
# 1. 停止新版本
docker compose -f docker-compose.env.yml down redis

# 2. 修改配置文件，改回旧版本
# 编辑 docker-compose.env.yml，将镜像版本改为：
# image: lcr.loongnix.cn/library/redis:7.0.0-alpine

# 3. 恢复数据（如果有备份）
docker run --rm -v wenshuintelligentcomputing-back_redis_data:/data -v $(pwd):/backup \
  lcr.loongnix.cn/library/redis:7.0.0-alpine \
  tar xzf /backup/redis_backup_TIMESTAMP.tar.gz -C /data

# 4. 重新启动
docker compose -f docker-compose.env.yml up -d redis
```

## 📞 获取帮助

如果遇到问题：

1. **查看日志**: `docker compose -f docker-compose.env.yml logs redis`
2. **检查配置**: `docker exec wenshu-redis redis-cli config get '*'`
3. **测试连接**: `docker exec wenshu-redis redis-cli ping`
4. **查看状态**: `docker exec wenshu-redis redis-cli info`

更多Redis 7.4.4的详细信息，请参考官方文档。
