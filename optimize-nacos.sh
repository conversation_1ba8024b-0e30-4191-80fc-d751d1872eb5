#!/bin/bash

# Nacos优化脚本 - 解决多服务连接时重启问题
echo "=== Nacos优化脚本 ==="
echo "解决多服务连接时Nacos自动重启的问题"
echo ""

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ 错误: Docker未运行，请先启动Docker"
    exit 1
fi

echo "✅ Docker运行正常"

# 1. 停止现有服务
echo ""
echo "1. 停止现有服务..."
docker compose -f docker-compose.env.yml down nacos 2>/dev/null
docker compose -f docker-compose.app.yml down auth gateway system 2>/dev/null

# 2. 清理Nacos数据（可选，重新开始）
read -p "是否清理Nacos数据重新开始？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "   清理Nacos数据卷..."
    docker volume rm wenshuintelligentcomputing-back_nacos_data 2>/dev/null || echo "   数据卷不存在或正在使用"
fi

# 3. 检查系统资源
echo ""
echo "2. 检查系统资源..."
total_mem=$(free -m | awk 'NR==2{printf "%.0f", $2}')
available_mem=$(free -m | awk 'NR==2{printf "%.0f", $7}')

echo "   总内存: ${total_mem}MB"
echo "   可用内存: ${available_mem}MB"

if [ $available_mem -lt 2048 ]; then
    echo "⚠️  警告: 可用内存不足2GB，可能影响Nacos稳定性"
    echo "   建议释放一些内存或增加系统内存"
fi

# 4. 修复数据库表结构
echo ""
echo "3. 修复数据库表结构..."
if [ -f "fix-nacos-db.sql" ]; then
    # 确保MySQL运行
    if ! docker ps --filter "name=wenshu-mysql" --format "{{.Names}}" | grep -q "wenshu-mysql"; then
        echo "   启动MySQL..."
        docker compose -f docker-compose.env.yml up -d mysql
        sleep 15
    fi
    
    echo "   执行数据库修复..."
    docker exec -i wenshu-mysql mysql -uroot -p2313147023 < fix-nacos-db.sql
    if [ $? -eq 0 ]; then
        echo "✅ 数据库表结构修复成功"
    else
        echo "⚠️  数据库修复失败，但继续启动"
    fi
else
    echo "   修复脚本不存在，跳过"
fi

# 5. 启动优化后的Nacos
echo ""
echo "4. 启动优化后的Nacos..."

# 确保基础服务运行
echo "   检查基础服务..."
if ! docker ps --filter "name=wenshu-mysql" --format "{{.Names}}" | grep -q "wenshu-mysql"; then
    echo "   启动MySQL..."
    docker compose -f docker-compose.env.yml up -d mysql
    sleep 10
fi

if ! docker ps --filter "name=wenshu-redis" --format "{{.Names}}" | grep -q "wenshu-redis"; then
    echo "   启动Redis..."
    docker compose -f docker-compose.env.yml up -d redis
    sleep 5
fi

# 启动优化后的Nacos
echo "   启动Nacos（优化配置）..."
docker compose -f docker-compose.env.yml up -d nacos

# 6. 等待Nacos完全启动
echo ""
echo "5. 等待Nacos启动完成..."
echo "   这可能需要1-2分钟，请耐心等待..."

for i in {1..60}; do
    if curl -f http://localhost:8848/nacos/actuator/health > /dev/null 2>&1; then
        echo "✅ Nacos启动成功！"
        break
    fi
    
    # 每10秒显示一次进度
    if [ $((i % 10)) -eq 0 ]; then
        echo "   等待Nacos启动... (${i}/60秒)"
    fi
    sleep 1
done

# 检查Nacos是否成功启动
if ! curl -f http://localhost:8848/nacos/actuator/health > /dev/null 2>&1; then
    echo "❌ Nacos启动失败！"
    echo ""
    echo "请检查日志："
    echo "   docker compose -f docker-compose.env.yml logs nacos"
    echo ""
    echo "常见问题："
    echo "   1. 内存不足 - 增加系统内存或关闭其他服务"
    echo "   2. 端口占用 - 检查8848端口是否被占用"
    echo "   3. 数据库连接失败 - 检查MySQL是否正常运行"
    exit 1
fi

# 7. 逐个启动应用服务
echo ""
echo "6. 启动应用服务..."

services=("auth" "system" "gateway")
for service in "${services[@]}"; do
    echo "   启动 $service 服务..."
    docker compose -f docker-compose.app.yml up -d $service
    
    # 等待服务启动
    sleep 20
    
    # 检查Nacos是否还在运行
    if ! docker ps --filter "name=wenshu-nacos" --format "{{.Names}}" | grep -q "wenshu-nacos"; then
        echo "❌ Nacos在启动 $service 后停止运行！"
        echo "   正在重启Nacos..."
        docker compose -f docker-compose.env.yml up -d nacos
        sleep 30
    else
        echo "✅ $service 服务启动成功，Nacos运行正常"
    fi
done

# 8. 最终检查
echo ""
echo "7. 最终状态检查..."
echo "=== 容器状态 ==="
docker ps --filter "name=wenshu-" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo "=== 服务健康检查 ==="
services_check=("nacos:8848" "auth:9200" "gateway:8080")
all_healthy=true

for service in "${services_check[@]}"; do
    name=$(echo $service | cut -d: -f1)
    port=$(echo $service | cut -d: -f2)
    
    if curl -f http://localhost:$port/actuator/health > /dev/null 2>&1; then
        echo "✅ $name 服务正常"
    else
        echo "❌ $name 服务异常"
        all_healthy=false
    fi
done

echo ""
if [ "$all_healthy" = true ]; then
    echo "🎉 所有服务启动成功！Nacos优化完成！"
    echo ""
    echo "访问地址："
    echo "  - Nacos控制台: http://localhost:8848/nacos"
    echo "  - 网关服务: http://localhost:8080"
    echo ""
    echo "优化内容："
    echo "  - JVM堆内存: 512MB → 1024MB"
    echo "  - 容器内存限制: 512MB → 1280MB"
    echo "  - Tomcat最大线程: 400"
    echo "  - 数据库连接池: 50"
    echo "  - 使用G1垃圾收集器"
else
    echo "⚠️  部分服务异常，请检查日志"
    echo ""
    echo "查看日志命令："
    echo "  docker compose -f docker-compose.env.yml logs nacos"
    echo "  docker compose -f docker-compose.app.yml logs auth"
    echo "  docker compose -f docker-compose.app.yml logs gateway"
fi

echo ""
echo "=== 监控建议 ==="
echo "1. 监控Nacos内存使用: docker stats wenshu-nacos"
echo "2. 查看Nacos日志: docker compose -f docker-compose.env.yml logs -f nacos"
echo "3. 如果仍有问题，可以进一步增加内存配置"
