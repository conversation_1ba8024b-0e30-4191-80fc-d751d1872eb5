package com.ruoyi.wenshuaudit.common;

import lombok.Data;

/**
 * 统一响应结果类
 */
@Data
public class RestResult<T> {

    private boolean success = true;
    
    private String msg = "";
    
    private String code = "0000";
    
    private T content;

    /**
     * 快速构建无内容成功结果
     */
    public static <T> RestResult<T> buildSuccessResult() {
        return new RestResult<>();
    }

    /**
     * 快速构建带内容成功结果
     */
    public static <T> RestResult<T> buildSuccessResult(T content) {
        RestResult<T> result = new RestResult<>();
        result.setContent(content);
        return result;
    }
    
    /**
     * 构建错误结果
     */
    public static <T> RestResult<T> buildErrorResult(String code, String msg) {
        RestResult<T> result = new RestResult<>();
        result.setSuccess(false);
        result.setCode(code);
        result.setMsg(msg);
        return result;
    }
} 