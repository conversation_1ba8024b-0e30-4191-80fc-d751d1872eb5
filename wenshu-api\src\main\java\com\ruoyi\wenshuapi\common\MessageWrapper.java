package com.ruoyi.wenshuapi.common;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessageWrapper {

    private MessageType type;

    private String content;

    public static String toConversationStr(List<MessageWrapper> messages) {
        return JsonUtil.toJsonString(messages);
    }

    public static List<MessageWrapper> fromConversationStr(String conversationStr) {
        if(conversationStr == null || conversationStr.isEmpty()){
            return new ArrayList<>();
        }
        TypeReference<List<MessageWrapper>> typeReference = new TypeReference<>() {
        };
        return JsonUtil.toJsonObject(conversationStr, typeReference);
    }
    
    /**
     * 消息类型枚举
     */
    public enum MessageType {
        USER,
        ASSISTANT,
        SYSTEM,
        TOOL
    }
} 