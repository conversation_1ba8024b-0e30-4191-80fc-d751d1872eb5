package com.ruoyi.wenshulivechat.model;

import com.ruoyi.wenshuapi.pojo.wchat.ChatRecord;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 新消息查询响应模型
 * 用于WebSocket服务端返回新消息查询结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewMessageResponse {
    
    /**
     * 查询状态
     */
    private Boolean success;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 是否有新消息需要提醒
     */
    private Boolean hasNewMessages;

    /**
     * 需要提醒的新消息列表（3秒内的未读消息）
     */
    private List<ChatRecord> newMessages;

    /**
     * 未读消息总数
     */
    private Integer unreadCount;

    /**
     * 需要提醒的消息数量
     */
    private Integer notificationCount;

    /**
     * 查询时间戳
     */
    private Long timestamp;

    /**
     * 查询的用户ID
     */
    private Long userId;

    /**
     * 响应类型（notification: 提醒消息, subscription: 订阅消息）
     */
    private String responseType;

    /**
     * 详细统计信息
     */
    private MessageStatistics statistics;
    
    /**
     * 创建成功响应（提醒消息）
     */
    public static NewMessageResponse success(List<ChatRecord> messages, Integer unreadCount, Long userId, MessageStatistics statistics) {
        NewMessageResponse response = new NewMessageResponse();
        response.setSuccess(true);
        response.setMessage("查询成功");
        response.setHasNewMessages(messages != null && !messages.isEmpty());
        response.setNewMessages(messages);
        response.setUnreadCount(unreadCount);
        response.setNotificationCount(messages != null ? messages.size() : 0);
        response.setTimestamp(System.currentTimeMillis());
        response.setUserId(userId);
        response.setResponseType("notification");
        response.setStatistics(statistics);
        return response;
    }

    /**
     * 创建订阅响应（所有未读消息）
     */
    public static NewMessageResponse subscription(List<ChatRecord> allUnreadMessages, Long userId, MessageStatistics statistics) {
        NewMessageResponse response = new NewMessageResponse();
        response.setSuccess(true);
        response.setMessage("订阅成功");
        response.setHasNewMessages(allUnreadMessages != null && !allUnreadMessages.isEmpty());
        response.setNewMessages(allUnreadMessages);
        response.setUnreadCount(allUnreadMessages != null ? allUnreadMessages.size() : 0);
        response.setNotificationCount(0); // 订阅不是提醒，设为0
        response.setTimestamp(System.currentTimeMillis());
        response.setUserId(userId);
        response.setResponseType("subscription");
        response.setStatistics(statistics);
        return response;
    }
    
    /**
     * 创建失败响应
     */
    public static NewMessageResponse error(String message, Long userId, String responseType) {
        NewMessageResponse response = new NewMessageResponse();
        response.setSuccess(false);
        response.setMessage(message);
        response.setHasNewMessages(false);
        response.setNewMessages(null);
        response.setUnreadCount(0);
        response.setNotificationCount(0);
        response.setTimestamp(System.currentTimeMillis());
        response.setUserId(userId);
        response.setResponseType(responseType != null ? responseType : "error");
        response.setStatistics(null);
        return response;
    }

    /**
     * 消息统计信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MessageStatistics {
        /**
         * 总未读消息数
         */
        private Integer totalUnreadCount;

        /**
         * 需要提醒的消息数（3秒内）
         */
        private Integer notificationCount;

        /**
         * 过滤掉的自发消息数
         */
        private Integer selfSentFilteredCount;

        /**
         * 过滤掉的超时消息数（超过3秒）
         */
        private Integer timeoutFilteredCount;

        /**
         * 查询时间
         */
        private String queryTime;

        /**
         * 最新消息时间
         */
        private String latestMessageTime;

        /**
         * 最旧未读消息时间
         */
        private String oldestUnreadTime;
    }
}
