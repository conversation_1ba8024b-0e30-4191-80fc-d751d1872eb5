package com.ruoyi.wenshulivechat.service;

import com.ruoyi.wenshulivechat.model.NewMessageRequest;
import com.ruoyi.wenshulivechat.model.NewMessageResponse;

/**
 * 实时聊天服务接口
 * 提供WebSocket相关的业务逻辑
 */
public interface LiveChatService {
    
    /**
     * 查询用户的新消息（提醒模式，只返回3秒内的未读消息）
     *
     * @param request 查询请求
     * @return 新消息响应
     */
    NewMessageResponse queryNewMessages(NewMessageRequest request);

    /**
     * 订阅用户的所有未读消息
     *
     * @param userId 用户ID
     * @return 所有未读消息响应
     */
    NewMessageResponse subscribeAllUnreadMessages(Long userId);

    /**
     * 获取用户未读消息数量
     *
     * @param userId 用户ID
     * @return 未读消息数量
     */
    Integer getUnreadMessageCount(Long userId);
}
