package com.ruoyi.wenshuchat.config;

import com.alibaba.cloud.ai.dashscope.api.DashScopeApi;
import com.alibaba.cloud.ai.dashscope.chat.DashScopeChatModel;
import com.alibaba.cloud.ai.dashscope.chat.DashScopeChatOptions;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ModelInit {
    /**
     * 系统提示词，指导AI如何使用日程管理函数
     */
    private static final String SYSTEM_PROMPT = """
        你是一个专业的智能办公助手
        """;
    
    @Bean
    public DashScopeChatModel dashScopeChatModel() {
        // 使用环境变量或默认值设置API密钥
        String apiKey = System.getenv("DASHSCOPE_API_KEY");
        if (apiKey == null || apiKey.isEmpty()) {
            apiKey = "sk-a4b80017093447aab1688acad39d24b6"; // 默认测试密钥
        }
        
        // 创建DashScopeApi实例
        DashScopeApi dashScopeApi = new DashScopeApi(apiKey);
        
        // 创建配置选项
        DashScopeChatOptions options = DashScopeChatOptions.builder()
                .withModel("qwen-max") // 默认使用通义千问Max模型
                .build();
        
        // 创建并返回DashScopeChatModel实例
        return new DashScopeChatModel(dashScopeApi, options);
    }
    
    @Bean
    public ChatClient getChatClient(DashScopeChatModel dashScopeChatModel) {
        // 为DashScope模型配置提示词
        return ChatClient
                .builder(dashScopeChatModel)
                .defaultSystem(SYSTEM_PROMPT)
                .build();
    }
}
