package com.ruoyi.wenshufile.controller;

import com.ruoyi.wenshuapi.pojo.file.FileInfoPojo;
import com.ruoyi.wenshuapi.util.file.ApiResponse;
import com.ruoyi.wenshufile.service.FileInfoService;
import com.ruoyi.wenshufile.service.FileStorageService;
import com.ruoyi.wenshufile.utils.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文件信息控制器
 * 提供文件管理的RESTful API接口，包括文件上传、存储、删除等功能
 * 顶层路径：/wenshu/fileinfo
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-06-28
 */
@RestController
@RequestMapping("/wenshu/fileinfo")
public class FileInfoController {

    private static final Logger logger = LoggerFactory.getLogger(FileInfoController.class);

    @Autowired
    private FileInfoService fileInfoService;

    @Autowired
    private FileStorageService fileStorageService;

    /**
     * 文件上传接口
     * 支持所有文件类型，单个文件不超过100MB
     * 上传成功后自动保存文件信息到数据库
     *
     * @param file 上传的文件
     * @param uploaderId 上传者ID（可选）
     * @param ownerType 所有者类型（个人/团队，默认为个人）
     * @param fileStatus 文件状态（可读写/协作/不可读写，默认为可读写）
     * @return 上传结果，包含文件信息和访问URL
     */
    @PostMapping("/upload")
    public ApiResponse<Map<String, Object>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "uploaderId", required = false) Integer uploaderId,
            @RequestParam(value = "ownerType", defaultValue = "个人") String ownerType,
            @RequestParam(value = "fileStatus", defaultValue = "可读写") String fileStatus) {

        logger.info("开始处理文件上传请求: 文件名={}, 上传者ID={}, 所有者类型={}, 文件状态={}",
                   file.getOriginalFilename(), uploaderId, ownerType, fileStatus);

        try {
            // 1. 验证文件
            FileUtils.FileValidationResult validationResult = FileUtils.validateFile(file);
            if (!validationResult.isValid()) {
                logger.warn("文件验证失败: {}", validationResult.getMessage());
                return ApiResponse.validateFailed(validationResult.getMessage());
            }

            // 2. 上传文件到服务器
            String filePath = fileStorageService.uploadFile(file, uploaderId);
            logger.info("文件上传成功: {} -> {}", file.getOriginalFilename(), filePath);

            // 3. 创建文件信息对象
            FileInfoPojo fileInfo = new FileInfoPojo();
            fileInfo.setFileName(file.getOriginalFilename());
            fileInfo.setFilePath(filePath);
            fileInfo.setUploadTime(LocalDateTime.now());
            fileInfo.setUploaderId(uploaderId);
            fileInfo.setFileSize(file.getSize());
            fileInfo.setFileStatus(fileStatus);
            fileInfo.setOwnerType(ownerType);

            // 4. 保存文件信息到数据库
            int result = fileInfoService.addFile(fileInfo);

            if (result > 0) {
                // 5. 构建返回结果
                Map<String, Object> responseData = new HashMap<>();
                responseData.put("fileId", fileInfo.getFileId());
                responseData.put("fileName", fileInfo.getFileName());
                responseData.put("filePath", filePath);
                responseData.put("fileSize", file.getSize());
                responseData.put("fileSizeFormatted", FileUtils.formatFileSize(file.getSize()));
                responseData.put("mimeType", FileUtils.getMimeType(file.getOriginalFilename()));
                responseData.put("accessUrl", fileStorageService.getFileAccessUrl(filePath));
                responseData.put("uploadTime", fileInfo.getUploadTime());
                responseData.put("uploaderId", uploaderId);
                responseData.put("ownerType", ownerType);
                responseData.put("fileStatus", fileStatus);

                logger.info("文件上传完成: 文件ID={}, 访问URL={}",
                           fileInfo.getFileId(), responseData.get("accessUrl"));

                return ApiResponse.success(responseData, "文件上传成功");
            } else {
                // 如果数据库保存失败，删除已上传的文件
                fileStorageService.deleteFile(filePath);
                logger.error("文件信息保存失败，已删除上传的文件: {}", filePath);
                return ApiResponse.failed("文件信息保存失败");
            }

        } catch (IllegalArgumentException e) {
            logger.warn("文件上传参数错误: {}", e.getMessage());
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            logger.error("文件上传失败: {}", e.getMessage(), e);
            return ApiResponse.failed("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 添加文件信息
     *
     * @param fileInfo 文件信息对象
     * @return 操作结果
     */
    @PostMapping("/add")
    public ApiResponse<Integer> addFile(@RequestBody FileInfoPojo fileInfo) {
        try {
            int result = fileInfoService.addFile(fileInfo);
            return ApiResponse.success(result, "文件添加成功");
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("文件添加失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件信息
     * 同时删除数据库记录和服务器上的物理文件
     *
     * @param fileId 文件ID
     * @return 操作结果
     */
    @DeleteMapping("/delete/{fileId}")
    public ApiResponse<Integer> deleteFile(@PathVariable("fileId") int fileId) {
        logger.info("开始删除文件: 文件ID={}", fileId);

        try {
            // 1. 先获取文件信息，用于删除物理文件
            FileInfoPojo fileInfo = fileInfoService.getFileById(fileId);
            if (fileInfo == null) {
                logger.warn("文件删除失败: 文件不存在，文件ID={}", fileId);
                return ApiResponse.failed("文件不存在");
            }

            String filePath = fileInfo.getFilePath();
            String fileName = fileInfo.getFileName();

            // 2. 删除数据库记录
            int result = fileInfoService.deleteFile(fileId);
            if (result > 0) {
                // 3. 删除物理文件
                boolean fileDeleted = false;
                if (filePath != null && !filePath.trim().isEmpty()) {
                    fileDeleted = fileStorageService.deleteFile(filePath);
                    if (fileDeleted) {
                        logger.info("物理文件删除成功: {}", filePath);
                    } else {
                        logger.warn("物理文件删除失败或文件不存在: {}", filePath);
                    }
                }

                logger.info("文件删除完成: 文件ID={}, 文件名={}, 数据库记录已删除, 物理文件删除={}",
                           fileId, fileName, fileDeleted ? "成功" : "失败或不存在");

                return ApiResponse.success(result, "文件删除成功");
            } else {
                logger.warn("文件删除失败: 数据库记录删除失败，文件ID={}", fileId);
                return ApiResponse.failed("文件删除失败");
            }

        } catch (IllegalStateException e) {
            logger.warn("文件删除失败: {}", e.getMessage());
            return ApiResponse.failed(e.getMessage(), 403);
        } catch (Exception e) {
            logger.error("文件删除失败: 文件ID={}, 错误信息={}", fileId, e.getMessage(), e);
            return ApiResponse.failed("文件删除失败: " + e.getMessage());
        }
    }

    /**
     * 更新文件信息
     *
     * @param fileInfo 文件信息对象
     * @return 操作结果
     */
    @PutMapping("/update")
    public ApiResponse<Integer> updateFile(@RequestBody FileInfoPojo fileInfo) {
        try {
            int result = fileInfoService.updateFile(fileInfo);
            if (result > 0) {
                return ApiResponse.success(result, "文件更新成功");
            }
            return ApiResponse.failed("文件不存在或更新失败");
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("文件更新失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取文件详情
     *
     * @param fileId 文件ID
     * @return 文件信息
     */
    @GetMapping("/get/{fileId}")
    public ApiResponse<FileInfoPojo> getFileById(@PathVariable("fileId") int fileId) {
        try {
            FileInfoPojo file = fileInfoService.getFileById(fileId);
            if (file != null) {
                return ApiResponse.success(file, "文件查询成功");
            }
            return ApiResponse.failed("文件不存在");
        } catch (Exception e) {
            return ApiResponse.failed("文件查询失败: " + e.getMessage());
        }
    }

    /**
     * 条件查询文件列表
     *
     * @param condition 查询条件
     * @return 文件列表
     */
    @GetMapping("/list")
    public ApiResponse<List<FileInfoPojo>> getFilesByCondition(FileInfoPojo condition) {
        try {
            List<FileInfoPojo> files = fileInfoService.getFilesByCondition(condition);
            return ApiResponse.success(files, "查询成功");
        } catch (Exception e) {
            return ApiResponse.failed("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据文件路径获取文件信息
     *
     * @param filePath 文件路径
     * @return 文件信息
     */
    @GetMapping("/byPath")
    public ApiResponse<FileInfoPojo> getFileByPath(@RequestParam("filePath") String filePath) {
        try {
            FileInfoPojo file = fileInfoService.getFileByPath(filePath);
            if (file != null) {
                return ApiResponse.success(file, "文件查询成功");
            }
            return ApiResponse.failed("文件不存在");
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("文件查询失败: " + e.getMessage());
        }
    }

    /**
     * 更新文件状态
     *
     * @param fileId 文件ID
     * @param status 新状态
     * @return 操作结果
     */
    @PutMapping("/updateStatus")
    public ApiResponse<Integer> changeFileStatus(
            @RequestParam("fileId") int fileId,
            @RequestParam("status") String status) {
        try {
            int result = fileInfoService.changeFileStatus(fileId, status);
            if (result > 0) {
                return ApiResponse.success(result, "状态更新成功");
            }
            return ApiResponse.failed("文件不存在或状态未变更");
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("状态更新失败: " + e.getMessage());
        }
    }

    /**
     * 统计用户上传的文件数量
     *
     * @param uploaderId 上传者ID
     * @return 文件数量
     */
    @GetMapping("/countByUser")
    public ApiResponse<Integer> countFilesByUploader(@RequestParam("uploaderId") int uploaderId) {
        try {
            int count = fileInfoService.countFilesByUploader(uploaderId);
            return ApiResponse.success(count, "统计成功");
        } catch (Exception e) {
            return ApiResponse.failed("统计失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新文件状态
     *
     * @param fileIds 文件ID列表
     * @param status 新状态
     * @return 操作结果
     */
    @PutMapping("/batchUpdateStatus")
    public ApiResponse<Integer> batchUpdateStatus(
            @RequestParam("fileIds") List<Integer> fileIds,
            @RequestParam("status") String status) {
        try {
            int result = fileInfoService.batchUpdateStatus(fileIds, status);
            return ApiResponse.success(result, "批量更新成功");
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("批量更新失败: " + e.getMessage());
        }
    }

    /**
     * 检查文件是否属于指定用户
     *
     * @param fileId 文件ID
     * @param userId 用户ID
     * @return 检查结果
     */
    @GetMapping("/isOwned")
    public ApiResponse<Boolean> isFileOwnedByUser(
            @RequestParam("fileId") int fileId,
            @RequestParam("userId") int userId) {
        try {
            boolean result = fileInfoService.isFileOwnedByUser(fileId, userId);
            return ApiResponse.success(result, "权限验证成功");
        } catch (Exception e) {
            return ApiResponse.failed("权限验证失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除文件
     * 同时删除数据库记录和服务器上的物理文件
     *
     * @param fileIds 文件ID列表
     * @return 删除结果
     */
    @DeleteMapping("/batchDelete")
    public ApiResponse<Map<String, Object>> batchDeleteFiles(@RequestParam("fileIds") List<Integer> fileIds) {
        logger.info("开始批量删除文件: 文件ID列表={}", fileIds);

        try {
            int successCount = 0;
            int failedCount = 0;
            int physicalFileDeletedCount = 0;

            for (Integer fileId : fileIds) {
                try {
                    // 1. 获取文件信息
                    FileInfoPojo fileInfo = fileInfoService.getFileById(fileId);
                    if (fileInfo == null) {
                        logger.warn("批量删除: 文件不存在，文件ID={}", fileId);
                        failedCount++;
                        continue;
                    }

                    String filePath = fileInfo.getFilePath();

                    // 2. 删除数据库记录
                    int result = fileInfoService.deleteFile(fileId);
                    if (result > 0) {
                        successCount++;

                        // 3. 删除物理文件
                        if (filePath != null && !filePath.trim().isEmpty()) {
                            boolean fileDeleted = fileStorageService.deleteFile(filePath);
                            if (fileDeleted) {
                                physicalFileDeletedCount++;
                                logger.debug("批量删除: 物理文件删除成功，文件ID={}, 路径={}", fileId, filePath);
                            } else {
                                logger.warn("批量删除: 物理文件删除失败或不存在，文件ID={}, 路径={}", fileId, filePath);
                            }
                        }
                    } else {
                        failedCount++;
                        logger.warn("批量删除: 数据库记录删除失败，文件ID={}", fileId);
                    }

                } catch (Exception e) {
                    failedCount++;
                    logger.error("批量删除: 处理文件失败，文件ID={}, 错误信息={}", fileId, e.getMessage(), e);
                }
            }

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", fileIds.size());
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            result.put("physicalFileDeletedCount", physicalFileDeletedCount);

            logger.info("批量删除完成: 总数={}, 成功={}, 失败={}, 物理文件删除={}",
                       fileIds.size(), successCount, failedCount, physicalFileDeletedCount);

            if (failedCount == 0) {
                return ApiResponse.success(result, "批量删除全部成功");
            } else if (successCount > 0) {
                return ApiResponse.success(result, "批量删除部分成功");
            } else {
                return ApiResponse.failed("批量删除全部失败", 500);
            }

        } catch (Exception e) {
            logger.error("批量删除失败: {}", e.getMessage(), e);
            return ApiResponse.failed("批量删除失败: " + e.getMessage());
        }
    }
}