server:
  port: 1015
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 2313147023
    url: *******************************************************************************************************************************************************************************************************


  application:
    # 应用名称
    name: wenshu-remind
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 8.155.31.217:8848
      config:
        # 配置中心地址
        server-addr: 8.155.31.217:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

# Jackson配置 - 解决LocalDateTime序列化问题
  jackson:
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss

# 日志配置
logging:
  config: classpath:logback-spring.xml
  level:
    com.ruoyi.wenshuremind: INFO
    com.ruoyi.wenshuapi.client: DEBUG
    org.springframework.web: DEBUG
    org.springframework.mail: DEBUG
    org.springframework.messaging: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
