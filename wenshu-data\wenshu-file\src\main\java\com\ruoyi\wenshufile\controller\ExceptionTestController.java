package com.ruoyi.wenshufile.controller;

import com.ruoyi.wenshuapi.util.file.ApiResponse;
import com.ruoyi.wenshufile.exception.FileStorageException;
import org.springframework.web.bind.annotation.*;

/**
 * 异常测试控制器
 * 用于测试各种异常情况的处理
 * 仅在开发和测试环境使用
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-28
 */
@RestController
@RequestMapping("/wenshu/fileinfo/test")
public class ExceptionTestController {

    /**
     * 测试HTTP方法不匹配异常
     * 这是一个POST接口，如果用GET访问会触发HttpRequestMethodNotSupportedException
     */
    @PostMapping("/method-not-allowed")
    public ApiResponse<String> testMethodNotAllowed() {
        return ApiResponse.success("这是一个POST接口");
    }

    /**
     * 测试参数验证异常
     */
    @GetMapping("/invalid-param")
    public ApiResponse<String> testInvalidParam(@RequestParam String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("参数name不能为空");
        }
        return ApiResponse.success("参数验证通过: " + name);
    }

    /**
     * 测试参数类型不匹配异常
     */
    @GetMapping("/type-mismatch")
    public ApiResponse<String> testTypeMismatch(@RequestParam Integer age) {
        return ApiResponse.success("年龄: " + age);
    }

    /**
     * 测试缺少必需参数异常
     */
    @GetMapping("/missing-param")
    public ApiResponse<String> testMissingParam(@RequestParam String requiredParam) {
        return ApiResponse.success("必需参数: " + requiredParam);
    }

    /**
     * 测试文件存储异常 - 文件过大
     */
    @GetMapping("/file-too-large")
    public ApiResponse<String> testFileTooLarge() {
        throw new FileStorageException(FileStorageException.FILE_TOO_LARGE, 
                                     "文件大小超过限制，最大允许 100 MB，当前文件 150 MB");
    }

    /**
     * 测试文件存储异常 - 文件不存在
     */
    @GetMapping("/file-not-found")
    public ApiResponse<String> testFileNotFound() {
        throw new FileStorageException(FileStorageException.FILE_NOT_FOUND, 
                                     "文件不存在: /path/to/nonexistent/file.txt");
    }

    /**
     * 测试文件存储异常 - 权限不足
     */
    @GetMapping("/permission-denied")
    public ApiResponse<String> testPermissionDenied() {
        throw new FileStorageException(FileStorageException.PERMISSION_DENIED, 
                                     "没有权限访问该文件");
    }

    /**
     * 测试文件存储异常 - 磁盘空间不足
     */
    @GetMapping("/disk-space-insufficient")
    public ApiResponse<String> testDiskSpaceInsufficient() {
        throw new FileStorageException(FileStorageException.DISK_SPACE_INSUFFICIENT, 
                                     "磁盘空间不足，无法保存文件");
    }

    /**
     * 测试状态异常
     */
    @GetMapping("/illegal-state")
    public ApiResponse<String> testIllegalState() {
        throw new IllegalStateException("当前状态不允许执行此操作");
    }

    /**
     * 测试运行时异常
     */
    @GetMapping("/runtime-exception")
    public ApiResponse<String> testRuntimeException() {
        throw new RuntimeException("模拟运行时异常");
    }

    /**
     * 测试通用异常
     */
    @GetMapping("/general-exception")
    public ApiResponse<String> testGeneralException() throws Exception {
        throw new Exception("模拟通用异常");
    }

    /**
     * 测试JSON格式错误
     * 需要发送格式错误的JSON来触发HttpMessageNotReadableException
     */
    @PostMapping("/json-error")
    public ApiResponse<String> testJsonError(@RequestBody Object data) {
        return ApiResponse.success("JSON解析成功");
    }

    /**
     * 测试媒体类型不支持
     * 需要发送不支持的Content-Type来触发HttpMediaTypeNotSupportedException
     */
    @PostMapping(value = "/media-type-error", consumes = "application/json")
    public ApiResponse<String> testMediaTypeError(@RequestBody String data) {
        return ApiResponse.success("媒体类型正确");
    }
}
