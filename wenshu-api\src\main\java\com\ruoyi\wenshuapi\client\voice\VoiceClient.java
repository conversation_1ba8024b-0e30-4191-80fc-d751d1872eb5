package com.ruoyi.wenshuapi.client.voice;

import com.ruoyi.wenshuapi.fallback.file.FileInfoClientFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

@FeignClient(
        name = "wenshu-voice",
        path = "/api/voice",
        contextId = "VoiceClient",
        fallbackFactory = FileInfoClientFallback.class,
        configuration = VoiceClientConfig.class
)
public interface VoiceClient {
    /**
     * 语音识别接口 - 处理音频上传并转换为文本
     * @param file 上传的音频文件
     * @return 处理结果（原始文本和去重后文本）
     */
    @PostMapping(value = "/SpeechRecognition", consumes = "multipart/form-data")
   RestResultL<String[]> handleAudioUpload( @RequestPart("file") MultipartFile file);
}
