package com.ruoyi.wenshuchat.controller;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 时间解析控制器
 * 使用AI大模型解析自然语言时间描述
 */
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class TimeParserController {
    
    private final ChatClient chatClient;

    @GetMapping("/parse")
    public ResponseEntity<TimeResponse> parseTime(@RequestParam("description") String timeDescription) {
        try {
            // 获取当前时间作为基准
            LocalDateTime now = LocalDateTime.now();
            String currentTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            
            // 构建提示词，要求AI解析时间
            String prompt = String.format(
                "你是一个专业的时间解析助手。请将以下时间描述解析为标准的日期时间格式。\n" +
                "当前时间是: %s\n" +
                "规则：\n" +
                "1. 输出格式必须是'yyyy-MM-dd HH:mm:ss'\n" +
                "2. 所有相对时间（今天、明天、下周、下个月等）都要基于当前时间计算\n" +
                "3. 只返回解析后的时间字符串，不要有任何其他文字\n" +
                "4. 如果描述中没有具体时间，使用当前时间\n" +
                "5. 如果是下午x点，请正确转换为24小时制\n\n" +
                "时间描述: %s", 
                currentTime,
                timeDescription
            );

            
            // 调用AI模型解析时间
            String parsedTimeStr = chatClient.prompt()
                    .user(prompt)
                    .call()
                    .content()
                    .trim();

            // 尝试将AI返回的结果解析为LocalDateTime
            LocalDateTime parsedTime = null;
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                parsedTime = LocalDateTime.parse(parsedTimeStr, formatter);
            } catch (Exception e) {
                // 如果AI返回的格式不正确，返回错误
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new TimeResponse(
                        400,
                        null,
                        parsedTimeStr, // 原始AI返回结果
                        "AI返回的时间格式无法解析: " + e.getMessage()
                    ));
            }
            
            // 成功响应
            return ResponseEntity.ok(new TimeResponse(
                200,
                parsedTime,
                parsedTimeStr,
                "时间解析成功"
            ));
        } catch (Exception e) {
            System.err.println("处理请求时出错: " + e.getMessage());
            e.printStackTrace();
            // 错误响应
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new TimeResponse(
                    500,
                    null,
                    null,
                    "处理请求时出错: " + e.getMessage()
                ));
        }
    }
    /**
     * 时间解析响应类
     */
    @lombok.Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TimeResponse {
        private int code;
        private LocalDateTime parsedTime;
        private String originalResponse;
        private String message;
    }
} 