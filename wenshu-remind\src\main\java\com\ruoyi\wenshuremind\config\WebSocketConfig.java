package com.ruoyi.wenshuremind.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.converter.DefaultContentTypeResolver;
import org.springframework.messaging.converter.MappingJackson2MessageConverter;
import org.springframework.messaging.converter.MessageConverter;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.util.MimeTypeUtils;
import org.springframework.web.socket.config.annotation.*;

import java.util.List;

@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketConfig.class);

    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        logger.info("配置WebSocket消息代理...");
        config.enableSimpleBroker("/topic");
        config.setApplicationDestinationPrefixes("/remind");
        logger.info("WebSocket消息代理配置完成");
    }

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        logger.info("注册STOMP端点...");
        // 配置WebSocket端点，允许特定来源
        registry.addEndpoint("/websocket-endpoint")
                .setAllowedOriginPatterns("*")
                .withSockJS();
        logger.info("STOMP端点注册完成");
    }

    @Override
    public void configureWebSocketTransport(WebSocketTransportRegistration registration) {
        registration.setSendTimeLimit(15 * 1000)
                .setSendBufferSizeLimit(512 * 1024);
    }

    /**
     * 配置消息转换器，解决LocalDateTime序列化问题
     */
    @Override
    public boolean configureMessageConverters(List<MessageConverter> messageConverters) {
        logger.info("🔧 配置WebSocket消息转换器以支持LocalDateTime...");

        // 创建支持Java 8时间类型的ObjectMapper
        ObjectMapper objectMapper = new ObjectMapper();

        // 注册JavaTimeModule以支持LocalDateTime等Java 8时间类型
        objectMapper.registerModule(new JavaTimeModule());

        // 禁用将日期写为时间戳的功能，使用ISO-8601格式
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        logger.info("ObjectMapper配置:");
        logger.info("  - ✅ 已注册JavaTimeModule");
        logger.info("  - ✅ 禁用WRITE_DATES_AS_TIMESTAMPS");
        logger.info("  - ✅ 使用ISO-8601日期格式");

        // 创建Jackson消息转换器
        MappingJackson2MessageConverter converter = new MappingJackson2MessageConverter();
        converter.setObjectMapper(objectMapper);

        // 设置内容类型解析器
        DefaultContentTypeResolver resolver = new DefaultContentTypeResolver();
        resolver.setDefaultMimeType(MimeTypeUtils.APPLICATION_JSON);
        converter.setContentTypeResolver(resolver);

        // 添加到转换器列表的开头，确保优先使用
        messageConverters.add(0, converter);

        logger.info("✅ WebSocket消息转换器配置完成，已添加支持LocalDateTime的Jackson转换器");

        // 返回false表示不清除默认转换器，只是添加我们的转换器
        return false;
    }
}