# Nacos版本更新指南

## 更新内容

已将Nacos版本从 `v2.2.0-BETA` 更新到 `v2.2.0`

### 修改的文件

1. **docker-compose.env.yml** - 主要的Nacos配置文件
2. **docker-compose.yml** - 备用配置文件（已注释）
3. **sql/ry-config.sql** - 修复了 `his_config_info` 表的 `nid` 字段问题

### 版本更新的好处

- **稳定性提升**: v2.2.0是稳定版本，比BETA版本更可靠，避免了JVM兼容性问题
- **Bug修复**: 修复了数据库表结构问题，解决了 `nid` 字段缺少默认值的错误
- **JVM兼容性**: 避免了v2.2.3中的JVM选项兼容性问题（如UseConcMarkSweepGC）

## 更新步骤

### 方法1: 使用自动化脚本（推荐）

#### Linux/macOS:
```bash
./update-nacos.sh
```

#### Windows:
```cmd
update-nacos.bat
```

### 方法2: 手动更新

#### 1. 停止现有服务
```bash
docker compose -f docker-compose.env.yml down nacos
docker compose -f docker-compose.app.yml down auth gateway system
```

#### 2. 拉取新镜像
```bash
docker pull lcr.loongnix.cn/nacos/nacos-server:v2.2.0
```

#### 3. 修复数据库（如果需要）
```bash
# 如果遇到 his_config_info 表的 nid 字段错误
docker exec -i wenshu-mysql mysql -uroot -p2313147023 < fix-nacos-db.sql
```

#### 4. 启动服务
```bash
# 启动基础设施
docker compose -f docker-compose.env.yml up -d mysql redis nacos

# 等待30秒让Nacos完全启动
sleep 30

# 启动应用服务
docker compose -f docker-compose.app.yml up -d auth system gateway
```

## 验证更新

### 1. 检查容器状态
```bash
docker ps --filter "name=wenshu-"
```

### 2. 检查Nacos控制台
访问: http://localhost:8848/nacos

### 3. 检查服务注册
在Nacos控制台的"服务管理" -> "服务列表"中应该看到：
- ruoyi-gateway
- ruoyi-auth
- ruoyi-system

### 4. 检查服务健康状态
```bash
curl http://localhost:8848/nacos/actuator/health
curl http://localhost:9200/actuator/health
curl http://localhost:8080/actuator/health
```

## 故障排除

### 1. Nacos启动失败
```bash
# 查看Nacos日志
docker compose -f docker-compose.env.yml logs nacos

# 常见问题：
# - 数据库连接失败：检查MySQL容器是否运行
# - 端口占用：检查8848端口是否被占用
# - 内存不足：增加JVM内存配置
```

### 2. 服务注册失败
```bash
# 查看应用服务日志
docker compose -f docker-compose.app.yml logs auth
docker compose -f docker-compose.app.yml logs gateway

# 常见问题：
# - Nacos地址配置错误
# - 网络连接问题
# - 服务启动顺序问题
```

### 3. 数据库表结构问题
如果遇到 `Field 'nid' doesn't have a default value` 错误：

```bash
# 执行数据库修复
docker exec -it wenshu-mysql mysql -uroot -p2313147023

# 在MySQL中执行：
USE ry_config;
ALTER TABLE his_config_info MODIFY COLUMN nid bigint unsigned NOT NULL AUTO_INCREMENT;
```

## 回滚方案

如果更新后出现问题，可以回滚到之前版本：

```bash
# 1. 停止服务
docker compose -f docker-compose.env.yml down nacos
docker compose -f docker-compose.app.yml down

# 2. 修改配置文件，将版本改回 v2.2.0-BETA
# 编辑 docker-compose.env.yml，将镜像版本改为：
# image: lcr.loongnix.cn/nacos/nacos-server:v2.2.0-BETA

# 3. 重新启动服务
docker compose -f docker-compose.env.yml up -d
docker compose -f docker-compose.app.yml up -d
```

## 注意事项

1. **数据备份**: 更新前建议备份Nacos配置数据
2. **服务依赖**: 确保按正确顺序启动服务（基础设施 -> 应用服务）
3. **网络配置**: 确保容器间网络连接正常
4. **资源监控**: 监控内存和CPU使用情况
5. **日志检查**: 定期检查服务日志，及时发现问题

## 联系支持

如果遇到问题，请：
1. 查看相关日志文件
2. 检查网络和端口配置
3. 确认数据库连接状态
4. 提供详细的错误信息
