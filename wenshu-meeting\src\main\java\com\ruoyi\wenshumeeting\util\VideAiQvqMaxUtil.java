package com.ruoyi.wenshumeeting.util;

import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversation;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationParam;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationResult;
import com.alibaba.dashscope.common.MultiModalMessage;
import com.alibaba.dashscope.common.Role;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.dashscope.exception.UploadFileException;
import io.reactivex.Flowable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.ConcurrentLinkedQueue;

public class VideAiQvqMaxUtil {
    private static final Logger logger = LoggerFactory.getLogger(VideAiQvqMaxUtil.class);

    // API调用频率控制 - 记录最近的调用时间
    private static final ConcurrentLinkedQueue<Long> apiCallTimes = new ConcurrentLinkedQueue<>();
    private static final int MAX_CALLS_PER_MINUTE = 3;
    private static final long MINUTE_IN_MILLIS = 60 * 1000;
    private static final long MIN_INTERVAL_BETWEEN_CALLS = 20 * 1000; // 20秒

    /**
     * 检查并等待，确保API调用频率不超过每分钟3次
     */
    private static void waitForRateLimit() {
        long currentTime = System.currentTimeMillis();

        // 清理一分钟前的记录
        while (!apiCallTimes.isEmpty() &&
               currentTime - apiCallTimes.peek() > MINUTE_IN_MILLIS) {
            apiCallTimes.poll();
        }

        // 检查最近一分钟的调用次数
        if (apiCallTimes.size() >= MAX_CALLS_PER_MINUTE) {
            long oldestCallTime = apiCallTimes.peek();
            long waitTime = MINUTE_IN_MILLIS - (currentTime - oldestCallTime) + 1000; // 多等1秒确保安全
            logger.info("API调用频率限制：需要等待 {} 毫秒", waitTime);
            try {
                Thread.sleep(waitTime);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.error("频率限制等待被中断");
            }
            currentTime = System.currentTimeMillis();
        }

        // 检查与上次调用的间隔
        if (!apiCallTimes.isEmpty()) {
            long lastCallTime = apiCallTimes.stream().max(Long::compareTo).orElse(0L);
            long timeSinceLastCall = currentTime - lastCallTime;
            if (timeSinceLastCall < MIN_INTERVAL_BETWEEN_CALLS) {
                long waitTime = MIN_INTERVAL_BETWEEN_CALLS - timeSinceLastCall;
                logger.info("API调用间隔控制：需要等待 {} 毫秒", waitTime);
                try {
                    Thread.sleep(waitTime);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    logger.error("间隔控制等待被中断");
                }
                currentTime = System.currentTimeMillis();
            }
        }

        // 记录本次调用时间
        apiCallTimes.offer(currentTime);
        logger.info("API调用频率状态：最近一分钟内已调用 {} 次", apiCallTimes.size());
    }

    // 主方法：传入文件路径，返回完整回复
    public static String analyzeVideo(String filePath) {
        return analyzeVideoSimple(filePath, false); // 使用简化版本，避免流式处理
    }

    // 带特殊处理的视频分析方法
    public static String analyzeVideoForLastSegment(String filePath) {
        return analyzeVideoSimple(filePath, true); // 最后切片使用更短超时的策略
    }

    // 简化的视频分析方法，使用流式API但简化处理逻辑
    public static String analyzeVideoSimple(String filePath, boolean isLastSegment) {
        logger.info("开始简化视频分析: {}, 是否最后切片: {}", filePath, isLastSegment);

        // 在API调用前进行频率控制
        waitForRateLimit();

        try {
            // 使用流式API（因为同步API不被支持）
            MultiModalConversation conv = new MultiModalConversation();
            String normalizedPath = filePath.replace("\\", "/");
            String fullFilePath = "file:///" + normalizedPath;
            String prompt = "详细分析视频内容，分析范围多元化，富有深度，面面俱到，不需要格式化输出，按照作文格式输出即可";

            MultiModalMessage userMessage = MultiModalMessage.builder()
                    .role(Role.USER.getValue())
                    .content(Arrays.asList(
                            new HashMap<String, Object>() {{ put("video", fullFilePath); }},
                            new HashMap<String, Object>() {{ put("text", prompt); }}
                    ))
                    .build();

            MultiModalConversationParam param = MultiModalConversationParam.builder()
                    .apiKey("sk-574f46304e5c4405aa5bbe26af6489b0")
                    .model("qvq-max")
                    .messages(Arrays.asList(userMessage))
                    .incrementalOutput(true)   // 使用流式输出
                    .maxTokens(500)           // 限制输出长度
                    .temperature(0.1f)
                    .build();

            // 使用流式调用，但简化处理逻辑
            Flowable<MultiModalConversationResult> result = conv.streamCall(param);

            StringBuilder finalContent = new StringBuilder();
            AtomicBoolean hasContent = new AtomicBoolean(false);

            // 根据是否为最后切片设置不同的超时时间
            long timeoutSeconds = isLastSegment ? 15 : 45; // 最后切片用更短的超时，快速失败
            result.timeout(timeoutSeconds, TimeUnit.SECONDS)
                  .blockingForEach(message -> {
                      if (message != null && message.getOutput() != null &&
                          message.getOutput().getChoices() != null &&
                          !message.getOutput().getChoices().isEmpty()) {

                          List<Map<String, Object>> content = message.getOutput().getChoices().get(0).getMessage().getContent();
                          if (content != null && !content.isEmpty()) {
                              Object text = content.get(0).get("text");
                              if (text != null) {
                                  finalContent.append(text.toString());
                                  hasContent.set(true);
                              }
                          }
                      }
                  });

            if (hasContent.get() && finalContent.length() > 0) {
                String resultText = finalContent.toString();
                logger.info("简化视频分析完成，结果长度: {}", resultText.length());
                return resultText;
            } else {
                logger.warn("简化视频分析返回空结果");
                return "视频分析完成，但未获取到有效内容";
            }

        } catch (Exception e) {
            logger.error("简化视频分析失败: {}", e.getMessage(), e);
            return "视频分析失败: " + e.getMessage();
        }
    }

    // 带重试机制的视频分析方法
    public static String analyzeVideoWithRetry(String filePath, int maxRetries) {
        logger.info("开始分析视频: {}, 最大重试次数: {}", filePath, maxRetries);

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                logger.info("第 {} 次尝试分析视频", attempt);
                String result = analyzeVideoContent(filePath);
                logger.info("视频分析完成，结果长度: {}", result.length());
                return result;
            } catch (Exception e) {
                logger.warn("第 {} 次分析尝试失败: {}", attempt, e.getMessage());

                if (attempt == maxRetries) {
                    logger.error("视频分析最终失败，已重试 {} 次: {}", maxRetries, e.getMessage(), e);
                    return "视频分析失败: " + e.getMessage();
                }

                // 等待后重试，避免API限流
                try {
                    long waitTime = attempt * 10000; // 递增等待时间：10秒、20秒、30秒
                    logger.info("等待 {} 毫秒后重试", waitTime);
                    Thread.sleep(waitTime);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    logger.error("重试等待被中断");
                    return "视频分析被中断: " + ie.getMessage();
                }
            }
        }

        return "视频分析失败: 超过最大重试次数";
    }

    // 核心分析逻辑
    private static String analyzeVideoContent(String filePath)
            throws NoApiKeyException, ApiException, InputRequiredException, UploadFileException {

        // 使用原子变量解决 Lambda 中的 final 限制
        AtomicReference<StringBuilder> reasoningContentRef = new AtomicReference<>(new StringBuilder());
        AtomicReference<StringBuilder> finalContentRef = new AtomicReference<>(new StringBuilder());
        AtomicBoolean isFirstPrint = new AtomicBoolean(true);
        AtomicBoolean hasOutputReasoning = new AtomicBoolean(false);

        MultiModalConversation conv = new MultiModalConversation();
        // 将Windows路径分隔符统一转换为Unix风格的正斜杠，以避免URI解析错误
        String normalizedPath = filePath.replace("\\", "/");
        String fullFilePath = "file:///" + normalizedPath;
        String pro="详细分析视频内容，输出无格式文本”）";
        MultiModalMessage userMessage = MultiModalMessage.builder()
                .role(Role.USER.getValue())
                .content(Arrays.asList(
                        new HashMap<String, Object>() {{ put("video", fullFilePath); }},
                        new HashMap<String, Object>() {{ put("text", pro); }}
                ))
                .build();

        MultiModalConversationParam param = buildMultiModalConversationParam(userMessage);
        Flowable<MultiModalConversationResult> result = conv.streamCall(param);

        // 添加超时处理，避免无限等待
        try {
            result.timeout(20, TimeUnit.SECONDS) // 20秒超时，快速失败
                  .blockingForEach(message -> {
            // 处理每一块结果
            String re = message.getOutput().getChoices().get(0).getMessage().getReasoningContent();
            String reasoning = Objects.isNull(re) ? "" : re;

            List<Map<String, Object>> content = message.getOutput().getChoices().get(0).getMessage().getContent();

            // 处理思考过程
            if (!reasoning.isEmpty()) {
                reasoningContentRef.get().append(reasoning);

                if (isFirstPrint.get()) {
                    System.out.println("====================思考过程====================");
                    isFirstPrint.set(false);
                    hasOutputReasoning.set(true);
                }

                System.out.print(reasoning);
            }

            // 处理完整回复
            if (Objects.nonNull(content) && !content.isEmpty()) {
                Object text = content.get(0).get("text");
                if (text != null) {
                    finalContentRef.get().append(text);

                    if (hasOutputReasoning.get()) {
                        System.out.println("\n====================完整回复====================");
                        hasOutputReasoning.set(false);
                    }

                    System.out.print(text);
                }
            }
            });
        } catch (Exception e) {
            logger.error("视频分析流处理异常: {}", e.getMessage(), e);
            // 如果流处理失败，返回已收集的内容
            String partialResult = finalContentRef.get().toString();
            if (partialResult.isEmpty()) {
                throw new RuntimeException("视频分析流处理失败: " + e.getMessage(), e);
            }
            logger.warn("返回部分分析结果，长度: {}", partialResult.length());
            return partialResult;
        }

        // 确保完整回复部分被正确输出
        if (hasOutputReasoning.get()) {
            System.out.println("\n====================完整回复====================");
            System.out.print(finalContentRef.get().toString());
        }

        String finalResult = finalContentRef.get().toString();
        if (finalResult.isEmpty()) {
            logger.warn("视频分析结果为空");
            return "视频分析完成，但未获取到有效内容";
        }

        return finalResult;
    }

    private static MultiModalConversationParam buildMultiModalConversationParam(MultiModalMessage Msg) {
        return MultiModalConversationParam.builder()
                .apiKey("sk-574f46304e5c4405aa5bbe26af6489b0")
                .model("qvq-max")
                .messages(Arrays.asList(Msg))
                .incrementalOutput(true)
                .maxTokens(2000)  // 限制输出长度，减少处理时间
                .temperature(0.1f) // 降低随机性，提高稳定性
                .build();
    }

    // 测试方法
    public static void main(String[] args) {
        String filePath = "C:/Users/<USER>/Desktop/7月1日.mp4";
        String result = analyzeVideo(filePath);

        System.out.println("\n==================== 最终分析结果 ====================");
        System.out.println(result);
    }
}