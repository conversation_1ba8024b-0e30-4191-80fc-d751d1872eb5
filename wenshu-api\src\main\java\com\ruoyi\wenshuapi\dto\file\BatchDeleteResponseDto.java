package com.ruoyi.wenshuapi.dto.file;

import lombok.Data;

import java.util.List;

/**
 * 批量删除响应DTO
 * 封装批量删除文件操作的响应数据
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-28
 */
@Data
public class BatchDeleteResponseDto {

    /**
     * 总文件数
     */
    private Integer totalCount;

    /**
     * 成功删除的文件数
     */
    private Integer successCount;

    /**
     * 删除失败的文件数
     */
    private Integer failedCount;

    /**
     * 物理文件删除成功数
     */
    private Integer physicalFileDeletedCount;

    /**
     * 成功删除的文件ID列表
     */
    private List<Integer> successFileIds;

    /**
     * 删除失败的文件ID列表
     */
    private List<Integer> failedFileIds;

    /**
     * 删除失败的详细信息
     */
    private List<FailedDeleteInfo> failedDetails;

    /**
     * 删除失败详细信息
     */
    @Data
    public static class FailedDeleteInfo {
        /**
         * 文件ID
         */
        private Integer fileId;

        /**
         * 文件名
         */
        private String fileName;

        /**
         * 失败原因
         */
        private String reason;

        /**
         * 错误代码
         */
        private String errorCode;
    }
}
