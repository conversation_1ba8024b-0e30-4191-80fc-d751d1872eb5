package com.ruoyi.wenshuvoice;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.env.Environment;

import java.util.Arrays;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@EnableFeignClients(basePackages = {
        "com.ruoyi.wenshucommon.feign",
        "com.ruoyi.wenshuapi.client"
})
@ComponentScan(basePackages = {
        "com.ruoyi.wenshuvoice",
        "com.ruoyi.wenshucommon",
        "com.ruoyi.wenshuapi"
})
@MapperScan(basePackages = {
        "com.ruoyi.wenshucommon.dao" // 添加 DAO 接口所在的包
})

@EnableDiscoveryClient
@SpringBootApplication
public class WenshuVoiceApplication {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static void printStartupBanner() {
        System.out.println();
        System.out.println("██╗    ██╗███████╗███╗   ██╗███████╗██╗  ██╗██╗   ██╗");
        System.out.println("██║    ██║██╔════╝████╗  ██║██╔════╝██║  ██║██║   ██║");
        System.out.println("██║ █╗ ██║█████╗  ██╔██╗ ██║███████╗███████║██║   ██║");
        System.out.println("██║███╗██║██╔══╝  ██║╚██╗██║╚════██║██╔══██║██║   ██║");
        System.out.println("╚███╔███╔╝███████╗██║ ╚████║███████║██║  ██║╚██████╔╝");
        System.out.println(" ╚══╝╚══╝ ╚══════╝╚═╝  ╚═══╝╚══════╝╚═╝  ╚═╝ ╚═════╝ ");
        System.out.println();
        System.out.println("WENSHU-VOICE 语音处理服务");
        System.out.println("启动时间: " + LocalDateTime.now().format(FORMATTER));
        System.out.println("服务端口: 1014");
        System.out.println("服务功能: 语音识别与合成");
        System.out.println();
    }

    public static void main(String[] args) {
        printStartupBanner();
        System.setProperty("spring.cloud.bootstrap.location", "classpath:bootstrap.yml");
        System.setProperty("spring.config.location", "classpath:bootstrap.yml");
        SpringApplication.run(WenshuVoiceApplication.class, args);
    }

}
