// 包声明：定义当前类所属的包路径，属于配置包
package com.ruoyi.wenshumeeting.config;

// 导入Jackson JSON自动检测注解，用于配置JSON序列化的可见性
import com.fasterxml.jackson.annotation.JsonAutoDetect;
// 导入Jackson属性访问器枚举，用于指定序列化时的属性访问方式
import com.fasterxml.jackson.annotation.PropertyAccessor;
// 导入Jackson对象映射器，用于Java对象与JSON之间的转换
import com.fasterxml.jackson.databind.ObjectMapper;
// 导入Spring缓存配置支持类，提供缓存配置的基础功能
import org.springframework.cache.annotation.CachingConfigurerSupport;
// 导入Spring启用缓存注解，开启缓存功能
import org.springframework.cache.annotation.EnableCaching;
// 导入Spring Bean注解，用于声明Spring管理的Bean
import org.springframework.context.annotation.Bean;
// 导入Spring配置注解，标识这是一个配置类
import org.springframework.context.annotation.Configuration;
// 导入Redis连接工厂接口，用于创建Redis连接
import org.springframework.data.redis.connection.RedisConnectionFactory;
// 导入Redis核心操作类，包含各种数据类型的操作接口
import org.springframework.data.redis.core.*;
// 导入Jackson JSON Redis序列化器，用于将对象序列化为JSON格式存储到Redis
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
// 导入字符串Redis序列化器，用于将字符串序列化存储到Redis
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * Redis配置类
 * @Configuration 标识这是一个Spring配置类
 * @EnableCaching 启用Spring缓存注解支持，开启缓存功能
 * 继承CachingConfigurerSupport提供缓存配置的基础支持
 */
@Configuration
@EnableCaching // 开启Spring缓存注解支持
public class RedisConfig extends CachingConfigurerSupport {

    /**
     * 配置RedisTemplate Bean
     * RedisTemplate是Spring Data Redis提供的核心类，用于Redis数据操作
     * @param factory Redis连接工厂，由Spring Boot自动配置提供
     * @return 配置完成的RedisTemplate实例
     */
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {

        // 创建RedisTemplate实例，泛型指定key为String类型，value为Object类型
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        // 设置Redis连接工厂，用于创建Redis连接
        template.setConnectionFactory(factory);

        // 创建Jackson2JsonRedisSerializer实例，用于序列化和反序列化Redis的value值
        // 默认情况下Redis使用JDK序列化方式，这里改为使用JSON序列化，提高可读性和跨语言兼容性
        Jackson2JsonRedisSerializer jacksonSeial = new Jackson2JsonRedisSerializer(Object.class);

        // 创建ObjectMapper实例，用于配置JSON序列化行为
        ObjectMapper om = new ObjectMapper();
        // 设置序列化时的可见性规则：ALL表示序列化所有字段（包括private、public等）
        // ANY表示不限制访问修饰符，包括private和public字段都会被序列化
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        // 启用默认类型信息，NON_FINAL表示只对非final类启用类型信息
        // 这样可以在反序列化时知道具体的类型，但final类（如String、Integer）会抛出异常
        om.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
        // 将配置好的ObjectMapper设置到Jackson序列化器中
        jacksonSeial.setObjectMapper(om);

        // 设置value的序列化器为JSON序列化器，存储时将对象转换为JSON格式
        template.setValueSerializer(jacksonSeial);
        // 设置key的序列化器为字符串序列化器，确保key以字符串形式存储
        template.setKeySerializer(new StringRedisSerializer());

        // 设置Hash数据类型的key序列化器为字符串序列化器
        template.setHashKeySerializer(new StringRedisSerializer());
        // 设置Hash数据类型的value序列化器为JSON序列化器
        template.setHashValueSerializer(jacksonSeial);
        // 调用afterPropertiesSet方法，完成RedisTemplate的初始化配置
        template.afterPropertiesSet();

        // 返回配置完成的RedisTemplate实例
        return template;
    }


    /**
     * 配置Redis Hash数据类型操作Bean
     * Hash是Redis中的一种数据结构，类似于Java中的HashMap
     * 适用于存储对象的多个字段，如用户信息、配置信息等
     *
     * @param redisTemplate 已配置的RedisTemplate实例，由Spring自动注入
     * @return HashOperations实例，用于执行Hash相关的Redis操作
     */
    @Bean
    public HashOperations<String, String, Object> hashOperations(RedisTemplate<String, Object> redisTemplate) {
        // 返回RedisTemplate的Hash操作接口，提供hset、hget、hdel等Hash操作方法
        return redisTemplate.opsForHash();
    }

    /**
     * 配置Redis String数据类型操作Bean
     * String是Redis中最基本的数据类型，可以存储字符串、数字、序列化对象等
     * 适用于缓存简单数据、计数器、分布式锁等场景
     *
     * @param redisTemplate 已配置的RedisTemplate实例，由Spring自动注入
     * @return ValueOperations实例，用于执行String相关的Redis操作
     */
    @Bean
    public ValueOperations<String, Object> valueOperations(RedisTemplate<String, Object> redisTemplate) {
        // 返回RedisTemplate的Value操作接口，提供set、get、incr、decr等String操作方法
        return redisTemplate.opsForValue();
    }

    /**
     * 配置Redis List数据类型操作Bean
     * List是Redis中的链表数据结构，支持从两端插入和删除元素
     * 适用于消息队列、最新消息列表、任务队列等场景
     *
     * @param redisTemplate 已配置的RedisTemplate实例，由Spring自动注入
     * @return ListOperations实例，用于执行List相关的Redis操作
     */
    @Bean
    public ListOperations<String, Object> listOperations(RedisTemplate<String, Object> redisTemplate) {
        // 返回RedisTemplate的List操作接口，提供lpush、rpush、lpop、rpop等List操作方法
        return redisTemplate.opsForList();
    }

    /**
     * 配置Redis Set数据类型操作Bean
     * Set是Redis中的无序集合数据结构，元素不重复
     * 适用于标签系统、好友关系、去重等场景
     *
     * @param redisTemplate 已配置的RedisTemplate实例，由Spring自动注入
     * @return SetOperations实例，用于执行Set相关的Redis操作
     */
    @Bean
    public SetOperations<String, Object> setOperations(RedisTemplate<String, Object> redisTemplate) {
        // 返回RedisTemplate的Set操作接口，提供sadd、srem、smembers等Set操作方法
        return redisTemplate.opsForSet();
    }

    /**
     * 配置Redis ZSet（有序集合）数据类型操作Bean
     * ZSet是Redis中的有序集合数据结构，每个元素都有一个分数用于排序
     * 适用于排行榜、优先级队列、范围查询等场景
     *
     * @param redisTemplate 已配置的RedisTemplate实例，由Spring自动注入
     * @return ZSetOperations实例，用于执行ZSet相关的Redis操作
     */
    @Bean
    public ZSetOperations<String, Object> zSetOperations(RedisTemplate<String, Object> redisTemplate) {
        // 返回RedisTemplate的ZSet操作接口，提供zadd、zrem、zrange等ZSet操作方法
        return redisTemplate.opsForZSet();
    }
}
