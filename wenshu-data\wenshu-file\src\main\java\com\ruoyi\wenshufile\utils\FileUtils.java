package com.ruoyi.wenshufile.utils;

import org.apache.commons.io.FilenameUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 文件处理工具类
 * 提供文件类型验证、文件名生成、文件大小检查等通用功能
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-28
 */
public class FileUtils {

    /**
     * 最大文件大小（10GB）
     */
    public static final long MAX_FILE_SIZE = 10L * 1024 * 1024 * 1024;

    /**
     * 最大文件名长度
     */
    public static final int MAX_FILENAME_LENGTH = 255;

    /**
     * 文件名非法字符正则表达式
     */
    private static final Pattern ILLEGAL_FILENAME_PATTERN = Pattern.compile("[<>:\"|?*\\\\]");

    /**
     * 常见文件类型映射
     */
    private static final Map<String, String> MIME_TYPE_MAP = new HashMap<>();

    static {
        // 文档类型
        MIME_TYPE_MAP.put("txt", "text/plain");
        MIME_TYPE_MAP.put("pdf", "application/pdf");
        MIME_TYPE_MAP.put("doc", "application/msword");
        MIME_TYPE_MAP.put("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        MIME_TYPE_MAP.put("xls", "application/vnd.ms-excel");
        MIME_TYPE_MAP.put("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        MIME_TYPE_MAP.put("ppt", "application/vnd.ms-powerpoint");
        MIME_TYPE_MAP.put("pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation");
        
        // 图片类型
        MIME_TYPE_MAP.put("jpg", "image/jpeg");
        MIME_TYPE_MAP.put("jpeg", "image/jpeg");
        MIME_TYPE_MAP.put("png", "image/png");
        MIME_TYPE_MAP.put("gif", "image/gif");
        MIME_TYPE_MAP.put("bmp", "image/bmp");
        MIME_TYPE_MAP.put("webp", "image/webp");
        MIME_TYPE_MAP.put("svg", "image/svg+xml");
        MIME_TYPE_MAP.put("ico", "image/x-icon");
        
        // 音频类型
        MIME_TYPE_MAP.put("mp3", "audio/mpeg");
        MIME_TYPE_MAP.put("wav", "audio/wav");
        MIME_TYPE_MAP.put("flac", "audio/flac");
        MIME_TYPE_MAP.put("aac", "audio/aac");
        
        // 视频类型
        MIME_TYPE_MAP.put("mp4", "video/mp4");
        MIME_TYPE_MAP.put("avi", "video/x-msvideo");
        MIME_TYPE_MAP.put("mov", "video/quicktime");
        MIME_TYPE_MAP.put("wmv", "video/x-ms-wmv");
        MIME_TYPE_MAP.put("flv", "video/x-flv");
        MIME_TYPE_MAP.put("webm", "video/webm");
        MIME_TYPE_MAP.put("mkv", "video/x-matroska");
        
        // 压缩文件类型
        MIME_TYPE_MAP.put("zip", "application/zip");
        MIME_TYPE_MAP.put("rar", "application/vnd.rar");
        MIME_TYPE_MAP.put("7z", "application/x-7z-compressed");
        MIME_TYPE_MAP.put("tar", "application/x-tar");
        MIME_TYPE_MAP.put("gz", "application/gzip");
        
        // 其他常见类型
        MIME_TYPE_MAP.put("json", "application/json");
        MIME_TYPE_MAP.put("xml", "application/xml");
        MIME_TYPE_MAP.put("csv", "text/csv");
        MIME_TYPE_MAP.put("html", "text/html");
        MIME_TYPE_MAP.put("css", "text/css");
        MIME_TYPE_MAP.put("js", "application/javascript");
    }

    /**
     * 验证文件是否符合上传要求
     * 
     * @param file 上传的文件
     * @return 验证结果
     */
    public static FileValidationResult validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return FileValidationResult.error("文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            return FileValidationResult.error(
                String.format("文件大小超过限制，最大允许 %s，当前文件 %s", 
                             formatFileSize(MAX_FILE_SIZE), 
                             formatFileSize(file.getSize())));
        }

        // 检查文件名
        String originalFilename = file.getOriginalFilename();
        if (!StringUtils.hasText(originalFilename)) {
            return FileValidationResult.error("文件名不能为空");
        }

        if (originalFilename.length() > MAX_FILENAME_LENGTH) {
            return FileValidationResult.error(
                String.format("文件名过长，最大允许 %d 个字符，当前 %d 个字符", 
                             MAX_FILENAME_LENGTH, originalFilename.length()));
        }

        // 检查文件名中的非法字符
        if (ILLEGAL_FILENAME_PATTERN.matcher(originalFilename).find()) {
            return FileValidationResult.error("文件名包含非法字符: < > : \" | ? * \\");
        }

        return FileValidationResult.success();
    }

    /**
     * 生成唯一的文件名
     * 保留原始文件扩展名，避免文件名冲突
     * 
     * @param originalFilename 原始文件名
     * @return 生成的唯一文件名
     */
    public static String generateUniqueFileName(String originalFilename) {
        if (!StringUtils.hasText(originalFilename)) {
            return generateRandomFileName();
        }
        
        String extension = FilenameUtils.getExtension(originalFilename);
        String baseName = FilenameUtils.getBaseName(originalFilename);
        
        // 生成时间戳 + 随机数的文件名
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String randomStr = generateRandomString(6);
        
        String newBaseName = String.format("%s_%s_%s", 
                                          sanitizeFileName(baseName), timestamp, randomStr);
        
        if (StringUtils.hasText(extension)) {
            return newBaseName + "." + extension.toLowerCase();
        } else {
            return newBaseName;
        }
    }

    /**
     * 根据文件扩展名获取MIME类型
     * 
     * @param filename 文件名
     * @return MIME类型
     */
    public static String getMimeType(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "application/octet-stream";
        }
        
        String extension = FilenameUtils.getExtension(filename).toLowerCase();
        return MIME_TYPE_MAP.getOrDefault(extension, "application/octet-stream");
    }

    /**
     * 格式化文件大小
     * 
     * @param size 文件大小（字节）
     * @return 格式化后的文件大小字符串
     */
    public static String formatFileSize(long size) {
        if (size <= 0) {
            return "0 B";
        }
        
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        
        return new DecimalFormat("#,##0.#").format(size / Math.pow(1024, digitGroups)) 
               + " " + units[digitGroups];
    }

    /**
     * 计算文件的MD5哈希值
     * 
     * @param file 文件
     * @return MD5哈希值
     * @throws IOException 读取文件失败
     */
    public static String calculateMD5(MultipartFile file) throws IOException {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = file.getBytes();
            byte[] digest = md.digest(bytes);
            
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5算法不可用", e);
        }
    }

    /**
     * 检查文件是否为图片
     * 
     * @param filename 文件名
     * @return 是否为图片
     */
    public static boolean isImage(String filename) {
        String extension = FilenameUtils.getExtension(filename).toLowerCase();
        return Arrays.asList("jpg", "jpeg", "png", "gif", "bmp", "webp", "svg", "ico").contains(extension);
    }

    /**
     * 检查文件是否为视频
     * 
     * @param filename 文件名
     * @return 是否为视频
     */
    public static boolean isVideo(String filename) {
        String extension = FilenameUtils.getExtension(filename).toLowerCase();
        return Arrays.asList("mp4", "avi", "mov", "wmv", "flv", "webm", "mkv", "3gp").contains(extension);
    }

    /**
     * 检查文件是否为音频
     * 
     * @param filename 文件名
     * @return 是否为音频
     */
    public static boolean isAudio(String filename) {
        String extension = FilenameUtils.getExtension(filename).toLowerCase();
        return Arrays.asList("mp3", "wav", "flac", "aac", "ogg", "wma").contains(extension);
    }

    /**
     * 检查文件是否为文档
     * 
     * @param filename 文件名
     * @return 是否为文档
     */
    public static boolean isDocument(String filename) {
        String extension = FilenameUtils.getExtension(filename).toLowerCase();
        return Arrays.asList("txt", "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx").contains(extension);
    }

    /**
     * 清理文件名，移除非法字符
     * 
     * @param filename 原始文件名
     * @return 清理后的文件名
     */
    private static String sanitizeFileName(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "file";
        }
        
        // 移除非法字符，替换为下划线
        String sanitized = ILLEGAL_FILENAME_PATTERN.matcher(filename).replaceAll("_");
        
        // 限制长度
        if (sanitized.length() > 50) {
            sanitized = sanitized.substring(0, 50);
        }
        
        return sanitized;
    }

    /**
     * 生成随机文件名
     * 
     * @return 随机文件名
     */
    private static String generateRandomFileName() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String randomStr = generateRandomString(8);
        return "file_" + timestamp + "_" + randomStr;
    }

    /**
     * 生成随机字符串
     * 
     * @param length 字符串长度
     * @return 随机字符串
     */
    private static String generateRandomString(int length) {
        String chars = "abcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return sb.toString();
    }

    /**
     * 文件验证结果类
     */
    public static class FileValidationResult {
        private final boolean valid;
        private final String message;

        private FileValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public static FileValidationResult success() {
            return new FileValidationResult(true, null);
        }

        public static FileValidationResult error(String message) {
            return new FileValidationResult(false, message);
        }

        public boolean isValid() {
            return valid;
        }

        public String getMessage() {
            return message;
        }
    }
}
