package com.ruoyi.wenshuwchat.service;

import com.ruoyi.wenshuapi.pojo.wchat.ChatRecord;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天记录服务层接口
 * 提供聊天记录的业务逻辑操作
 */
public interface ChatRecordService {

    /**
     * 发送消息
     * 
     * @param chatRecord 聊天记录实体
     * @return 创建成功的消息ID
     */
    Long sendMessage(ChatRecord chatRecord);

    /**
     * 删除消息
     * 
     * @param id 消息ID
     * @return 删除操作影响的行数
     */
    int deleteMessage(Long id);

    /**
     * 删除会话的所有消息
     * 
     * @param sessionId 会话ID
     * @return 删除操作影响的行数
     */
    int deleteSessionMessages(String sessionId);

    /**
     * 标记消息为已读
     * 
     * @param id 消息ID
     * @return 更新操作影响的行数
     */
    int markMessageAsRead(Long id);

    /**
     * 标记会话中接收者的所有消息为已读
     * 
     * @param sessionId 会话ID
     * @param receiverId 接收者ID
     * @return 更新操作影响的行数
     */
    int markSessionMessagesAsRead(String sessionId, Long receiverId);

    /**
     * 根据ID获取消息详情
     * 
     * @param id 消息ID
     * @return 聊天记录实体
     */
    ChatRecord getMessageById(Long id);

    /**
     * 根据会话ID获取聊天记录
     * 
     * @param sessionId 会话ID
     * @return 聊天记录列表
     */
    List<ChatRecord> getMessagesBySessionId(String sessionId);

    /**
     * 分页获取会话聊天记录
     * 
     * @param sessionId 会话ID
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 聊天记录列表
     */
    List<ChatRecord> getMessagesBySessionIdWithPaging(String sessionId, int page, int size);

    /**
     * 获取用户的未读消息数量
     *
     * @param receiverId 接收者ID
     * @return 未读消息数量
     */
    int getUnreadMessageCount(Long receiverId);

    /**
     * 获取用户的未读消息列表
     *
     * @param receiverId 接收者ID
     * @return 未读消息列表
     */
    List<ChatRecord> getUnreadMessages(Long receiverId);

    /**
     * 获取指定会话的未读消息数量
     * 
     * @param sessionId 会话ID
     * @param receiverId 接收者ID
     * @return 未读消息数量
     */
    int getUnreadMessageCountBySession(String sessionId, Long receiverId);

    /**
     * 获取会话的最新一条消息
     * 
     * @param sessionId 会话ID
     * @return 最新的聊天记录
     */
    ChatRecord getLatestMessage(String sessionId);

    /**
     * 根据时间范围获取聊天记录
     * 
     * @param sessionId 会话ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 聊天记录列表
     */
    List<ChatRecord> getMessagesByTimeRange(String sessionId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 生成会话ID
     * 根据两个用户ID生成唯一的会话ID
     * 
     * @param userId1 用户1的ID
     * @param userId2 用户2的ID
     * @return 会话ID
     */
    String generateSessionId(Long userId1, Long userId2);
}
