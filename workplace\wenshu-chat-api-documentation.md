# Wenshu-Chat 智能聊天服务模块 - 详细API接口文档

## 📋 模块概述

**服务名称**: wenshu-chat  
**服务端口**: 8701  
**版本**: v1.0.0  
**最后更新**: 2024-12-28  

**核心功能**: 智能聊天服务，提供AI对话、意图识别、日程管理、知识库问答、文档解析等功能

**技术栈**:
- Spring Boot 3.x
- Spring Cloud 2023.x
- Spring AI (DashScope集成)
- Milvus向量数据库
- MySQL数据库
- MyBatis-Plus
- Feign客户端
- 阿里云DashScope AI模型

---

## 🏗️ 服务架构

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                Wenshu-Chat 智能聊天服务                      │
│                        (Port: 8701)                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│   AI对话      │ │意图识别  │ │ 知识库     │
│ (Chat AI)     │ │(Intent) │ │(Knowledge)│
└───────────────┘ └────────┘ └───────────┘
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│ DashScope     │ │日程管理  │ │ Milvus    │
│ AI模型        │ │集成     │ │ 向量存储   │
└───────────────┘ └────────┘ └───────────┘
```

### 服务依赖关系
- **wenshu-celebdar**: 日程管理服务
- **DashScope**: 阿里云AI模型服务
- **Milvus**: 向量数据库
- **MySQL**: 关系型数据库
- **Nacos**: 服务注册与发现

---

## 🌐 REST API接口

### 基础信息
- **Base URL**: `http://localhost:8701`
- **Content-Type**: `application/json`
- **鉴权方式**: JWT Token (Header: `Authorization: Bearer <token>`)

---

## 💬 智能对话API

### 1. 核心聊天接口

#### 1.1 发送消息
**接口路径**: `POST /api/chat/send`

**功能描述**: 发送消息给AI助手，支持多种意图识别和智能回复

**请求参数**:
```http
POST /api/chat/send
Content-Type: application/json

{
  "message": "明天下午3点提醒我开会",
  "userId": "1001",
  "conversationId": "conv_12345"
}
```

**请求字段说明**:
- `message`: 用户消息内容 (必填)
- `userId`: 用户ID (必填)
- `conversationId`: 对话ID，用于维持对话上下文 (可选)

**请求示例**:
```bash
curl -X POST "http://localhost:8701/api/chat/send" \
  -H "Authorization: Bearer <your-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "明天下午3点提醒我开会",
    "userId": "1001",
    "conversationId": "conv_12345"
  }'
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "处理成功",
  "data": "已成功添加日程：开会\n时间：2024-12-29 15:00:00\n\n好的，我已经为您安排了明天下午3点的会议提醒。届时我会提前10分钟通知您。",
  "timestamp": 1640995200000
}
```

#### 1.2 获取聊天回复
**接口路径**: `POST /api/chat/get-chat-response`

**功能描述**: 获取AI聊天回复，不保存对话历史

**请求参数**:
```http
POST /api/chat/get-chat-response
Content-Type: application/json

{
  "message": "今天天气怎么样？",
  "userId": "1001"
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "处理成功", 
  "data": "很抱歉，我无法获取实时天气信息。建议您查看天气预报应用或网站获取准确的天气信息。",
  "timestamp": 1640995200000
}
```

#### 1.3 简单AI调用
**接口路径**: `POST /api/chat/send-easy`

**功能描述**: 简单的AI调用接口，供内部服务使用

**请求参数**:
```http
POST /api/chat/send-easy
Content-Type: application/json

{
  "message": "解释一下什么是人工智能"
}
```

**响应格式**:
```text
人工智能(Artificial Intelligence, AI)是指由机器展现出的智能行为，它能够感知环境、学习、推理并做出决策来实现特定目标。AI系统可以处理复杂的任务，如语音识别、图像识别、自然语言处理等。
```

#### 1.4 知识库增强AI调用
**接口路径**: `POST /api/chat/send-easy-kowlage`

**功能描述**: 带知识库增强的AI调用，会根据消息内容自动判断是否使用知识库

**请求参数**:
```http
POST /api/chat/send-easy-kowlage
Content-Type: application/json

{
  "message": "根据劳动法，员工加班费应该如何计算？"
}
```

**响应格式**:
```text
根据《中华人民共和国劳动法》相关规定，员工加班费计算标准如下：

1. 工作日延长工作时间：支付不低于工资150%的工资报酬
2. 休息日安排工作：支付不低于工资200%的工资报酬
3. 法定休假日安排工作：支付不低于工资300%的工资报酬

具体计算公式：加班费 = 月工资基数 ÷ 21.75天 ÷ 8小时 × 加班小时数 × 相应倍数
```

#### 1.5 结果封装接口
**接口路径**: `POST /api/chat/result`

**功能描述**: 返回封装后的API响应格式

**请求参数**: 同 `/api/chat/send`

**响应格式**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "body": "AI回复内容",
    "statusCode": "OK",
    "headers": {}
  },
  "timestamp": 1640995200000
}
```

---

## 🎯 意图识别与智能处理

### 2. 支持的意图类型

#### 2.1 普通对话
- **触发条件**: 日常聊天、问候、咨询等
- **处理方式**: 使用聊天提示词进行回复
- **示例**: "你好"、"今天心情不错"、"能帮我解答问题吗"

#### 2.2 日程操作
- **触发条件**: 包含时间、日程、提醒等关键词
- **支持操作**: 查询、添加、修改、删除日程
- **示例**: "明天上午10点开会"、"查看本周日程"、"取消下午的会议"

#### 2.3 语音聊天
- **触发条件**: 语音转文字后的聊天内容
- **处理方式**: 针对语音识别可能的错误进行容错处理
- **示例**: 语音识别后的文本内容

#### 2.4 文档解析
- **触发条件**: 涉及合同、法律文档解析
- **处理方式**: 使用专门的法律文档解析提示词
- **示例**: "帮我分析这份合同"、"解读法律条款"

### 3. 日程管理集成

#### 3.1 日程查询
**功能**: 根据自然语言查询日程
**示例输入**: "查看明天的日程"、"本周有什么安排"
**处理流程**:
1. 识别查询意图
2. 提取时间范围
3. 调用日程服务查询
4. 格式化返回结果

#### 3.2 日程添加
**功能**: 从自然语言中提取日程信息并添加
**示例输入**: "明天下午2点在会议室开项目讨论会"
**处理流程**:
1. 识别添加意图
2. 提取日程信息(标题、时间、地点等)
3. 调用日程服务添加
4. 返回添加结果

#### 3.3 日程修改
**功能**: 修改已存在的日程
**示例输入**: "把明天的会议改到下午3点"
**处理流程**:
1. 识别修改意图
2. 查找目标日程
3. 提取修改内容
4. 调用日程服务更新
5. 返回修改结果

#### 3.4 日程删除
**功能**: 删除指定日程
**示例输入**: "取消明天的项目会议"
**处理流程**:
1. 识别删除意图
2. 查找目标日程
3. 调用日程服务删除
4. 返回删除结果

---

---

## 📚 知识库问答

### 4. 知识库集成

#### 4.1 向量搜索机制
- **向量数据库**: Milvus
- **嵌入模型**: text-embedding-v1
- **相似度阈值**: 0.35 (严格模式)
- **搜索策略**: 余弦相似度匹配

#### 4.2 知识库触发条件
- **关键词匹配**: 法律、法规、条款、案例、规定、法条、条例
- **明确请求**: "根据知识库"、"参考文档"
- **相似度检查**: 向量距离小于设定阈值

#### 4.3 知识库问答流程
1. **消息预处理**: 检查是否包含知识库相关关键词
2. **向量搜索**: 在Milvus中搜索相关文档
3. **相似度评估**: 计算文档与查询的相似度
4. **上下文增强**: 将相关文档作为上下文提供给AI
5. **生成回答**: 基于知识库内容生成专业回答

---

## 🔧 技术实现细节

### AI模型配置
- **主模型**: deepseek-r1-distill-llama-70b
- **嵌入模型**: text-embedding-v1
- **API提供商**: 阿里云DashScope
- **向量维度**: 1536

### 数据库配置
- **关系数据库**: MySQL 8.0
- **向量数据库**: Milvus
- **ORM框架**: MyBatis-Plus
- **连接池**: HikariCP

### 对话记忆管理
- **记忆存储**: 数据库持久化
- **上下文维护**: 基于conversationId
- **用户隔离**: 基于userId的会话管理

---

## 📊 性能指标

### 处理性能
| 指标 | 数值 | 说明 |
|------|------|------|
| 响应延迟 | < 2秒 | 普通对话 |
| 知识库查询 | < 3秒 | 向量搜索+AI生成 |
| 日程操作 | < 5秒 | 包含外部服务调用 |
| 并发处理 | 100+ | 同时处理的对话数 |
| 准确率 | > 90% | 意图识别准确率 |

### 资源消耗
- **内存使用**: 平均 1GB
- **CPU使用**: 平均 < 30%
- **网络带宽**: 依赖AI服务调用
- **存储空间**: 对话历史和向量数据

---

## 🛠️ 配置说明

### 服务配置
```yaml
server:
  port: 8701

spring:
  application:
    name: wenshu-chat
  datasource:
    url: ************************************
    username: root
    password: your-password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

### AI模型配置
```yaml
spring:
  ai:
    dashscope:
      api-key: your-dashscope-api-key
      chat:
        model: deepseek-r1-distill-llama-70b
      embedding:
        options:
          model: text-embedding-v1
```

### 向量数据库配置
```yaml
spring:
  ai:
    vectorstore:
      milvus:
        client:
          host: localhost
          port: 19530
        databaseName: default
        collectionName: vector_store
        embeddingDimension: 1536
        indexType: IVF_FLAT
        metricType: COSINE
        initialize-schema: true
```

---

## 🔍 错误处理

### 常见错误码
| 错误码 | 错误信息 | 解决方案 |
|-------|---------|----------|
| 400 | 参数错误 | 检查请求参数格式 |
| 401 | 未授权 | 提供有效的JWT Token |
| 500 | AI服务异常 | 检查DashScope服务状态 |
| 500 | 数据库连接失败 | 检查数据库连接配置 |
| 500 | 向量搜索失败 | 检查Milvus服务状态 |
| 500 | 日程服务调用失败 | 检查wenshu-celebdar服务 |

### 错误响应格式
```json
{
  "code": 500,
  "msg": "处理消息时发生错误: AI服务暂时不可用",
  "data": null,
  "timestamp": 1640995200000
}
```

---

## 🔒 安全措施

### 数据安全
- **用户隔离**: 基于userId的数据隔离
- **对话加密**: 敏感对话内容加密存储
- **访问控制**: JWT Token验证
- **审计日志**: 完整的对话记录

### API安全
- **请求验证**: 参数格式和内容验证
- **频率限制**: 防止API滥用
- **错误处理**: 避免敏感信息泄露
- **日志记录**: 详细的操作日志

---

## 💻 开发集成指南

### JavaScript客户端示例
```javascript
class WenshuChatClient {
  constructor(baseUrl, token) {
    this.baseUrl = baseUrl;
    this.token = token;
  }

  async sendMessage(message, userId, conversationId = null) {
    try {
      const response = await fetch(`${this.baseUrl}/api/chat/send`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: message,
          userId: userId,
          conversationId: conversationId
        })
      });

      const result = await response.json();
      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg);
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      throw error;
    }
  }

  async getChatResponse(message, userId) {
    try {
      const response = await fetch(`${this.baseUrl}/api/chat/get-chat-response`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: message,
          userId: userId
        })
      });

      const result = await response.json();
      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg);
      }
    } catch (error) {
      console.error('获取回复失败:', error);
      throw error;
    }
  }
}

// 使用示例
const chatClient = new WenshuChatClient('http://localhost:8701', 'your-jwt-token');

// 发送消息
chatClient.sendMessage('明天上午10点提醒我开会', '1001', 'conv_12345')
  .then(response => {
    console.log('AI回复:', response);
  })
  .catch(error => {
    console.error('错误:', error);
  });
```

### Python客户端示例
```python
import requests
import json

class WenshuChatClient:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }

    def send_message(self, message, user_id, conversation_id=None):
        url = f"{self.base_url}/api/chat/send"
        data = {
            'message': message,
            'userId': user_id,
            'conversationId': conversation_id
        }

        response = requests.post(url, headers=self.headers, json=data)
        result = response.json()

        if result['code'] == 200:
            return result['data']
        else:
            raise Exception(f"发送消息失败: {result['msg']}")

    def get_chat_response(self, message, user_id):
        url = f"{self.base_url}/api/chat/get-chat-response"
        data = {
            'message': message,
            'userId': user_id
        }

        response = requests.post(url, headers=self.headers, json=data)
        result = response.json()

        if result['code'] == 200:
            return result['data']
        else:
            raise Exception(f"获取回复失败: {result['msg']}")

# 使用示例
client = WenshuChatClient('http://localhost:8701', 'your-jwt-token')

try:
    response = client.send_message('查看今天的日程安排', '1001')
    print(f"AI回复: {response}")
except Exception as e:
    print(f"错误: {e}")
```

---

*文档版本: v1.0.0 | 最后更新: 2024-12-28 | Wenshu-Chat智能聊天服务*
