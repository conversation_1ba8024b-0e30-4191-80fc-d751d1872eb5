package com.ruoyi.wenshumeeting.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;
import java.util.Collections;

/**
 * 跨域配置类
 * 用于配置CORS（跨源资源共享）策略，解决前后端分离项目的跨域问题
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Configuration
public class CorsConfig implements WebMvcConfigurer {

    /**
     * 通过实现WebMvcConfigurer接口配置跨域
     * 这种方式适用于Spring MVC的请求处理
     * 
     * @param registry CORS注册器
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")  // 对所有路径生效
                .allowedOriginPatterns("*")  // 允许所有来源（支持通配符）
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH")  // 允许的HTTP方法
                .allowedHeaders("*")  // 允许所有请求头
                .allowCredentials(true)  // 允许发送Cookie和认证信息
                .maxAge(3600);  // 预检请求的缓存时间（秒）
    }

    /**
     * 配置CORS过滤器Bean
     * 这种方式在过滤器层面处理跨域，优先级更高
     * 适用于所有类型的请求，包括非Spring MVC处理的请求
     * 
     * @return CorsFilter CORS过滤器实例
     */
    @Bean
    public CorsFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();
        
        // 允许所有来源访问
        config.setAllowedOriginPatterns(Collections.singletonList("*"));
        
        // 允许发送Cookie和认证信息
        config.setAllowCredentials(true);
        
        // 允许所有请求头
        config.addAllowedHeader("*");
        
        // 允许的HTTP方法
        config.setAllowedMethods(Arrays.asList(
            "GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH", "HEAD"
        ));
        
        // 暴露给客户端的响应头
        config.setExposedHeaders(Arrays.asList(
            "Authorization", 
            "Content-Type", 
            "X-Requested-With", 
            "Accept", 
            "Origin", 
            "Access-Control-Request-Method", 
            "Access-Control-Request-Headers",
            "Access-Control-Allow-Origin",
            "Access-Control-Allow-Credentials",
            "X-Total-Count"  // 用于分页信息
        ));
        
        // 预检请求的缓存时间（秒）
        config.setMaxAge(3600L);
        
        // 创建CORS配置源
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);
        
        return new CorsFilter(source);
    }

    /**
     * 配置CORS配置源Bean（可选）
     * 提供更细粒度的CORS配置
     * 
     * @return CorsConfigurationSource CORS配置源
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        
        // 允许的来源（可以根据环境配置不同的域名）
        configuration.setAllowedOriginPatterns(Arrays.asList(
            "http://localhost:*",     // 本地开发环境
            "http://127.0.0.1:*",    // 本地开发环境
            "https://*.example.com",  // 生产环境域名（需要根据实际情况修改）
            "*"                       // 允许所有来源（开发阶段使用）
        ));
        
        // 允许的HTTP方法
        configuration.setAllowedMethods(Arrays.asList(
            "GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH", "HEAD"
        ));
        
        // 允许的请求头
        configuration.setAllowedHeaders(Arrays.asList(
            "Origin",
            "Content-Type",
            "Accept",
            "Authorization",
            "X-Requested-With",
            "Access-Control-Request-Method",
            "Access-Control-Request-Headers",
            "Cache-Control",
            "Pragma",
            "Expires",
            "Last-Modified",
            "If-Modified-Since"
        ));
        
        // 暴露给客户端的响应头
        configuration.setExposedHeaders(Arrays.asList(
            "Authorization",
            "Content-Type",
            "X-Total-Count",
            "Access-Control-Allow-Origin",
            "Access-Control-Allow-Credentials"
        ));
        
        // 允许发送认证信息
        configuration.setAllowCredentials(true);
        
        // 预检请求缓存时间
        configuration.setMaxAge(3600L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        
        return source;
    }
}
