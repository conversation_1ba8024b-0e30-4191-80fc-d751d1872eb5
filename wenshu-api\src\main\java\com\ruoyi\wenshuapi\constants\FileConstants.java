package com.ruoyi.wenshuapi.constants;

/**
 * 文件相关常量定义
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-28
 */
public class FileConstants {

    /**
     * 文件大小限制
     */
    public static class FileSize {
        /**
         * 最大文件大小（10GB）
         */
        public static final long MAX_FILE_SIZE = 10L * 1024 * 1024 * 1024;

        /**
         * 最大文件名长度
         */
        public static final int MAX_FILENAME_LENGTH = 255;

        /**
         * 文件大小阈值（2KB）
         */
        public static final long FILE_SIZE_THRESHOLD = 2 * 1024;
    }

    /**
     * 文件状态
     */
    public static class FileStatus {
        /**
         * 可读写
         */
        public static final String READABLE_WRITABLE = "可读写";

        /**
         * 协作
         */
        public static final String COLLABORATIVE = "协作";

        /**
         * 不可读写
         */
        public static final String READ_ONLY = "不可读写";
    }

    /**
     * 所有者类型
     */
    public static class OwnerType {
        /**
         * 个人
         */
        public static final String PERSONAL = "个人";

        /**
         * 团队
         */
        public static final String TEAM = "团队";
    }

    /**
     * 文件类型
     */
    public static class FileType {
        /**
         * 图片类型
         */
        public static final String[] IMAGE_TYPES = {
            "jpg", "jpeg", "png", "gif", "bmp", "webp", "svg", "ico"
        };

        /**
         * 视频类型
         */
        public static final String[] VIDEO_TYPES = {
            "mp4", "avi", "mov", "wmv", "flv", "webm", "mkv", "3gp"
        };

        /**
         * 音频类型
         */
        public static final String[] AUDIO_TYPES = {
            "mp3", "wav", "flac", "aac", "ogg", "wma"
        };

        /**
         * 文档类型
         */
        public static final String[] DOCUMENT_TYPES = {
            "txt", "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx"
        };

        /**
         * 压缩文件类型
         */
        public static final String[] ARCHIVE_TYPES = {
            "zip", "rar", "7z", "tar", "gz"
        };
    }

    /**
     * MIME类型
     */
    public static class MimeType {
        /**
         * 默认MIME类型
         */
        public static final String DEFAULT = "application/octet-stream";

        /**
         * JSON类型
         */
        public static final String JSON = "application/json";

        /**
         * 表单数据类型
         */
        public static final String FORM_DATA = "multipart/form-data";

        /**
         * 文本类型
         */
        public static final String TEXT_PLAIN = "text/plain";
    }

    /**
     * 错误代码
     */
    public static class ErrorCode {
        /**
         * 文件过大
         */
        public static final String FILE_TOO_LARGE = "FILE_TOO_LARGE";

        /**
         * 文件类型不允许
         */
        public static final String FILE_TYPE_NOT_ALLOWED = "FILE_TYPE_NOT_ALLOWED";

        /**
         * 文件名无效
         */
        public static final String FILE_NAME_INVALID = "FILE_NAME_INVALID";

        /**
         * 存储路径未找到
         */
        public static final String STORAGE_PATH_NOT_FOUND = "STORAGE_PATH_NOT_FOUND";

        /**
         * 上传失败
         */
        public static final String UPLOAD_FAILED = "UPLOAD_FAILED";

        /**
         * 删除失败
         */
        public static final String DELETE_FAILED = "DELETE_FAILED";

        /**
         * 文件未找到
         */
        public static final String FILE_NOT_FOUND = "FILE_NOT_FOUND";

        /**
         * 权限被拒绝
         */
        public static final String PERMISSION_DENIED = "PERMISSION_DENIED";

        /**
         * 磁盘空间不足
         */
        public static final String DISK_SPACE_INSUFFICIENT = "DISK_SPACE_INSUFFICIENT";
    }

    /**
     * HTTP状态码
     */
    public static class HttpStatus {
        /**
         * 成功
         */
        public static final int OK = 200;

        /**
         * 参数错误
         */
        public static final int BAD_REQUEST = 400;

        /**
         * 权限不足
         */
        public static final int FORBIDDEN = 403;

        /**
         * 资源不存在
         */
        public static final int NOT_FOUND = 404;

        /**
         * 方法不允许
         */
        public static final int METHOD_NOT_ALLOWED = 405;

        /**
         * 状态冲突
         */
        public static final int CONFLICT = 409;

        /**
         * 文件过大
         */
        public static final int PAYLOAD_TOO_LARGE = 413;

        /**
         * 媒体类型不支持
         */
        public static final int UNSUPPORTED_MEDIA_TYPE = 415;

        /**
         * 服务器内部错误
         */
        public static final int INTERNAL_SERVER_ERROR = 500;

        /**
         * 服务不可用
         */
        public static final int SERVICE_UNAVAILABLE = 503;

        /**
         * 存储空间不足
         */
        public static final int INSUFFICIENT_STORAGE = 507;
    }

    /**
     * 文件路径
     */
    public static class FilePath {
        /**
         * 文件访问前缀
         */
        public static final String FILE_ACCESS_PREFIX = "/wenshu-files";

        /**
         * 用户文件目录前缀
         */
        public static final String USER_DIR_PREFIX = "users";

        /**
         * 匿名用户目录
         */
        public static final String ANONYMOUS_DIR = "anonymous";

        /**
         * 临时文件目录
         */
        public static final String TEMP_DIR = "temp";
    }

    /**
     * 日期格式
     */
    public static class DateFormat {
        /**
         * 文件目录日期格式
         */
        public static final String FILE_DIR_DATE = "yyyy/MM/dd";

        /**
         * 文件名时间戳格式
         */
        public static final String FILE_TIMESTAMP = "yyyyMMddHHmmss";

        /**
         * API响应时间格式
         */
        public static final String API_RESPONSE_TIME = "yyyy-MM-dd'T'HH:mm:ss";
    }
}
