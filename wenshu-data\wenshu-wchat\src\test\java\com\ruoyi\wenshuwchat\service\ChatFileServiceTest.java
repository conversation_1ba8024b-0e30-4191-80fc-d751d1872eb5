package com.ruoyi.wenshuwchat.service;

import com.ruoyi.wenshuwchat.service.impl.ChatFileServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 聊天文件服务测试类
 * 测试文件上传功能，验证所有类型的文件都能上传
 */
@ExtendWith(MockitoExtension.class)
public class ChatFileServiceTest {

    @InjectMocks
    private ChatFileServiceImpl chatFileService;

    @BeforeEach
    void setUp() {
        // 设置测试用的配置值
        ReflectionTestUtils.setField(chatFileService, "chatFileBasePath", "D:/test/chat-files");
        ReflectionTestUtils.setField(chatFileService, "fileDomain", "http://localhost:8080");
        ReflectionTestUtils.setField(chatFileService, "filePrefix", "/chat-files");
    }

    @Test
    void testIsFileTypeAllowed_AllFileTypes() {
        // 测试各种文件类型都能通过验证

        // 测试图片文件
        MultipartFile imageFile = new MockMultipartFile("test", "test.jpg", "image/jpeg", "test content".getBytes());
        assertTrue(chatFileService.isFileTypeAllowed(imageFile, "image"), "JPG图片应该被允许");

        MultipartFile pngFile = new MockMultipartFile("test", "test.png", "image/png", "test content".getBytes());
        assertTrue(chatFileService.isFileTypeAllowed(pngFile, "image"), "PNG图片应该被允许");

        // 测试视频文件
        MultipartFile videoFile = new MockMultipartFile("test", "test.mp4", "video/mp4", "test content".getBytes());
        assertTrue(chatFileService.isFileTypeAllowed(videoFile, "video"), "MP4视频应该被允许");

        // 测试文档文件
        MultipartFile pdfFile = new MockMultipartFile("test", "test.pdf", "application/pdf", "test content".getBytes());
        assertTrue(chatFileService.isFileTypeAllowed(pdfFile, "file"), "PDF文件应该被允许");

        MultipartFile docFile = new MockMultipartFile("test", "test.docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "test content".getBytes());
        assertTrue(chatFileService.isFileTypeAllowed(docFile, "file"), "DOCX文件应该被允许");

        // 测试压缩文件
        MultipartFile zipFile = new MockMultipartFile("test", "test.zip", "application/zip", "test content".getBytes());
        assertTrue(chatFileService.isFileTypeAllowed(zipFile, "archive"), "ZIP文件应该被允许");

        // 测试其他类型文件
        MultipartFile txtFile = new MockMultipartFile("test", "test.txt", "text/plain", "test content".getBytes());
        assertTrue(chatFileService.isFileTypeAllowed(txtFile, "file"), "TXT文件应该被允许");

        // 测试不常见的文件类型
        MultipartFile customFile = new MockMultipartFile("test", "test.xyz", "application/octet-stream", "test content".getBytes());
        assertTrue(chatFileService.isFileTypeAllowed(customFile, "file"), "自定义扩展名文件应该被允许");

        // 测试没有扩展名的文件
        MultipartFile noExtFile = new MockMultipartFile("test", "testfile", "application/octet-stream", "test content".getBytes());
        assertTrue(chatFileService.isFileTypeAllowed(noExtFile, "file"), "没有扩展名的文件应该被允许");
    }

    @Test
    void testIsFileTypeAllowed_InvalidFiles() {
        // 测试无效文件应该被拒绝

        // 空文件
        MultipartFile emptyFile = new MockMultipartFile("test", "", "text/plain", new byte[0]);
        assertFalse(chatFileService.isFileTypeAllowed(emptyFile, "file"), "空文件应该被拒绝");

        // null文件
        assertFalse(chatFileService.isFileTypeAllowed(null, "file"), "null文件应该被拒绝");

        // 文件名为null
        MultipartFile nullNameFile = new MockMultipartFile("test", null, "text/plain", "test content".getBytes());
        assertFalse(chatFileService.isFileTypeAllowed(nullNameFile, "file"), "文件名为null的文件应该被拒绝");

        // 危险的扩展名
        MultipartFile dangerousFile = new MockMultipartFile("test", "test.exe", "application/octet-stream", "test content".getBytes());
        assertTrue(chatFileService.isFileTypeAllowed(dangerousFile, "file"), "即使是exe文件也应该被允许（因为我们移除了限制）");

        // 超长扩展名
        MultipartFile longExtFile = new MockMultipartFile("test", "test.verylongextension", "application/octet-stream", "test content".getBytes());
        assertFalse(chatFileService.isFileTypeAllowed(longExtFile, "file"), "超长扩展名应该被拒绝");
    }

    @Test
    void testGetFileAccessUrl() {
        String filePath = "image/2024/01/01/test.jpg";
        String expectedUrl = "http://localhost:8080/chat-files/image/2024/01/01/test.jpg";
        
        String actualUrl = chatFileService.getFileAccessUrl(filePath);
        assertEquals(expectedUrl, actualUrl, "文件访问URL应该正确生成");
    }

    @Test
    void testGetFullFilePath() {
        String filePath = "image/2024/01/01/test.jpg";
        String expectedPath = "D:/test/chat-files/image/2024/01/01/test.jpg";
        
        String actualPath = chatFileService.getFullFilePath(filePath);
        assertEquals(expectedPath, actualPath, "完整文件路径应该正确生成");
    }
}
