package com.ruoyi.wenshuteam.controller;

import com.ruoyi.wenshuapi.pojo.team.TeamInfo;
import com.ruoyi.wenshuapi.util.file.ApiResponse;
import com.ruoyi.wenshuteam.service.TeamInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 团队信息管理控制器
 * 顶层路径: /wenshu/teaminfo
 */
@RestController
@RequestMapping("/wenshu/teaminfo")
public class TeamInfoController {

    private final TeamInfoService teamInfoService;

    @Autowired
    public TeamInfoController(TeamInfoService teamInfoService) {
        this.teamInfoService = teamInfoService;
    }

    /**
     * 创建新团队
     *
     * @param teamInfo 团队信息实体
     * @return 包含新团队ID的响应
     */
    @PostMapping
    public ApiResponse<Integer> createTeam(@RequestBody TeamInfo teamInfo) {
        try {
            int teamId = teamInfoService.createTeam(teamInfo);
            return ApiResponse.success(teamId, "团队创建成功");
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("团队创建失败: " + e.getMessage());
        }
    }

    /**
     * 删除团队
     *
     * @param teamId 团队ID
     * @return 操作结果响应
     */
    @DeleteMapping("/{teamId}")
    public ApiResponse<Void> deleteTeam(@PathVariable("teamId") Integer teamId) {
        try {
            int result = teamInfoService.deleteTeam(teamId);
            if (result > 0) {
                return ApiResponse.success(null, "团队删除成功");
            }
            return ApiResponse.failed("团队删除失败: 未找到指定团队");
        } catch (IllegalArgumentException | IllegalStateException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("团队删除失败: " + e.getMessage());
        }
    }

    /**
     * 更新团队信息
     *
     * @param teamInfo 团队信息实体
     * @return 操作结果响应
     */
    @PutMapping
    public ApiResponse<Void> updateTeam(@RequestBody TeamInfo teamInfo) {
        try {
            int result = teamInfoService.updateTeam(teamInfo);
            if (result > 0) {
                return ApiResponse.success(null, "团队更新成功");
            }
            return ApiResponse.failed("团队更新失败: 未找到指定团队");
        } catch (IllegalArgumentException | IllegalStateException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("团队更新失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取团队详情
     *
     * @param teamId 团队ID
     * @return 团队信息响应
     */
    @GetMapping("/{teamId}")
    public ApiResponse<TeamInfo> getTeamById(@PathVariable("teamId") Integer teamId) {
        try {
            TeamInfo teamInfo = teamInfoService.getTeamById(teamId);
            if (teamInfo != null) {
                return ApiResponse.success(teamInfo, "团队详情获取成功");
            }
            return ApiResponse.failed("未找到指定团队", 404);
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("获取团队详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有团队列表
     *
     * @return 团队列表响应
     */
    @GetMapping("/all")
    public ApiResponse<List<TeamInfo>> getAllTeams() {
        try {
            List<TeamInfo> teams = teamInfoService.getAllTeams();
            return ApiResponse.success(teams, "团队列表获取成功");
        } catch (Exception e) {
            return ApiResponse.failed("获取团队列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据状态获取团队列表
     *
     * @param status 团队状态 (0-禁用, 1-启用)
     * @return 团队列表响应
     */
    @GetMapping("/status/{status}")
    public ApiResponse<List<TeamInfo>> getTeamsByStatus(@PathVariable("status") Byte status) {
        try {
            List<TeamInfo> teams = teamInfoService.getTeamsByStatus(status);
            return ApiResponse.success(teams, "团队列表获取成功");
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("获取团队列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据团队名称搜索团队
     *
     * @param name 团队名称关键词
     * @return 团队列表响应
     */
    @GetMapping("/search")
    public ApiResponse<List<TeamInfo>> searchTeamsByName(@RequestParam("name") String name) {
        try {
            List<TeamInfo> teams = teamInfoService.searchTeamsByName(name);
            return ApiResponse.success(teams, "团队搜索完成");
        } catch (IllegalArgumentException e) {
            return ApiResponse.validateFailed(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.failed("团队搜索失败: " + e.getMessage());
        }
    }
}