# Wenshu-Remind 智能提醒模块 - 详细API接口文档

## 📋 模块概述

**服务名称**: wenshu-remind  
**服务端口**: 1015  
**版本**: v1.0.0  
**最后更新**: 2024-12-28  

**核心功能**: 智能提醒服务，提供邮件提醒、WebSocket实时提醒、天气集成、AI个性化提醒等功能

**技术栈**:
- Spring Boot 3.x
- Spring Cloud 2023.x
- Spring WebSocket + STOMP
- Spring Mail (邮件服务)
- Nacos服务注册与发现
- OpenFeign微服务调用
- Jackson时间序列化支持

---

## 🏗️ 服务架构

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                  Wenshu-Remind 智能提醒服务                  │
│                        (Port: 1015)                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│   邮件服务     │ │WebSocket│ │ AI提醒生成 │
│ (Mail Service)│ │实时通信  │ │(AI Content)│
└───────────────┘ └────────┘ └───────────┘
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│   SMTP服务器   │ │STOMP协议│ │wenshu-chat│
│   (QQ邮箱)    │ │消息代理  │ │  服务    │
└───────────────┘ └────────┘ └───────────┘
```

### 服务依赖关系
- **wenshu-chat**: AI智能对话服务
- **programme-manage**: 日程管理服务  
- **wenshu-api**: 通用API服务
- **天气API**: 实时天气信息获取
- **QQ邮箱SMTP**: 邮件发送服务

---

## 🌐 REST API接口

### 基础信息
- **Base URL**: `http://localhost:1015`
- **API前缀**: `/remind`
- **Content-Type**: `application/x-www-form-urlencoded` / `multipart/form-data`
- **鉴权方式**: JWT Token (Header: `Authorization: Bearer <token>`)

---

## 📚 邮件服务API接口

### 1. 发送纯文本邮件

#### 1.1 基础文本邮件
**接口路径**: `POST /remind/send-simple`

**功能描述**: 发送纯文本格式的邮件，适用于简单的通知和提醒

**请求参数**:
```http
POST /remind/send-simple
Content-Type: application/x-www-form-urlencoded

to: [收件人邮箱] (必填)
subject: [邮件主题] (必填)
text: [邮件正文内容] (必填)
```

**请求示例**:
```bash
curl -X POST http://localhost:1015/remind/send-simple \
  -H "Authorization: Bearer <your-token>" \
  -d "to=<EMAIL>" \
  -d "subject=会议提醒" \
  -d "text=您有一个重要会议即将开始，请准时参加。"
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "纯文本邮件发送成功",
  "data": "邮件已发送至: <EMAIL>",
  "timestamp": 1640995200000
}
```

**错误响应**:
```json
{
  "code": 500,
  "msg": "邮件发送失败: 收件人地址无效",
  "data": null,
  "timestamp": 1640995200000
}
```

---

### 2. 发送HTML格式邮件

#### 2.1 富文本邮件
**接口路径**: `POST /remind/send-html`

**功能描述**: 发送HTML格式的邮件，支持富文本样式和格式化内容

**请求参数**:
```http
POST /remind/send-html
Content-Type: application/x-www-form-urlencoded

to: [收件人邮箱] (必填)
subject: [邮件主题] (必填)
htmlContent: [HTML格式内容] (必填)
```

**请求示例**:
```bash
curl -X POST http://localhost:1015/remind/send-html \
  -H "Authorization: Bearer <your-token>" \
  -d "to=<EMAIL>" \
  -d "subject=重要通知" \
  -d "htmlContent=<h3>会议提醒</h3><p>您的<strong>项目讨论会</strong>将在今天下午3点开始。</p><ul><li>地点：会议室A</li><li>时长：2小时</li></ul>"
```

**HTML模板结构**:
系统会自动为HTML内容添加专业的邮件模板：
```html
<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
  <h2 style='color: #2c3e50;'>[邮件主题]</h2>
  <div style='background-color: #f8f9fa; padding: 20px; border-radius: 5px;'>
    [用户HTML内容]
  </div>
  <p style='color: #7f8c8d; margin-top: 20px;'>此邮件由Spring Boot邮件系统发送</p>
</div>
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "HTML邮件发送成功",
  "data": "邮件已发送至: <EMAIL>",
  "timestamp": 1640995200000
}
```

---

### 3. 发送带附件邮件

#### 3.1 附件邮件
**接口路径**: `POST /remind/send-attachment`

**功能描述**: 发送包含附件的邮件，支持各种文件格式

**请求参数**:
```http
POST /remind/send-attachment
Content-Type: multipart/form-data

to: [收件人邮箱] (必填)
subject: [邮件主题] (必填)
text: [邮件正文] (必填)
attachment: [附件文件] (必填)
```

**请求示例**:
```bash
curl -X POST http://localhost:1015/remind/send-attachment \
  -H "Authorization: Bearer <your-token>" \
  -F "to=<EMAIL>" \
  -F "subject=会议资料" \
  -F "text=请查收本次会议的相关资料。" \
  -F "attachment=@meeting_agenda.pdf"
```

**支持的附件格式**:
- 文档类: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX
- 图片类: JPG, PNG, GIF, BMP
- 压缩包: ZIP, RAR, 7Z
- 文本类: TXT, CSV, JSON, XML
- 其他常见格式

**响应示例**:
```json
{
  "code": 200,
  "msg": "附件邮件发送成功",
  "data": "邮件已发送至: <EMAIL>",
  "timestamp": 1640995200000
}
```

**错误处理**:
```json
{
  "code": 400,
  "msg": "附件不能为空",
  "data": null,
  "timestamp": 1640995200000
}
```

---

## 🔌 WebSocket实时提醒接口

### WebSocket连接配置

#### 连接信息
- **WebSocket端点**: `ws://localhost:1015/websocket-endpoint`
- **协议**: STOMP over WebSocket
- **支持**: SockJS降级支持

#### 连接示例
```javascript
// 使用SockJS和STOMP连接
const socket = new SockJS('http://localhost:1015/websocket-endpoint');
const stompClient = Stomp.over(socket);

// 连接配置
const connectHeaders = {
  'Authorization': 'Bearer ' + token
};

stompClient.connect(connectHeaders, function(frame) {
  console.log('WebSocket连接成功: ' + frame);
  
  // 订阅日程提醒主题
  stompClient.subscribe('/topic/programme/1001/广东/深圳', function(message) {
    const reminder = JSON.parse(message.body);
    handleReminder(reminder);
  });
});
```

---

### 4. 智能日程提醒

#### 4.1 日程提醒处理
**消息路径**: `/remind/programme/{userid}/{sheng}/{place}/{email}`  
**订阅路径**: `/topic/programme/{userid}/{sheng}/{place}`

**功能描述**: 智能检测用户日程，结合天气信息生成个性化提醒，并通过邮件和WebSocket推送

**路径参数**:
- `userid`: 用户ID
- `sheng`: 省份名称（用于天气查询）
- `place`: 城市名称（用于天气查询）
- `email`: 用户邮箱地址

**发送消息示例**:
```javascript
// 发送日程提醒请求
stompClient.send('/remind/programme/1001/广东/深圳/<EMAIL>', {}, JSON.stringify({}));
```

**响应数据结构**:
```json
{
  "code": 200,
  "message": "发现待提醒日程",
  "data": {
    "reminderRequired": true,
    "todayEventsCount": 3,
    "event": {
      "eventId": 1,
      "title": "项目讨论会",
      "description": "讨论项目进展和下一步计划",
      "startTime": "2024-12-29T15:00:00",
      "remindTime": "2024-12-29T14:50:00",
      "creatorId": 1001,
      "ownerType": "个人",
      "eventStatus": 1
    },
    "aiChatResponse": "您好！您的项目讨论会将在10分钟后开始。今天深圳天气晴朗，温度适宜，是个开会的好日子。今日您还有2个其他安排，请注意时间安排。祝您会议顺利！"
  }
}
```

#### 4.2 无提醒响应
```json
{
  "code": 200,
  "message": "无待提醒日程",
  "data": {
    "reminderRequired": false,
    "todayEventsCount": 1,
    "reason": "当前无符合提醒条件的日程"
  }
}
```

#### 4.3 错误响应
```json
{
  "code": 400,
  "message": "参数错误",
  "data": {
    "reminderRequired": false,
    "reason": "用户ID格式错误: abc"
  }
}
```

---

## 🔧 技术实现细节

### 智能提醒算法
1. **时间匹配**: 精确匹配当前时间与日程提醒时间（精确到分钟）
2. **状态检查**: 只处理状态为启用(1)的日程
3. **天气集成**: 实时获取用户所在城市的天气信息
4. **AI内容生成**: 根据日程数量和天气情况生成个性化提醒
5. **多渠道推送**: 同时通过邮件和WebSocket推送提醒

### 邮件服务配置
- **SMTP服务器**: QQ邮箱 (smtp.qq.com)
- **发送邮箱**: <EMAIL>
- **安全协议**: TLS/SSL加密
- **字符编码**: UTF-8
- **附件支持**: 无大小限制（受服务器配置影响）

### WebSocket特性
- **协议支持**: STOMP over WebSocket
- **降级支持**: SockJS自动降级
- **消息格式**: JSON序列化
- **时间处理**: 支持LocalDateTime序列化
- **跨域支持**: 配置CORS允许所有来源

---

## 📊 性能指标

### 处理性能
| 指标 | 数值 | 说明 |
|------|------|------|
| 邮件发送延迟 | < 3秒 | 普通文本邮件 |
| HTML邮件延迟 | < 5秒 | 富文本邮件 |
| 附件邮件延迟 | < 10秒 | 依赖附件大小 |
| WebSocket延迟 | < 100ms | 实时消息推送 |
| 并发连接数 | 1000+ | WebSocket连接 |
| 日程检查频率 | 每分钟 | 自动提醒检查 |

### 资源消耗
- **内存使用**: 平均 256MB
- **CPU使用**: 平均 < 30%
- **网络带宽**: 依赖邮件内容大小
- **存储空间**: 临时附件自动清理

---

## 🛠️ 配置说明

### 服务配置
```yaml
server:
  port: 1015

spring:
  application:
    name: wenshu-remind
  
  # 邮件服务配置
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: ${MAIL_PASSWORD}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
```

### WebSocket配置
```yaml
# WebSocket传输配置
websocket:
  transport:
    send-time-limit: 15000      # 发送超时时间(ms)
    send-buffer-size: 524288    # 发送缓冲区大小(bytes)
    message-size-limit: 65536   # 消息大小限制(bytes)
```

### Jackson时间配置
```yaml
spring:
  jackson:
    serialization:
      write-dates-as-timestamps: false
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
```

---

## 🔍 错误处理

### 常见错误码
| 错误码 | 错误信息 | 解决方案 |
|-------|---------|----------|
| 400 | 参数错误 | 检查请求参数格式 |
| 401 | 未授权 | 提供有效的JWT Token |
| 500 | 邮件发送失败 | 检查邮箱配置和网络 |
| 500 | 收件人地址无效 | 验证邮箱地址格式 |
| 500 | 附件不能为空 | 确保上传了有效附件 |
| 500 | WebSocket连接失败 | 检查网络和服务状态 |
| 500 | AI服务调用失败 | 使用备用提醒内容 |
| 500 | 日程数据获取失败 | 检查数据库连接 |

### 错误处理策略
1. **邮件发送失败**: 记录错误日志，返回具体失败原因
2. **AI服务不可用**: 自动降级使用预设提醒模板
3. **WebSocket断开**: 客户端自动重连机制
4. **附件处理失败**: 自动清理临时文件
5. **参数验证失败**: 返回详细的参数错误信息

---

## 🔒 安全措施

### 邮件安全
- **SMTP加密**: 使用TLS/SSL加密传输
- **身份验证**: SMTP服务器身份验证
- **内容过滤**: 防止邮件内容注入
- **附件检查**: 验证附件类型和大小

### WebSocket安全
- **JWT认证**: 连接时验证用户身份
- **CORS配置**: 控制跨域访问
- **消息验证**: 验证消息格式和内容
- **连接限制**: 防止恶意连接攻击

### 数据保护
- **敏感信息**: 邮箱密码等敏感配置加密存储
- **日志脱敏**: 邮箱地址等信息脱敏记录
- **临时文件**: 附件处理后自动清理
- **访问控制**: 基于用户权限的访问控制

---

---

## 💻 开发集成指南

### 客户端集成示例

#### JavaScript/Web集成
```javascript
// 邮件服务客户端
class WenshuRemindClient {
  constructor(baseUrl, token) {
    this.baseUrl = baseUrl;
    this.token = token;
  }

  // 发送纯文本邮件
  async sendSimpleEmail(to, subject, text) {
    const formData = new FormData();
    formData.append('to', to);
    formData.append('subject', subject);
    formData.append('text', text);

    try {
      const response = await fetch(`${this.baseUrl}/remind/send-simple`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`
        },
        body: formData
      });

      const result = await response.json();
      if (result.code === 200) {
        console.log('邮件发送成功:', result.data);
        return result;
      } else {
        throw new Error(result.msg);
      }
    } catch (error) {
      console.error('邮件发送失败:', error);
      throw error;
    }
  }

  // 发送HTML邮件
  async sendHtmlEmail(to, subject, htmlContent) {
    const formData = new FormData();
    formData.append('to', to);
    formData.append('subject', subject);
    formData.append('htmlContent', htmlContent);

    try {
      const response = await fetch(`${this.baseUrl}/remind/send-html`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`
        },
        body: formData
      });

      return await response.json();
    } catch (error) {
      console.error('HTML邮件发送失败:', error);
      throw error;
    }
  }

  // 发送带附件邮件
  async sendAttachmentEmail(to, subject, text, file) {
    const formData = new FormData();
    formData.append('to', to);
    formData.append('subject', subject);
    formData.append('text', text);
    formData.append('attachment', file);

    try {
      const response = await fetch(`${this.baseUrl}/remind/send-attachment`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`
        },
        body: formData
      });

      return await response.json();
    } catch (error) {
      console.error('附件邮件发送失败:', error);
      throw error;
    }
  }
}

// WebSocket提醒客户端
class WenshuReminderWebSocket {
  constructor(baseUrl, userId, province, city, email, token) {
    this.baseUrl = baseUrl;
    this.userId = userId;
    this.province = province;
    this.city = city;
    this.email = email;
    this.token = token;
    this.stompClient = null;
    this.connected = false;
  }

  // 连接WebSocket
  connect() {
    return new Promise((resolve, reject) => {
      const socket = new SockJS(`${this.baseUrl}/websocket-endpoint`);
      this.stompClient = Stomp.over(socket);

      const connectHeaders = {
        'Authorization': `Bearer ${this.token}`
      };

      this.stompClient.connect(connectHeaders, (frame) => {
        console.log('WebSocket连接成功:', frame);
        this.connected = true;

        // 订阅日程提醒
        this.subscribeToReminders();
        resolve(frame);
      }, (error) => {
        console.error('WebSocket连接失败:', error);
        this.connected = false;
        reject(error);
      });
    });
  }

  // 订阅提醒消息
  subscribeToReminders() {
    const topic = `/topic/programme/${this.userId}/${this.province}/${this.city}`;

    this.stompClient.subscribe(topic, (message) => {
      const reminder = JSON.parse(message.body);
      this.handleReminder(reminder);
    });

    console.log(`已订阅提醒主题: ${topic}`);
  }

  // 处理提醒消息
  handleReminder(reminder) {
    console.log('收到提醒:', reminder);

    if (reminder.data && reminder.data.reminderRequired) {
      const event = reminder.data.event;
      const aiResponse = reminder.data.aiChatResponse;

      // 显示桌面通知
      this.showDesktopNotification(event.title, aiResponse);

      // 触发自定义事件
      this.onReminderReceived(reminder);
    }
  }

  // 显示桌面通知
  showDesktopNotification(title, message) {
    if (Notification.permission === 'granted') {
      new Notification(title, {
        body: message,
        icon: '/assets/reminder-icon.png',
        tag: 'wenshu-reminder'
      });
    }
  }

  // 请求日程提醒检查
  requestReminderCheck() {
    if (this.connected && this.stompClient) {
      const destination = `/remind/programme/${this.userId}/${this.province}/${this.city}/${this.email}`;
      this.stompClient.send(destination, {}, JSON.stringify({}));
      console.log('已请求日程提醒检查');
    } else {
      console.warn('WebSocket未连接，无法发送提醒请求');
    }
  }

  // 断开连接
  disconnect() {
    if (this.stompClient && this.connected) {
      this.stompClient.disconnect(() => {
        console.log('WebSocket连接已断开');
        this.connected = false;
      });
    }
  }

  // 自定义提醒处理回调（可重写）
  onReminderReceived(reminder) {
    // 子类可以重写此方法来自定义处理逻辑
    console.log('默认提醒处理:', reminder);
  }
}

// 使用示例
const remindClient = new WenshuRemindClient('http://localhost:1015', 'your-jwt-token');

// 发送会议提醒邮件
remindClient.sendSimpleEmail(
  '<EMAIL>',
  '会议提醒',
  '您的项目讨论会将在30分钟后开始，请准时参加。'
);

// 发送HTML格式的周报邮件
const htmlContent = `
  <h3>本周工作总结</h3>
  <ul>
    <li>完成项目需求分析</li>
    <li>设计系统架构</li>
    <li>编写API文档</li>
  </ul>
  <p>下周计划：开始编码实现</p>
`;

remindClient.sendHtmlEmail(
  '<EMAIL>',
  '周工作总结',
  htmlContent
);

// WebSocket提醒连接
const reminderWS = new WenshuReminderWebSocket(
  'http://localhost:1015',
  1001,
  '广东',
  '深圳',
  '<EMAIL>',
  'your-jwt-token'
);

// 连接并设置自动提醒检查
reminderWS.connect().then(() => {
  // 每分钟检查一次提醒
  setInterval(() => {
    reminderWS.requestReminderCheck();
  }, 60000);
});

// 自定义提醒处理
reminderWS.onReminderReceived = function(reminder) {
  // 在页面上显示提醒
  const reminderDiv = document.createElement('div');
  reminderDiv.className = 'reminder-notification';
  reminderDiv.innerHTML = `
    <h4>${reminder.data.event.title}</h4>
    <p>${reminder.data.aiChatResponse}</p>
    <small>开始时间: ${reminder.data.event.startTime}</small>
  `;
  document.body.appendChild(reminderDiv);

  // 3秒后自动隐藏
  setTimeout(() => {
    reminderDiv.remove();
  }, 3000);
};
```

#### Python集成示例
```python
import requests
import json
import websocket
import threading
from typing import Optional, Dict, Any

class WenshuRemindClient:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {token}'
        }

    def send_simple_email(self, to: str, subject: str, text: str) -> Dict[str, Any]:
        """发送纯文本邮件"""
        url = f"{self.base_url}/remind/send-simple"
        data = {
            'to': to,
            'subject': subject,
            'text': text
        }

        response = requests.post(url, headers=self.headers, data=data)
        result = response.json()

        if result['code'] == 200:
            print(f"邮件发送成功: {result['data']}")
            return result
        else:
            raise Exception(f"邮件发送失败: {result['msg']}")

    def send_html_email(self, to: str, subject: str, html_content: str) -> Dict[str, Any]:
        """发送HTML邮件"""
        url = f"{self.base_url}/remind/send-html"
        data = {
            'to': to,
            'subject': subject,
            'htmlContent': html_content
        }

        response = requests.post(url, headers=self.headers, data=data)
        return response.json()

    def send_attachment_email(self, to: str, subject: str, text: str, file_path: str) -> Dict[str, Any]:
        """发送带附件邮件"""
        url = f"{self.base_url}/remind/send-attachment"

        with open(file_path, 'rb') as f:
            files = {'attachment': f}
            data = {
                'to': to,
                'subject': subject,
                'text': text
            }
            response = requests.post(url, headers=self.headers, files=files, data=data)

        return response.json()

class WenshuReminderWebSocket:
    def __init__(self, base_url: str, user_id: int, province: str, city: str, email: str, token: str):
        self.base_url = base_url
        self.user_id = user_id
        self.province = province
        self.city = city
        self.email = email
        self.token = token
        self.ws = None
        self.connected = False

    def on_message(self, ws, message):
        """处理接收到的消息"""
        try:
            reminder = json.loads(message)
            print(f"收到提醒: {reminder}")

            if reminder.get('data', {}).get('reminderRequired'):
                event = reminder['data']['event']
                ai_response = reminder['data']['aiChatResponse']

                print(f"提醒事件: {event['title']}")
                print(f"AI回复: {ai_response}")

                # 调用自定义处理方法
                self.handle_reminder(reminder)
        except Exception as e:
            print(f"消息处理错误: {e}")

    def on_error(self, ws, error):
        """处理WebSocket错误"""
        print(f"WebSocket错误: {error}")
        self.connected = False

    def on_close(self, ws, close_status_code, close_msg):
        """处理WebSocket关闭"""
        print("WebSocket连接已关闭")
        self.connected = False

    def on_open(self, ws):
        """WebSocket连接打开"""
        print("WebSocket连接已建立")
        self.connected = True

        # 发送订阅消息
        subscribe_msg = {
            "command": "SUBSCRIBE",
            "destination": f"/topic/programme/{self.user_id}/{self.province}/{self.city}",
            "headers": {
                "Authorization": f"Bearer {self.token}"
            }
        }
        ws.send(json.dumps(subscribe_msg))

    def connect(self):
        """连接WebSocket"""
        ws_url = f"ws://localhost:1015/websocket-endpoint"
        self.ws = websocket.WebSocketApp(
            ws_url,
            on_open=self.on_open,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close
        )

        # 在新线程中运行WebSocket
        ws_thread = threading.Thread(target=self.ws.run_forever)
        ws_thread.daemon = True
        ws_thread.start()

    def request_reminder_check(self):
        """请求提醒检查"""
        if self.connected and self.ws:
            check_msg = {
                "command": "SEND",
                "destination": f"/remind/programme/{self.user_id}/{self.province}/{self.city}/{self.email}",
                "body": "{}"
            }
            self.ws.send(json.dumps(check_msg))
            print("已请求提醒检查")

    def handle_reminder(self, reminder):
        """自定义提醒处理方法（可重写）"""
        # 子类可以重写此方法
        pass

    def disconnect(self):
        """断开WebSocket连接"""
        if self.ws:
            self.ws.close()

# 使用示例
if __name__ == "__main__":
    # 邮件服务示例
    client = WenshuRemindClient('http://localhost:1015', 'your-jwt-token')

    # 发送简单邮件
    client.send_simple_email(
        '<EMAIL>',
        '系统通知',
        '您的账户密码将在7天后过期，请及时更新。'
    )

    # 发送HTML邮件
    html_content = """
    <div style="font-family: Arial, sans-serif;">
        <h2 style="color: #2c3e50;">系统维护通知</h2>
        <p>尊敬的用户，</p>
        <p>我们将在今晚<strong>22:00-24:00</strong>进行系统维护，期间服务可能暂时不可用。</p>
        <p>给您带来的不便，敬请谅解。</p>
        <hr>
        <p style="color: #7f8c8d;">此邮件由系统自动发送，请勿回复。</p>
    </div>
    """

    client.send_html_email(
        '<EMAIL>',
        '系统维护通知',
        html_content
    )

    # WebSocket提醒示例
    class CustomReminderHandler(WenshuReminderWebSocket):
        def handle_reminder(self, reminder):
            """自定义提醒处理"""
            event = reminder['data']['event']

            # 记录到日志文件
            with open('reminders.log', 'a', encoding='utf-8') as f:
                f.write(f"{event['startTime']}: {event['title']}\n")

            # 发送邮件通知
            client.send_simple_email(
                self.email,
                f"提醒: {event['title']}",
                reminder['data']['aiChatResponse']
            )

    # 创建WebSocket连接
    reminder_ws = CustomReminderHandler(
        'http://localhost:1015',
        1001,
        '广东',
        '深圳',
        '<EMAIL>',
        'your-jwt-token'
    )

    reminder_ws.connect()

    # 定期检查提醒
    import time
    while True:
        time.sleep(60)  # 每分钟检查一次
        reminder_ws.request_reminder_check()
```

---

## 🎯 使用场景示例

### 场景1: 智能会议提醒系统
```javascript
// 会议提醒管理器
class MeetingReminderManager {
  constructor(remindClient, reminderWS) {
    this.remindClient = remindClient;
    this.reminderWS = reminderWS;
    this.reminderHistory = new Map();
  }

  // 设置会议提醒
  async setupMeetingReminder(meeting, attendees) {
    const reminderTime = new Date(meeting.startTime);
    reminderTime.setMinutes(reminderTime.getMinutes() - 10); // 提前10分钟

    // 生成会议提醒邮件内容
    const htmlContent = this.generateMeetingReminderHTML(meeting);

    // 发送邮件给所有参会者
    for (const attendee of attendees) {
      try {
        await this.remindClient.sendHtmlEmail(
          attendee.email,
          `会议提醒: ${meeting.title}`,
          htmlContent
        );

        console.log(`会议提醒已发送给: ${attendee.email}`);
      } catch (error) {
        console.error(`发送提醒失败 ${attendee.email}:`, error);
      }
    }

    // 设置WebSocket实时提醒
    this.scheduleWebSocketReminder(meeting, reminderTime);
  }

  generateMeetingReminderHTML(meeting) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px;">
        <h2 style="color: #2c3e50;">📅 会议提醒</h2>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
          <h3 style="color: #e74c3c;">${meeting.title}</h3>
          <p><strong>时间:</strong> ${meeting.startTime}</p>
          <p><strong>地点:</strong> ${meeting.location}</p>
          <p><strong>时长:</strong> ${meeting.duration}</p>
          <div style="margin-top: 15px;">
            <h4>会议议程:</h4>
            <ul>
              ${meeting.agenda.map(item => `<li>${item}</li>`).join('')}
            </ul>
          </div>
          <div style="margin-top: 20px; padding: 15px; background: #e8f5e8; border-radius: 5px;">
            <p><strong>温馨提示:</strong> 请提前5分钟到达会议室，确保设备正常运行。</p>
          </div>
        </div>
      </div>
    `;
  }

  scheduleWebSocketReminder(meeting, reminderTime) {
    const now = new Date();
    const delay = reminderTime.getTime() - now.getTime();

    if (delay > 0) {
      setTimeout(() => {
        this.reminderWS.requestReminderCheck();
      }, delay);
    }
  }
}
```

### 场景2: 智能工作报告系统
```javascript
// 工作报告提醒系统
class WorkReportReminderSystem {
  constructor(remindClient) {
    this.remindClient = remindClient;
    this.reportTemplates = new Map();
  }

  // 发送周报提醒
  async sendWeeklyReportReminder(employees) {
    const htmlContent = this.generateWeeklyReportTemplate();

    for (const employee of employees) {
      await this.remindClient.sendHtmlEmail(
        employee.email,
        '📊 周报提交提醒',
        htmlContent.replace('{{employeeName}}', employee.name)
      );
    }
  }

  // 发送项目进度报告
  async sendProjectProgressReport(project, stakeholders, progressData) {
    const htmlContent = this.generateProgressReportHTML(project, progressData);

    // 生成进度图表附件
    const chartFile = await this.generateProgressChart(progressData);

    for (const stakeholder of stakeholders) {
      await this.remindClient.sendAttachmentEmail(
        stakeholder.email,
        `项目进度报告: ${project.name}`,
        `请查收${project.name}项目的最新进度报告。`,
        chartFile
      );
    }
  }

  generateWeeklyReportTemplate() {
    return `
      <div style="font-family: Arial, sans-serif;">
        <h2 style="color: #3498db;">📋 周报提交提醒</h2>
        <p>亲爱的 {{employeeName}}，</p>
        <p>请在本周五下午6点前提交您的周工作报告。</p>

        <div style="background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <h3>报告内容包括:</h3>
          <ul>
            <li>本周完成的主要工作</li>
            <li>遇到的问题和解决方案</li>
            <li>下周工作计划</li>
            <li>需要的支持和资源</li>
          </ul>
        </div>

        <p>如有疑问，请及时联系您的直属主管。</p>
        <p>谢谢配合！</p>
      </div>
    `;
  }

  generateProgressReportHTML(project, progressData) {
    return `
      <div style="font-family: Arial, sans-serif;">
        <h2 style="color: #27ae60;">📈 项目进度报告</h2>
        <h3>${project.name}</h3>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
            <div>
              <strong>总体进度:</strong> ${progressData.overallProgress}%
            </div>
            <div>
              <strong>预计完成:</strong> ${progressData.estimatedCompletion}
            </div>
          </div>

          <h4>各模块进度:</h4>
          <ul>
            ${progressData.modules.map(module =>
              `<li>${module.name}: ${module.progress}%
               <span style="color: ${module.status === 'on-track' ? 'green' : 'red'};">
                 (${module.status === 'on-track' ? '按计划' : '延期'})
               </span>
              </li>`
            ).join('')}
          </ul>

          <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 5px;">
            <h4>风险提示:</h4>
            <ul>
              ${progressData.risks.map(risk => `<li>${risk}</li>`).join('')}
            </ul>
          </div>
        </div>
      </div>
    `;
  }
}
```

### 场景3: 智能客户服务提醒
```javascript
// 客户服务提醒系统
class CustomerServiceReminderSystem {
  constructor(remindClient, reminderWS) {
    this.remindClient = remindClient;
    this.reminderWS = reminderWS;
    this.serviceTemplates = new Map();
  }

  // 发送客户生日祝福
  async sendBirthdayGreeting(customer) {
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; text-align: center;">
        <h2 style="color: #e74c3c;">🎉 生日快乐！</h2>
        <p>亲爱的 ${customer.name}，</p>
        <p>在这个特殊的日子里，我们向您送上最真挚的祝福！</p>

        <div style="background: linear-gradient(45deg, #ff6b6b, #feca57);
                    color: white; padding: 20px; border-radius: 10px; margin: 20px 0;">
          <h3>🎂 生日特惠</h3>
          <p>专属优惠码: BIRTHDAY2024</p>
          <p>享受全场8折优惠，有效期至月底</p>
        </div>

        <p>感谢您一直以来的支持和信任！</p>
        <p>祝您生日快乐，身体健康，工作顺利！</p>
      </div>
    `;

    await this.remindClient.sendHtmlEmail(
      customer.email,
      '🎂 生日快乐 - 专属优惠等您来领取',
      htmlContent
    );
  }

  // 发送服务到期提醒
  async sendServiceExpirationReminder(customer, service) {
    const daysLeft = this.calculateDaysLeft(service.expirationDate);

    const htmlContent = `
      <div style="font-family: Arial, sans-serif;">
        <h2 style="color: #f39c12;">⏰ 服务到期提醒</h2>
        <p>尊敬的 ${customer.name}，</p>
        <p>您的 <strong>${service.name}</strong> 服务将在 <strong>${daysLeft}</strong> 天后到期。</p>

        <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <h4>服务详情:</h4>
          <ul>
            <li>服务名称: ${service.name}</li>
            <li>到期时间: ${service.expirationDate}</li>
            <li>剩余天数: ${daysLeft} 天</li>
          </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <a href="${service.renewalLink}"
             style="background: #3498db; color: white; padding: 12px 24px;
                    text-decoration: none; border-radius: 5px; display: inline-block;">
            立即续费
          </a>
        </div>

        <p>如需帮助，请联系我们的客服团队。</p>
      </div>
    `;

    await this.remindClient.sendHtmlEmail(
      customer.email,
      `服务到期提醒 - ${service.name}`,
      htmlContent
    );
  }

  calculateDaysLeft(expirationDate) {
    const now = new Date();
    const expiration = new Date(expirationDate);
    const diffTime = expiration.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
}
```

---

---

## 🔧 高级配置

### 邮件服务高级配置
```yaml
# application.yml
spring:
  mail:
    host: smtp.qq.com
    port: 587
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD}
    protocol: smtp
    test-connection: false
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          ssl:
            trust: smtp.qq.com
          connectiontimeout: 5000
          timeout: 3000
          writetimeout: 5000
        debug: false

# 邮件模板配置
wenshu:
  mail:
    templates:
      simple:
        from-name: "文书智能助手"
        reply-to: "<EMAIL>"
      html:
        template-path: "classpath:templates/email/"
        default-template: "default.html"
      attachment:
        max-size: 25MB
        allowed-types:
          - "application/pdf"
          - "application/msword"
          - "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
          - "image/jpeg"
          - "image/png"
```

### WebSocket高级配置
```yaml
# WebSocket性能调优
websocket:
  stomp:
    # 心跳配置
    heartbeat:
      client: 10000    # 客户端心跳间隔(ms)
      server: 10000    # 服务端心跳间隔(ms)

    # 消息大小限制
    message:
      size-limit: 65536        # 消息大小限制(bytes)
      buffer-size: 8192        # 缓冲区大小(bytes)

    # 连接配置
    connection:
      max-sessions: 1000       # 最大连接数
      session-timeout: 300000  # 会话超时(ms)

  # 线程池配置
  task-executor:
    core-pool-size: 10
    max-pool-size: 50
    queue-capacity: 100
    thread-name-prefix: "websocket-"
```

### 智能提醒算法配置
```yaml
wenshu:
  reminder:
    # 提醒检查配置
    check:
      interval: 60000          # 检查间隔(ms)
      batch-size: 100          # 批处理大小
      max-retry: 3             # 最大重试次数

    # AI内容生成配置
    ai:
      enabled: true
      fallback-template: "您有一个重要日程即将开始，请注意时间安排。"
      weather-integration: true
      context-length: 500

    # 天气服务配置
    weather:
      api-key: ${WEATHER_API_KEY}
      cache-duration: 1800000  # 缓存时长(ms)
      timeout: 5000           # 请求超时(ms)

    # 提醒规则配置
    rules:
      advance-minutes: 10      # 提前提醒分钟数
      max-daily-reminders: 50  # 每日最大提醒数
      quiet-hours:            # 免打扰时间
        start: "22:00"
        end: "08:00"
```

### 缓存配置
```java
@Configuration
@EnableCaching
public class CacheConfig {

    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(Duration.ofMinutes(30))
            .recordStats());
        return cacheManager;
    }

    @Bean
    public CacheManager weatherCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager("weather");
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(500)
            .expireAfterWrite(Duration.ofMinutes(30))
            .recordStats());
        return cacheManager;
    }
}
```

### 异步处理配置
```java
@Configuration
@EnableAsync
public class AsyncConfig {

    @Bean(name = "emailTaskExecutor")
    public TaskExecutor emailTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("email-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean(name = "reminderTaskExecutor")
    public TaskExecutor reminderTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(3);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("reminder-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}
```

---

## 📊 监控和运维

### 健康检查配置
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
      show-components: always
  health:
    mail:
      enabled: true
    websocket:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
```

### 自定义健康检查
```java
@Component
public class EmailServiceHealthIndicator implements HealthIndicator {

    private final JavaMailSender mailSender;

    public EmailServiceHealthIndicator(JavaMailSender mailSender) {
        this.mailSender = mailSender;
    }

    @Override
    public Health health() {
        try {
            // 测试邮件服务连接
            mailSender.testConnection();
            return Health.up()
                .withDetail("smtp", "连接正常")
                .withDetail("provider", "QQ邮箱")
                .build();
        } catch (Exception e) {
            return Health.down()
                .withDetail("smtp", "连接失败")
                .withDetail("error", e.getMessage())
                .build();
        }
    }
}

@Component
public class WebSocketHealthIndicator implements HealthIndicator {

    private final SimpMessagingTemplate messagingTemplate;
    private final AtomicInteger activeConnections = new AtomicInteger(0);

    @Override
    public Health health() {
        int connections = activeConnections.get();

        if (connections >= 0) {
            return Health.up()
                .withDetail("activeConnections", connections)
                .withDetail("maxConnections", 1000)
                .withDetail("status", "正常运行")
                .build();
        } else {
            return Health.down()
                .withDetail("status", "WebSocket服务异常")
                .build();
        }
    }

    public void incrementConnections() {
        activeConnections.incrementAndGet();
    }

    public void decrementConnections() {
        activeConnections.decrementAndGet();
    }
}
```

### 监控指标
```java
@Component
public class RemindMetrics {

    private final MeterRegistry meterRegistry;
    private final Counter emailSentCounter;
    private final Counter reminderSentCounter;
    private final Timer emailSendTimer;
    private final Gauge activeWebSocketConnections;

    public RemindMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;

        this.emailSentCounter = Counter.builder("remind.email.sent.total")
            .description("Total emails sent")
            .register(meterRegistry);

        this.reminderSentCounter = Counter.builder("remind.reminder.sent.total")
            .description("Total reminders sent")
            .register(meterRegistry);

        this.emailSendTimer = Timer.builder("remind.email.send.duration")
            .description("Email send duration")
            .register(meterRegistry);

        this.activeWebSocketConnections = Gauge.builder("remind.websocket.connections.active")
            .description("Active WebSocket connections")
            .register(meterRegistry, this, RemindMetrics::getActiveConnections);
    }

    public void recordEmailSent(String type, boolean success) {
        emailSentCounter.increment(
            Tags.of("type", type, "status", success ? "success" : "failure")
        );
    }

    public void recordReminderSent(String channel) {
        reminderSentCounter.increment(Tags.of("channel", channel));
    }

    public Timer.Sample startEmailTimer() {
        return Timer.start(meterRegistry);
    }

    public void stopEmailTimer(Timer.Sample sample, String type) {
        sample.stop(emailSendTimer.tag("type", type));
    }

    private double getActiveConnections() {
        // 返回当前活跃的WebSocket连接数
        return WebSocketSessionManager.getActiveConnectionCount();
    }
}
```

### 日志配置
```yaml
logging:
  level:
    com.ruoyi.wenshuremind: DEBUG
    org.springframework.mail: DEBUG
    org.springframework.messaging: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: logs/wenshu-remind/remind.log
    max-size: 100MB
    max-history: 30

# 自定义日志配置
wenshu:
  logging:
    email:
      enabled: true
      level: INFO
      file: logs/wenshu-remind/email.log
    websocket:
      enabled: true
      level: DEBUG
      file: logs/wenshu-remind/websocket.log
    reminder:
      enabled: true
      level: INFO
      file: logs/wenshu-remind/reminder.log
```

---

## 🚀 部署指南

### Docker部署
```dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app

# 复制应用程序
COPY target/wenshu-remind-0.0.1-SNAPSHOT.jar app.jar

# 创建日志目录
RUN mkdir -p /app/logs/wenshu-remind

# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' > /etc/timezone

# 暴露端口
EXPOSE 1015

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:1015/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", "-jar", "-Xmx512m", "-Xms256m", "app.jar"]
```

### Docker Compose部署
```yaml
version: '3.8'

services:
  wenshu-remind:
    build: .
    container_name: wenshu-remind
    ports:
      - "1015:1015"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - NACOS_SERVER_ADDR=nacos:8848
      - MAIL_PASSWORD=${MAIL_PASSWORD}
      - WEATHER_API_KEY=${WEATHER_API_KEY}
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      - nacos
      - redis
    networks:
      - wenshu-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:1015/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  nacos:
    image: nacos/nacos-server:v2.2.3
    container_name: nacos
    ports:
      - "8848:8848"
    environment:
      - MODE=standalone
    networks:
      - wenshu-network

  redis:
    image: redis:7-alpine
    container_name: redis
    ports:
      - "6379:6379"
    networks:
      - wenshu-network

networks:
  wenshu-network:
    driver: bridge
```

### Kubernetes部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wenshu-remind
  labels:
    app: wenshu-remind
spec:
  replicas: 2
  selector:
    matchLabels:
      app: wenshu-remind
  template:
    metadata:
      labels:
        app: wenshu-remind
    spec:
      containers:
      - name: wenshu-remind
        image: wenshu/remind:1.0.0
        ports:
        - containerPort: 1015
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: NACOS_SERVER_ADDR
          value: "nacos-service:8848"
        - name: MAIL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mail-secret
              key: password
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 1015
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 1015
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: logs
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: wenshu-remind-service
spec:
  selector:
    app: wenshu-remind
  ports:
  - port: 1015
    targetPort: 1015
  type: ClusterIP

---
apiVersion: v1
kind: Secret
metadata:
  name: mail-secret
type: Opaque
data:
  password: <base64-encoded-password>
```

### 生产环境配置
```yaml
# application-prod.yml
spring:
  profiles:
    active: prod

  # 邮件服务生产配置
  mail:
    host: smtp.qq.com
    port: 465
    username: ${MAIL_USERNAME}
    password: ${MAIL_PASSWORD}
    properties:
      mail:
        smtp:
          auth: true
          ssl:
            enable: true
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory
            port: 465

# 生产环境监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  metrics:
    export:
      prometheus:
        enabled: true
        step: 60s

# 日志配置
logging:
  level:
    root: INFO
    com.ruoyi.wenshuremind: INFO
  file:
    name: /app/logs/wenshu-remind/application.log
    max-size: 100MB
    max-history: 30
```

---

## 📞 技术支持

### 常见问题解答

**Q1: 邮件发送失败怎么办？**
A: 检查SMTP配置、网络连接和邮箱授权码是否正确。

**Q2: WebSocket连接断开怎么处理？**
A: 客户端应实现自动重连机制，服务端会自动清理断开的连接。

**Q3: 如何自定义邮件模板？**
A: 在resources/templates/email/目录下创建HTML模板文件。

**Q4: 提醒不及时怎么办？**
A: 检查系统时间同步，确认日程提醒时间设置正确。

**Q5: 如何扩展提醒渠道？**
A: 实现RemindChannel接口，添加新的提醒方式（如短信、钉钉等）。

### 性能优化建议

1. **邮件发送优化**
   - 使用异步发送避免阻塞
   - 实现邮件发送队列
   - 配置连接池复用连接

2. **WebSocket优化**
   - 合理设置心跳间隔
   - 实现消息压缩
   - 使用集群模式支持更多连接

3. **缓存优化**
   - 缓存天气信息减少API调用
   - 缓存用户配置信息
   - 使用Redis集群提高可用性

### 联系方式
- **技术支持**: <EMAIL>
- **API文档**: https://docs.wenshu.com/remind
- **问题反馈**: https://github.com/wenshu/remind/issues

---

*文档版本: v1.0.0 | 最后更新: 2024-12-28 | Wenshu-Remind智能提醒服务*
