package com.ruoyi.wenshuremind.model;

public class Message<T> {
    private int code;
    private String message;
    private T data;

    public static <T> Message<T> success(String message, T data) {
        Message<T> result = new Message<>();
        result.setCode(200);
        result.setMessage(message);
        result.setData(data);
        return result;
    }

    public static <T> Message<T> error(int code, String message) {
        Message<T> result = new Message<>();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }
    public static <T> Message<T> error(int code, String message,T data) {
        Message<T> result = new Message<>();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
// getters/setters 省略
}