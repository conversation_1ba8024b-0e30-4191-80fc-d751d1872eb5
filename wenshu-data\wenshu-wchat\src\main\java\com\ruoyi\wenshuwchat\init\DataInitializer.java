//package com.ruoyi.wenshuwchat.init;
//
//import com.ruoyi.wenshuapi.pojo.wchat.ChatRecord;
//import com.ruoyi.wenshuapi.pojo.wchat.FriendList;
//import com.ruoyi.wenshuwchat.config.DataInitConfig;
//import com.ruoyi.wenshuwchat.service.ChatRecordService;
//import com.ruoyi.wenshuwchat.service.FriendListService;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.ApplicationArguments;
//import org.springframework.boot.ApplicationRunner;
//import org.springframework.stereotype.Component;
//
//import java.time.LocalDateTime;
//import java.util.*;
//
///**
// * 数据初始化器
// * 在应用启动时自动添加虚拟数据
// */
//@Component
//public class DataInitializer implements ApplicationRunner {
//
//    private static final Logger logger = LoggerFactory.getLogger(DataInitializer.class);
//
//    private final FriendListService friendListService;
//    private final ChatRecordService chatRecordService;
//    private final DataInitConfig dataInitConfig;
//
//    // 预定义的聊天消息模板
//    private static final String[] TEXT_MESSAGES = {
//        "你好！", "最近怎么样？", "今天天气不错", "有空一起吃饭吗？",
//        "工作顺利吗？", "周末有什么计划？", "谢谢你的帮助", "晚安！",
//        "早上好！", "中午好！", "下午好！", "晚上好！",
//        "今天过得怎么样？", "有什么新鲜事吗？", "最近在忙什么？",
//        "这个想法不错", "我同意你的观点", "让我想想", "好的，没问题",
//        "哈哈哈", "😊", "👍", "❤️", "🎉"
//    };
//
//    // 文件路径模板（模拟已上传的文件）
//    private static final String[] IMAGE_PATHS = {
//        "image/2024/01/15/1705123456789_abc123.jpg",
//        "image/2024/01/15/1705123456790_def456.png",
//        "image/2024/01/16/1705123456791_ghi789.gif",
//        "image/2024/01/16/1705123456792_jkl012.webp"
//    };
//
//    private static final String[] VIDEO_PATHS = {
//        "video/2024/01/15/1705123456793_mno345.mp4",
//        "video/2024/01/16/1705123456794_pqr678.avi",
//        "video/2024/01/16/1705123456795_stu901.mov"
//    };
//
//    private static final String[] FILE_PATHS = {
//        "file/2024/01/15/1705123456796_vwx234.pdf",
//        "file/2024/01/15/1705123456797_yza567.docx",
//        "file/2024/01/16/1705123456798_bcd890.xlsx",
//        "file/2024/01/16/1705123456799_efg123.pptx"
//    };
//
//    @Autowired
//    public DataInitializer(FriendListService friendListService, ChatRecordService chatRecordService, DataInitConfig dataInitConfig) {
//        this.friendListService = friendListService;
//        this.chatRecordService = chatRecordService;
//        this.dataInitConfig = dataInitConfig;
//    }
//
//    @Override
//    public void run(ApplicationArguments args) throws Exception {
//        // 检查是否启用数据初始化
//        if (!dataInitConfig.isEnabled()) {
//            logger.info("数据初始化已禁用，跳过初始化");
//            return;
//        }
//
//        logger.info("开始初始化虚拟数据...");
//
//        try {
//            // 检查是否已经有数据，避免重复初始化
//            if (hasExistingData()) {
//                logger.info("检测到已有数据，跳过初始化");
//                return;
//            }
//
//            // 初始化好友关系数据
//            initFriendListData();
//
//            // 初始化聊天记录数据
//            initChatRecordData();
//
//            logger.info("虚拟数据初始化完成！");
//        } catch (Exception e) {
//            logger.error("虚拟数据初始化失败: {}", e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 检查是否已有数据
//     */
//    private boolean hasExistingData() {
//        try {
//            List<FriendList> friends = friendListService.getAllFriends();
//            return !friends.isEmpty();
//        } catch (Exception e) {
//            logger.warn("检查现有数据时出错: {}", e.getMessage());
//            return false;
//        }
//    }
//
//    /**
//     * 初始化好友关系数据
//     */
//    private void initFriendListData() {
//        logger.info("开始初始化好友关系数据...");
//
//        Random random = new Random();
//        Set<String> addedRelations = new HashSet<>();
//        int friendCount = 0;
//
//        // 生成配置数量的好友关系
//        while (friendCount < dataInitConfig.getFriendCount()) {
//            Long userId = getRandomUserId(random);
//            Long friendId = getRandomUserId(random);
//
//            // 确保不是自己和自己的关系，且关系未添加过
//            if (!userId.equals(friendId)) {
//                String relationKey = Math.min(userId, friendId) + "-" + Math.max(userId, friendId);
//                if (!addedRelations.contains(relationKey)) {
//                    try {
//                        FriendList friendList = new FriendList();
//                        friendList.setUserId(userId);
//                        friendList.setFriendId(friendId);
//                        friendList.setStatus("active");
//                        friendList.setCreatedAt(getRandomDateTime(random));
//                        friendList.setUpdatedAt(LocalDateTime.now());
//
//                        friendListService.addFriend(friendList);
//                        addedRelations.add(relationKey);
//                        friendCount++;
//
//                        logger.debug("添加好友关系: {} <-> {}", userId, friendId);
//                    } catch (Exception e) {
//                        logger.warn("添加好友关系失败: {}", e.getMessage());
//                    }
//                }
//            }
//        }
//
//        logger.info("好友关系数据初始化完成，共添加 {} 条记录", friendCount);
//    }
//
//    /**
//     * 初始化聊天记录数据
//     */
//    private void initChatRecordData() {
//        logger.info("开始初始化聊天记录数据...");
//
//        Random random = new Random();
//        int chatCount = 0;
//
//        // 获取所有好友关系，为他们生成聊天记录
//        List<FriendList> friendList = friendListService.getAllFriends();
//
//        for (FriendList friend : friendList) {
//            // 为每对好友生成2-5条聊天记录
//            int messageCount = 2 + random.nextInt(4);
//
//            for (int i = 0; i < messageCount; i++) {
//                try {
//                    ChatRecord chatRecord = generateRandomChatRecord(random, friend.getUserId(), friend.getFriendId());
//                    chatRecordService.sendMessage(chatRecord);
//                    chatCount++;
//
//                    logger.debug("添加聊天记录: {} -> {}, 类型: {}",
//                        chatRecord.getSenderId(), chatRecord.getReceiverId(), chatRecord.getContentType());
//                } catch (Exception e) {
//                    logger.warn("添加聊天记录失败: {}", e.getMessage());
//                }
//            }
//        }
//
//        // 如果聊天记录不足配置数量，继续生成随机聊天记录
//        while (chatCount < dataInitConfig.getChatCount()) {
//            try {
//                Long senderId = getRandomUserId(random);
//                Long receiverId = getRandomUserId(random);
//
//                if (!senderId.equals(receiverId)) {
//                    ChatRecord chatRecord = generateRandomChatRecord(random, senderId, receiverId);
//                    chatRecordService.sendMessage(chatRecord);
//                    chatCount++;
//                }
//            } catch (Exception e) {
//                logger.warn("添加随机聊天记录失败: {}", e.getMessage());
//            }
//        }
//
//        logger.info("聊天记录数据初始化完成，共添加 {} 条记录", chatCount);
//    }
//
//    /**
//     * 生成随机聊天记录
//     */
//    private ChatRecord generateRandomChatRecord(Random random, Long senderId, Long receiverId) {
//        ChatRecord chatRecord = new ChatRecord();
//        chatRecord.setSenderId(senderId);
//        chatRecord.setReceiverId(receiverId);
//        chatRecord.setSendTime(getRandomDateTime(random));
//        chatRecord.setIsRead(random.nextBoolean());
//
//        // 随机选择消息类型
//        String[] contentTypes = {"text", "image", "video", "file"};
//        String contentType = contentTypes[random.nextInt(contentTypes.length)];
//        chatRecord.setContentType(contentType);
//
//        // 根据类型设置内容
//        switch (contentType) {
//            case "text":
//                chatRecord.setContent(TEXT_MESSAGES[random.nextInt(TEXT_MESSAGES.length)]);
//                break;
//            case "image":
//                chatRecord.setContent(IMAGE_PATHS[random.nextInt(IMAGE_PATHS.length)]);
//                break;
//            case "video":
//                chatRecord.setContent(VIDEO_PATHS[random.nextInt(VIDEO_PATHS.length)]);
//                break;
//            case "file":
//                chatRecord.setContent(FILE_PATHS[random.nextInt(FILE_PATHS.length)]);
//                break;
//        }
//
//        return chatRecord;
//    }
//
//    /**
//     * 获取随机用户ID
//     */
//    private Long getRandomUserId(Random random) {
//        Long minId = dataInitConfig.getMinUserId();
//        Long maxId = dataInitConfig.getMaxUserId();
//        return minId + random.nextInt((int) (maxId - minId + 1));
//    }
//
//    /**
//     * 获取随机时间（最近30天内）
//     */
//    private LocalDateTime getRandomDateTime(Random random) {
//        LocalDateTime now = LocalDateTime.now();
//        int daysAgo = random.nextInt(30);
//        int hoursAgo = random.nextInt(24);
//        int minutesAgo = random.nextInt(60);
//
//        return now.minusDays(daysAgo).minusHours(hoursAgo).minusMinutes(minutesAgo);
//    }
//}
