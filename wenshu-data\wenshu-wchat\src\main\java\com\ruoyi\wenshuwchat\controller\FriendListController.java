package com.ruoyi.wenshuwchat.controller;

import com.ruoyi.wenshuapi.pojo.wchat.FriendList;
import com.ruoyi.wenshuwchat.service.FriendListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 好友关系控制器
 * 提供好友关系管理的RESTful API接口
 * 顶层路径: /wenshu/wchat/friend
 */
@RestController
@RequestMapping("/wenshu/wchat/friend")
public class FriendListController {

    private final FriendListService friendListService;

    @Autowired
    public FriendListController(FriendListService friendListService) {
        this.friendListService = friendListService;
    }

    /**
     * 添加好友关系
     *
     * @param friendList 好友关系实体
     * @return 包含新好友关系ID的响应
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> addFriend(@RequestBody FriendList friendList) {
        Map<String, Object> response = new HashMap<>();
        try {
            Long friendId = friendListService.addFriend(friendList);
            response.put("success", true);
            response.put("message", "好友添加成功");
            response.put("data", friendId);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "好友添加失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 删除好友关系
     *
     * @param id 好友关系ID
     * @return 删除结果响应
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteFriend(@PathVariable("id") Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            int result = friendListService.deleteFriend(id);
            response.put("success", true);
            response.put("message", "好友删除成功");
            response.put("data", result);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "好友删除失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据用户ID和好友ID删除好友关系
     *
     * @param userId 用户ID
     * @param friendId 好友ID
     * @return 删除结果响应
     */
    @DeleteMapping("/user/{userId}/friend/{friendId}")
    public ResponseEntity<Map<String, Object>> deleteFriendByUserAndFriend(
            @PathVariable("userId") Long userId, @PathVariable("friendId") Long friendId) {
        Map<String, Object> response = new HashMap<>();
        try {
            int result = friendListService.deleteFriendByUserAndFriend(userId, friendId);
            response.put("success", true);
            response.put("message", "好友关系删除成功");
            response.put("data", result);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "好友关系删除失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 更新好友关系状态
     *
     * @param friendList 好友关系实体
     * @return 更新结果响应
     */
    @PutMapping
    public ResponseEntity<Map<String, Object>> updateFriendStatus(@RequestBody FriendList friendList) {
        Map<String, Object> response = new HashMap<>();
        try {
            int result = friendListService.updateFriendStatus(friendList);
            response.put("success", true);
            response.put("message", "好友状态更新成功");
            response.put("data", result);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "好友状态更新失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据ID获取好友关系详情
     *
     * @param id 好友关系ID
     * @return 好友关系详情响应
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getFriendById(@PathVariable("id") Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            FriendList friend = friendListService.getFriendById(id);
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", friend);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据用户ID获取好友列表
     *
     * @param userId 用户ID
     * @return 好友列表响应
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<Map<String, Object>> getFriendsByUserId(@PathVariable("userId") Long userId) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<FriendList> friends = friendListService.getFriendsByUserId(userId);
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", friends);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据用户ID和状态获取好友列表
     *
     * @param userId 用户ID
     * @param status 关系状态
     * @return 好友列表响应
     */
    @GetMapping("/user/{userId}/status/{status}")
    public ResponseEntity<Map<String, Object>> getFriendsByUserIdAndStatus(
            @PathVariable("userId") Long userId, @PathVariable("status") String status) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<FriendList> friends = friendListService.getFriendsByUserIdAndStatus(userId, status);
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", friends);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 检查好友关系
     *
     * @param userId 用户ID
     * @param friendId 好友ID
     * @return 好友关系检查结果响应
     */
    @GetMapping("/check/{userId}/{friendId}")
    public ResponseEntity<Map<String, Object>> checkFriendship(
            @PathVariable("userId") Long userId, @PathVariable("friendId") Long friendId) {
        Map<String, Object> response = new HashMap<>();
        try {
            FriendList friendship = friendListService.checkFriendship(userId, friendId);
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", friendship);
            response.put("isFriend", friendship != null);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取用户的活跃好友列表
     *
     * @param userId 用户ID
     * @return 活跃好友列表响应
     */
    @GetMapping("/user/{userId}/active")
    public ResponseEntity<Map<String, Object>> getActiveFriends(@PathVariable("userId") Long userId) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<FriendList> friends = friendListService.getActiveFriends(userId);
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", friends);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取所有好友关系列表
     *
     * @return 所有好友关系列表响应
     */
    @GetMapping("/all")
    public ResponseEntity<Map<String, Object>> getAllFriends() {
        Map<String, Object> response = new HashMap<>();
        try {
            List<FriendList> friends = friendListService.getAllFriends();
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", friends);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
