package com.ruoyi.wenshuapi.pojo.multimodal;

import lombok.Data;

// Operation.java
@Data
public class Operation {
    private Type type;
    private int position;
    private String text;
    private String userId;
    private long revision;
    private long timestamp;

    public enum Type { INSERT, DELETE }

    // 添加构造函数
    public Operation() {
        this.timestamp = System.currentTimeMillis();
    }
}