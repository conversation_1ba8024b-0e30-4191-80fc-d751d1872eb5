<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi</artifactId>
            <version>3.6.4</version>
        </parent>
	<groupId>com.ruoyi</groupId>
	<artifactId>wenshu-meeting</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>wenshu-meeting</name>
	<description>wenshu-meeting</description>
	<url/>
	<licenses>
		<license/>
	</licenses>
	<developers>
		<developer/>
	</developers>
	<scm>
		<connection/>
		<developerConnection/>
		<tag/>
		<url/>
	</scm>
	<properties>
		<java.version>17</java.version>
	</properties>
	<dependencies>
	<dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp-sse</artifactId>
            <version>4.12.0</version> <!-- 使用最新稳定版 -->
        </dependency>
<!-- JavaCV 和 FFmpeg 依赖 -->
<dependency>
    <groupId>org.bytedeco</groupId>
    <artifactId>javacv</artifactId>
    <version>1.5.10</version>
</dependency>

<!-- FFmpeg platform dependencies for JavaCV -->
<!-- https://mvnrepository.com/artifact/org.bytedeco/ffmpeg-platform -->
<dependency>
    <groupId>org.bytedeco</groupId>
    <artifactId>ffmpeg-platform</artifactId>
    <version>7.1.1-1.5.12</version>
</dependency>
<!-- https://mvnrepository.com/artifact/org.springframework/spring-mock -->
<dependency>
    <groupId>org.springframework</groupId>
    <artifactId>spring-mock</artifactId>
    <version>2.0.8</version>
    <scope>test</scope>
</dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.12.0</version> <!-- 确保主库版本匹配 -->
        </dependency>
          <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>dashscope-sdk-java</artifactId>
                <version>2.20.3</version>
                 <exclusions>
                        <exclusion>
                            <groupId>org.slf4j</groupId>
                            <artifactId>slf4j-simple</artifactId>
                        </exclusion>
                    </exclusions>
            </dependency>
			<!-- Spring Boot Starter -->
        		<dependency>
        			<groupId>org.springframework.boot</groupId>
        			<artifactId>spring-boot-starter</artifactId>
        		</dependency>

                        <dependency>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-starter-data-redis</artifactId>
                        </dependency>
        		<!-- Spring Boot Web Starter -->
        		<dependency>
        			<groupId>org.springframework.boot</groupId>
        			<artifactId>spring-boot-starter-web</artifactId>
        		</dependency>

        		<!-- Spring Boot JPA Starter -->
        		<dependency>
        			<groupId>org.springframework.boot</groupId>
        			<artifactId>spring-boot-starter-data-jpa</artifactId>
        		</dependency>

        		<!-- MySQL Connector -->
        		<dependency>
        			<groupId>com.mysql</groupId>
        			<artifactId>mysql-connector-j</artifactId>
        			<scope>runtime</scope>
        		</dependency>

        		<!-- H2 Database for Testing -->
        		<dependency>
        			<groupId>com.h2database</groupId>
        			<artifactId>h2</artifactId>
        			<scope>test</scope>
        		</dependency>

        		<!-- WebSocket Support -->
        		<dependency>
        			<groupId>org.springframework.boot</groupId>
        			<artifactId>spring-boot-starter-websocket</artifactId>
        		</dependency>


        		<!-- Spring Cloud OpenFeign -->
        		<dependency>
        			<groupId>org.springframework.cloud</groupId>
        			<artifactId>spring-cloud-starter-openfeign</artifactId>
        		</dependency>

        		<!-- Spring Cloud Bootstrap -->
        		<dependency>
        			<groupId>org.springframework.cloud</groupId>
        			<artifactId>spring-cloud-starter-bootstrap</artifactId>
        		</dependency>

        		<!-- Nacos Discovery -->
        		<dependency>
        			<groupId>com.alibaba.cloud</groupId>
        			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        		</dependency>

        		<!-- Nacos Config -->
        		<dependency>
        			<groupId>com.alibaba.cloud</groupId>
        			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        		</dependency>

        		<!-- Spring Cloud LoadBalancer -->
        		<dependency>
        			<groupId>org.springframework.cloud</groupId>
        			<artifactId>spring-cloud-starter-loadbalancer</artifactId>
        		</dependency>

        		<!-- Spring Boot Actuator -->
        		<dependency>
        			<groupId>org.springframework.boot</groupId>
        			<artifactId>spring-boot-starter-actuator</artifactId>
        		</dependency>
  <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        		<!-- wenshu-api 客户端 -->
        		<dependency>
        			<groupId>com.ruoyi</groupId>
        			<artifactId>wenshu-api</artifactId>
        			<version>0.0.1-SNAPSHOT</version>
        		</dependency>

        		<!-- JSON处理 -->
        		<dependency>
        			<groupId>com.fasterxml.jackson.core</groupId>
        			<artifactId>jackson-databind</artifactId>
        		</dependency>

        		<!-- Jackson JSR310模块 - 支持Java 8日期时间类型 -->
        		<dependency>
        			<groupId>com.fasterxml.jackson.datatype</groupId>
        			<artifactId>jackson-datatype-jsr310</artifactId>
        		</dependency>

        		<!-- Lombok -->
        		<dependency>
        			<groupId>org.projectlombok</groupId>
        			<artifactId>lombok</artifactId>
        			<optional>true</optional>
        		</dependency>

        		<!-- Spring Boot Test -->
        		<dependency>
        			<groupId>org.springframework.boot</groupId>
        			<artifactId>spring-boot-starter-test</artifactId>
        			<scope>test</scope>
        		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>
