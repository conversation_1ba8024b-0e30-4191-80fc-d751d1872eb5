package com.ruoyi.wenshuapi.client.file;




import com.ruoyi.wenshuapi.fallback.file.FileUserTeamClientFallback;
import com.ruoyi.wenshuapi.pojo.file.FileUserTeamPojo;
import com.ruoyi.wenshuapi.util.file.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 文件-用户-团队关联关系 Feign 客户端
 * 用于其他服务调用 wenshu-file 服务的关联关系接口
 */
@FeignClient(
        value = "wenshu-file",
        path = "/wenshu/file",
        contextId = "fileUserTeamClient",// 关联关系服务
        fallbackFactory = FileUserTeamClientFallback.class)
public interface FileUserTeamClient {

    /**
     * 创建新的关联关系
     * @param pojo 关联实体数据
     * @return 创建结果
     */
    @PostMapping
    ApiResponse<FileUserTeamPojo> createAssociation(@RequestBody FileUserTeamPojo pojo);

    /**
     * 删除特定关联关系
     * @param fileId 文件ID
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 删除结果
     */
    @DeleteMapping("/{fileId}/{userId}/{teamId}")
    ApiResponse<String> deleteAssociation(
            @PathVariable("fileId") int fileId,
            @PathVariable("userId") int userId,
            @PathVariable("teamId") int teamId);

    /**
     * 删除文件相关的所有关联
     * @param fileId 文件ID
     * @return 删除结果
     */
    @DeleteMapping("/file/{fileId}")
    ApiResponse<String> deleteByFile(@PathVariable("fileId") int fileId);

    /**
     * 删除用户相关的所有关联
     * @param userId 用户ID
     * @return 删除结果
     */
    @DeleteMapping("/user/{userId}")
    ApiResponse<String> deleteByUser(@PathVariable("userId") int userId);

    /**
     * 删除团队相关的所有关联
     * @param teamId 团队ID
     * @return 删除结果
     */
    @DeleteMapping("/team/{teamId}")
    ApiResponse<String> deleteByTeam(@PathVariable("teamId") int teamId);

    /**
     * 获取特定关联关系详情
     * @param fileId 文件ID
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 关联实体详情
     */
    @GetMapping("/{fileId}/{userId}/{teamId}")
    ApiResponse<FileUserTeamPojo> getAssociation(
            @PathVariable("fileId") int fileId,
            @PathVariable("userId") int userId,
            @PathVariable("teamId") int teamId);

    /**
     * 获取文件相关的所有关联
     * @param fileId 文件ID
     * @return 关联列表
     */
    @GetMapping("/file/{fileId}")
    ApiResponse<List<FileUserTeamPojo>> getByFile(@PathVariable("fileId") int fileId);

    /**
     * 获取用户相关的所有关联
     * @param userId 用户ID
     * @return 关联列表
     */
    @GetMapping("/user/{userId}")
    ApiResponse<List<FileUserTeamPojo>> getByUser(@PathVariable("userId") int userId);

    /**
     * 获取团队相关的所有关联
     * @param teamId 团队ID
     * @return 关联列表
     */
    @GetMapping("/team/{teamId}")
    ApiResponse<List<FileUserTeamPojo>> getByTeam(@PathVariable("teamId") int teamId);

    /**
     * 更新关联关系
     * @param oldFileId 原文件ID
     * @param oldUserId 原用户ID
     * @param oldTeamId 原团队ID
     * @param newPojo 新关联数据
     * @return 更新结果
     */
    @PutMapping("/{oldFileId}/{oldUserId}/{oldTeamId}")
    ApiResponse<FileUserTeamPojo> updateAssociation(
            @PathVariable("oldFileId") int oldFileId,
            @PathVariable("oldUserId") int oldUserId,
            @PathVariable("oldTeamId") int oldTeamId,
            @RequestBody FileUserTeamPojo newPojo);
}