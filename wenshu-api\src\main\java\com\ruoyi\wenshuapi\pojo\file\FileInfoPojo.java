package com.ruoyi.wenshuapi.pojo.file;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;

import java.time.LocalDateTime;

public class FileInfoPojo {
    /**
     * 文件ID（主键）
     */
    private int fileId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件存储路径（允许NULL）
     */
    private String filePath;

    /**
     * 文件上传时间（允许NULL）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime uploadTime;

    /**
     * 上传者ID（允许NULL）
     */
    private Integer uploaderId;

    /**
     * 文件大小（字节，允许NULL）
     */
    private Long fileSize;

    /**
     * 文件状态（可读写/协作/不可读写）
     */
    private String fileStatus;

    /**
     * 所有者类型（个人/团队）
     */
    private String ownerType;

    // 无参构造方法
    public FileInfoPojo() {
    }

    // 全参构造方法
    public FileInfoPojo(int fileId, String fileName, String filePath, LocalDateTime uploadTime,
                    Integer uploaderId, Long fileSize, String fileStatus, String ownerType) {
        this.fileId = fileId;
        this.fileName = fileName;
        this.filePath = filePath;
        this.uploadTime = uploadTime;
        this.uploaderId = uploaderId;
        this.fileSize = fileSize;
        this.fileStatus = fileStatus;
        this.ownerType = ownerType;
    }

    // Getter和Setter方法
    public int getFileId() {
        return fileId;
    }

    public void setFileId(int fileId) {
        this.fileId = fileId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public LocalDateTime getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(LocalDateTime uploadTime) {
        this.uploadTime = uploadTime;
    }

    public Integer getUploaderId() {
        return uploaderId;
    }

    public void setUploaderId(Integer uploaderId) {
        this.uploaderId = uploaderId;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileStatus() {
        return fileStatus;
    }

    public void setFileStatus(String fileStatus) {
        this.fileStatus = fileStatus;
    }

    public String getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(String ownerType) {
        this.ownerType = ownerType;
    }

    // toString方法
    @Override
    public String toString() {
        return "FileInfo{" +
               "fileId=" + fileId +
               ", fileName='" + fileName + '\'' +
               ", filePath='" + filePath + '\'' +
               ", uploadTime=" + uploadTime +
               ", uploaderId=" + uploaderId +
               ", fileSize=" + fileSize +
               ", fileStatus='" + fileStatus + '\'' +
               ", ownerType='" + ownerType + '\'' +
               '}';
    }
}