package com.ruoyi.wenshuwchat.dto;

import org.springframework.web.multipart.MultipartFile;

/**
 * 发送消息请求DTO
 */
public class SendMessageRequest {
    
    private Long senderId;          // 发送者ID
    private Long receiverId;        // 接收者ID
    private String contentType;     // 内容类型: text, image, video, file
    private String textContent;     // 文本内容（当contentType为text时使用）
    private MultipartFile file;     // 文件（当contentType为image/video/file时使用）

    public SendMessageRequest() {
    }

    public SendMessageRequest(Long senderId, Long receiverId, String contentType, String textContent, MultipartFile file) {
        this.senderId = senderId;
        this.receiverId = receiverId;
        this.contentType = contentType;
        this.textContent = textContent;
        this.file = file;
    }

    public Long getSenderId() {
        return senderId;
    }

    public void setSenderId(Long senderId) {
        this.senderId = senderId;
    }

    public Long getReceiverId() {
        return receiverId;
    }

    public void setReceiverId(Long receiverId) {
        this.receiverId = receiverId;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getTextContent() {
        return textContent;
    }

    public void setTextContent(String textContent) {
        this.textContent = textContent;
    }

    public MultipartFile getFile() {
        return file;
    }

    public void setFile(MultipartFile file) {
        this.file = file;
    }

    /**
     * 验证请求参数
     */
    public String validate() {
        if (senderId == null) {
            return "发送者ID不能为空";
        }
        
        if (receiverId == null) {
            return "接收者ID不能为空";
        }
        
        if (contentType == null || contentType.trim().isEmpty()) {
            return "内容类型不能为空";
        }
        
        String type = contentType.toLowerCase().trim();
        if (!"text".equals(type) && !"image".equals(type) && !"video".equals(type) && !"file".equals(type)) {
            return "不支持的内容类型，仅支持: text, image, video, file";
        }
        
        if ("text".equals(type)) {
            if (textContent == null || textContent.trim().isEmpty()) {
                return "文本内容不能为空";
            }
            if (file != null && !file.isEmpty()) {
                return "发送文本消息时不能包含文件";
            }
        } else {
            if (file == null || file.isEmpty()) {
                return "发送" + type + "消息时必须包含文件";
            }
            if (textContent != null && !textContent.trim().isEmpty()) {
                return "发送文件消息时不能包含文本内容";
            }
        }
        
        return null; // 验证通过
    }

    @Override
    public String toString() {
        return "SendMessageRequest{" +
                "senderId=" + senderId +
                ", receiverId=" + receiverId +
                ", contentType='" + contentType + '\'' +
                ", textContent='" + textContent + '\'' +
                ", file=" + (file != null ? file.getOriginalFilename() : "null") +
                '}';
    }
}
