package com.ruoyi.wenshuapi.client.wchat;

import com.ruoyi.wenshuapi.fallback.wchat.FriendListClientFallback;
import com.ruoyi.wenshuapi.pojo.wchat.FriendList;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 好友关系服务Feign客户端接口
 * 用于微服务间远程调用好友关系管理功能
 * 
 * FeignClient参数说明：
 *   name = "wenshu-wchat"  // 目标服务在注册中心的应用名称
 *   path = "/wenshu/wchat/friend" // 统一路径前缀（与Controller顶层路径匹配）
 *   contextId = "friendListClient" // 防止与其他同名Feign冲突
 *   fallback = FriendListClientFallback.class // 降级处理类
 * 
 * 注意：接口方法需与Controller方法签名严格匹配（路径/参数/返回类型）
 */
@FeignClient(
    name = "wenshu-wchat",
    path = "/wenshu/wchat/friend",
    contextId = "friendListClient",
    fallback = FriendListClientFallback.class
)
public interface FriendListClient {

    /**
     * 添加好友关系
     *
     * @param friendList 好友关系实体
     * @return 包含新好友关系ID的响应
     */
    @PostMapping
    ResponseEntity<Map<String, Object>> addFriend(@RequestBody FriendList friendList);

    /**
     * 根据好友关系ID删除好友关系
     *
     * @param friendId 好友关系ID
     * @return 删除结果响应
     */
    @DeleteMapping("/{friendId}")
    ResponseEntity<Map<String, Object>> deleteFriend(@PathVariable("friendId") Long friendId);

    /**
     * 根据用户ID和好友ID删除好友关系
     *
     * @param userId 用户ID
     * @param friendUserId 好友用户ID
     * @return 删除结果响应
     */
    @DeleteMapping("/user/{userId}/friend/{friendUserId}")
    ResponseEntity<Map<String, Object>> deleteFriendByUserIds(
            @PathVariable("userId") Long userId, 
            @PathVariable("friendUserId") Long friendUserId);

    /**
     * 更新好友关系状态
     *
     * @param friendId 好友关系ID
     * @param status 新状态
     * @return 更新结果响应
     */
    @PutMapping("/{friendId}/status")
    ResponseEntity<Map<String, Object>> updateFriendStatus(
            @PathVariable("friendId") Long friendId, 
            @RequestParam("status") String status);

    /**
     * 根据好友关系ID获取好友关系详情
     *
     * @param friendId 好友关系ID
     * @return 好友关系详情响应
     */
    @GetMapping("/{friendId}")
    ResponseEntity<Map<String, Object>> getFriendById(@PathVariable("friendId") Long friendId);

    /**
     * 根据用户ID获取好友列表
     *
     * @param userId 用户ID
     * @return 好友列表响应
     */
    @GetMapping("/user/{userId}")
    ResponseEntity<Map<String, Object>> getFriendsByUserId(@PathVariable("userId") Long userId);

    /**
     * 根据用户ID和状态获取好友列表
     *
     * @param userId 用户ID
     * @param status 关系状态
     * @return 好友列表响应
     */
    @GetMapping("/user/{userId}/status/{status}")
    ResponseEntity<Map<String, Object>> getFriendsByUserIdAndStatus(
            @PathVariable("userId") Long userId, 
            @PathVariable("status") String status);

    /**
     * 检查两个用户是否为好友关系
     *
     * @param userId1 用户1的ID
     * @param userId2 用户2的ID
     * @return 好友关系检查结果响应
     */
    @GetMapping("/check/{userId1}/{userId2}")
    ResponseEntity<Map<String, Object>> checkFriendship(
            @PathVariable("userId1") Long userId1, 
            @PathVariable("userId2") Long userId2);

    /**
     * 获取所有好友关系列表
     *
     * @return 所有好友关系列表响应
     */
    @GetMapping("/all")
    ResponseEntity<Map<String, Object>> getAllFriends();

    /**
     * 根据用户ID获取好友数量
     *
     * @param userId 用户ID
     * @return 好友数量响应
     */
    @GetMapping("/user/{userId}/count")
    ResponseEntity<Map<String, Object>> getFriendCountByUserId(@PathVariable("userId") Long userId);

    /**
     * 根据用户ID和状态获取好友数量
     *
     * @param userId 用户ID
     * @param status 关系状态
     * @return 好友数量响应
     */
    @GetMapping("/user/{userId}/status/{status}/count")
    ResponseEntity<Map<String, Object>> getFriendCountByUserIdAndStatus(
            @PathVariable("userId") Long userId, 
            @PathVariable("status") String status);

    /**
     * 搜索好友
     * 根据用户ID和搜索关键词查找好友
     *
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @return 搜索结果响应
     */
    @GetMapping("/user/{userId}/search")
    ResponseEntity<Map<String, Object>> searchFriends(
            @PathVariable("userId") Long userId,
            @RequestParam("keyword") String keyword);

    /**
     * 批量添加好友
     *
     * @param friendLists 好友关系列表
     * @return 批量添加结果响应
     */
    @PostMapping("/batch")
    ResponseEntity<Map<String, Object>> batchAddFriends(@RequestBody List<FriendList> friendLists);

    /**
     * 批量删除好友
     *
     * @param friendIds 好友关系ID列表
     * @return 批量删除结果响应
     */
    @DeleteMapping("/batch")
    ResponseEntity<Map<String, Object>> batchDeleteFriends(@RequestBody List<Long> friendIds);

    /**
     * 批量更新好友状态
     *
     * @param friendIds 好友关系ID列表
     * @param status 新状态
     * @return 批量更新结果响应
     */
    @PutMapping("/batch/status")
    ResponseEntity<Map<String, Object>> batchUpdateFriendStatus(
            @RequestBody List<Long> friendIds,
            @RequestParam("status") String status);
}
