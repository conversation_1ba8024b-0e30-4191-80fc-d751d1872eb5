// 包声明：定义当前类所属的包路径，属于WebSocket工具包
package com.ruoyi.wenshumeeting.websocket.util;

// 导入阿里巴巴FastJSON库的JSON解析类，用于JSON字符串与Java对象的转换
import com.alibaba.fastjson.JSON;
// 导入自定义的信号实体类，用于封装WebSocket通信数据
import com.ruoyi.wenshumeeting.controller.SignalEntity;
// 导入WebSocket解码异常类，当解码过程出错时抛出
import jakarta.websocket.DecodeException;
// 导入WebSocket解码器接口，用于将文本消息解码为Java对象
import jakarta.websocket.Decoder;
// 导入WebSocket端点配置接口，用于解码器的初始化配置
import jakarta.websocket.EndpointConfig;

/**
 * WebSocket消息解码器工具类
 * 实现Decoder.Text接口，用于将客户端发送的JSON字符串消息解码为SignalEntity对象
 * 在WebSocket通信中，客户端发送的文本消息需要通过解码器转换为服务端可处理的Java对象
 *
 * 泛型参数说明：
 * - Decoder.Text<SignalEntity>: 表示这是一个文本解码器，将文本解码为SignalEntity类型
 *
 * 作者信息：
 * @Author: wu.shaoya
 * @Description: WebSocket消息解码器
 * @Date: 10:14 2019/10/31
 */
public class DecoderUtil implements Decoder.Text<SignalEntity> {

    /**
     * 解码方法实现
     * 将客户端发送的JSON字符串消息解码为SignalEntity对象
     * 这是解码器的核心方法，WebSocket框架会自动调用此方法进行消息转换
     *
     * @param jsonMessage 客户端发送的JSON格式字符串消息
     * @return SignalEntity 解码后的信号实体对象，包含消息的类型、内容和数据
     * @throws DecodeException 当JSON解析失败或格式不正确时抛出解码异常
     */
    @Override
    public SignalEntity decode(String jsonMessage) throws DecodeException {
        // 使用FastJSON库将JSON字符串解析为SignalEntity对象
        // JSON.parseObject方法会根据SignalEntity类的字段自动映射JSON属性
        return JSON.parseObject(jsonMessage, SignalEntity.class);
    }

    /**
     * 解码条件判断方法
     * 用于判断传入的消息是否可以被当前解码器处理
     * WebSocket框架在调用decode方法之前会先调用此方法进行预检查
     *
     * @param jsonMessage 待检查的消息字符串
     * @return boolean 如果消息可以被解码则返回true，否则返回false
     */
    @Override
    public boolean willDecode(String jsonMessage) {
        // 注释掉的代码展示了更严格的JSON格式验证方式：
        /* try {
            // 检查传入的消息是否为有效的JSON格式
            // 方式1：使用JSON.createReader进行验证
            // JSON.createReader(new StringReader(jsonMessage)).readObject();

            // 方式2：使用ObjectMapper进行JSON格式验证
            final ObjectMapper mapper = new ObjectMapper();
            mapper.readTree(jsonMessage);
            return true;
        } catch (Exception e) {
            // 如果JSON解析失败，说明格式不正确，返回false
            return false;
        }*/

        // 当前实现：直接返回true，表示接受所有消息
        // 这种方式性能更好，但缺少格式预验证
        // 实际的格式验证会在decode方法中进行，如果格式错误会抛出DecodeException
        return true;
    }

    /**
     * 解码器初始化方法
     * 在WebSocket端点创建解码器实例时调用
     * 可以在此方法中进行解码器的初始化配置
     *
     * @param ec WebSocket端点配置对象，包含端点的配置信息
     */
    @Override
    public void init(EndpointConfig ec) {
        // 初始化逻辑（当前为空实现）
        // 如果需要可以在此处添加解码器的初始化代码
        // 例如：配置JSON解析器的特殊设置、初始化缓存等
        // System.out.println("MessageDecoder - init method called");
    }

    /**
     * 解码器销毁方法
     * 在WebSocket端点关闭或解码器不再使用时调用
     * 可以在此方法中进行资源清理工作
     */
    @Override
    public void destroy() {
        // 清理逻辑（当前为空实现）
        // 如果需要可以在此处添加资源清理代码
        // 例如：关闭文件流、清理缓存、释放连接等
        // System.out.println("MessageDecoder - destroy method called");
    }
}

