#!/bin/bash

# 文书智能计算系统 - 打包脚本
# 在本地打包所有服务的jar文件

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # 恢复颜色

# 打印分隔线
print_line() {
  echo -e "${BLUE}=====================================${NC}"
}

# 显示帮助信息
show_help() {
  echo -e "${GREEN}文书智能计算系统打包脚本${NC}"
  echo -e "${YELLOW}用法: $0 [选项]${NC}"
  echo -e "选项:"
  echo -e "  -a, --all       打包所有服务"
  echo -e "  -s, --service   指定打包单个服务，例如: $0 -s gateway"
  echo -e "  -h, --help      显示此帮助信息"
  echo -e "\n可用的服务名称:"
  echo -e "${BLUE}基础模块:${NC}"
  echo -e "  common          若依公共模块 (ruoyi-common)"
  echo -e "  wenshu-common   文书公共模块 (wenshu-common)"
  echo -e "${BLUE}API模块:${NC}"
  echo -e "  ruoyi-api       若依API模块 (ruoyi-api)"
  echo -e "  api             文书API服务 (wenshu-api)"
  echo -e "${BLUE}数据模块:${NC}"
  echo -e "  wenshu-data     文书数据模块 (wenshu-data)"
  echo -e "  calebdar        日历服务 (wenshu-data/wenshu-calebdar)"
  echo -e "  wenshu-base     基础数据服务 (wenshu-data/wenshu-base)"
  echo -e "  wenshu-team     团队数据服务 (wenshu-data/wenshu-team)"
  echo -e "  wenshu-audit    审计数据服务 (wenshu-data/wenshu-audit)"
  echo -e "  wenshu-file     文件数据服务 (wenshu-data/wenshu-file)"
  echo -e "  wenshu-wchat    微信聊天服务 (wenshu-data/wenshu-wchat)"
  echo -e "${BLUE}核心服务:${NC}"
  echo -e "  gateway         网关服务 (ruoyi-gateway)"
  echo -e "  auth            认证服务 (ruoyi-auth)"
  echo -e "${BLUE}系统模块:${NC}"
  echo -e "  system          系统服务 (ruoyi-modules/ruoyi-system)"
  echo -e "  file            文件服务 (ruoyi-modules/ruoyi-file)"
  echo -e "  job             任务调度服务 (ruoyi-modules/ruoyi-job)"
  echo -e "  gen             代码生成服务 (ruoyi-modules/ruoyi-gen)"
  echo -e "  monitor         监控服务 (ruoyi-visual/ruoyi-monitor)"
  echo -e "${BLUE}业务服务:${NC}"
  echo -e "  chat            聊天服务 (wenshu-chat)"
  echo -e "  voice           语音服务 (wenshu-voice)"
  echo -e "  meeting         会议服务 (wenshu-meeting)"
  echo -e "  remind          提醒服务 (wenshu-remind)"
  echo -e "  livechat        实时聊天服务 (wenshu-livechat)"
  echo -e "  multimodal      多模态服务 (wenshu-multimodal)"
  echo -e "  programme       项目管理服务 (programme-manage)"
  exit 0
}

# 检查Maven是否已安装
check_maven() {
  echo -e "${YELLOW}检查Maven...${NC}"
  
  if ! command -v mvn &> /dev/null; then
    echo -e "${RED}未找到Maven，请先安装Maven${NC}"
    exit 1
  fi
  
  echo -e "${GREEN}Maven版本:${NC}"
  mvn --version | head -n 1
  
  print_line
}

# 创建输出目录
create_output_dir() {
  echo -e "${YELLOW}创建输出目录...${NC}"
  
  mkdir -p target/jars
  
  echo -e "${GREEN}输出目录创建成功${NC}"
  print_line
}

# 统一的模块打包函数（使用install）
package_service() {
  local service_name=$1
  local module_path=$2

  echo -e "${YELLOW}开始构建并安装 ${service_name}...${NC}"

  # 使用 mvn clean install -pl ... -am 来构建指定模块及其依赖
  # 这会确保模块被安装到本地Maven仓库，供其他模块使用
  if mvn clean install -pl ${module_path} -am -DskipTests; then
    echo -e "${GREEN}${service_name} 构建并安装成功${NC}"

    # 尝试找到jar文件并复制到输出目录
    # 我们需要从子模块的target目录中寻找jar
    jar_file=$(find ${module_path}/target -name "*.jar" 2>/dev/null | grep -v "sources\|javadoc\|original" | head -n 1)
    
    if [ -z "$jar_file" ]; then
        # 如果在模块路径下没找到，对于父模块，可能在其子模块中
        # 这是一个简单的查找，可能不完全，但能覆盖大多数情况
        jar_file=$(find ${module_path} -path "*/target/*.jar" 2>/dev/null | grep -v "sources\|javadoc\|original" | head -n 1)
    fi

    if [ -f "$jar_file" ]; then
      cp "$jar_file" target/jars/
      echo -e "${GREEN}已复制jar文件到输出目录: target/jars/$(basename $jar_file)${NC}"
    else
      echo -e "${YELLOW}在 ${module_path} 中未找到可复制的jar文件（这对于父pom模块是正常的）${NC}"
    fi
  else
    echo -e "${RED}${service_name} 构建失败${NC}"
    return 1
  fi

  echo -e "${BLUE}----------------------------${NC}"
}

# 打包所有服务
package_all() {
  echo -e "${YELLOW}打包所有服务...${NC}"

  # 创建输出目录
  mkdir -p target/jars

  # 服务列表，格式为 "服务名:模块路径"
  # 按照依赖关系排序：基础模块 -> API模块 -> 业务基础模块 -> 数据模块 -> 核心服务 -> 系统功能模块 -> 业务服务模块
  local services=(
    # 第一阶段：基础公共模块（其他模块的依赖）
    "common:ruoyi-common"
    "wenshu-common:wenshu-common"

    # 第二阶段：API接口模块（被业务模块依赖）
    "ruoyi-api:ruoyi-api"
    "api:wenshu-api"

    # 第三阶段：业务基础模块（被其他模块依赖）
    "programme:programme-manage"

    # 第四阶段：数据模块（包含基础数据服务）
    "wenshu-data:wenshu-data"
    "calebdar:wenshu-data/wenshu-calebdar"
    "wenshu-base:wenshu-data/wenshu-base"
    "wenshu-team:wenshu-data/wenshu-team"
    "wenshu-audit:wenshu-data/wenshu-audit"
    "wenshu-file:wenshu-data/wenshu-file"
    "wenshu-wchat:wenshu-data/wenshu-wchat"

    # 第五阶段：系统核心模块
    "system:ruoyi-modules/ruoyi-system"

    # 第六阶段：核心基础服务（网关和认证）
    "gateway:ruoyi-gateway"
    "auth:ruoyi-auth"

    # 第七阶段：系统功能模块
    "file:ruoyi-modules/ruoyi-file"
    "job:ruoyi-modules/ruoyi-job"
    "gen:ruoyi-modules/ruoyi-gen"
    "monitor:ruoyi-visual/ruoyi-monitor"

    # 第八阶段：业务服务模块
    "chat:wenshu-chat"
    "voice:wenshu-voice"
    "meeting:wenshu-meeting"
    "remind:wenshu-remind"
    "livechat:wenshu-livechat"
    "multimodal:wenshu-multimodal"
  )
  
  for service in "${services[@]}"; do
    # 分割服务名和模块路径
    IFS=':' read -r name path <<< "$service"

    # 执行打包，如果是基础模块失败则停止整个构建
    if ! package_service "$name" "$path"; then
      if [[ "$name" == "common" || "$name" == "wenshu-common" || "$name" == "ruoyi-api" || "$name" == "api" || "$name" == "wenshu-data" || "$name" == "programme" ]]; then
        echo -e "${RED}基础模块 ${name} 构建失败，停止整个构建过程${NC}"
        exit 1
      else
        echo -e "${YELLOW}服务 ${name} 构建失败，但继续构建其他服务${NC}"
      fi
    fi
  done
  
  echo -e "${GREEN}所有服务打包完成${NC}"
  print_line
}

# 获取服务的模块路径
get_module_path() {
  local service_name=$1
  local module_path=""

  case "$service_name" in
    # 基础模块
    common)
      module_path="ruoyi-common"
      ;;
    wenshu-common)
      module_path="wenshu-common"
      ;;
    # API模块
    ruoyi-api)
      module_path="ruoyi-api"
      ;;
    api)
      module_path="wenshu-api"
      ;;
    # 数据模块
    wenshu-data)
      module_path="wenshu-data"
      ;;
    calebdar)
      module_path="wenshu-data/wenshu-calebdar"
      ;;
    # 新增：系统核心模块
    system)
      module_path="ruoyi-modules/ruoyi-system"
      ;;
    wenshu-base)
      module_path="wenshu-data/wenshu-base"
      ;;
    wenshu-team)
      module_path="wenshu-data/wenshu-team"
      ;;
    wenshu-audit)
      module_path="wenshu-data/wenshu-audit"
      ;;
    wenshu-file)
      module_path="wenshu-data/wenshu-file"
      ;;
    wenshu-wchat)
      module_path="wenshu-data/wenshu-wchat"
      ;;
    # 核心服务
    gateway)
      module_path="ruoyi-gateway"
      ;;
    auth)
      module_path="ruoyi-auth"
      ;;
    # 系统模块
    file)
      module_path="ruoyi-modules/ruoyi-file"
      ;;
    job)
      module_path="ruoyi-modules/ruoyi-job"
      ;;
    gen)
      module_path="ruoyi-modules/ruoyi-gen"
      ;;
    monitor)
      module_path="ruoyi-visual/ruoyi-monitor"
      ;;
    # 业务服务模块
    chat)
      module_path="wenshu-chat"
      ;;
    voice)
      module_path="wenshu-voice"
      ;;
    meeting)
      module_path="wenshu-meeting"
      ;;
    remind)
      module_path="wenshu-remind"
      ;;
    livechat)
      module_path="wenshu-livechat"
      ;;
    multimodal)
      module_path="wenshu-multimodal"
      ;;
    programme)
      module_path="programme-manage"
      ;;
    *)
      echo -e "${RED}未知的服务名: ${service_name}${NC}"
      show_help
      exit 1
      ;;
  esac

  echo "$module_path"
}

# 主函数
main() {
  print_line
  echo -e "${GREEN}文书智能计算系统 - 打包脚本${NC}"
  print_line
  
  # 解析命令行参数
  if [ $# -eq 0 ]; then
    show_help
  fi
  
  while [ $# -gt 0 ]; do
    case "$1" in
      -a|--all)
        check_maven
        create_output_dir
        package_all
        shift
        ;;
      -s|--service)
        if [ -n "$2" ]; then
          check_maven
          create_output_dir
          module_path=$(get_module_path $2)
          package_service "$2" "$module_path"
          shift 2
        else
          echo -e "${RED}错误: -s 选项需要指定服务名${NC}"
          show_help
        fi
        ;;
      -h|--help)
        show_help
        ;;
      *)
        echo -e "${RED}未知选项: $1${NC}"
        show_help
        ;;
    esac
  done
  
  echo -e "${GREEN}打包过程完成！${NC}"
  echo -e "${YELLOW}所有jar文件位于 target/jars/ 目录${NC}"
}

# 执行主函数
main "$@" 