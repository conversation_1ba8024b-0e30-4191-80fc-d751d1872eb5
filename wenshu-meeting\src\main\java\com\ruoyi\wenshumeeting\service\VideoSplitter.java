package com.ruoyi.wenshumeeting.service;

import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

@Service
public class VideoSplitter {

    /**
     * 切分视频文件为每段10秒的小片段
     * @param inputVideoPath 输入视频文件路径
     * @return 切分后的视频片段路径列表
     */
    public List<String> splitVideo(String inputVideoPath) {
        List<String> outputPaths = new ArrayList<>();

        // 检查输入文件是否存在
        File inputFile = new File(inputVideoPath);
        if (!inputFile.exists()) {
            throw new IllegalArgumentException("输入视频文件不存在: " + inputVideoPath);
        }

        // 获取视频总时长（需要实际实现）
        double durationInSeconds = getVideoDuration(inputVideoPath);

        // 计算需要切分的段数
        int segmentCount = (int) Math.ceil(durationInSeconds / 10);

        // 准备输出目录
        String outputDir = prepareOutputDirectory(inputVideoPath);

        // 使用FFmpeg进行切分（假设已安装FFmpeg并可用）
        for (int i = 0; i < segmentCount; i++) {
            int startTime = i * 10;
            // 使用File.separator确保路径分隔符与操作系统一致
            String outputPath = String.format("%s%ssegment_%03d.mp4", outputDir, File.separator, i);

            // 构建FFmpeg命令
            String[] cmd = {
                    "ffmpeg",
                    "-i", inputVideoPath,
                    "-ss", String.valueOf(startTime),
                    "-t", "10",
                    "-c", "copy",
                    outputPath
            };

            try {
                // 执行命令
                Process process = Runtime.getRuntime().exec(cmd);
                process.waitFor();

                // 检查输出文件是否生成
                File outputFile = new File(outputPath);
                if (outputFile.exists()) {
                    outputPaths.add(outputPath);
                }
            } catch (Exception e) {
                System.err.println("切分视频时出错: " + e.getMessage());
            }
        }

        return outputPaths;
    }

    /**
     * 获取视频时长（秒）
     * @param videoPath 视频路径
     * @return 视频时长（秒）
     */
    private double getVideoDuration(String videoPath) {
        // 实际实现需要使用FFmpeg或其他库来获取视频时长
        // 这里只是一个示例实现
        try {
            String[] cmd = {
                    "ffprobe",
                    "-v", "error",
                    "-show_entries", "format=duration",
                    "-of", "default=noprint_wrappers=1:nokey=1",
                    videoPath
            };

            Process process = Runtime.getRuntime().exec(cmd);
            process.waitFor();

            // 读取输出
            java.io.InputStream is = process.getInputStream();
            byte[] buffer = new byte[is.available()];
            is.read(buffer);
            String output = new String(buffer);

            return Double.parseDouble(output.trim());
        } catch (Exception e) {
            System.err.println("获取视频时长失败: " + e.getMessage());
            return 0;
        }
    }

    /**
     * 准备输出目录
     * @param inputPath 输入文件路径
     * @return 输出目录路径
     */
    private String prepareOutputDirectory(String inputPath) {
        File inputFile = new File(inputPath);
        String parentDir = inputFile.getParent();
        // 使用File.separator确保路径分隔符与操作系统一致
        String outputDir = parentDir + File.separator + "segments_" + System.currentTimeMillis();

        File dir = new File(outputDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        return outputDir;
    }

    public static void main(String[] args) {
        VideoSplitter splitter = new VideoSplitter();
        String videoPath = "D:/桌面杂项/abc/QQ2025530-2125-HD.mp4";

        List<String> segments = splitter.splitVideo(videoPath);

        System.out.println("视频切分完成，生成以下片段:");
        for (String segment : segments) {
            System.out.println(segment);
        }
    }
}