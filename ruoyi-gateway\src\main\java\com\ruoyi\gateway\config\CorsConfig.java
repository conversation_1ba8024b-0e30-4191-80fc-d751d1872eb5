package com.ruoyi.gateway.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.cors.reactive.CorsUtils;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;

@Configuration
public class CorsConfig {

    // 原有配置的请求头
    private static final String DEFAULT_ALLOWED_HEADERS =
            "X-Requested-With, Content-Type, Authorization, credential, X-XSRF-TOKEN, token, Admin-Token, App-To<PERSON>, istoken";

    // wenshulivechat需要的额外请求头（根据实际情况添加）
    private static final String WENSHU_EXTRA_HEADERS =
            "fileId, analysisId, username, category, Custom-Header";

    // 合并后的请求头
    private static final String ALLOWED_HEADERS =
            DEFAULT_ALLOWED_HEADERS + ", " + WENSHU_EXTRA_HEADERS;

    private static final String ALLOWED_METHODS = "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH";
    private static final String ALLOWED_EXPOSE = "*";
    private static final String MAX_AGE = "3600";

    // wenshulivechat的特定路径（根据实际情况调整）
    private static final List<String> WENSHU_PATHS = Arrays.asList(
            "/wenshu/meeting/analysis",
            "/wenshu/meeting/SpeechRecognition"
    );

    @Bean
    public WebFilter corsFilter() {
        return (ServerWebExchange ctx, WebFilterChain chain) -> {
            ServerHttpRequest request = ctx.getRequest();
            String path = request.getURI().getPath();

            // 1. 跳过WebSocket路径
            if (path.contains("/websocket-endpoint")) {
                return chain.filter(ctx);
            }

            if (CorsUtils.isCorsRequest(request)) {
                ServerHttpResponse response = ctx.getResponse();
                HttpHeaders headers = response.getHeaders();

                // 2. 动态确定配置策略
                final String origin = request.getHeaders().getOrigin();
                final String finalAllowedOrigin = origin != null ? origin : "*";
                final String finalAllowedHeaders;
                final String finalAllowedMethods;

                // 3. 如果是wenshulivechat的路径，使用更宽松的配置
                if (isWenshuPath(path)) {
                    finalAllowedHeaders = "*";  // 允许所有请求头
                    finalAllowedMethods = "*";  // 允许所有HTTP方法
                } else {
                    finalAllowedHeaders = ALLOWED_HEADERS;
                    finalAllowedMethods = ALLOWED_METHODS;
                }

                // 4. 设置响应头（避免重复）
                if (!headers.containsKey(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN)) {
                    headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, finalAllowedOrigin);
                    headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, finalAllowedMethods);
                    headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, finalAllowedHeaders);
                    headers.add(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, ALLOWED_EXPOSE);
                    headers.add(HttpHeaders.ACCESS_CONTROL_MAX_AGE, MAX_AGE);
                    headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS, "true");
                }

                // 5. 处理OPTIONS预检请求
                if (request.getMethod() == HttpMethod.OPTIONS) {
                    response.setStatusCode(HttpStatus.OK);
                    return Mono.empty();
                }
            }
            return chain.filter(ctx);
        };
    }

    // 判断是否是wenshulivechat的路径
    private boolean isWenshuPath(String path) {
        return WENSHU_PATHS.stream().anyMatch(path::contains);
    }
}
