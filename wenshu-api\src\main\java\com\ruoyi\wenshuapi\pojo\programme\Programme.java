package com.ruoyi.wenshuapi.pojo.programme;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
@Data
@TableName(value = "wenshu_calendar_event")
/**
 * 主键id
 * 日程标题
 * 日程描述
 * 创建时间
 * 日程开始时间
 * 创建者ID (外键sys_user.id)
 * 所有者类型（个人、团队）
 * 重复规则（每天重复、一次性)
 * 提醒时间（默认提前十分钟，可修改）
 * 日程状态(0-取消,1-正常)
 */
public class Programme {
            @TableId(type = IdType.AUTO)
            private  int eventId;
            private String title;
            private String description;
            private LocalDateTime createTime;
            private LocalDateTime startTime;
            private int  creatorId;
            private String ownerType;
            private String repeatRule;
            private LocalDateTime remindTime;
            private int eventStatus;
}
