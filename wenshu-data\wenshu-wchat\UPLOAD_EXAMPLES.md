# 文件上传功能使用示例

## 概述

wenshu-wchat 模块现在支持上传所有类型的文件，不再有文件类型限制。只要文件大小不超过100MB，任何格式的文件都可以上传。

## 支持的文件类型

### 图片文件
- 常见格式：jpg, jpeg, png, gif, bmp, webp, svg, ico, tiff, tif
- 其他格式：任何图片格式都支持

### 视频文件
- 常见格式：mp4, avi, mov, wmv, flv, webm, mkv, m4v, 3gp, ogv
- 其他格式：任何视频格式都支持

### 文档文件
- Office文档：doc, docx, xls, xlsx, ppt, pptx
- PDF文档：pdf
- 文本文件：txt, rtf
- 开放文档：odt, ods, odp
- 其他格式：任何文档格式都支持

### 压缩文件
- 常见格式：zip, rar, 7z, tar, gz, bz2, xz
- 其他格式：任何压缩格式都支持

### 其他文件
- 代码文件：java, js, html, css, xml, json等
- 数据文件：csv, sql, db等
- 媒体文件：音频、字体等
- **任何其他格式的文件都支持**

## API 使用示例

### 1. 使用统一发送消息接口

```bash
# 上传图片
curl -X POST "http://localhost:8080/wenshu/wchat/chat/send" \
  -F "senderId=1001" \
  -F "receiverId=1002" \
  -F "contentType=image" \
  -F "file=@/path/to/image.jpg"

# 上传视频
curl -X POST "http://localhost:8080/wenshu/wchat/chat/send" \
  -F "senderId=1001" \
  -F "receiverId=1002" \
  -F "contentType=video" \
  -F "file=@/path/to/video.mp4"

# 上传文档
curl -X POST "http://localhost:8080/wenshu/wchat/chat/send" \
  -F "senderId=1001" \
  -F "receiverId=1002" \
  -F "contentType=file" \
  -F "file=@/path/to/document.pdf"

# 上传任意格式文件
curl -X POST "http://localhost:8080/wenshu/wchat/chat/send" \
  -F "senderId=1001" \
  -F "receiverId=1002" \
  -F "contentType=file" \
  -F "file=@/path/to/anyfile.xyz"
```

### 2. 使用专门的文件类型接口

```bash
# 发送图片消息
curl -X POST "http://localhost:8080/wenshu/wchat/chat/send/image" \
  -F "senderId=1001" \
  -F "receiverId=1002" \
  -F "file=@/path/to/image.png"

# 发送视频消息
curl -X POST "http://localhost:8080/wenshu/wchat/chat/send/video" \
  -F "senderId=1001" \
  -F "receiverId=1002" \
  -F "file=@/path/to/video.avi"

# 发送文件消息
curl -X POST "http://localhost:8080/wenshu/wchat/chat/send/file" \
  -F "senderId=1001" \
  -F "receiverId=1002" \
  -F "file=@/path/to/document.docx"
```

## Java 客户端使用示例

### 使用 wenshu-api 客户端

```java
@Autowired
private ChatRecordClient chatRecordClient;

// 上传任意类型文件
public void uploadAnyFile() {
    try {
        // 创建 MultipartFile（实际使用中通常来自前端上传）
        MultipartFile file = new MockMultipartFile(
            "file", 
            "example.xyz", 
            "application/octet-stream", 
            fileBytes
        );
        
        // 发送文件消息
        ResponseEntity<Map<String, Object>> response = chatRecordClient.sendMessage(
            1001L,      // 发送者ID
            1002L,      // 接收者ID
            "file",     // 内容类型
            null,       // 文本内容（文件消息时为null）
            file        // 文件
        );
        
        if (response.getStatusCode().is2xxSuccessful()) {
            Map<String, Object> result = response.getBody();
            System.out.println("文件上传成功: " + result);
        }
    } catch (Exception e) {
        System.err.println("文件上传失败: " + e.getMessage());
    }
}
```

## 文件存储结构

上传的文件按以下结构存储：

```
chat-files/
├── image/
│   └── 2024/01/15/
│       └── 1705123456789_abc123def456.jpg
├── video/
│   └── 2024/01/15/
│       └── 1705123456790_def456ghi789.mp4
├── file/
│   └── 2024/01/15/
│       └── 1705123456791_ghi789jkl012.pdf
└── archive/
    └── 2024/01/15/
        └── 1705123456792_jkl012mno345.zip
```

## 安全性说明

1. **文件大小限制**：最大支持100MB
2. **扩展名验证**：检查扩展名长度和危险字符
3. **唯一文件名**：自动生成唯一文件名防止冲突
4. **路径安全**：防止路径遍历攻击

## 注意事项

1. 虽然支持所有文件类型，但建议根据实际业务需求进行适当的文件类型检查
2. 对于可执行文件等敏感类型，建议在业务层面进行额外的安全检查
3. 大文件上传可能需要较长时间，建议前端添加上传进度提示
4. 建议定期清理过期的聊天文件以节省存储空间
