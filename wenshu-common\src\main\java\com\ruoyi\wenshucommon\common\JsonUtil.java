package com.ruoyi.wenshucommon.common;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;

/**
 * JSON工具类
 */
public class JsonUtil {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 对象转Json字符串
     */
    public static String toJsonString(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("转换对象到JSON字符串失败", e);
        }
    }

    /**
     * Json字符串转对象
     */
    public static <T> T toJsonObject(String jsonString, Class<T> valueType) {
        try {
            return objectMapper.readValue(jsonString, valueType);
        } catch (IOException e) {
            throw new RuntimeException("转换JSON字符串到对象失败", e);
        }
    }

    /**
     * Json字符串转复杂对象（如含泛型）
     */
    public static <T> T toJsonObject(String jsonString, TypeReference<T> valueTypeRef) {
        try {
            return objectMapper.readValue(jsonString, valueTypeRef);
        } catch (IOException e) {
            throw new RuntimeException("转换JSON字符串到复杂对象失败", e);
        }
    }
} 