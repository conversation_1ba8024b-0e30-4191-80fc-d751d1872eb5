// 包声明：定义当前类所属的包路径，属于WebSocket包
package com.ruoyi.wenshumeeting.websocket;

// 导入阿里巴巴FastJSON库的JSON解析类，用于JSON字符串与Java对象的转换
import com.alibaba.fastjson.JSON;
// 导入阿里巴巴FastJSON库的JSONObject类，用于操作JSON对象
import com.alibaba.fastjson.JSONObject;
// 导入自定义的信号实体类，用于封装WebSocket通信数据
import com.ruoyi.wenshumeeting.controller.SignalEntity;
// 导入自定义的Spring工具类，用于在非Spring管理的类中获取Bean
import com.ruoyi.wenshumeeting.util.SpringUtils;
// 导入自定义的WebSocket消息解码器工具类
import com.ruoyi.wenshumeeting.websocket.util.DecoderUtil;
// 导入自定义的WebSocket消息编码器工具类
import com.ruoyi.wenshumeeting.websocket.util.EncoderUtil;
// 导入Jakarta WebSocket API的相关类（Jakarta EE 9+版本）
import jakarta.websocket.*;
// 导入WebSocket路径参数注解，用于获取URL中的参数
import jakarta.websocket.server.PathParam;
// 导入WebSocket服务端点注解，用于标识WebSocket端点
import jakarta.websocket.server.ServerEndpoint;
// 导入Lombok的日志注解，自动生成日志对象
import lombok.extern.slf4j.Slf4j;
// 导入Spring Data Redis的RedisTemplate类，用于Redis操作
import org.springframework.data.redis.core.RedisTemplate;
// 导入Spring组件注解，标识这是一个Spring管理的组件
import org.springframework.stereotype.Component;

// 以下是注释掉的旧版本导入（javax.websocket），现已升级为jakarta.websocket
//import xx.call.dto.SignalEntity;
//import xx.call.util.SpringUtils;
//import xx.call.websocket.util.DecoderUtil;
//import xx.call.websocket.util.EncoderUtil;
//
//import javax.websocket.*;
//import javax.websocket.server.PathParam;
//import javax.websocket.server.ServerEndpoint;

// 导入Java IO异常类，用于处理输入输出异常
import java.io.IOException;
// 导入ArrayList集合类，用于存储列表数据
import java.util.ArrayList;
// 导入List接口，用于定义列表类型
import java.util.List;
// 导入Map接口，用于定义键值对映射类型
import java.util.Map;
// 导入Objects工具类，用于对象比较和空值检查
import java.util.Objects;
// 导入线程安全的ConcurrentHashMap类，用于存储WebSocket连接
import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocket服务器类
 * 用于处理实时音视频会议的WebSocket连接和信令传输
 *
 * @Slf4j Lombok注解，自动生成log日志对象
 * @Component Spring组件注解，将此类注册为Spring管理的Bean
 * @ServerEndpoint WebSocket服务端点注解，定义WebSocket连接的URL路径和编解码器
 * - value: WebSocket连接路径，包含房间ID、用户ID、用户身份等路径参数
 * - encoders: 消息编码器，用于将Java对象编码为WebSocket消息
 * - decoders: 消息解码器，用于将WebSocket消息解码为Java对象
 */
@Slf4j
@Component
@ServerEndpoint(value = "/signal/{roomId}/{userId}/{pub}", encoders = {EncoderUtil.class}, decoders = {DecoderUtil.class})
public class WebSocketServer {

    /**
     * Redis模板对象
     * 通过SpringUtils工具类从Spring容器中获取RedisTemplate Bean
     * 用于在Redis中存储和管理会议室用户信息
     */
    private RedisTemplate redisTemplate = (RedisTemplate) SpringUtils.getBean("redisTemplate");

    /**
     * Redis中会议室数据的键前缀
     * 用于在Redis中存储会议室相关数据，格式为：meeting-room::{roomId}
     */
    private static String ROOM_KEY = "meeting-room::";

    /**
     * 静态变量：当前在线连接数计数器
     * 使用静态变量确保所有WebSocket实例共享同一个计数器
     * 需要配合synchronized关键字确保线程安全
     */
    private static int onlineCount = 0;

    /**
     * 线程安全的WebSocket连接映射表
     * 使用ConcurrentHashMap确保在多线程环境下的线程安全
     * Key: 用户ID（String类型）
     * Value: 对应的WebSocketServer实例
     * 用于根据用户ID快速查找对应的WebSocket连接
     */
    private static ConcurrentHashMap<String, WebSocketServer> webSocketMap = new ConcurrentHashMap<>();

    /**
     * WebSocket会话对象
     * 每个WebSocket连接都有一个唯一的Session对象
     * 通过Session可以向客户端发送消息、获取连接信息等
     */
    private Session session;

    /**
     * 用户ID字段
     * 存储当前WebSocket连接对应的用户唯一标识
     * 从WebSocket连接URL的路径参数中获取
     */
    private String userId = "";

    /**
     * 会议室ID字段
     * 存储当前用户所在的会议室唯一标识
     * 从WebSocket连接URL的路径参数中获取
     */
    private String roomId = "";

    /**
     * 用户身份标识字段
     * 用于区分用户在会议中的角色：
     * - 0: 普通参会者
     * - 1: 主播或会议主持人
     * 从WebSocket连接URL的路径参数中获取
     */
    private Integer pub = 0;

    /**
     * WebSocket连接建立时的回调方法
     * 当客户端成功连接到WebSocket服务端点时自动调用
     * 负责初始化连接信息、更新在线状态、将用户加入会议室
     *
     * @OnOpen WebSocket注解，标识这是连接建立时的处理方法
     * @param session WebSocket会话对象，用于与客户端通信
     * @param userId 用户ID，从URL路径参数中获取，用于唯一标识用户
     * @param roomId 会议室ID，从URL路径参数中获取，标识用户要加入的会议室
     * @param pub 用户身份标识，从URL路径参数中获取，0表示普通用户，1表示主持人
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("userId") String userId,
                       @PathParam("roomId") String roomId,
                       @PathParam("pub") Integer pub) {
        // 保存WebSocket会话对象到实例变量，用于后续消息发送
        this.session = session;
        // 保存用户ID到实例变量
        this.userId = userId;
        // 保存会议室ID到实例变量
        this.roomId = roomId;
        // 保存用户身份标识到实例变量
        this.pub = pub;
        // 将当前WebSocket实例添加到全局连接映射表中，以用户ID为键
        webSocketMap.put(userId, this);
        // 增加在线连接数计数
        addOnlineCount();
        // 将用户信息添加到Redis中的会议室数据，并通知其他用户
        addUserToRoom(userId, roomId, pub);
        // 记录用户加入日志，包含房间号、用户ID、身份和当前在线人数
        log.info("房间号：{} 用户加入:{},身份：{},当前在线人数为:{}", roomId, userId, pub, getOnlineCount());
    }

    /**
     * WebSocket连接关闭时的回调方法
     * 当客户端断开WebSocket连接时自动调用
     * 负责清理连接信息、更新在线状态、将用户从会议室移除
     *
     * @OnClose WebSocket注解，标识这是连接关闭时的处理方法
     */
    @OnClose
    public void onClose() {
        // 检查全局连接映射表中是否包含当前用户
        if (webSocketMap.containsKey(userId)) {
            // 从全局连接映射表中移除当前用户的WebSocket连接
            webSocketMap.remove(userId);
            // 减少在线连接数计数
            subOnlineCount();
            // 将用户从Redis中的会议室数据移除，并通知其他用户
            leaveRoom(roomId, userId);
        }
        // 记录用户退出日志，包含房间号、用户ID和当前在线人数
        log.info("房间号：" + roomId + ",用户退出:" + userId + ",当前在线人数为:" + getOnlineCount());
    }


    /**
     * WebSocket接收到客户端消息时的回调方法
     * 处理客户端发送的各种类型的信令消息，包括WebRTC相关的offer、answer、candidate等
     * 根据消息类型进行相应的业务处理和消息转发
     *
     * @OnMessage WebSocket注解，标识这是接收消息时的处理方法
     * @param message 客户端发送的JSON格式字符串消息
     */
    @OnMessage
    public void onMessage(String message) {
        // 记录接收到的用户消息日志，包含用户ID和完整消息内容
        log.info("用户消息:" + userId + ",报文:" + message);
        // 检查消息是否为空字符串，避免处理无效消息
        if (!("").equals(message)) {
            // 将JSON字符串解析为JSONObject对象，便于提取字段信息
            JSONObject jsonObject = JSON.parseObject(message);
            // 从消息中提取type字段，用于判断消息类型
            String type = jsonObject.getString("type");

            // 处理WebRTC offer信令：发起方向接收方发送连接提议
            if (Objects.equals(type, "offer")) {
                // 从消息中提取目标用户ID
                String targetUid = jsonObject.getString("targetUid");
                // 向目标用户转发offer信令，包含WebRTC会话描述信息
                oneToOne(targetUid, new SignalEntity("offer", "rtc offer", 200, jsonObject));
            }
            // 处理远程呼叫信令：用户发起音视频通话请求
            else if (Objects.equals(type, "call")) {
                // 从消息中提取目标用户ID
                String targetUid = jsonObject.getString("targetUid");
                // 向目标用户转发呼叫信令，通知有用户发起通话
                oneToOne(targetUid, new SignalEntity("call", "远程呼叫", 200, jsonObject));
            }
            // 处理获取房间用户列表请求：客户端请求当前房间的所有用户信息
            else if (Objects.equals(type, "roomUserList")) {
                // 从消息中提取房间ID（也可以使用实例变量this.roomId）
                String roomId = jsonObject.getString("roomId");
                // 向请求用户返回房间用户列表信息
                oneToOne(userId, new SignalEntity("roomUserList", "房间人数", 200, getRoomUserList(roomId)));
            }
            // 处理WebRTC ICE candidate信令：交换网络连接候选信息
            else if (Objects.equals(type, "candidate")) {
                // 从消息中提取目标用户ID
                String targetUid = jsonObject.getString("targetUid");
                // 向目标用户转发ICE candidate信令，用于建立P2P连接
                oneToOne(targetUid, new SignalEntity("candidate", "ice candidate", 200, jsonObject));
            }
            // 处理WebRTC answer信令：接收方向发起方发送连接应答
            else if (Objects.equals(type, "answer")) {
                // 从消息中提取目标用户ID
                String targetUid = jsonObject.getString("targetUid");
                // 向目标用户转发answer信令，完成WebRTC连接协商
                oneToOne(targetUid, new SignalEntity("answer", "rtc answer", 200, jsonObject));
            }
        }
    }

    /**
     * 点对点消息发送方法
     * 向指定用户发送WebSocket消息，用于实现用户间的直接通信
     * 包含线程安全处理和异常捕获机制
     *
     * @param to 目标用户ID，消息接收者的唯一标识
     * @param data 要发送的信号实体对象，包含消息类型、内容和数据
     */
    public void oneToOne(String to, SignalEntity data) {
        // 从全局连接映射表中查找目标用户的WebSocket会话
        // 使用三元运算符：如果用户在线则获取其session，否则为null
        Session session = webSocketMap.containsKey(to) ? webSocketMap.get(to).session : null;

        // 检查会话是否存在且连接状态为打开
        if (session != null && session.isOpen()) {
            try {
                // 使用synchronized关键字对session对象加锁
                // 避免多线程并发发送消息时造成的线程安全问题
                synchronized (session) {
                    // 通过WebSocket会话发送对象消息
                    // getBasicRemote()获取同步消息发送器
                    // sendObject()会自动使用配置的编码器将对象转换为消息
                    session.getBasicRemote().sendObject(data);
                }
            } catch (IOException e) {
                // 捕获IO异常，通常是网络连接问题导致
                log.error("消息发送IO异常：[{}]", e.toString());
            } catch (EncodeException e) {
                // 捕获编码异常，通常是对象序列化失败导致
                log.error("消息发送Encode异常：[{}]", e.toString());
            }
        } else {
            // 目标用户不在线或连接已断开，记录警告日志
            log.warn("用户：[{}]-->不在线", to);
        }
    }

    /**
     * 房间广播消息发送方法
     * 向指定房间内的所有用户发送WebSocket消息，用于实现群组通信
     * 如用户加入/离开房间的通知等
     *
     * @param roomId 目标房间ID，标识要广播消息的会议室
     * @param data 要发送的信号实体对象，包含消息类型、内容和数据
     */
    public void oneToRoom(String roomId, SignalEntity data) {
        // 从Redis中获取指定房间的所有用户信息
        Map<String, Object> res = getAllUserFromRoom(roomId);
        // 遍历房间内的所有用户，向每个用户发送消息
        // key为用户ID，value为用户详细信息
        res.forEach((key, value) -> {
            // 调用点对点发送方法，向每个用户发送相同的消息
            oneToOne(key, data);
        });
    }

    /**
     * 获取房间用户列表方法
     * 从Redis中查询指定房间的所有用户信息，并转换为List格式返回
     * 用于响应客户端的房间用户列表查询请求
     *
     * @param roomId 房间ID，标识要查询的会议室
     * @return List<Object> 包含房间内所有用户信息的列表
     */
    public List<Object> getRoomUserList(String roomId) {
        // 创建ArrayList用于存储用户信息
        List<Object> list = new ArrayList<>();
        // 从Redis中获取指定房间的所有用户信息映射
        Map<String, Object> res = getAllUserFromRoom(roomId);
        // 遍历用户信息映射，将每个用户的详细信息添加到列表中
        res.forEach((key, value) -> {
            // key为用户ID，value为用户详细信息（JSON对象）
            list.add(value);
        });
        // 返回包含所有用户信息的列表
        return list;
    }

    /**
     * 用户离开房间处理方法
     * 当用户断开WebSocket连接时调用，负责清理用户在Redis中的房间数据
     * 并通知房间内其他用户该用户已离开
     *
     * @param roomId 房间ID，标识用户要离开的会议室
     * @param userId 用户ID，标识要离开房间的用户
     */
    public void leaveRoom(String roomId, String userId) {
        // 从Redis的Hash结构中删除用户信息
        // 使用ROOM_KEY + roomId作为Hash的key，userId作为Hash的field
        redisTemplate.opsForHash().delete(ROOM_KEY + roomId, userId);

        // 创建用户离开事件的JSON对象，包含用户和房间信息
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userId", userId);           // 用户ID
        jsonObject.put("roomId", roomId);           // 房间ID
        jsonObject.put("nickName", userId);         // 用户昵称（这里使用userId作为昵称）
        jsonObject.put("pub", pub);                 // 用户身份标识

        // 向房间内所有其他用户广播用户离开消息
        oneToRoom(roomId, new SignalEntity("leave", userId + " leave then room", 200, jsonObject));
    }

    /**
     * 根据房间ID获取房间内所有用户信息
     * 从Redis中查询指定房间的完整用户信息映射表
     * 用于房间管理和用户列表查询
     *
     * @param roomId 房间ID，标识要查询的会议室
     * @return Map<String, Object> 用户信息映射表，key为用户ID，value为用户详细信息
     */
    public Map<String, Object> getAllUserFromRoom(String roomId) {
        // 从Redis的Hash结构中获取所有用户信息
        // 使用ROOM_KEY + roomId作为Hash的key，返回所有field-value对
        Map<String, Object> map = redisTemplate.opsForHash().entries(ROOM_KEY + roomId);
        return map;
    }

    /**
     * 用户加入房间处理方法
     * 当用户建立WebSocket连接时调用，负责将用户信息存储到Redis
     * 并通知房间内其他用户有新用户加入
     *
     * @param userId 用户ID，标识要加入房间的用户
     * @param roomId 房间ID，标识用户要加入的会议室
     * @param pub 用户身份标识，0表示普通用户，1表示主持人
     */
    public void addUserToRoom(String userId, String roomId, Integer pub) {
        // 创建用户信息的JSON对象，包含用户的完整信息
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userId", userId);           // 用户ID
        jsonObject.put("roomId", roomId);           // 房间ID
        jsonObject.put("nickName", userId);         // 用户昵称（这里使用userId作为昵称）
        jsonObject.put("pub", pub);                 // 用户身份标识

        // 将用户信息存储到Redis的Hash结构中
        // 使用ROOM_KEY + roomId作为Hash的key，userId作为Hash的field，用户信息JSON作为value
        redisTemplate.opsForHash().put(ROOM_KEY + roomId, userId, jsonObject);

        // 向房间内所有用户（包括新加入的用户）广播用户加入消息
        oneToRoom(roomId, new SignalEntity("join", userId + " join then room", 200, jsonObject));
    }

    /**
     * WebSocket连接发生错误时的回调方法
     * 当WebSocket连接过程中出现异常时自动调用
     * 用于记录错误信息，便于问题排查和系统监控
     *
     * @OnError WebSocket注解，标识这是错误处理方法
     * @param session 发生错误的WebSocket会话对象
     * @param error 具体的错误异常对象，包含错误详细信息
     */
    @OnError
    public void onError(Session session, Throwable error) {
        // 记录错误日志，包含出错用户的ID和具体错误原因
        // 使用this.userId确保记录的是当前WebSocket实例对应的用户
        log.error("用户错误:" + this.userId + ",原因:" + error.getMessage());
    }

    /**
     * 获取当前在线连接数的静态方法
     * 提供线程安全的在线人数查询功能
     * 用于系统监控和统计分析
     *
     * @return int 当前WebSocket在线连接的总数
     */
    public static synchronized int getOnlineCount() {
        // 使用synchronized关键字确保多线程环境下的数据一致性
        // 返回静态变量onlineCount的当前值
        return onlineCount;
    }

    /**
     * 增加在线连接数的静态方法
     * 当有新用户建立WebSocket连接时调用
     * 使用synchronized确保线程安全的计数操作
     *
     * @return void 无返回值
     */
    public static synchronized void addOnlineCount() {
        // 使用synchronized关键字确保原子性操作
        // 将静态变量onlineCount的值增加1
        WebSocketServer.onlineCount++;
    }

    /**
     * 减少在线连接数的静态方法
     * 当用户断开WebSocket连接时调用
     * 使用synchronized确保线程安全的计数操作
     *
     * @return void 无返回值
     */
    public static synchronized void subOnlineCount() {
        // 使用synchronized关键字确保原子性操作
        // 将静态变量onlineCount的值减少1
        WebSocketServer.onlineCount--;
    }
}
