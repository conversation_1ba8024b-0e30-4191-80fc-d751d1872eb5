FROM lcr.loongnix.cn/library/openjdk:17

USER root
ENV TZ=Asia/Shanghai
WORKDIR /tmp
RUN mkdir -p /tmp/uploadPath
COPY ruoyi-modules/ruoyi-file/target/*.jar /tmp/app.jar
ENV SPRING_PROFILES_ACTIVE=prod
ENV JAVA_OPTS="-Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
EXPOSE 9300
VOLUME ["/tmp/uploadPath"]
ENTRYPOINT ["java", "-jar", "/tmp/app.jar"]
