package com.ruoyi.wenshuapi.pojo.audit;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName(value = "wenshu_team_audit")
public class WenshuTeamAudit {
    @TableId(type = IdType.AUTO)
    private int auditId;
    private String applyTeamName;
    private String applyLeader;
    private String applyCreator;
    private LocalDateTime applyTime;
    private String applyDescription;
    private String auditStatus;
}
