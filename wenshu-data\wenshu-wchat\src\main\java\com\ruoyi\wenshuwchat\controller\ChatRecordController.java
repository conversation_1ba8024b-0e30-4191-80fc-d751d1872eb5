package com.ruoyi.wenshuwchat.controller;

import com.ruoyi.wenshuapi.pojo.wchat.ChatRecord;
import com.ruoyi.wenshuwchat.service.ChatRecordService;
import com.ruoyi.wenshuwchat.service.ChatFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 聊天记录控制器
 * 提供聊天记录管理的RESTful API接口
 * 顶层路径: /wenshu/wchat/chat
 */
@RestController
@RequestMapping("/wenshu/wchat/chat")
public class ChatRecordController {

    private final ChatRecordService chatRecordService;
    private final ChatFileService chatFileService;

    @Autowired
    public ChatRecordController(ChatRecordService chatRecordService, ChatFileService chatFileService) {
        this.chatRecordService = chatRecordService;
        this.chatFileService = chatFileService;
    }

    /**
     * 发送文本消息
     *
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @param content 文本内容
     * @return 包含新消息ID的响应
     */
    @PostMapping("/send/text")
    public ResponseEntity<Map<String, Object>> sendTextMessage(
            @RequestParam("senderId") Long senderId,
            @RequestParam("receiverId") Long receiverId,
            @RequestParam("content") String content) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 验证参数
            if (senderId == null || receiverId == null) {
                response.put("success", false);
                response.put("message", "发送者ID和接收者ID不能为空");
                return ResponseEntity.badRequest().body(response);
            }

            if (content == null || content.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "文本内容不能为空");
                return ResponseEntity.badRequest().body(response);
            }

            // 创建聊天记录
            ChatRecord chatRecord = new ChatRecord();
            chatRecord.setSenderId(senderId);
            chatRecord.setReceiverId(receiverId);
            chatRecord.setContent(content.trim());
            chatRecord.setContentType("text");
            chatRecord.setSendTime(LocalDateTime.now());
            chatRecord.setIsRead(false);

            Long messageId = chatRecordService.sendMessage(chatRecord);

            response.put("success", true);
            response.put("message", "文本消息发送成功");
            response.put("data", Map.of(
                "messageId", messageId,
                "contentType", "text",
                "content", content.trim()
            ));
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "文本消息发送失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 发送图片消息
     *
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @param file 图片文件
     * @return 包含新消息ID的响应
     */
    @PostMapping("/send/image")
    public ResponseEntity<Map<String, Object>> sendImageMessage(
            @RequestParam("senderId") Long senderId,
            @RequestParam("receiverId") Long receiverId,
            @RequestParam("file") MultipartFile file) {
        return sendFileMessage(senderId, receiverId, file, "image");
    }

    /**
     * 发送视频消息
     *
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @param file 视频文件
     * @return 包含新消息ID的响应
     */
    @PostMapping("/send/video")
    public ResponseEntity<Map<String, Object>> sendVideoMessage(
            @RequestParam("senderId") Long senderId,
            @RequestParam("receiverId") Long receiverId,
            @RequestParam("file") MultipartFile file) {
        return sendFileMessage(senderId, receiverId, file, "video");
    }

    /**
     * 发送文件消息
     *
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @param file 文件
     * @return 包含新消息ID的响应
     */
    @PostMapping("/send/file")
    public ResponseEntity<Map<String, Object>> sendFileMessage(
            @RequestParam("senderId") Long senderId,
            @RequestParam("receiverId") Long receiverId,
            @RequestParam("file") MultipartFile file) {
        return sendFileMessage(senderId, receiverId, file, "file");
    }

    /**
     * 通用文件消息发送方法
     *
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @param file 文件
     * @param contentType 内容类型
     * @return 包含新消息ID的响应
     */
    private ResponseEntity<Map<String, Object>> sendFileMessage(
            Long senderId, Long receiverId, MultipartFile file, String contentType) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 验证参数
            if (senderId == null || receiverId == null) {
                response.put("success", false);
                response.put("message", "发送者ID和接收者ID不能为空");
                return ResponseEntity.badRequest().body(response);
            }

            if (file == null || file.isEmpty()) {
                response.put("success", false);
                response.put("message", "文件不能为空");
                return ResponseEntity.badRequest().body(response);
            }

            // 验证文件类型
            if (!chatFileService.isFileTypeAllowed(file, contentType)) {
                response.put("success", false);
                response.put("message", "不支持的文件类型");
                return ResponseEntity.badRequest().body(response);
            }

            // 上传文件
            String filePath = chatFileService.uploadChatFile(file, contentType);
            String accessUrl = chatFileService.getFileAccessUrl(filePath);

            // 创建聊天记录
            ChatRecord chatRecord = new ChatRecord();
            chatRecord.setSenderId(senderId);
            chatRecord.setReceiverId(receiverId);
            chatRecord.setContent(filePath); // 存储文件路径
            chatRecord.setContentType(contentType);
            chatRecord.setSendTime(LocalDateTime.now());
            chatRecord.setIsRead(false);

            Long messageId = chatRecordService.sendMessage(chatRecord);

            response.put("success", true);
            response.put("message", contentType + "消息发送成功");
            response.put("data", Map.of(
                "messageId", messageId,
                "contentType", contentType,
                "filePath", filePath,
                "accessUrl", accessUrl,
                "fileName", file.getOriginalFilename(),
                "fileSize", file.getSize()
            ));
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", contentType + "消息发送失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 统一发送消息接口
     * 支持文本、图片、视频、文件四种类型的消息
     *
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @param contentType 内容类型 (text, image, video, file)
     * @param textContent 文本内容（当contentType为text时使用）
     * @param file 文件（当contentType为image/video/file时使用）
     * @return 包含新消息ID的响应
     */
    @PostMapping("/send")
    public ResponseEntity<Map<String, Object>> sendMessage(
            @RequestParam("senderId") Long senderId,
            @RequestParam("receiverId") Long receiverId,
            @RequestParam("contentType") String contentType,
            @RequestParam(value = "textContent", required = false) String textContent,
            @RequestParam(value = "file", required = false) MultipartFile file) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 验证基本参数
            if (senderId == null || receiverId == null) {
                response.put("success", false);
                response.put("message", "发送者ID和接收者ID不能为空");
                return ResponseEntity.badRequest().body(response);
            }

            if (contentType == null || contentType.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "内容类型不能为空");
                return ResponseEntity.badRequest().body(response);
            }

            contentType = contentType.toLowerCase().trim();

            // 验证内容类型
            if (!Arrays.asList("text", "image", "video", "file").contains(contentType)) {
                response.put("success", false);
                response.put("message", "不支持的内容类型，仅支持: text, image, video, file");
                return ResponseEntity.badRequest().body(response);
            }

            // 根据内容类型处理消息
            switch (contentType) {
                case "text":
                    return handleTextMessage(senderId, receiverId, textContent);
                case "image":
                case "video":
                case "file":
                    return handleFileMessage(senderId, receiverId, file, contentType);
                default:
                    response.put("success", false);
                    response.put("message", "未知的内容类型: " + contentType);
                    return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "消息发送失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 处理文本消息
     */
    private ResponseEntity<Map<String, Object>> handleTextMessage(Long senderId, Long receiverId, String textContent) {
        Map<String, Object> response = new HashMap<>();

        if (textContent == null || textContent.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "文本内容不能为空");
            return ResponseEntity.badRequest().body(response);
        }

        try {
            ChatRecord chatRecord = new ChatRecord();
            chatRecord.setSenderId(senderId);
            chatRecord.setReceiverId(receiverId);
            chatRecord.setContent(textContent.trim());
            chatRecord.setContentType("text");
            chatRecord.setSendTime(LocalDateTime.now());
            chatRecord.setIsRead(false);

            Long messageId = chatRecordService.sendMessage(chatRecord);

            response.put("success", true);
            response.put("message", "文本消息发送成功");
            response.put("data", Map.of(
                "messageId", messageId,
                "contentType", "text",
                "content", textContent.trim()
            ));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "文本消息发送失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 处理文件消息
     */
    private ResponseEntity<Map<String, Object>> handleFileMessage(Long senderId, Long receiverId, MultipartFile file, String contentType) {
        Map<String, Object> response = new HashMap<>();

        if (file == null || file.isEmpty()) {
            response.put("success", false);
            response.put("message", "文件不能为空");
            return ResponseEntity.badRequest().body(response);
        }

        try {
            // 验证文件类型
            if (!chatFileService.isFileTypeAllowed(file, contentType)) {
                response.put("success", false);
                response.put("message", "不支持的文件类型");
                return ResponseEntity.badRequest().body(response);
            }

            // 上传文件
            String filePath = chatFileService.uploadChatFile(file, contentType);
            String accessUrl = chatFileService.getFileAccessUrl(filePath);

            // 创建聊天记录
            ChatRecord chatRecord = new ChatRecord();
            chatRecord.setSenderId(senderId);
            chatRecord.setReceiverId(receiverId);
            chatRecord.setContent(filePath);
            chatRecord.setContentType(contentType);
            chatRecord.setSendTime(LocalDateTime.now());
            chatRecord.setIsRead(false);

            Long messageId = chatRecordService.sendMessage(chatRecord);

            response.put("success", true);
            response.put("message", contentType + "消息发送成功");
            response.put("data", Map.of(
                "messageId", messageId,
                "contentType", contentType,
                "filePath", filePath,
                "accessUrl", accessUrl,
                "fileName", file.getOriginalFilename(),
                "fileSize", file.getSize()
            ));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", contentType + "消息发送失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }



    /**
     * 获取文件信息
     *
     * @param filePath 文件相对路径 (例如: image/2024/01/15/filename.jpg)
     * @return 文件信息响应
     */
    @GetMapping("/file/info")
    public ResponseEntity<Map<String, Object>> getFileInfo(@RequestParam("path") String filePath) {
        Map<String, Object> response = new HashMap<>();
        try {
            if (!chatFileService.fileExists(filePath)) {
                response.put("success", false);
                response.put("message", "文件不存在");
                return ResponseEntity.notFound().build();
            }

            String accessUrl = chatFileService.getFileAccessUrl(filePath);
            response.put("success", true);
            response.put("message", "获取文件信息成功");
            response.put("data", Map.of(
                "filePath", filePath,
                "accessUrl", accessUrl,
                "exists", true
            ));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取文件信息失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 下载文件
     *
     * @param filePath 文件相对路径
     * @return 文件内容
     */
    @GetMapping("/file/download")
    public ResponseEntity<byte[]> downloadFile(@RequestParam("path") String filePath) {
        try {
            if (!chatFileService.fileExists(filePath)) {
                return ResponseEntity.notFound().build();
            }

            // 构建完整文件路径
            String fullFilePath = chatFileService.getFullFilePath(filePath);
            Path fullPath = Paths.get(fullFilePath);
            byte[] fileContent = Files.readAllBytes(fullPath);

            // 获取文件名
            String fileName = fullPath.getFileName().toString();

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

            return new ResponseEntity<>(fileContent, headers, HttpStatus.OK);
        } catch (IOException e) {
            return ResponseEntity.internalServerError().build();
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 删除聊天文件
     *
     * @param filePath 文件相对路径
     * @return 删除结果响应
     */
    @DeleteMapping("/file")
    public ResponseEntity<Map<String, Object>> deleteChatFile(@RequestParam("path") String filePath) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean deleted = chatFileService.deleteChatFile(filePath);
            if (deleted) {
                response.put("success", true);
                response.put("message", "文件删除成功");
            } else {
                response.put("success", false);
                response.put("message", "文件删除失败或文件不存在");
            }
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "文件删除失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 删除消息
     *
     * @param id 消息ID
     * @return 删除结果响应
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteMessage(@PathVariable("id") Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            int result = chatRecordService.deleteMessage(id);
            response.put("success", true);
            response.put("message", "消息删除成功");
            response.put("data", result);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "消息删除失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 删除会话的所有消息
     *
     * @param sessionId 会话ID
     * @return 删除结果响应
     */
    @DeleteMapping("/session/{sessionId}")
    public ResponseEntity<Map<String, Object>> deleteSessionMessages(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> response = new HashMap<>();
        try {
            int result = chatRecordService.deleteSessionMessages(sessionId);
            response.put("success", true);
            response.put("message", "会话消息删除成功");
            response.put("data", result);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "会话消息删除失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 标记消息为已读
     *
     * @param id 消息ID
     * @return 更新结果响应
     */
    @PutMapping("/{id}/read")
    public ResponseEntity<Map<String, Object>> markMessageAsRead(@PathVariable("id") Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            int result = chatRecordService.markMessageAsRead(id);
            response.put("success", true);
            response.put("message", "消息已标记为已读");
            response.put("data", result);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "标记已读失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 标记会话中接收者的所有消息为已读
     *
     * @param sessionId 会话ID
     * @param receiverId 接收者ID
     * @return 更新结果响应
     */
    @PutMapping("/session/{sessionId}/receiver/{receiverId}/read")
    public ResponseEntity<Map<String, Object>> markSessionMessagesAsRead(
            @PathVariable("sessionId") String sessionId, @PathVariable("receiverId") Long receiverId) {
        Map<String, Object> response = new HashMap<>();
        try {
            int result = chatRecordService.markSessionMessagesAsRead(sessionId, receiverId);
            response.put("success", true);
            response.put("message", "会话消息已标记为已读");
            response.put("data", result);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "标记已读失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据ID获取消息详情
     *
     * @param id 消息ID
     * @return 消息详情响应
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getMessageById(@PathVariable("id") Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            ChatRecord message = chatRecordService.getMessageById(id);
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", message);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据会话ID获取聊天记录
     *
     * @param sessionId 会话ID
     * @return 聊天记录列表响应
     */
    @GetMapping("/session/{sessionId}")
    public ResponseEntity<Map<String, Object>> getMessagesBySessionId(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<ChatRecord> messages = chatRecordService.getMessagesBySessionId(sessionId);
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", messages);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 分页获取会话聊天记录
     *
     * @param sessionId 会话ID
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 聊天记录列表响应
     */
    @GetMapping("/session/{sessionId}/page")
    public ResponseEntity<Map<String, Object>> getMessagesBySessionIdWithPaging(
            @PathVariable("sessionId") String sessionId,
            @RequestParam(value = "page", defaultValue = "1") int page,  // 显式指定参数名
            @RequestParam(value = "size", defaultValue = "20") int size) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<ChatRecord> messages = chatRecordService.getMessagesBySessionIdWithPaging(sessionId, page, size);
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", messages);
            response.put("page", page);
            response.put("size", size);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取用户的未读消息数量
     *
     * @param receiverId 接收者ID
     * @return 未读消息数量响应
     */
    @GetMapping("/unread/user/{receiverId}")
    public ResponseEntity<Map<String, Object>> getUnreadMessageCount(@PathVariable("receiverId") Long receiverId) {
        Map<String, Object> response = new HashMap<>();
        try {
            int count = chatRecordService.getUnreadMessageCount(receiverId);
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", count);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取用户的未读消息列表
     *
     * @param receiverId 接收者ID
     * @return 未读消息列表响应
     */
    @GetMapping("/unread/{receiverId}")
    public ResponseEntity<Map<String, Object>> getUnreadMessages(@PathVariable("receiverId") Long receiverId) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<ChatRecord> messages = chatRecordService.getUnreadMessages(receiverId);
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", messages);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取指定会话的未读消息数量
     *
     * @param sessionId 会话ID
     * @param receiverId 接收者ID
     * @return 未读消息数量响应
     */
    @GetMapping("/unread/session/{sessionId}/receiver/{receiverId}")
    public ResponseEntity<Map<String, Object>> getUnreadMessageCountBySession(
            @PathVariable("sessionId") String sessionId, @PathVariable("receiverId") Long receiverId) {
        Map<String, Object> response = new HashMap<>();
        try {
            int count = chatRecordService.getUnreadMessageCountBySession(sessionId, receiverId);
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", count);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取会话的最新一条消息
     *
     * @param sessionId 会话ID
     * @return 最新消息响应
     */
    @GetMapping("/session/{sessionId}/latest")
    public ResponseEntity<Map<String, Object>> getLatestMessage(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> response = new HashMap<>();
        try {
            ChatRecord message = chatRecordService.getLatestMessage(sessionId);
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", message);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 根据时间范围获取聊天记录
     *
     * @param sessionId 会话ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 聊天记录列表响应
     */
    @GetMapping("/session/{sessionId}/timerange")
    public ResponseEntity<Map<String, Object>> getMessagesByTimeRange(
            @PathVariable("sessionId") String sessionId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<ChatRecord> messages = chatRecordService.getMessagesByTimeRange(sessionId, startTime, endTime);
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", messages);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 生成会话ID
     *
     * @param userId1 用户1的ID
     * @param userId2 用户2的ID
     * @return 会话ID响应
     */
    @GetMapping("/session/generate/{userId1}/{userId2}")
    public ResponseEntity<Map<String, Object>> generateSessionId(
            @PathVariable("userId1") Long userId1, @PathVariable("userId2") Long userId2) {
        Map<String, Object> response = new HashMap<>();
        try {
            String sessionId = chatRecordService.generateSessionId(userId1, userId2);
            response.put("success", true);
            response.put("message", "会话ID生成成功");
            response.put("data", sessionId);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "会话ID生成失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
