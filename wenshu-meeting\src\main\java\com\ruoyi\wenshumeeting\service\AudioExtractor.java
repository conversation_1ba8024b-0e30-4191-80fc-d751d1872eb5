package com.ruoyi.wenshumeeting.service;

import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.concurrent.TimeUnit;

@Service
public class AudioExtractor {

    // 设置 FFmpeg 可执行文件路径（根据你的系统配置修改）
    private static final String FFMPEG_PATH = "ffmpeg";

    // 增加超时时间到10分钟，适应大文件处理
    private static final int TIMEOUT_MINUTES = 10;

    public String extractAudio(String videoPath) throws IOException, InterruptedException {
        // 验证视频文件是否存在
        File videoFile = new File(videoPath);
        if (!videoFile.exists()) {
            throw new IOException("视频文件不存在: " + videoPath);
        }

        System.out.println("开始提取音频，视频文件大小: " + (videoFile.length() / 1024 / 1024) + " MB");

        // 构建输出音频路径（相同目录，扩展名改为.mp3）
        String audioPath = videoPath.substring(0, videoPath.lastIndexOf('.')) + ".mp3";
        File audioFile = new File(audioPath);

        // 如果输出文件已存在，先删除
        if (audioFile.exists()) {
            audioFile.delete();
        }

        // 构建 FFmpeg 命令
        ProcessBuilder processBuilder = new ProcessBuilder(
                FFMPEG_PATH,
                "-y",  // 覆盖已存在文件
                "-i", videoPath,
                "-vn", // 禁用视频流
                "-acodec", "libmp3lame", // 使用 MP3 编码
                "-q:a", "2", // 音频质量 (0-9, 0为最高)
                "-map_metadata", "0", // 保留元数据
                "-progress", "pipe:1", // 输出进度信息
                audioPath
        );

        // 不重定向错误流，分别处理标准输出和错误输出
        processBuilder.redirectErrorStream(false);

        System.out.println("执行FFmpeg命令: " + String.join(" ", processBuilder.command()));

        // 执行命令
        Process process = processBuilder.start();

        // 创建线程读取错误输出
        Thread errorReaderThread = new Thread(() -> {
            try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    System.out.println("FFmpeg: " + line);
                }
            } catch (IOException e) {
                System.err.println("读取FFmpeg错误输出时发生异常: " + e.getMessage());
            }
        });
        errorReaderThread.start();

        // 创建线程读取标准输出（进度信息）
        Thread outputReaderThread = new Thread(() -> {
            try (BufferedReader outputReader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = outputReader.readLine()) != null) {
                    if (line.startsWith("out_time=")) {
                        System.out.println("处理进度: " + line);
                    }
                }
            } catch (IOException e) {
                System.err.println("读取FFmpeg输出时发生异常: " + e.getMessage());
            }
        });
        outputReaderThread.start();

        // 设置超时并等待完成
        boolean finished = process.waitFor(TIMEOUT_MINUTES, TimeUnit.MINUTES);

        if (!finished) {
            process.destroyForcibly();
            throw new IOException("FFmpeg 处理超时（超过 " + TIMEOUT_MINUTES + " 分钟）");
        }

        // 等待读取线程完成
        try {
            errorReaderThread.join(5000);
            outputReaderThread.join(5000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 检查退出码
        int exitCode = process.exitValue();
        if (exitCode != 0) {
            throw new IOException("音频提取失败，FFmpeg 错误码: " + exitCode);
        }

        // 验证输出文件
        if (!audioFile.exists()) {
            throw new IOException("输出音频文件创建失败: " + audioPath);
        }

        if (audioFile.length() == 0) {
            throw new IOException("输出音频文件为空: " + audioPath);
        }

        System.out.println("音频提取成功！输出文件: " + audioPath);
        System.out.println("输出音频文件大小: " + (audioFile.length() / 1024 / 1024) + " MB");

        return audioPath;
    }
}