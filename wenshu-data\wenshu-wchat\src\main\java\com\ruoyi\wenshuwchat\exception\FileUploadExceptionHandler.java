package com.ruoyi.wenshuwchat.exception;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartException;

import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传异常处理器
 * 专门处理文件上传相关的异常，包括文件大小超限等问题
 */
@ControllerAdvice
public class FileUploadExceptionHandler {

    /**
     * 处理文件大小超限异常
     * 当上传的文件超过配置的最大大小时触发
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<Map<String, Object>> handleMaxUploadSizeExceeded(MaxUploadSizeExceededException e) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "文件大小超过限制，最大支持10GB");
        response.put("error", "FILE_SIZE_EXCEEDED");
        response.put("maxSize", "10GB");
        
        return ResponseEntity.status(HttpStatus.PAYLOAD_TOO_LARGE).body(response);
    }

    /**
     * 处理其他文件上传异常
     * 包括文件格式错误、上传失败等
     */
    @ExceptionHandler(MultipartException.class)
    public ResponseEntity<Map<String, Object>> handleMultipartException(MultipartException e) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        
        // 检查是否是文件大小问题
        if (e.getCause() instanceof IllegalStateException && 
            e.getMessage().contains("exceeds the configured maximum")) {
            response.put("message", "文件大小超过限制，最大支持10GB");
            response.put("error", "FILE_SIZE_EXCEEDED");
            return ResponseEntity.status(HttpStatus.PAYLOAD_TOO_LARGE).body(response);
        }
        
        response.put("message", "文件上传失败: " + e.getMessage());
        response.put("error", "UPLOAD_FAILED");
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理一般的文件上传相关异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<Map<String, Object>> handleIllegalArgumentException(IllegalArgumentException e) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", e.getMessage());
        response.put("error", "INVALID_ARGUMENT");
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }
}
