package com.ruoyi.wenshufile.exception;

/**
 * 文件存储异常类
 * 用于处理文件上传、存储、删除等操作中的异常情况
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-28
 */
public class FileStorageException extends RuntimeException {

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 构造函数
     * 
     * @param message 错误信息
     */
    public FileStorageException(String message) {
        super(message);
    }

    /**
     * 构造函数
     * 
     * @param message 错误信息
     * @param cause 原因异常
     */
    public FileStorageException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param message 错误信息
     */
    public FileStorageException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param message 错误信息
     * @param cause 原因异常
     */
    public FileStorageException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    /**
     * 获取错误代码
     * 
     * @return 错误代码
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 设置错误代码
     * 
     * @param errorCode 错误代码
     */
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    // 常见错误代码常量
    public static final String FILE_TOO_LARGE = "FILE_TOO_LARGE";
    public static final String FILE_TYPE_NOT_ALLOWED = "FILE_TYPE_NOT_ALLOWED";
    public static final String FILE_NAME_INVALID = "FILE_NAME_INVALID";
    public static final String STORAGE_PATH_NOT_FOUND = "STORAGE_PATH_NOT_FOUND";
    public static final String UPLOAD_FAILED = "UPLOAD_FAILED";
    public static final String DELETE_FAILED = "DELETE_FAILED";
    public static final String FILE_NOT_FOUND = "FILE_NOT_FOUND";
    public static final String PERMISSION_DENIED = "PERMISSION_DENIED";
    public static final String DISK_SPACE_INSUFFICIENT = "DISK_SPACE_INSUFFICIENT";
}
