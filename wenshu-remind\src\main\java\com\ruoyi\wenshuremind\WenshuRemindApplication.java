package com.ruoyi.wenshuremind;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;


// Generated by https://start.springboot.io
// 优质的 spring/boot/data/security/cloud 框架中文文档尽在 => https://springdoc.cn
@EnableFeignClients(basePackages = {
        "com.ruoyi.wenshuapi"
}, defaultConfiguration = com.ruoyi.wenshuremind.config.WenshuRemindFeignConfig.class)
@ComponentScan(basePackages = {
        "com.ruoyi.wenshuapi",
        "com.ruoyi.wenshuremind"
})
@EnableDiscoveryClient
@SpringBootApplication
public class WenshuRemindApplication {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static void printStartupBanner() {
        System.out.println();
        System.out.println("██╗    ██╗███████╗███╗   ██╗███████╗██╗  ██╗██╗   ██╗");
        System.out.println("██║    ██║██╔════╝████╗  ██║██╔════╝██║  ██║██║   ██║");
        System.out.println("██║ █╗ ██║█████╗  ██╔██╗ ██║███████╗███████║██║   ██║");
        System.out.println("██║███╗██║██╔══╝  ██║╚██╗██║╚════██║██╔══██║██║   ██║");
        System.out.println("╚███╔███╔╝███████╗██║ ╚████║███████║██║  ██║╚██████╔╝");
        System.out.println(" ╚══╝╚══╝ ╚══════╝╚═╝  ╚═══╝╚══════╝╚═╝  ╚═╝ ╚═════╝ ");
        System.out.println();
        System.out.println("WENSHU-REMIND 提醒服务");
        System.out.println("启动时间: " + LocalDateTime.now().format(FORMATTER));
        System.out.println("服务端口: 1015");
        System.out.println("服务功能: 智能提醒管理");
        System.out.println();
    }

    public static void main(String[] args) {
        printStartupBanner();
        System.setProperty("spring.cloud.bootstrap.location", "classpath:bootstrap.yml");
        System.setProperty("spring.config.location", "classpath:bootstrap.yml");
        SpringApplication.run(WenshuRemindApplication.class, args);
    }

}
