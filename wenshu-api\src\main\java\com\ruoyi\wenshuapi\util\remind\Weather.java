package com.ruoyi.wenshuapi.util.remind;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

public class Weather {

    private static final Logger logger = LoggerFactory.getLogger(Weather.class);

    // 天气API常量
    private static final String API_URL = "https://cn.apihz.cn/api/tianqi/tqyb.php";
    private static final String PUBLIC_ID = "10005610";
    private static final String PUBLIC_KEY = "e7a7d3fb36356a3d2ba354af247b4373";

    /**
     * 获取指定地点的天气信息
     *
     * @param province 省份名称
     * @param city 城市名称
     * @return 天气信息字符串
     *
     * @throws IllegalArgumentException 当参数为空时抛出
     * @throws RuntimeException 当HTTP请求失败或处理异常时抛出
     */
    public static String getWeather(String province, String city) {
        logger.info("=== 开始获取天气信息 ===");
        logger.info("省份: {}", province);
        logger.info("城市: {}", city);

        // 参数校验
        if (province == null || province.isEmpty() || city == null || city.isEmpty()) {
            logger.error("❌ 参数校验失败: province={}, city={}", province, city);
            throw new IllegalArgumentException("省份和城市参数不能为空");
        }

        try {
            // 构建URL
            String encodedProvince = URLEncoder.encode(province, StandardCharsets.UTF_8);
            String encodedCity = URLEncoder.encode(city, StandardCharsets.UTF_8);
            String fullUrl = String.format("%s?id=%s&key=%s&sheng=%s&place=%s",
                    API_URL, PUBLIC_ID, PUBLIC_KEY, encodedProvince, encodedCity);

            logger.info("🌐 构建天气API请求URL:");
            logger.info("  - 原始省份: {}", province);
            logger.info("  - 编码省份: {}", encodedProvince);
            logger.info("  - 原始城市: {}", city);
            logger.info("  - 编码城市: {}", encodedCity);
            logger.info("  - 完整URL: {}", fullUrl);

            // 创建连接
            logger.info("🔗 创建HTTP连接...");
            HttpURLConnection connection = (HttpURLConnection) new URL(fullUrl).openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            logger.info("  - 请求方法: GET");
            logger.info("  - 连接超时: 5000ms");
            logger.info("  - 读取超时: 5000ms");

            // 获取响应
            logger.info("📡 发送HTTP请求...");
            int status = connection.getResponseCode();
            logger.info("📡 HTTP响应状态码: {}", status);

            if (status != HttpURLConnection.HTTP_OK) {
                logger.error("❌ HTTP请求失败，状态码: {}", status);
                throw new RuntimeException("HTTP请求失败，状态码: " + status);
            }

            // 读取响应
            logger.info("📖 读取响应内容...");
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                StringBuilder response = new StringBuilder();
                String line;
                int lineCount = 0;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                    lineCount++;
                }

                String weatherData = response.toString();
                logger.info("✅ 天气数据获取成功");
                logger.info("  - 响应行数: {}", lineCount);
                logger.info("  - 响应长度: {} 字符", weatherData.length());
                logger.info("  - 响应内容: {}", weatherData);

                return weatherData;
            }
        } catch (Exception e) {
            logger.error("❌ 天气请求异常");
            logger.error("异常类型: {}", e.getClass().getSimpleName());
            logger.error("异常信息: {}", e.getMessage());
            logger.error("异常堆栈: ", e);
            throw new RuntimeException("天气请求异常: " + e.getMessage(), e);
        }
    }
}
