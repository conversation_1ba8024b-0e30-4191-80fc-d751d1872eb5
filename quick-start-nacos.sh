#!/bin/bash

# 快速启动Nacos v2.2.0
echo "=== 快速启动Nacos v2.2.0 ==="

# 1. 拉取镜像
echo "1. 拉取Nacos v2.2.0镜像..."
docker pull lcr.loongnix.cn/nacos/nacos-server:v2.2.0

# 2. 停止旧服务
echo "2. 停止现有服务..."
docker compose -f docker-compose.env.yml down nacos 2>/dev/null
docker compose -f docker-compose.app.yml down auth gateway system 2>/dev/null

# 3. 修复数据库（如果需要）
echo "3. 修复数据库表结构..."
if [ -f "fix-nacos-db.sql" ] && docker ps --filter "name=wenshu-mysql" --format "{{.Names}}" | grep -q "wenshu-mysql"; then
    docker exec -i wenshu-mysql mysql -uroot -p2313147023 < fix-nacos-db.sql
    echo "✅ 数据库修复完成"
fi

# 4. 启动基础服务
echo "4. 启动基础服务..."
docker compose -f docker-compose.env.yml up -d mysql redis
sleep 10

# 5. 启动Nacos
echo "5. 启动Nacos..."
docker compose -f docker-compose.env.yml up -d nacos
sleep 30

# 6. 启动应用服务
echo "6. 启动应用服务..."
docker compose -f docker-compose.app.yml up -d auth
sleep 15
docker compose -f docker-compose.app.yml up -d system gateway
sleep 10

# 7. 检查状态
echo "7. 检查服务状态..."
docker ps --filter "name=wenshu-" --format "table {{.Names}}\t{{.Status}}"

echo ""
echo "✅ 启动完成！"
echo "访问 Nacos 控制台: http://localhost:8848/nacos"
echo "访问网关: http://localhost:8080"
