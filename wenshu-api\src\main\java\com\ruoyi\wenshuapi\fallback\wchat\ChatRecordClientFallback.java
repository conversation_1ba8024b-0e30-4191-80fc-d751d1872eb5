package com.ruoyi.wenshuapi.fallback.wchat;

import com.ruoyi.wenshuapi.client.wchat.ChatRecordClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 聊天记录服务降级处理类
 *
 * <p><strong>功能说明：</strong>
 * 当聊天记录服务(wenshu-wchat)不可用或调用超时时，触发熔断降级机制，
 * 避免服务雪崩，同时提供可读性强的错误提示和详细的日志记录。</p>
 *
 * <p><strong>降级策略：</strong>
 * 1. 返回友好的错误响应（HTTP 503状态）
 * 2. 记录完整的异常堆栈信息
 * 3. 空操作保护（如发送/删除操作不执行实际动作）
 * 4. 查询操作返回空数据集
 * </p>
 */
@Slf4j
@Component
public class ChatRecordClientFallback implements ChatRecordClient {

    private static final String SERVICE_UNAVAILABLE_MSG = "聊天记录服务暂时不可用，请稍后重试";

    /**
     * 创建标准的降级响应
     */
    private ResponseEntity<Map<String, Object>> createFallbackResponse(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        response.put("data", data);
        response.put("fallback", true);
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
    }

    /**
     * 创建成功的降级响应（用于查询操作）
     */
    private ResponseEntity<Map<String, Object>> createSuccessFallbackResponse(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "（降级数据）" + message);
        response.put("data", data);
        response.put("fallback", true);
        return ResponseEntity.ok(response);
    }

    @Override
    public ResponseEntity<Map<String, Object>> sendMessage(Long senderId, Long receiverId, String contentType, String textContent, MultipartFile file) {
        log.warn("聊天记录服务降级触发 >> 发送消息失败 - 发送者: {}, 接收者: {}, 类型: {}", senderId, receiverId, contentType);
        return createFallbackResponse("消息发送失败，" + SERVICE_UNAVAILABLE_MSG, null);
    }

    @Override
    public ResponseEntity<Map<String, Object>> sendTextMessage(Long senderId, Long receiverId, String content) {
        log.warn("聊天记录服务降级触发 >> 发送文本消息失败 - 发送者: {}, 接收者: {}", senderId, receiverId);
        return createFallbackResponse("文本消息发送失败，" + SERVICE_UNAVAILABLE_MSG, null);
    }

    @Override
    public ResponseEntity<Map<String, Object>> sendImageMessage(Long senderId, Long receiverId, MultipartFile file) {
        log.warn("聊天记录服务降级触发 >> 发送图片消息失败 - 发送者: {}, 接收者: {}", senderId, receiverId);
        return createFallbackResponse("图片消息发送失败，" + SERVICE_UNAVAILABLE_MSG, null);
    }

    @Override
    public ResponseEntity<Map<String, Object>> sendVideoMessage(Long senderId, Long receiverId, MultipartFile file) {
        log.warn("聊天记录服务降级触发 >> 发送视频消息失败 - 发送者: {}, 接收者: {}", senderId, receiverId);
        return createFallbackResponse("视频消息发送失败，" + SERVICE_UNAVAILABLE_MSG, null);
    }

    @Override
    public ResponseEntity<Map<String, Object>> sendFileMessage(Long senderId, Long receiverId, MultipartFile file) {
        log.warn("聊天记录服务降级触发 >> 发送文件消息失败 - 发送者: {}, 接收者: {}, 文件名: {}",
                senderId, receiverId, file != null ? file.getOriginalFilename() : "null");
        return createFallbackResponse("文件消息发送失败，" + SERVICE_UNAVAILABLE_MSG, null);
    }

    @Override
    public ResponseEntity<Map<String, Object>> getFileInfo(String filePath) {
        log.warn("聊天记录服务降级触发 >> 获取文件信息失败 - 文件路径: {}", filePath);
        return createFallbackResponse("文件信息获取失败，" + SERVICE_UNAVAILABLE_MSG, null);
    }

    @Override
    public ResponseEntity<byte[]> downloadFile(String filePath) {
        log.warn("聊天记录服务降级触发 >> 文件下载失败 - 文件路径: {}", filePath);
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(new byte[0]);
    }

    @Override
    public ResponseEntity<Map<String, Object>> deleteMessage(Long messageId) {
        log.warn("聊天记录服务降级触发 >> 删除消息失败 - 消息ID: {}", messageId);
        return createFallbackResponse("消息删除失败，" + SERVICE_UNAVAILABLE_MSG, null);
    }

    @Override
    public ResponseEntity<Map<String, Object>> markAsRead(Long messageId) {
        log.warn("聊天记录服务降级触发 >> 标记已读失败 - 消息ID: {}", messageId);
        return createFallbackResponse("消息标记已读失败，" + SERVICE_UNAVAILABLE_MSG, null);
    }

    @Override
    public ResponseEntity<Map<String, Object>> batchMarkAsRead(List<Long> messageIds) {
        log.warn("聊天记录服务降级触发 >> 批量标记已读失败 - 消息数量: {}", messageIds != null ? messageIds.size() : 0);
        return createFallbackResponse("批量标记已读失败，" + SERVICE_UNAVAILABLE_MSG, null);
    }

    @Override
    public ResponseEntity<Map<String, Object>> getMessagesBySenderAndReceiver(Long senderId, Long receiverId) {
        log.warn("聊天记录服务降级触发 >> 获取聊天记录失败 - 发送者: {}, 接收者: {}", senderId, receiverId);
        return createSuccessFallbackResponse(SERVICE_UNAVAILABLE_MSG, new ArrayList<>());
    }

    @Override
    public ResponseEntity<Map<String, Object>> getMessagesBySenderAndReceiverWithPaging(Long senderId, Long receiverId, int page, int size) {
        log.warn("聊天记录服务降级触发 >> 分页获取聊天记录失败 - 发送者: {}, 接收者: {}, 页码: {}, 大小: {}", senderId, receiverId, page, size);
        Map<String, Object> data = new HashMap<>();
        data.put("messages", new ArrayList<>());
        data.put("page", page);
        data.put("size", size);
        data.put("total", 0);
        return createSuccessFallbackResponse(SERVICE_UNAVAILABLE_MSG, data);
    }

    @Override
    public ResponseEntity<Map<String, Object>> getMessagesBySessionId(String sessionId) {
        log.warn("聊天记录服务降级触发 >> 根据会话ID获取聊天记录失败 - 会话ID: {}", sessionId);
        return createSuccessFallbackResponse(SERVICE_UNAVAILABLE_MSG, new ArrayList<>());
    }

    @Override
    public ResponseEntity<Map<String, Object>> getMessagesBySessionIdWithPaging(String sessionId, int page, int size) {
        log.warn("聊天记录服务降级触发 >> 分页获取会话聊天记录失败 - 会话ID: {}, 页码: {}, 大小: {}", sessionId, page, size);
        Map<String, Object> data = new HashMap<>();
        data.put("messages", new ArrayList<>());
        data.put("page", page);
        data.put("size", size);
        data.put("total", 0);
        return createSuccessFallbackResponse(SERVICE_UNAVAILABLE_MSG, data);
    }

    @Override
    public ResponseEntity<Map<String, Object>> getUnreadMessageCount(Long userId) {
        log.warn("聊天记录服务降级触发 >> 获取未读消息数量失败 - 用户ID: {}", userId);
        return createSuccessFallbackResponse(SERVICE_UNAVAILABLE_MSG, 0);
    }

    @Override
    public ResponseEntity<Map<String, Object>> getUnreadMessages(Long userId) {
        log.warn("聊天记录服务降级触发 >> 获取未读消息列表失败 - 用户ID: {}", userId);
        return createSuccessFallbackResponse(SERVICE_UNAVAILABLE_MSG, new ArrayList<>());
    }

    @Override
    public ResponseEntity<Map<String, Object>> getMessagesByTimeRange(String sessionId, LocalDateTime startTime, LocalDateTime endTime) {
        log.warn("聊天记录服务降级触发 >> 根据时间范围获取聊天记录失败 - 会话ID: {}, 开始时间: {}, 结束时间: {}", sessionId, startTime, endTime);
        return createSuccessFallbackResponse(SERVICE_UNAVAILABLE_MSG, new ArrayList<>());
    }

    @Override
    public ResponseEntity<Map<String, Object>> generateSessionId(Long userId1, Long userId2) {
        log.warn("聊天记录服务降级触发 >> 生成会话ID失败 - 用户1: {}, 用户2: {}", userId1, userId2);
        return createFallbackResponse("会话ID生成失败，" + SERVICE_UNAVAILABLE_MSG, null);
    }

    @Override
    public ResponseEntity<Map<String, Object>> getMessageById(Long messageId) {
        log.warn("聊天记录服务降级触发 >> 根据消息ID获取聊天记录失败 - 消息ID: {}", messageId);
        return createSuccessFallbackResponse(SERVICE_UNAVAILABLE_MSG, null);
    }

    @Override
    public ResponseEntity<Map<String, Object>> searchMessages(String sessionId, String keyword) {
        log.warn("聊天记录服务降级触发 >> 搜索聊天记录失败 - 会话ID: {}, 关键词: {}", sessionId, keyword);
        return createSuccessFallbackResponse(SERVICE_UNAVAILABLE_MSG, new ArrayList<>());
    }
}
