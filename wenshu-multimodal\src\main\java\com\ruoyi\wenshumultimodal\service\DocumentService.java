package com.ruoyi.wenshumultimodal.service;

import java.util.Map;

/**
 * 文档服务接口
 */
public interface DocumentService {
    
    /**
     * 生成Markdown格式的文档内容
     * 
     * @param prompt 提示词
     * @param userId 用户ID
     * @return 生成的Markdown内容
     */
    String generateMarkdownContent(String prompt, String userId);
    
    /**
     * 生成Markdown格式的文档内容（增强版）
     * 
     * @param prompt 提示词
     * @param userId 用户ID
     * @param style 文档风格
     * @param structureType 文档结构类型
     * @return 生成的Markdown内容
     */
    String generateMarkdownContent(String prompt, String userId, String style, String structureType);
    
    /**
     * 将Markdown内容转换为HTML
     * 
     * @param markdown Markdown内容
     * @return HTML内容
     */
    String convertMarkdownToHtml(String markdown);
    
    /**
     * 将Markdown内容转换为HTML（增强版）
     * 
     * @param markdown Markdown内容
     * @param theme 主题风格
     * @param customCss 自定义CSS
     * @return HTML内容
     */
    String convertMarkdownToHtml(String markdown, String theme, String customCss);
    
    /**
     * 生成文档并保存到指定路径
     * 
     * @param fileId 文件ID
     * @param prompt 提示词
     * @param format 文档格式 (md, html, docx, pdf)
     * @param outputPath 输出路径
     * @param userId 用户ID
     * @return 文件路径
     */
    String generateDocument(int fileId, String prompt, String format, String outputPath, String userId);
    
    /**
     * 生成增强文档并保存到指定路径
     * 
     * @param fileId 文件ID
     * @param prompt 提示词
     * @param format 文档格式 (md, html, docx, pdf)
     * @param outputPath 输出路径
     * @param userId 用户ID
     * @param docConfig 文档配置参数
     * @return 文件路径
     */
    String generateEnhancedDocument(int fileId, String prompt, String format, String outputPath, String userId, Map<String, Object> docConfig);
    
    /**
     * 解析文档内容
     * 
     * @param prompt 提示词
     * @param userId 用户ID
     * @return 解析结果
     */
    String praseDoucment(String prompt, String userId);
    
    /**
     * 解析文档内容（增强版）
     * 
     * @param prompt 提示词
     * @param userId 用户ID
     * @param extractionType 提取类型
     * @return 解析结果
     */
    String parseDocument(String prompt, String userId, String extractionType);
} 