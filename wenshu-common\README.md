# Wenshu 公共模块

本模块包含 wenshu-chat 和 wenshu-voice 项目共用的实体、服务接口和工具类。通过 Feign 实现微服务间远程调用。

## 模块结构

```
wenshu-common
├── src/main/java/com/ruoyi/wenshucommon
│   ├── advisor         # 顾问类
│   ├── common          # 通用类
│   ├── entity          # 实体类
│   ├── feign           # Feign 客户端
│   ├── response        # 响应对象
│   ├── service         # 服务接口
│   └── util            # 工具类
└── pom.xml
```

## 主要功能

1. **公共实体类**
   - `BaseConversation`：对话基础实体类
   - `MessageWrapper`：消息包装类

2. **公共工具类**
   - `RestResult`：统一响应结果类
   - `JsonUtil`：JSON处理工具类
   - `IdUtil`：ID生成工具类

3. **公共服务接口**
   - `ChatService`：对话服务接口
   - `ConversationService`：对话数据访问接口

4. **Feign 客户端**
   - `ChatServiceClient`：对话服务客户端，用于远程调用

## 使用说明

1. 在 pom.xml 中添加依赖：

```xml
<dependency>
    <groupId>com.ruoyi</groupId>
    <artifactId>wenshu-common</artifactId>
    <version>0.0.1-SNAPSHOT</version>
</dependency>
```

2. 在启动类中启用 Feign 客户端：

```java
@SpringBootApplication
@EnableFeignClients(basePackages = "com.ruoyi.wenshucommon.feign")
public class Application {
    // ...
}
```

3. 注入和使用 Feign 客户端：

```java
@Service
@RequiredArgsConstructor
public class YourService {
    private final ChatServiceClient chatServiceClient;
    
    public void someMethod() {
        // 使用 chatServiceClient 调用远程服务
    }
}
```

## 注意事项

1. 确保微服务注册中心（如 Nacos）正确配置并启动
2. 服务名称必须与 @FeignClient 的 name 属性一致
3. 服务实现类需要实现公共接口 