// src/main/java/com/ruoyi/wenshuremind/client/EmailServiceClient.java
package com.ruoyi.wenshuapi.client.remind;

import com.ruoyi.wenshuapi.fallback.file.FileInfoClientFallback;
import com.ruoyi.wenshuapi.fallback.remind.EmailServiceClientFallback;
import com.ruoyi.wenshuapi.util.file.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

/**
 * 邮件服务Feign客户端
 * 通过Feign调用wenshu-remind服务的邮件功能
 */
@FeignClient(name = "wenshu-remind", path = "/remind",fallbackFactory = EmailServiceClientFallback.class)
public interface EmailServiceClient {

    /**
     * 调用发送纯文本邮件接口
     * 
     * @param to      收件人地址
     * @param subject 邮件主题
     * @param text    邮件正文内容
     * @return 统一API响应结果
     */
    @PostMapping("/send-simple")
    ApiResponse<String> sendSimpleMail(
            @RequestParam("to") String to,
            @RequestParam("subject") String subject,
            @RequestParam("text") String text);

    /**
     * 调用发送HTML邮件接口
     * 
     * @param to          收件人地址
     * @param subject     邮件主题
     * @param htmlContent HTML格式邮件内容
     * @return 统一API响应结果
     */
    @PostMapping("/send-html")
    ApiResponse<String> sendHtmlMail(
            @RequestParam("to") String to,
            @RequestParam("subject") String subject,
            @RequestParam("htmlContent") String htmlContent);

    /**
     * 调用发送带附件的邮件接口
     * 
     * @param to        收件人地址
     * @param subject   邮件主题
     * @param text      邮件正文内容
     * @param attachment 邮件附件
     * @return 统一API响应结果
     */
    @PostMapping(value = "/send-attachment", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ApiResponse<String> sendAttachmentMail(
            @RequestParam("to") String to,
            @RequestParam("subject") String subject,
            @RequestParam("text") String text,
            @RequestPart("attachment") MultipartFile attachment);
}