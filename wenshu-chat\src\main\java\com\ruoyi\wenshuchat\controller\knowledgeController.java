package com.ruoyi.wenshuchat.controller;

import com.ruoyi.wenshuapi.client.file.FileStorageClient;
import com.ruoyi.wenshuapi.util.file.ApiResponse;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFComment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.ai.document.Document;
import org.springframework.ai.document.DocumentReader;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.reader.TextReader;
import org.springframework.ai.transformer.splitter.TextSplitter;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.ai.vectorstore.milvus.MilvusVectorStore;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.mozilla.universalchardet.UniversalDetector;

@RestController
@RequestMapping("/embeding")
@RequiredArgsConstructor
public class knowledgeController {
    private final MilvusVectorStore vectorStore;
    private final FileStorageClient fileStorageClient;
//学习用例
//    @GetMapping("/embed")
//    public String embed(){
//        EmbeddingResponse embeddingResponse=embeddingModel.embedForResponse(List.of("今天天气不错"));
//        System.out.println(Arrays.toString(embeddingResponse.getResult().getOutput()));
//        float[]embed=embeddingModel.embed("挺风和日丽");
//        System.out.println(Arrays.toString(embed));
//        return "success";
//    }
//    @PostConstruct
//    public void init() {
//        List<Document> documents = List.of(
//                new Document("1", "今天天气不错", Map.of("country", "郑州", "date", "2025-05-13")),
//                new Document("2", "天气不错，适合旅游", Map.of("country", "开封", "date", "2025-05-15")),
//                new Document("3", "去哪里旅游好呢", Map.of("country", "洛阳", "date", "2025-05-15")));
//
//        vectorStore.add(documents);
////        // 存储数据
////        simpleVectorStore.add(documents);
//    }
//    @GetMapping("/store")
//    public String store(@RequestParam("message") String message){
//        List<Document> list=simpleVectorStore.similaritySearch(message);
//        System.out.println(list.size());
//        System.out.println(list.get(0).getText());
//        return "success";
//    }

//    @GetMapping("/search")
//    public String search(@RequestParam("message") String message){
//        List<Document> list=vectorStore.similaritySearch(message);
//        System.out.println(list.size());
//        System.out.println(list.get(0).getText());
//        return "success";
//    }


@PostMapping("/add")
public ApiResponse<String> upload(
        @RequestParam("file") MultipartFile file,
        @RequestParam("userid") int userid) {

        try {
            if (file.isEmpty()) {
                return ApiResponse.failed("上传文件不能为空");
            }
            
            // 处理上传的文件
            String content;
            String filename=file.getOriginalFilename();
            String extension=filename.substring(filename.lastIndexOf(".")+1).toLowerCase();
            switch (extension){
                case "txt":
                    content = new String(file.getBytes(), detectCharset(file));break;
                case "docx":
                    content=parseDocx(file.getInputStream());break;
                case "doc":
                    content=paresDoc(file.getInputStream());break;
                case "pdf":
                    content=prasePdf(file.getInputStream());break;
                default:
                    return ApiResponse.failed("不支持的文件格式"+extension);
            }
            // 创建Document对象而不是使用TextReader
//            Document document = new Document(content, Map.of("filename", file.getOriginalFilename(), "userId", String.valueOf(userid)));
//            List<Document> documents = List.of(document);
//            List<Document> splitDocuments = new TokenTextSplitter(400,
//                    100,
//                    20,
//                    200,
//                    true)
//                    .apply(documents);
            Map<String, Object> metadata = Map.of(
                    "filename", file.getOriginalFilename(),
                    "user_id", String.valueOf(userid),  // 关键：添加用户ID
                    "file_type", extension,
                    "upload_time", Instant.now().toString()
            );
            Document document = new Document(content, metadata);

            // 使用更智能的分块策略
            TextSplitter splitter = new TokenTextSplitter(
                    800,  // 目标块大小
                    200,   // 最小块大小
                    1200,  // 最大块大小
                    100,   // 块重叠
                    true   // 保留分段
            );
            vectorStore.add(splitter.apply(List.of(document)));

            return ApiResponse.success("上传成功");
        } catch (IOException e) {
            e.printStackTrace();
            return ApiResponse.failed("文件处理失败: " + e.getMessage());
        }
    }

    private String parseDocx(InputStream inputStream) throws IOException {
        try(XWPFDocument doc=new XWPFDocument(inputStream);
            XWPFWordExtractor extractor=new XWPFWordExtractor(doc)
        ){
            return extractor.getText();
        }
    }

    private String paresDoc(InputStream inputStream) throws IOException {
        try(HWPFDocument doc=new HWPFDocument(inputStream)){
            return doc.getDocumentText();
        }
    }
    private String prasePdf(InputStream inputStream) throws IOException {
        try(PDDocument doc=PDDocument.load(inputStream)){
            PDFTextStripper stripper=new PDFTextStripper();
            return stripper.getText(doc);
        }
    }

    /**
     * 检测文件的字符编码
     * @param file 上传的文件
     * @return 检测到的字符集
     */
    private Charset detectCharset(MultipartFile file) throws IOException {
        byte[] bytes = file.getBytes();
        UniversalDetector detector = new UniversalDetector(null);
        detector.handleData(bytes, 0, bytes.length);
        detector.dataEnd();

        String charsetName = detector.getDetectedCharset();
        detector.reset();

        if (StringUtils.hasText(charsetName)) {
            try {
                return Charset.forName(charsetName);
            } catch (UnsupportedCharsetException e) {
                // 如果检测到的字符集不支持，返回UTF-8
            }
        }

        // 默认返回UTF-8
        return StandardCharsets.UTF_8;
    }
}
