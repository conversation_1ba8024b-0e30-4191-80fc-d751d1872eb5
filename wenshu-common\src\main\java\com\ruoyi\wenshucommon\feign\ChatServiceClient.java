package com.ruoyi.wenshucommon.feign;

import com.ruoyi.wenshucommon.common.RestResult;
import com.ruoyi.wenshucommon.response.ConversationVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 对话服务Feign客户端
 */
@FeignClient(name = "wenshu-chat", path = "/api/conversation", contextId = "chatServiceClient")
public interface ChatServiceClient {

    /**
     * 创建对话
     */
    @PostMapping("/create")
    RestResult<ConversationVO> create();

    /**
     * 编辑对话
     */
    @PutMapping("/edit")
    RestResult<Void> edit(@RequestParam("conversationId") String conversationId, 
                          @RequestParam("title") String title);

    /**
     * 获取对话列表
     */
    @GetMapping("/list")
    RestResult<List<ConversationVO>> list();

    /**
     * 删除对话
     */
    @DeleteMapping("/del")
    RestResult<Void> delete(@RequestParam("conversationId") String conversationId);

    /**
     * 获取单个对话
     */
    @GetMapping("/get")
    RestResult<ConversationVO> get(@RequestParam("conversationId") String conversationId);
    
    /**
     * 聊天
     */
    @GetMapping("/chat")
    RestResult<String> chat(@RequestParam("conversationId") String conversationId, 
                            @RequestParam("message") String message);
} 