package com.ruoyi.wenshufile.service.impl;

import com.ruoyi.wenshufile.dao.FileInfoDao;
import com.ruoyi.wenshufile.service.FileInfoService;
import com.ruoyi.wenshuapi.pojo.file.FileInfoPojo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 文件信息服务实现类
 * 提供文件信息的业务逻辑具体实现
 */
@Service
public class FileInfoServiceImpl implements FileInfoService {

    // 预定义合法的文件状态值
    private static final Set<String> VALID_STATUSES = Set.of("可读写", "协作", "不可读写");
    // 预定义合法的所有者类型
    private static final Set<String> VALID_OWNER_TYPES = Set.of("个人", "团队");
    
    @Autowired
    private FileInfoDao fileInfoDao;

    /**
     * 添加文件信息
     * 
     * @param fileInfo 文件信息对象
     * @return 插入的行数
     * @throws IllegalArgumentException 如果文件名为空或包含非法字符
     */
    @Override
    @Transactional
    public int addFile(FileInfoPojo fileInfo) {
        // 参数校验
        validateFileName(fileInfo.getFileName());
        validateOwnerType(fileInfo.getOwnerType());
        validateFileStatus(fileInfo.getFileStatus());
        
        // 设置默认值
        if (fileInfo.getUploadTime() == null) {
            fileInfo.setUploadTime(java.time.LocalDateTime.now());
        }
        
        return fileInfoDao.insertFile(fileInfo);
    }

    /**
     * 删除文件信息
     * 
     * @param fileId 文件ID
     * @return 删除的行数
     * @throws IllegalStateException 如果文件状态为不可删除状态
     */
    @Override
    @Transactional
    public int deleteFile(int fileId) {
        FileInfoPojo existingFile = getFileById(fileId);
        if (existingFile == null) {
            return 0; // 文件不存在
        }
        
        // 检查文件状态是否允许删除
        if ("不可读写".equals(existingFile.getFileStatus())) {
            throw new IllegalStateException("文件处于不可读写状态，禁止删除");
        }
        
        return fileInfoDao.deleteById(fileId);
    }

    /**
     * 更新文件信息
     * 
     * @param fileInfo 包含更新数据的文件对象
     * @return 更新的行数
     * @throws IllegalArgumentException 如果文件ID无效或文件名为空
     */
    @Override
    @Transactional
    public int updateFile(FileInfoPojo fileInfo) {
        if (fileInfo.getFileId() <= 0) {
            throw new IllegalArgumentException("无效的文件ID");
        }
        
        // 如果提供了新文件名，进行校验
        if (fileInfo.getFileName() != null) {
            validateFileName(fileInfo.getFileName());
        }
        
        // 校验状态和所有者类型
        if (fileInfo.getFileStatus() != null) {
            validateFileStatus(fileInfo.getFileStatus());
        }
        if (fileInfo.getOwnerType() != null) {
            validateOwnerType(fileInfo.getOwnerType());
        }
        
        return fileInfoDao.updateFile(fileInfo);
    }

    /**
     * 根据ID获取文件详情
     * 
     * @param fileId 文件ID
     * @return 文件信息对象
     */
    @Override
    public FileInfoPojo getFileById(int fileId) {
        return fileInfoDao.selectById(fileId);
    }

    /**
     * 条件查询文件列表
     * 
     * @param condition 查询条件对象
     * @return 符合条件的文件列表
     */
    @Override
    public List<FileInfoPojo> getFilesByCondition(FileInfoPojo condition) {
        // 安全处理：限制最大查询结果数量
        // 实际项目中应添加分页支持
        List<FileInfoPojo> files = fileInfoDao.selectFilesByCondition(condition);
        
        // 限制返回结果不超过1000条
        return files.size() > 1000 ? files.subList(0, 1000) : files;
    }

    /**
     * 根据文件路径获取文件信息
     * 
     * @param filePath 文件存储路径
     * @return 文件信息对象
     * @throws IllegalArgumentException 如果文件路径为空
     */
    @Override
    public FileInfoPojo getFileByPath(String filePath) {
        if (!StringUtils.hasText(filePath)) {
            throw new IllegalArgumentException("文件路径不能为空");
        }
        return fileInfoDao.selectByPath(filePath);
    }

    /**
     * 更新文件状态
     * 
     * @param fileId 文件ID
     * @param status 新状态值
     * @return 更新的行数
     * @throws IllegalArgumentException 如果状态值不在允许范围内
     */
    @Override
    @Transactional
    public int changeFileStatus(int fileId, String status) {
        validateFileStatus(status);
        return fileInfoDao.updateFileStatus(fileId, status);
    }

    /**
     * 统计用户上传的文件数量
     * 
     * @param uploaderId 上传者ID
     * @return 文件数量
     */
    @Override
    public int countFilesByUploader(int uploaderId) {
        return fileInfoDao.countByUploader(uploaderId);
    }

    /**
     * 批量更新文件状态
     * 
     * @param fileIds 文件ID列表
     * @param status 新状态值
     * @return 成功更新的记录数
     * @throws IllegalArgumentException 如果ID列表为空或状态值无效
     */
    @Override
    @Transactional
    public int batchUpdateStatus(List<Integer> fileIds, String status) {
        if (fileIds == null || fileIds.isEmpty()) {
            throw new IllegalArgumentException("文件ID列表不能为空");
        }
        
        validateFileStatus(status);
        
        int successCount = 0;
        for (Integer fileId : fileIds) {
            try {
                int result = fileInfoDao.updateFileStatus(fileId, status);
                successCount += result;
            } catch (Exception e) {
                // 记录错误日志但继续处理其他文件
                // 实际项目中应使用日志框架记录
                System.err.println("更新文件状态失败: fileId=" + fileId + ", error: " + e.getMessage());
            }
        }
        return successCount;
    }

    /**
     * 检查文件是否属于指定用户
     * 
     * @param fileId 文件ID
     * @param userId 用户ID
     * @return 是否属于该用户
     */
    @Override
    public boolean isFileOwnedByUser(int fileId, int userId) {
        FileInfoPojo file = getFileById(fileId);
        if (file == null) {
            return false; // 文件不存在
        }
        return userId == file.getUploaderId();
    }

    // ============ 私有校验方法 ============
    
    /**
     * 验证文件名有效性
     * 
     * @param fileName 文件名
     * @throws IllegalArgumentException 如果文件名为空或包含非法字符
     */
    private void validateFileName(String fileName) {
        if (!StringUtils.hasText(fileName)) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        
        // 检查文件名是否包含非法字符
        String[] invalidChars = {"..", "/", "\\", ":", "*", "?", "\"", "<", ">", "|"};
        if (Arrays.stream(invalidChars).anyMatch(fileName::contains)) {
            throw new IllegalArgumentException("文件名包含非法字符");
        }
        
        // 检查文件名长度
        if (fileName.length() > 255) {
            throw new IllegalArgumentException("文件名长度超过255字符限制");
        }
    }

    /**
     * 验证文件状态有效性
     * 
     * @param status 文件状态
     * @throws IllegalArgumentException 如果状态值无效
     */
    private void validateFileStatus(String status) {
        if (status != null && !VALID_STATUSES.contains(status)) {
            throw new IllegalArgumentException("无效的文件状态: " + status + 
                    "，有效值为: " + VALID_STATUSES);
        }
    }

    /**
     * 验证所有者类型有效性
     * 
     * @param ownerType 所有者类型
     * @throws IllegalArgumentException 如果类型值无效
     */
    private void validateOwnerType(String ownerType) {
        if (ownerType != null && !VALID_OWNER_TYPES.contains(ownerType)) {
            throw new IllegalArgumentException("无效的所有者类型: " + ownerType + 
                    "，有效值为: " + VALID_OWNER_TYPES);
        }
    }
}