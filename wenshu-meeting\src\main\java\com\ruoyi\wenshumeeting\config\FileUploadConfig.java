package com.ruoyi.wenshumeeting.config;

import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.support.StandardServletMultipartResolver;

import jakarta.servlet.MultipartConfigElement;

/**
 * 文件上传配置类
 * 配置视频文件上传的大小限制和相关参数
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Configuration
public class FileUploadConfig {

    /**
     * 配置文件上传参数
     * 设置较大的上传限制以支持视频文件上传
     * 
     * @return MultipartConfigElement 文件上传配置元素
     */
    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        
        // 设置单个文件最大大小为10GB（视频文件通常较大）
        factory.setMaxFileSize(DataSize.ofGigabytes(10));

        // 设置整个请求最大大小为10GB
        factory.setMaxRequestSize(DataSize.ofGigabytes(10));
        
        // 设置文件写入磁盘的阈值为2KB
        factory.setFileSizeThreshold(DataSize.ofKilobytes(20));
        
        // 设置临时文件存储位置
        factory.setLocation(System.getProperty("java.io.tmpdir"));
        
        return factory.createMultipartConfig();
    }

    /**
     * 配置文件上传解析器
     * 使用标准的Servlet文件上传解析器
     * 
     * @return MultipartResolver 文件上传解析器
     */
    @Bean
    public MultipartResolver multipartResolver() {
        StandardServletMultipartResolver resolver = new StandardServletMultipartResolver();
        resolver.setResolveLazily(false); // 不延迟解析文件
        return resolver;
    }
}
