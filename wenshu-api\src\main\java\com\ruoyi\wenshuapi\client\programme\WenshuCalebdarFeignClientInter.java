package com.ruoyi.wenshuapi.client.programme;

import com.ruoyi.wenshuapi.common.ApiResponse;
import com.ruoyi.wenshuapi.pojo.programme.Programme;
import com.ruoyi.wenshuapi.pojo.vo.ProgrammeVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(value = "wenshu-calebdar",path = "/programme")
public interface WenshuCalebdarFeignClientInter {
    @PostMapping("/add/{userId}")
    public ApiResponse<?> addProgramme(@PathVariable("userId") Long userId, @RequestBody Programme programmeVO);
    /**
     * 删除日程
     * @param event_id 日程ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{event_id}")
    public ApiResponse<?> deleteProgramme(@PathVariable("event_id") int event_id);
    /*
     * 根据时间范围查询所有日程
     * @param start_time 开始时间，格式：yyyy-MM-dd HH:mm:ss
     * @param end_time 结束时间，格式：yyyy-MM-dd HH:mm:ss
     * @return 日程列表
     */
    @GetMapping("/getByTime")
    public ApiResponse<?> listProgramme(@RequestParam(required = false,value = "userId") Long userId,
                                        @RequestParam(required = false,value = "startTime") String startTime,
                                        @RequestParam(required = false,value = "endTime") String endTime);

    @GetMapping("/getById/{event_id}")
    public ApiResponse<?> getById(@PathVariable("event_id") int event_id);

    @PutMapping("/update/{event_id}")
    public ApiResponse<?> updateProgramme(@PathVariable("event_id") int event_id, @RequestBody Programme programmeVO) ;

    @GetMapping("/getAll")
    public ApiResponse<?> getAll(@RequestParam(required = false,value = "userId") Long userId) ;
}
