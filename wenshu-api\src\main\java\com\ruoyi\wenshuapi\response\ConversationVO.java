package com.ruoyi.wenshuapi.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.wenshuapi.common.MessageWrapper;
import lombok.*;

import java.util.List;

/**
 * 对话返回视图对象
 */
@Data
@AllArgsConstructor // 可选：全参构造器
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class ConversationVO {
    public ConversationVO() {
    }
    private String conversationId;

    private String title;

    private List<MessageWrapper> messages;
} 