package com.ruoyi.wenshuchat.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.wenshucommon.entity.BaseConversation;

import java.util.Optional;

/**
 * 对话数据访问服务接口
 */
public interface ConversationService extends IService<BaseConversation> {
    /**
     * 获取单个对话，使用Optional包装
     */
    Optional<BaseConversation> getOneOpt(Wrapper<BaseConversation> queryWrapper);
} 