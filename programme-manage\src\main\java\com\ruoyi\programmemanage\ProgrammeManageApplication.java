package com.ruoyi.programmemanage;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 日程管理应用程序启动类
 */
@SpringBootApplication
@EnableFeignClients(basePackages = {"com.ruoyi.programmemanage.client","com.ruoyi.wenshuapi"}, defaultConfiguration = {})
public class ProgrammeManageApplication {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static void printStartupBanner() {
        System.out.println();
        System.out.println("██╗    ██╗███████╗███╗   ██╗███████╗██╗  ██╗██╗   ██╗");
        System.out.println("██║    ██║██╔════╝████╗  ██║██╔════╝██║  ██║██║   ██║");
        System.out.println("██║ █╗ ██║█████╗  ██╔██╗ ██║███████╗███████║██║   ██║");
        System.out.println("██║███╗██║██╔══╝  ██║╚██╗██║╚════██║██╔══██║██║   ██║");
        System.out.println("╚███╔███╔╝███████╗██║ ╚████║███████║██║  ██║╚██████╔╝");
        System.out.println(" ╚══╝╚══╝ ╚══════╝╚═╝  ╚═══╝╚══════╝╚═╝  ╚═╝ ╚═════╝ ");
        System.out.println();
        System.out.println("WENSHU-PROGRAMME 日程管理服务");
        System.out.println("启动时间: " + LocalDateTime.now().format(FORMATTER));
        System.out.println("服务端口: 8605");
        System.out.println("服务功能: 日程管理与规划");
        System.out.println();
    }

    /**
     * 额外配置，禁用DashScope自动配置
     */
    @Configuration
    @ConditionalOnClass(name = "com.alibaba.cloud.ai.autoconfigure.dashscope.DashScopeAutoConfiguration")
    static class DashScopeExclusionConfig {
        // 空配置类，仅用于条件性排除DashScope自动配置
    }

    public static void main(String[] args) {
        printStartupBanner();
        System.setProperty("spring.cloud.bootstrap.location", "classpath:bootstrap.yml");
        System.setProperty("spring.config.location", "classpath:bootstrap.yml");
        SpringApplication.run(ProgrammeManageApplication.class, args);
    }
}
