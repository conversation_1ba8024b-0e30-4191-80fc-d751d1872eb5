//package com.ruoyi.wenshuwchat.init;
//
//import com.ruoyi.wenshuapi.pojo.wchat.ChatRecord;
//import com.ruoyi.wenshuapi.pojo.wchat.FriendList;
//import com.ruoyi.wenshuwchat.config.DataInitConfig;
//import com.ruoyi.wenshuwchat.service.ChatRecordService;
//import com.ruoyi.wenshuwchat.service.FriendListService;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.*;
//
///**
// * 数据初始化器测试类
// */
//@ExtendWith(MockitoExtension.class)
//class DataInitializerTest {
//
//    @Mock
//    private FriendListService friendListService;
//
//    @Mock
//    private ChatRecordService chatRecordService;
//
//    @Mock
//    private DataInitConfig dataInitConfig;
//
//    private DataInitializer dataInitializer;
//
//    @BeforeEach
//    void setUp() {
//        dataInitializer = new DataInitializer(friendListService, chatRecordService, dataInitConfig);
//    }
//
//    @Test
//    void testRunWhenDisabled() throws Exception {
//        // 配置为禁用状态
//        when(dataInitConfig.isEnabled()).thenReturn(false);
//
//        // 执行初始化
//        dataInitializer.run(null);
//
//        // 验证没有调用任何服务方法
//        verify(friendListService, never()).getAllFriends();
//        verify(friendListService, never()).addFriend(any());
//        verify(chatRecordService, never()).sendMessage(any());
//    }
//
//    @Test
//    void testRunWhenEnabledButHasExistingData() throws Exception {
//        // 配置为启用状态
//        when(dataInitConfig.isEnabled()).thenReturn(true);
//
//        // 模拟已有数据
//        List<FriendList> existingFriends = new ArrayList<>();
//        existingFriends.add(new FriendList());
//        when(friendListService.getAllFriends()).thenReturn(existingFriends);
//
//        // 执行初始化
//        dataInitializer.run(null);
//
//        // 验证只调用了检查方法，没有添加新数据
//        verify(friendListService, times(1)).getAllFriends();
//        verify(friendListService, never()).addFriend(any());
//        verify(chatRecordService, never()).sendMessage(any());
//    }
//
//    @Test
//    void testRunWhenEnabledAndNoExistingData() throws Exception {
//        // 配置为启用状态
//        when(dataInitConfig.isEnabled()).thenReturn(true);
//        when(dataInitConfig.getFriendCount()).thenReturn(5);
//        when(dataInitConfig.getChatCount()).thenReturn(10);
//        when(dataInitConfig.getMinUserId()).thenReturn(1001L);
//        when(dataInitConfig.getMaxUserId()).thenReturn(1005L);
//
//        // 模拟无现有数据
//        when(friendListService.getAllFriends()).thenReturn(new ArrayList<>());
//
//        // 模拟添加好友成功
//        when(friendListService.addFriend(any(FriendList.class))).thenReturn(1L);
//
//        // 模拟发送消息成功
//        when(chatRecordService.sendMessage(any(ChatRecord.class))).thenReturn(1L);
//
//        // 执行初始化
//        dataInitializer.run(null);
//
//        // 验证调用了相关方法
//        verify(friendListService, atLeastOnce()).getAllFriends();
//        verify(friendListService, atLeastOnce()).addFriend(any());
//        verify(chatRecordService, atLeastOnce()).sendMessage(any());
//    }
//}
