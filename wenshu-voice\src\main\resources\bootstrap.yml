server:
  port: 1014
  tomcat:
    max-http-post-size: 1048576000  # 1000MB = 1000 * 1024 * 1024 bytes
    max-swallow-size: 1048576000    # 1000MB，防止大文件上传时连接被重置
    connection-timeout: 3600000     # 连接超时时间（1小时）
    max-connections: 8192           # 最大连接数
    max-threads: 200                # 最大线程数
    accept-count: 100               # 等待队列长度

spring:
  profiles:
    active: dev
  application:
    name: wenshu-voice
  servlet:
    multipart:
      max-file-size: 1000MB      # 单个文件最大大小限制为1000MB
      max-request-size: 1000MB   # 整个请求最大大小限制为1000MB
      enabled: true              # 启用multipart文件上传
      file-size-threshold: 2KB   # 文件大小阈值，超过此值将写入磁盘
  cloud:
    nacos:
      discovery:
        server-addr: ************:8848
        namespace: public
        group: DEFAULT_GROUP
        health-check-enabled: true  # 启用健康检查
      config:
        server-addr: ************:8848
        file-extension: yaml
        namespace: public
        group: DEFAULT_GROUP
        shared-configs:
          - data-id: wenshu-voice-dev.yaml
            group: DEFAULT_GROUP
            refresh: true
    loadbalancer:
      enabled: true
      health-check:  # 负载均衡健康检查
        enabled: true
        interval: 30s
        initial-delay: 15s

  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************
    username: root
    password: **********
    hikari:
      connection-timeout: 30000
      maximum-pool-size: 20

  ai:
    dashscope:
      api-key: sk-a4b80017093447aab1688acad39d24b6
      chat:
        options:
          model: qwen-plus

feign:
  client:
    config:
      default:
        connect-timeout: 30000   # 增加连接超时时间（30秒）
        read-timeout: 3600000    # 增加读取超时时间（1小时）
        logger-level: basic
  hystrix:
    enabled: true
  compression:
    request:
      enabled: true
    response:
      enabled: true

# Hystrix熔断器配置
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 3600000  # 默认超时时间（1小时）

logging:
  level:
    org.springframework.cloud.openfeign: DEBUG
    org.springframework.cloud.loadbalancer: DEBUG