// 包声明：定义当前类所属的包路径，遵循Java包命名规范
package com.ruoyi.wenshumeeting;

// 导入Spring Boot核心启动类，用于启动Spring Boot应用程序
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
// 导入Spring Boot自动配置注解，启用自动配置功能
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
// 导入Java 8时间API中的LocalDateTime类，用于获取当前时间
import java.time.LocalDateTime;
// 导入日期时间格式化器，用于格式化时间显示
import java.time.format.DateTimeFormatter;


// 项目生成工具标识：使用Spring Boot官方脚手架生成
// Generated by https://start.springboot.io
// Spring框架中文文档参考链接
// 优质的 spring/boot/data/security/cloud 框架中文文档尽在 => https://springdoc.cn

/**
 * 文书会议系统主启动类
 * @SpringBootApplication 是一个组合注解，包含：
 * - @Configuration：标识这是一个配置类
 * - @EnableAutoConfiguration：启用Spring Boot的自动配置机制
 * - @ComponentScan：启用组件扫描，自动发现并注册Spring组件
 */
@EnableFeignClients(basePackages = {
        "com.ruoyi.wenshuapi.client"
})
@ComponentScan(basePackages = {
        "com.ruoyi.wenshuapi",
        "com.ruoyi.wenshumeeting"
})
@EnableJpaRepositories(basePackages = {
        "com.ruoyi.wenshumeeting.dao"
})
@EnableDiscoveryClient
@SpringBootApplication
public class WenshuMeetingApplication {

    /**
     * 静态常量：日期时间格式化器
     * 定义时间显示格式为：年-月-日 时:分:秒
     * 使用final确保格式化器不可变，static确保类级别共享
     */
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 打印应用启动横幅的静态方法
     * 在控制台输出ASCII艺术字和系统信息
     */
    public static void printStartupBanner() {
        // 输出空行，用于美化显示效果
        System.out.println();
        // 输出ASCII艺术字横幅 - WENSHU字样第1行
        System.out.println("██╗    ██╗███████╗███╗   ██╗███████╗██╗  ██╗██╗   ██╗");
        // 输出ASCII艺术字横幅 - WENSHU字样第2行
        System.out.println("██║    ██║██╔════╝████╗  ██║██╔════╝██║  ██║██║   ██║");
        // 输出ASCII艺术字横幅 - WENSHU字样第3行
        System.out.println("██║ █╗ ██║█████╗  ██╔██╗ ██║███████╗███████║██║   ██║");
        // 输出ASCII艺术字横幅 - WENSHU字样第4行
        System.out.println("██║███╗██║██╔══╝  ██║╚██╗██║╚════██║██╔══██║██║   ██║");
        // 输出ASCII艺术字横幅 - WENSHU字样第5行
        System.out.println("╚███╔███╔╝███████╗██║ ╚████║███████║██║  ██║╚██████╔╝");
        // 输出ASCII艺术字横幅 - WENSHU字样第6行（底部装饰）
        System.out.println(" ╚══╝╚══╝ ╚══════╝╚═╝  ╚═══╝╚══════╝╚═╝  ╚═╝ ╚═════╝ ");
        // 输出空行，用于分隔横幅和信息
        System.out.println();
        // 输出系统名称标识
        System.out.println("WENSHU-MEETING 会议管理服务");
        // 输出当前启动时间，使用预定义的格式化器格式化当前时间
        System.out.println("启动时间: " + LocalDateTime.now().format(FORMATTER));
        // 输出服务监听端口信息
        System.out.println("服务端口: 8080");
        // 输出服务功能描述
        System.out.println("服务功能: 在线会议管理");
        // 输出空行，用于美化显示效果
        System.out.println();
    }

    /**
     * 应用程序主入口方法
     * JVM启动时会首先调用此方法
     * @param args 命令行参数数组，用于接收启动时传入的参数
     */
    public static void main(String[] args) {
        // 调用自定义方法打印启动横幅，提升用户体验
        printStartupBanner();
        // 启动Spring Boot应用程序
        // SpringApplication.run()方法会：
        // 1. 创建Spring应用上下文
        // 2. 启动嵌入式Tomcat服务器
        // 3. 初始化所有Spring Bean
        // 4. 执行自动配置
        SpringApplication.run(WenshuMeetingApplication.class, args);
    }

}
