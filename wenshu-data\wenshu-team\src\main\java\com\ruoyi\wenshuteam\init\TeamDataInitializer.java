//package com.ruoyi.wenshuteam.init;
//
//import com.ruoyi.wenshuapi.pojo.team.TeamInfo;
//import com.ruoyi.wenshuteam.service.TeamInfoService;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.stereotype.Component;
//
//import java.time.LocalDateTime;
//import java.util.Random;
//import java.util.concurrent.atomic.AtomicInteger;
//
///**
// * 团队数据初始化器
// * 在应用启动时自动向数据库填充100条测试数据
// */
//@Component
//public class TeamDataInitializer implements CommandLineRunner {
//
//    private final TeamInfoService teamInfoService;
//
//    // 团队名称前缀
//    private static final String[] TEAM_PREFIXES = {"研发", "产品", "设计", "测试", "运维", "市场", "销售", "客服", "财务", "人事"};
//    private static final String[] TEAM_SUFFIXES = {"一组", "二组", "三组", "四组", "五组", "中心", "团队", "部门", "小组", "分队"};
//
//    // 团队描述模板
//    private static final String[] DESCRIPTIONS = {
//        "专注于技术研发和创新",
//        "负责产品设计和用户体验",
//        "提供高质量的设计方案",
//        "确保产品质量和稳定性",
//        "维护系统稳定运行",
//        "开拓市场推广产品",
//        "负责产品销售和客户关系",
//        "提供优质的客户服务",
//        "管理公司财务和预算",
//        "负责人才招聘和团队建设"
//    };
//
//    private final Random random = new Random();
//    private final AtomicInteger counter = new AtomicInteger(1);
//
//    public TeamDataInitializer(TeamInfoService teamInfoService) {
//        this.teamInfoService = teamInfoService;
//    }
//
//    @Override
//    public void run(String... args) {
//        // 检查是否已有数据
//        if (!teamInfoService.getAllTeams().isEmpty()) {
//            return; // 如果已有数据，则不初始化
//        }
//
//        // 创建100个测试团队
//        for (int i = 0; i < 100; i++) {
//            TeamInfo team = createRandomTeam();
//            try {
//                teamInfoService.createTeam(team);
//            } catch (Exception e) {
//                // 忽略可能的重复名称异常
//            }
//        }
//    }
//
//    /**
//     * 创建随机团队信息
//     */
//    private TeamInfo createRandomTeam() {
//        TeamInfo team = new TeamInfo();
//
//        // 生成团队名称
//        String prefix = TEAM_PREFIXES[random.nextInt(TEAM_PREFIXES.length)];
//        String suffix = TEAM_SUFFIXES[random.nextInt(TEAM_SUFFIXES.length)];
//        team.setTeamName(prefix + suffix + counter.getAndIncrement());
//
//        // 随机生成负责人和创建者ID (1-100)
//        team.setLeaderId(random.nextInt(100) + 1);
//        team.setCreatorId(random.nextInt(100) + 1);
//
//        // 设置创建时间为过去365天内的随机时间
//        int daysAgo = random.nextInt(365);
//        team.setCreateTime(LocalDateTime.now().minusDays(daysAgo));
//
//        // 随机选择描述
//        team.setDescription(DESCRIPTIONS[random.nextInt(DESCRIPTIONS.length)]);
//
//        // 随机状态 (80%启用，20%禁用)
//        team.setStatus((byte) (random.nextFloat() < 0.8 ? 1 : 0));
//
//        return team;
//    }
//}