package com.ruoyi.wenshuapi.pojo.wchat;

import java.time.LocalDateTime;

/**
 * 聊天记录实体类
 * 对应数据库表：wenshu-chat_records
 */
public class ChatRecord {
    private Long id;                    // 消息ID
    private String sessionId;           // 会话ID(MD5哈希: LEAST(user1,user2)-GREATEST(user1,user2))
    private Long senderId;              // 发送人ID
    private Long receiverId;            // 接收人ID
    private LocalDateTime sendTime;     // 发送时间(毫秒精度)
    private String content;             // 消息内容
    private String contentType;         // 消息类型: text, image, video, file
    private Boolean isRead;             // 是否已读(0未读,1已读)

    // 无参构造器
    public ChatRecord() {
    }

    // 全参构造器
    public ChatRecord(Long id, String sessionId, Long senderId, Long receiverId,
                     LocalDateTime sendTime, String content, String contentType, Boolean isRead) {
        this.id = id;
        this.sessionId = sessionId;
        this.senderId = senderId;
        this.receiverId = receiverId;
        this.sendTime = sendTime;
        this.content = content;
        this.contentType = contentType;
        this.isRead = isRead;
    }

    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Long getSenderId() {
        return senderId;
    }

    public void setSenderId(Long senderId) {
        this.senderId = senderId;
    }

    public Long getReceiverId() {
        return receiverId;
    }

    public void setReceiverId(Long receiverId) {
        this.receiverId = receiverId;
    }

    public LocalDateTime getSendTime() {
        return sendTime;
    }

    public void setSendTime(LocalDateTime sendTime) {
        this.sendTime = sendTime;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public Boolean getIsRead() {
        return isRead;
    }

    public void setIsRead(Boolean isRead) {
        this.isRead = isRead;
    }

    @Override
    public String toString() {
        return "ChatRecord{" +
                "id=" + id +
                ", sessionId='" + sessionId + '\'' +
                ", senderId=" + senderId +
                ", receiverId=" + receiverId +
                ", sendTime=" + sendTime +
                ", content='" + content + '\'' +
                ", contentType='" + contentType + '\'' +
                ", isRead=" + isRead +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ChatRecord that = (ChatRecord) o;

        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (sessionId != null ? !sessionId.equals(that.sessionId) : that.sessionId != null) return false;
        if (senderId != null ? !senderId.equals(that.senderId) : that.senderId != null) return false;
        return receiverId != null ? receiverId.equals(that.receiverId) : that.receiverId == null;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (sessionId != null ? sessionId.hashCode() : 0);
        result = 31 * result + (senderId != null ? senderId.hashCode() : 0);
        result = 31 * result + (receiverId != null ? receiverId.hashCode() : 0);
        return result;
    }
}
