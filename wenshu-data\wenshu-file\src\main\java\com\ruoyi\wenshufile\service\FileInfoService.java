package com.ruoyi.wenshufile.service;

import com.ruoyi.wenshuapi.pojo.file.FileInfoPojo;
import java.util.List;

/**
 * 文件信息服务接口
 * 提供文件信息的业务逻辑操作，包括文件的增删改查及状态管理等
 */
public interface FileInfoService {

    /**
     * 添加文件信息
     * 将新文件信息持久化到数据库
     * 
     * @param fileInfo 文件信息对象
     * @return 插入操作影响的行数（通常为1）
     * @throws IllegalArgumentException 如果文件名为空或包含非法字符
     */
    int addFile(FileInfoPojo fileInfo);

    /**
     * 删除文件信息
     * 根据文件ID物理删除文件记录
     * 
     * @param fileId 文件ID
     * @return 删除操作影响的行数（1表示成功，0表示记录不存在）
     * @throws IllegalStateException 如果文件状态为不可删除状态
     */
    int deleteFile(int fileId);

    /**
     * 更新文件信息
     * 根据文件ID更新文件信息，仅更新非空字段
     * 
     * @param fileInfo 包含更新数据的文件对象
     * @return 更新操作影响的行数（1表示成功，0表示记录不存在）
     * @throws IllegalArgumentException 如果文件ID无效或文件名为空
     */
    int updateFile(FileInfoPojo fileInfo);

    /**
     * 根据ID获取文件详情
     * 
     * @param fileId 文件ID
     * @return 文件信息对象，如果不存在返回null
     */
    FileInfoPojo getFileById(int fileId);

    /**
     * 条件查询文件列表
     * 支持按文件名（模糊匹配）、上传者ID、文件状态和所有者类型组合查询
     * 
     * @param condition 查询条件对象（非空字段作为查询条件）
     * @return 符合条件的文件信息列表，无结果时返回空列表
     */
    List<FileInfoPojo> getFilesByCondition(FileInfoPojo condition);

    /**
     * 根据文件路径获取文件信息
     * 
     * @param filePath 文件存储路径
     * @return 文件信息对象，如果不存在返回null
     * @throws IllegalArgumentException 如果文件路径为空
     */
    FileInfoPojo getFileByPath(String filePath);

    /**
     * 更新文件状态
     * 
     * @param fileId 文件ID
     * @param status 新状态值（可读写/协作/不可读写）
     * @return 更新操作影响的行数（1表示成功，0表示记录不存在）
     * @throws IllegalArgumentException 如果状态值不在允许范围内
     */
    int changeFileStatus(int fileId, String status);

    /**
     * 统计用户上传的文件数量
     * 
     * @param uploaderId 上传者ID
     * @return 该用户上传的文件总数
     */
    int countFilesByUploader(int uploaderId);

    /**
     * 批量更新文件状态
     * 
     * @param fileIds 文件ID列表
     * @param status 新状态值
     * @return 成功更新的记录数
     * @throws IllegalArgumentException 如果ID列表为空或状态值无效
     */
    int batchUpdateStatus(List<Integer> fileIds, String status);

    /**
     * 检查文件是否属于指定用户
     * 
     * @param fileId 文件ID
     * @param userId 用户ID
     * @return true表示文件属于该用户，false表示不属于或文件不存在
     */
    boolean isFileOwnedByUser(int fileId, int userId);
}