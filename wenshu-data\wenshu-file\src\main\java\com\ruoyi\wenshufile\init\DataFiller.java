//package com.ruoyi.wenshufile.util;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.jdbc.core.JdbcTemplate;
//import org.springframework.stereotype.Component;
//import javax.annotation.PostConstruct;
//import java.util.Random;
//
///**
// * 数据库填充工具 - 为wenshu_file_participant表生成100条测试数据
// * 说明：需要确保关联的文件ID（1-50）、用户ID（1-20）、团队ID（1-10）在各自表中存在
// */
//@Component
//public class DataFiller {
//
//    private final JdbcTemplate jdbcTemplate;
//    private final Random random = new Random();
//
//    @Autowired
//    public DataFiller(JdbcTemplate jdbcTemplate) {
//        this.jdbcTemplate = jdbcTemplate;
//    }
//
//    @PostConstruct
//    public void fillData() {
//        // 清空现有测试数据（可选）
//        jdbcTemplate.execute("DELETE FROM wenshu_file_participant");
//
//        // 生成100条测试数据
//        for (int i = 0; i < 100; i++) {
//            int fileId = random.nextInt(50) + 1;     // 文件ID范围：1-50
//            int userId = random.nextInt(20) + 1;      // 用户ID范围：1-20
//            int teamId = random.nextInt(10) + 1;      // 团队ID范围：1-10
//
//            // 防止重复主键（实际业务中复合主键需唯一）
//            if (!isDuplicate(fileId, userId, teamId)) {
//                insertRecord(fileId, userId, teamId);
//            } else {
//                i--;  // 遇到重复则重试
//            }
//        }
//        System.out.println("成功生成100条文件-用户-团队关联数据");
//    }
//
//    private boolean isDuplicate(int fileId, int userId, int teamId) {
//        String sql = "SELECT COUNT(*) FROM wenshu_file_participant WHERE file_id = ? AND user_id = ? AND team_id = ?";
//        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, fileId, userId, teamId);
//        return count != null && count > 0;
//    }
//
//    private void insertRecord(int fileId, int userId, int teamId) {
//        String sql = "INSERT INTO wenshu_file_participant (file_id, user_id, team_id) VALUES (?, ?, ?)";
//        jdbcTemplate.update(sql, fileId, userId, teamId);
//    }
//}