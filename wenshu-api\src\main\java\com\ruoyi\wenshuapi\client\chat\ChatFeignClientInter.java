package com.ruoyi.wenshuapi.client.chat;

import com.ruoyi.wenshuapi.pojo.chat.RequestBodyDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@FeignClient(
        name = "wenshu-chat",
        contextId = "chatFeignClientInter",
        path = "/api/chat"
)
public interface ChatFeignClientInter {

    @PostMapping("/send")
    ResponseEntity<String> sendMessage(@RequestBody RequestBodyDTO requestBodyDTO);

    @PostMapping("/get-chat-response")
        // 修改路径避免冲突
    ResponseEntity<String> getChatResponse(@RequestBody RequestBodyDTO requestBodyDTO);

    @PostMapping("/send-easy")
    String sendMessageEasy(@RequestBody RequestBodyDTO requestBodyDTO);

    @PostMapping("/send-easy-kowlage")
    String sendMessageEasyMemory(@RequestBody RequestBodyDTO requestBodyDTO);
    @PostMapping("/docment-parse-send")
    public String sendDocmentParseSend(@RequestBody RequestBodyDTO requestBodyDTO);
    @PostMapping("/docment-send")
    public String sendDocmentSend(@RequestBody RequestBodyDTO requestBodyDTO);
}