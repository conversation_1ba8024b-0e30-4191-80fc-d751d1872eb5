package com.ruoyi.wenshuteam.service.impl;

import com.ruoyi.wenshuapi.pojo.team.TeamInfo;
import com.ruoyi.wenshuteam.dao.TeamInfoDao;
import com.ruoyi.wenshuteam.service.TeamInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 团队信息服务实现类
 */
@Service
public class TeamInfoServiceImpl implements TeamInfoService {

    private final TeamInfoDao teamInfoDao;

    @Autowired
    public TeamInfoServiceImpl(TeamInfoDao teamInfoDao) {
        this.teamInfoDao = teamInfoDao;
    }

    @Override
    @Transactional
    public int createTeam(TeamInfo teamInfo) {
        // 创建前校验团队名称是否已存在
        if (teamInfoDao.selectByTeamName(teamInfo.getTeamName()).size() > 0) {
            throw new IllegalArgumentException("团队名称已存在");
        }
        
        // 设置默认状态为启用（1）
        if (teamInfo.getStatus() == null) {
            teamInfo.setStatus((byte) 1);
        }
        
        // 执行创建操作
        int result = teamInfoDao.insertTeam(teamInfo);
        if (result == 0) {
            throw new RuntimeException("创建团队失败");
        }
        return teamInfo.getTeamId();  // 返回新创建的团队ID
    }

    @Override
    @Transactional
    public int deleteTeam(Integer teamId) {
        // 检查团队是否存在
        TeamInfo existingTeam = teamInfoDao.selectByTeamId(teamId);
        if (existingTeam == null) {
            throw new IllegalArgumentException("团队不存在");
        }
        
        // 检查团队状态（可选：只有禁用状态才能删除）
        if (existingTeam.getStatus() == 1) {
            throw new IllegalStateException("启用状态的团队不能删除");
        }
        
        return teamInfoDao.deleteByTeamId(teamId);
    }

    @Override
    @Transactional
    public int updateTeam(TeamInfo teamInfo) {
        // 检查团队是否存在
        TeamInfo existingTeam = teamInfoDao.selectByTeamId(teamInfo.getTeamId());
        if (existingTeam == null) {
            throw new IllegalArgumentException("团队不存在");
        }
        
        // 如果修改了团队名称，检查新名称是否已存在
        if (!existingTeam.getTeamName().equals(teamInfo.getTeamName())) {
            if (teamInfoDao.selectByTeamName(teamInfo.getTeamName()).size() > 0) {
                throw new IllegalArgumentException("团队名称已存在");
            }
        }
        
        return teamInfoDao.updateTeam(teamInfo);
    }

    @Override
    public TeamInfo getTeamById(Integer teamId) {
        TeamInfo team = teamInfoDao.selectByTeamId(teamId);
        if (team == null) {
            throw new IllegalArgumentException("团队不存在");
        }
        return team;
    }

    @Override
    public List<TeamInfo> getAllTeams() {
        return teamInfoDao.selectAllTeams();
    }

    @Override
    public List<TeamInfo> getTeamsByStatus(Byte status) {
        // 验证状态值有效性
        if (status != 0 && status != 1) {
            throw new IllegalArgumentException("无效的状态值");
        }
        return teamInfoDao.selectByStatus(status);
    }

    @Override
    public List<TeamInfo> searchTeamsByName(String teamName) {
        // 验证搜索关键词
        if (teamName == null || teamName.trim().isEmpty()) {
            throw new IllegalArgumentException("搜索关键词不能为空");
        }
        return teamInfoDao.selectByTeamName(teamName.trim());
    }
}