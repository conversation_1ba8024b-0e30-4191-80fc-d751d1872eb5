//package com.ruoyi.wenshuvoice.controller;

import com.ruoyi.wenshuapi.client.chat.ChatFeignClientInter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//@RestController
//@RequestMapping("/testchat")
//public class TestController {
//    @Autowired
//    private ChatFeignClientInter chatClient;
//    @GetMapping("/chats")
//    public ResponseEntity<String> chat(@RequestParam String message) {
//        return chatClient.getChatResponse(message);
//    }
//}
