package com.ruoyi.wenshulivechat.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 实时聊天配置属性类
 * 映射bootstrap.yml中的livechat配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "livechat")
public class LiveChatProperties {

    /**
     * WebSocket配置
     */
    private WebSocket websocket = new WebSocket();

    /**
     * 用户会话配置
     */
    private Session session = new Session();

    /**
     * 心跳配置
     */
    private Heartbeat heartbeat = new Heartbeat();

    /**
     * 消息路由配置
     */
    private Routing routing = new Routing();

    @Data
    public static class WebSocket {
        /**
         * 消息大小限制（KB）
         */
        private Integer messageSizeLimit = 64;

        /**
         * 发送缓冲区大小限制（KB）
         */
        private Integer sendBufferSizeLimit = 512;

        /**
         * 发送超时时间（秒）
         */
        private Integer sendTimeout = 15;

        /**
         * WebSocket端点路径
         */
        private String endpoint = "/ws";

        /**
         * 允许的跨域来源
         */
        private String allowedOrigins = "*";
    }

    @Data
    public static class Session {
        /**
         * 用户离线超时时间（分钟）
         */
        private Integer offlineTimeout = 5;

        /**
         * 清理任务执行间隔（分钟）
         */
        private Integer cleanupInterval = 5;
    }

    @Data
    public static class Heartbeat {
        /**
         * 心跳间隔（秒）
         */
        private Integer interval = 30;

        /**
         * 心跳超时（秒）
         */
        private Integer timeout = 60;
    }

    @Data
    public static class Routing {
        /**
         * 接收消息前缀
         */
        private String recvPrefix = "/recv";

        /**
         * 发送消息前缀
         */
        private String sendPrefix = "/send";

        /**
         * 系统通知前缀
         */
        private String systemPrefix = "/system";
    }
}
