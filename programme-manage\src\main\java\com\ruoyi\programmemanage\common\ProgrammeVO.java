package com.ruoyi.programmemanage.common;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 日程视图对象
 */
@Data
public class ProgrammeVO {
    /**
     * 日程ID
     */
    private Integer eventId;
    
    /**
     * 标题
     */
    private String title;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 创建者ID
     */
    private Integer creatorId;
    
    /**
     * 所有者类型
     */
    private String ownerType;
    
    /**
     * 重复规则
     */
    private String repeatRule;
    
    /**
     * 提醒时间
     */
    private LocalDateTime remindTime;
    
    /**
     * 日程状态
     */
    private Integer eventStatus = 1; // 默认为正常状态
} 