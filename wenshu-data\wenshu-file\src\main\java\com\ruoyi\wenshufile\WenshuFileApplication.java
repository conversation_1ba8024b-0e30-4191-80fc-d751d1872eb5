package com.ruoyi.wenshufile;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;


// Generated by https://start.springboot.io
// 优质的 spring/boot/data/security/cloud 框架中文文档尽在 => https://springdoc.cn
@SpringBootApplication
@EnableDiscoveryClient
public class WenshuFileApplication {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static void printStartupBanner() {
        System.out.println();
        System.out.println("██╗    ██╗███████╗███╗   ██╗███████╗██╗  ██╗██╗   ██╗");
        System.out.println("██║    ██║██╔════╝████╗  ██║██╔════╝██║  ██║██║   ██║");
        System.out.println("██║ █╗ ██║█████╗  ██╔██╗ ██║███████╗███████║██║   ██║");
        System.out.println("██║███╗██║██╔══╝  ██║╚██╗██║╚════██║██╔══██║██║   ██║");
        System.out.println("╚███╔███╔╝███████╗██║ ╚████║███████║██║  ██║╚██████╔╝");
        System.out.println(" ╚══╝╚══╝ ╚══════╝╚═╝  ╚═══╝╚══════╝╚═╝  ╚═╝ ╚═════╝ ");
        System.out.println();
        System.out.println("WENSHU-FILE 文件服务");
        System.out.println("启动时间: " + LocalDateTime.now().format(FORMATTER));
        System.out.println("服务端口: 1016");
        System.out.println("服务功能: 文件存储管理");
        System.out.println();
    }

    public static void main(String[] args) {
        printStartupBanner();
        System.setProperty("spring.cloud.bootstrap.location", "classpath:bootstrap.yml");
        System.setProperty("spring.config.location", "classpath:bootstrap.yml");
        SpringApplication.run(WenshuFileApplication.class, args);
    }

}
