package com.ruoyi.wenshulivechat.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.converter.MappingJackson2MessageConverter;
import org.springframework.messaging.converter.MessageConverter;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * WebSocket配置类
 * 配置STOMP协议的WebSocket消息代理
 *
 * 功能特性：
 * - 配置WebSocket端点
 * - 配置消息代理
 * - 配置跨域支持
 * - 配置消息路由前缀
 * - 配置消息转换器（支持LocalDateTime序列化）
 */
@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    @Value("${livechat.websocket.endpoint:/ws}")
    private String websocketEndpoint;

    @Value("${livechat.websocket.allowed-origins:*}")
    private String allowedOrigins;

    @Value("${livechat.routing.recv-prefix:/recv}")
    private String recvPrefix;

    @Value("${livechat.routing.send-prefix:/send}")
    private String sendPrefix;

    @Value("${livechat.routing.system-prefix:/system}")
    private String systemPrefix;

    /**
     * 配置消息代理
     * 设置消息路由前缀和应用程序目标前缀
     */
    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        // 启用简单消息代理，用于向客户端发送消息
        // 客户端订阅以下前缀的目的地来接收消息
        config.enableSimpleBroker(recvPrefix, systemPrefix);
        
        // 设置应用程序目标前缀
        // 客户端发送消息到以下前缀的目的地
        config.setApplicationDestinationPrefixes(sendPrefix);
        
        // 设置用户目标前缀（用于点对点消息）
        config.setUserDestinationPrefix("/user");
    }

    /**
     * 注册STOMP端点
     * 配置WebSocket连接端点和跨域支持
     */
    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        // 注册WebSocket端点，支持SockJS
        registry.addEndpoint(websocketEndpoint)
                .setAllowedOriginPatterns(allowedOrigins)
                .withSockJS();
        
        // 注册原生WebSocket端点（不使用SockJS）
        registry.addEndpoint(websocketEndpoint + "-native")
                .setAllowedOriginPatterns(allowedOrigins);
    }

    /**
     * 配置ObjectMapper，支持Java 8日期时间类型
     * 设置LocalDateTime的序列化格式为 yyyy-MM-dd HH:mm:ss
     */
    @Bean(name = "webSocketObjectMapper")
    public ObjectMapper webSocketObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        // 创建自定义的JavaTimeModule，设置LocalDateTime的序列化格式
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(formatter));

        objectMapper.registerModule(javaTimeModule);
        // 禁用将日期写成时间戳
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        return objectMapper;
    }

    /**
     * 配置WebSocket消息转换器
     * 使用自定义的ObjectMapper来处理消息序列化
     */
    @Bean
    public MappingJackson2MessageConverter mappingJackson2MessageConverter() {
        MappingJackson2MessageConverter converter = new MappingJackson2MessageConverter();
        converter.setObjectMapper(webSocketObjectMapper());
        return converter;
    }

    /**
     * 配置消息转换器列表
     * 将自定义的消息转换器添加到WebSocket配置中
     * 主要用于响应消息的序列化（支持LocalDateTime）
     */
    @Override
    public boolean configureMessageConverters(List<MessageConverter> messageConverters) {
        // 只添加我们的自定义转换器用于响应消息序列化
        messageConverters.add(mappingJackson2MessageConverter());
        return true; // 返回true表示保留默认转换器
    }
}
