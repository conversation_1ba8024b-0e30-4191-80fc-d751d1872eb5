package com.ruoyi.job;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.ruoyi.common.security.annotation.EnableCustomConfig;
import com.ruoyi.common.security.annotation.EnableRyFeignClients;
import com.ruoyi.common.swagger.annotation.EnableCustomSwagger2;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 定时任务
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication
public class RuoYiJobApplication
{
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static void printStartupBanner() {
        System.out.println();
        System.out.println("██╗    ██╗███████╗███╗   ██╗███████╗██╗  ██╗██╗   ██╗");
        System.out.println("██║    ██║██╔════╝████╗  ██║██╔════╝██║  ██║██║   ██║");
        System.out.println("██║ █╗ ██║█████╗  ██╔██╗ ██║███████╗███████║██║   ██║");
        System.out.println("██║███╗██║██╔══╝  ██║╚██╗██║╚════██║██╔══██║██║   ██║");
        System.out.println("╚███╔███╔╝███████╗██║ ╚████║███████║██║  ██║╚██████╔╝");
        System.out.println(" ╚══╝╚══╝ ╚══════╝╚═╝  ╚═══╝╚══════╝╚═╝  ╚═╝ ╚═════╝ ");
        System.out.println();
        System.out.println("WENSHU-JOB 定时任务模块");
        System.out.println("启动时间: " + LocalDateTime.now().format(FORMATTER));
        System.out.println("服务端口: 9203");
        System.out.println("服务功能: 定时任务管理");
        System.out.println();
    }

    public static void main(String[] args)
    {
        printStartupBanner();
        SpringApplication.run(RuoYiJobApplication.class, args);
    }
}
