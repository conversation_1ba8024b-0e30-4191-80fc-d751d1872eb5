package com.ruoyi.wenshuremind.pojo;


import java.time.LocalDateTime;

public class sedan {
            private  int event_id;
            private String title;
            private String description;
            private LocalDateTime create_time;
            private LocalDateTime start_time;
            private int  creator_id;
            private String owner_type;
            private String repeat_rule;
            private int remind_time;
            private int event_status;

    public sedan(int event_id, String title, String description, LocalDateTime create_time, LocalDateTime start_time, int creator_id, String owner_type, String repeat_rule, int remind_time, int event_status) {
        this.event_id = event_id;
        this.title = title;
        this.description = description;
        this.create_time = create_time;
        this.start_time = start_time;
        this.creator_id = creator_id;
        this.owner_type = owner_type;
        this.repeat_rule = repeat_rule;
        this.remind_time = remind_time;
        this.event_status = event_status;
    }

    public int getEvent_id() {
        return event_id;
    }

    public void setEvent_id(int event_id) {
        this.event_id = event_id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreate_time() {
        return create_time;
    }

    public void setCreate_time(LocalDateTime create_time) {
        this.create_time = create_time;
    }

    public LocalDateTime getStart_time() {
        return start_time;
    }

    public void setStart_time(LocalDateTime start_time) {
        this.start_time = start_time;
    }

    public int getCreator_id() {
        return creator_id;
    }

    public void setCreator_id(int creator_id) {
        this.creator_id = creator_id;
    }

    public String getOwner_type() {
        return owner_type;
    }

    public void setOwner_type(String owner_type) {
        this.owner_type = owner_type;
    }

    public String getRepeat_rule() {
        return repeat_rule;
    }

    public void setRepeat_rule(String repeat_rule) {
        this.repeat_rule = repeat_rule;
    }

    public int getRemind_time() {
        return remind_time;
    }

    public void setRemind_time(int remind_time) {
        this.remind_time = remind_time;
    }

    public int getEvent_status() {
        return event_status;
    }

    public void setEvent_status(int event_status) {
        this.event_status = event_status;
    }
}
