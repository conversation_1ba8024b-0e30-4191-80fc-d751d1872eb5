# 文书智能计算 - 多模态服务

该服务提供文档处理、多模态数据处理等功能。

## Docker部署说明

### 单独构建与运行

1. 构建Docker镜像:

```bash
# 在项目根目录下执行
docker build -f wenshu-multimodal/Dockerfile -t wenshu-multimodal:latest .
```

2. 运行容器:

```bash
docker run -d --name wenshu-multimodal \
  -p 8702:8702 \
  -e SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR=nacos:8848 \
  -e SPRING_CLOUD_NACOS_CONFIG_SERVER-ADDR=nacos:8848 \
  -e SPRING_DATASOURCE_URL=***************************************************************************************************** \
  -e SPRING_DATASOURCE_USERNAME=root \
  -e SPRING_DATASOURCE_PASSWORD=2313147023 \
  -v ./onlyoffice-files:/app/onlyoffice-files \
  wenshu-multimodal:latest
```

### 使用docker-compose部署

1. 在项目根目录下运行:

```bash
docker-compose up -d
```

这将启动以下服务:
- MySQL数据库
- Nacos服务发现和配置中心
- wenshu-multimodal服务

2. 查看日志:

```bash
docker-compose logs -f wenshu-multimodal
```

## 配置说明

主要配置位于`bootstrap.yml`文件中，可通过环境变量进行覆盖:

- `SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR`: Nacos服务地址
- `SPRING_DATASOURCE_URL`: MySQL数据库URL
- `SPRING_DATASOURCE_USERNAME`: 数据库用户名
- `SPRING_DATASOURCE_PASSWORD`: 数据库密码
- `ONLYOFFICE_DOCUMENT_SERVER_URL`: OnlyOffice文档服务器地址
- `ONLYOFFICE_CALLBACK_URL`: 回调URL

## 端口

- 8702: 服务HTTP端口

## 存储卷

- `/app/onlyoffice-files`: OnlyOffice文档存储路径 