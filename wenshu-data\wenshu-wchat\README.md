# wenshu-wchat 模块优化说明

## 修改概述

本次对 wenshu-wchat 模块进行了以下优化：

### 1. 显式声明 @PathVariable 参数

将所有控制器中的 `@PathVariable` 注解改为显式声明参数名，提高代码的可读性和维护性。

**修改前：**
```java
@PathVariable String sessionId
```

**修改后：**
```java
@PathVariable("sessionId") String sessionId
```

### 2. 创建文件存储服务

新增了专门的聊天文件存储服务，支持图片、视频、文件等多媒体内容的管理。

**新增文件：**
- `ChatFileService.java` - 文件存储服务接口
- `ChatFileServiceImpl.java` - 文件存储服务实现
- `ChatFileConfig.java` - 文件访问配置

**功能特性：**
- 支持按内容类型分类存储（image/video/file/document/archive等）
- 按日期目录结构组织文件
- 支持所有文件类型上传（移除文件类型限制）
- 文件大小限制（最大100MB）
- 唯一文件名生成
- 安全的文件扩展名验证

### 3. 添加文件上传接口

在 `ChatRecordController` 中新增文件上传接口：

```
POST /wenshu/wchat/chat/upload
```

**参数：**
- `file`: 上传的文件
- `contentType`: 内容类型（image/video/file）

**响应：**
```json
{
  "success": true,
  "message": "文件上传成功",
  "data": {
    "filePath": "image/2024/01/15/timestamp_uuid.jpg",
    "accessUrl": "http://localhost:8083/chat-files/image/2024/01/15/timestamp_uuid.jpg",
    "fileName": "original.jpg",
    "fileSize": 1024,
    "contentType": "image"
  }
}
```

### 4. 优化 sendMessage 方法

增强了消息发送方法，支持不同类型的消息内容验证：

- **text**: 验证文本内容不为空
- **image/video/file**: 验证文件路径格式正确

**文件路径格式：** `contentType/年/月/日/文件名`

### 5. 添加文件访问接口

新增了文件访问和管理接口：

#### 获取文件信息
```
GET /wenshu/wchat/chat/file/info?path=image/2024/01/15/filename.jpg
```

#### 下载文件
```
GET /wenshu/wchat/chat/file/download?path=image/2024/01/15/filename.jpg
```

#### 删除文件
```
DELETE /wenshu/wchat/chat/file?path=image/2024/01/15/filename.jpg
```

## 配置说明

### application.yml 配置

```yaml
# 聊天文件配置
chat:
  file:
    # 文件存储根路径
    path: D:/wenshu/chat-files
    # 文件访问域名
    domain: http://localhost:8083
    # 文件访问前缀
    prefix: /chat-files
```

### 文件存储结构

```
D:/wenshu/chat-files/
├── image/
│   └── 2024/
│       └── 01/
│           └── 15/
│               └── timestamp_uuid.jpg
├── video/
│   └── 2024/
│       └── 01/
│           └── 15/
│               └── timestamp_uuid.mp4
└── file/
    └── 2024/
        └── 01/
            └── 15/
                └── timestamp_uuid.pdf
```

## 支持的文件类型

### 图片 (image)
- jpg, jpeg, png, gif, bmp, webp

### 视频 (video)
- mp4, avi, mov, wmv, flv, webm, mkv

### 文件 (file)
- pdf, doc, docx, xls, xlsx, ppt, pptx, txt, zip, rar, 7z

## API接口说明

### 统一发送消息接口

```
POST /wenshu/wchat/chat/send
Content-Type: multipart/form-data
```

**参数说明：**
- `senderId` (必填): 发送者ID
- `receiverId` (必填): 接收者ID
- `contentType` (必填): 内容类型 (text/image/video/file)
- `textContent` (可选): 文本内容（contentType为text时必填）
- `file` (可选): 文件（contentType为image/video/file时必填）

**注意：** 每次发送消息只能包含一种类型的内容，不可同时发送文本和文件。

### 分类发送接口

#### 发送文本消息
```
POST /wenshu/wchat/chat/send/text
Content-Type: application/x-www-form-urlencoded

senderId=1001&receiverId=1002&content=Hello, World!
```

#### 发送图片消息
```
POST /wenshu/wchat/chat/send/image
Content-Type: multipart/form-data

senderId: 1001
receiverId: 1002
file: [图片文件]
```

#### 发送视频消息
```
POST /wenshu/wchat/chat/send/video
Content-Type: multipart/form-data

senderId: 1001
receiverId: 1002
file: [视频文件]
```

#### 发送文件消息
```
POST /wenshu/wchat/chat/send/file
Content-Type: multipart/form-data

senderId: 1001
receiverId: 1002
file: [文档文件]
```

## 使用示例

### 发送文本消息
```bash
curl -X POST "http://localhost:8083/wenshu/wchat/chat/send" \
  -F "senderId=1001" \
  -F "receiverId=1002" \
  -F "contentType=text" \
  -F "textContent=Hello, World!"
```

### 发送图片消息
```bash
curl -X POST "http://localhost:8083/wenshu/wchat/chat/send" \
  -F "senderId=1001" \
  -F "receiverId=1002" \
  -F "contentType=image" \
  -F "file=@/path/to/image.jpg"
```

### 发送视频消息
```bash
curl -X POST "http://localhost:8083/wenshu/wchat/chat/send" \
  -F "senderId=1001" \
  -F "receiverId=1002" \
  -F "contentType=video" \
  -F "file=@/path/to/video.mp4"
```

### 发送文件消息
```bash
curl -X POST "http://localhost:8083/wenshu/wchat/chat/send" \
  -F "senderId=1001" \
  -F "receiverId=1002" \
  -F "contentType=file" \
  -F "file=@/path/to/document.pdf"
```

## 响应示例

### 文本消息响应
```json
{
  "success": true,
  "message": "文本消息发送成功",
  "data": {
    "messageId": 123,
    "contentType": "text",
    "content": "Hello, World!"
  }
}
```

### 文件消息响应
```json
{
  "success": true,
  "message": "image消息发送成功",
  "data": {
    "messageId": 124,
    "contentType": "image",
    "filePath": "image/2024/01/15/1705123456789_abc123.jpg",
    "accessUrl": "http://localhost:8083/chat-files/image/2024/01/15/1705123456789_abc123.jpg",
    "fileName": "photo.jpg",
    "fileSize": 1024000
  }
}
```

### 错误响应
```json
{
  "success": false,
  "message": "不支持的文件类型"
}
```

## 数据初始化功能

### 自动初始化

模块启动时会自动初始化虚拟数据，包括：
- 30条好友关系记录
- 70条聊天记录（包含文本、图片、视频、文件等类型）

### 配置说明

```yaml
wchat:
  data:
    init:
      # 是否启用数据初始化（生产环境建议设为false）
      enabled: true
      # 好友关系数量
      friend-count: 30
      # 聊天记录数量
      chat-count: 70
      # 虚拟用户ID范围
      min-user-id: 1001
      max-user-id: 1020
```

### 管理接口

#### 手动触发初始化
```
POST /wenshu/wchat/admin/data/init
```

#### 获取数据统计
```
GET /wenshu/wchat/admin/data/stats
```

#### 检查初始化状态
```
GET /wenshu/wchat/admin/data/status
```

#### 清理测试数据
```
DELETE /wenshu/wchat/admin/data/clear
```

### 虚拟数据说明

**用户ID范围：** 1001-1020（共20个虚拟用户）

**好友关系：** 随机生成30对好友关系，状态为"active"

**聊天记录类型分布：**
- 文本消息：包含问候语、表情符号等
- 图片消息：模拟已上传的图片文件路径
- 视频消息：模拟已上传的视频文件路径
- 文件消息：模拟已上传的文档文件路径

**时间分布：** 聊天记录时间随机分布在最近30天内

## 注意事项

1. 文件大小限制：最大 50MB
2. 文件路径格式必须正确，否则消息发送会失败
3. 删除消息时不会自动删除关联的文件，需要手动调用文件删除接口
4. 确保配置的文件存储路径有足够的磁盘空间和读写权限
5. 生产环境请将 `wchat.data.init.enabled` 设置为 `false`
6. 数据初始化会检查现有数据，避免重复初始化
