# wenshu-livechat 实时聊天服务配置
server:
  port: 1020
  servlet:
    context-path: /
  tomcat:
    max-http-form-post-size: 10GB
    max-swallow-size: 10GB

spring:
  # 应用配置
  application:
    name: wenshu-livechat
  profiles:
    active: dev

  # 允许Bean覆盖，解决冲突
  main:
    allow-bean-definition-overriding: true

  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 2313147023
    url: *******************************************************************************************************************************************************************************************************

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10GB
      max-request-size: 10GB
      enabled: true

  # Cloud配置
  cloud:
    nacos:
      discovery:
        server-addr: ************:8848
        namespace:
        group: DEFAULT_GROUP
        enabled: true
      config:
        server-addr: ************:8848
        file-extension: yml
        namespace:
        group: DEFAULT_GROUP
        enabled: true
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

    # LoadBalancer配置
    loadbalancer:
      ribbon:
        enabled: false
      cache:
        enabled: true
        ttl: 35s
        capacity: 256

# Feign配置
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 10000
        loggerLevel: basic
  hystrix:
    enabled: false
  circuitbreaker:
    enabled: false

# 日志配置
logging:
  level:
    com.ruoyi.wenshulivechat: INFO
    com.ruoyi.wenshuapi: INFO
    org.springframework.cloud.openfeign: INFO
    org.springframework.web.socket: INFO
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# 自定义配置
livechat:
  # WebSocket配置
  websocket:
    # 消息大小限制（KB）
    message-size-limit: 64
    # 发送缓冲区大小（KB）
    send-buffer-size-limit: 512
    # 发送超时时间（秒）
    send-timeout: 15
    # WebSocket端点路径
    endpoint: /ws
    # 允许的跨域来源
    allowed-origins: "*"

  # 用户会话配置
  session:
    # 用户离线超时时间（分钟）
    offline-timeout: 5
    # 清理任务执行间隔（分钟）
    cleanup-interval: 5

  # 心跳配置
  heartbeat:
    # 心跳间隔（秒）
    interval: 30
    # 心跳超时（秒）
    timeout: 60

  # 消息路由配置
  routing:
    # 接收消息前缀
    recv-prefix: /recv
    # 发送消息前缀
    send-prefix: /send
    # 系统通知前缀
    system-prefix: /system
