package com.ruoyi.wenshucommon.common;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;

import java.util.ArrayList;
import java.util.List;

@Data
@Slf4j
public class MessageWrapper {
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    private String role;
    private String content;

    public MessageWrapper() {
    }

    public MessageWrapper(Message message) {
        this.content = message.getContent();
        if (message instanceof AssistantMessage) {
            this.role = "assistant";
        } else if (message instanceof UserMessage) {
            this.role = "user";
        } else if (message instanceof SystemMessage) {
            this.role = "system";
        } else {
            this.role = "unknown";
        }
    }

    public Message toMessage() {
        switch (role) {
            case "assistant":
                return new AssistantMessage(content);
            case "user":
                return new UserMessage(content);
            case "system":
                return new SystemMessage(content);
            default:
                return new UserMessage(content);
        }
    }

    public static String toConversationStr(List<MessageWrapper> messages) {
        try {
            return objectMapper.writeValueAsString(messages);
        } catch (JsonProcessingException e) {
            log.error("转换消息为字符串时出错", e);
            return "[]";
        }
    }

    public static List<MessageWrapper> fromConversationStr(String conversationStr) {
        try {
            if (conversationStr == null || conversationStr.trim().isEmpty()) {
                return new ArrayList<>();
            }
            
            // 尝试处理可能存在的非标准JSON
            String jsonStr = conversationStr.trim();
            if (!jsonStr.startsWith("[")) {
                log.warn("对话字符串不是有效的JSON数组格式: {}", conversationStr);
                // 尝试从字符串中提取可能的JSON部分
                int start = jsonStr.indexOf('[');
                int end = jsonStr.lastIndexOf(']');
                if (start >= 0 && end > start) {
                    jsonStr = jsonStr.substring(start, end + 1);
                    log.info("提取的JSON部分: {}", jsonStr);
                } else {
                    log.error("无法从对话字符串中提取JSON数组");
                    return new ArrayList<>();
                }
            }
            
            // 解析JSON
            List<MessageWrapper> result = objectMapper.readValue(jsonStr, new TypeReference<>() {});
            return result != null ? result : new ArrayList<>();
        } catch (Exception e) {
            log.error("解析对话字符串时出错: {}", conversationStr, e);
            return new ArrayList<>();
        }
    }
} 