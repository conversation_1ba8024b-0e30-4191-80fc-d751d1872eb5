package com.ruoyi.wenshulivechat.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 新消息查询请求模型
 * 用于WebSocket客户端请求查询新消息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewMessageRequest {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 最后查询时间（可选）
     * 用于增量查询，只获取此时间之后的消息
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastQueryTime;
    
    /**
     * 是否只查询未读消息
     * 默认为true
     */
    private Boolean unreadOnly = true;
    
    /**
     * 查询限制数量
     * 默认为50条
     */
    private Integer limit = 50;
}
