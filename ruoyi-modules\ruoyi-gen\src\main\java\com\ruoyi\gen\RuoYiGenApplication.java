package com.ruoyi.gen;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.ruoyi.common.security.annotation.EnableCustomConfig;
import com.ruoyi.common.security.annotation.EnableRyFeignClients;
import com.ruoyi.common.swagger.annotation.EnableCustomSwagger2;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 代码生成
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication
public class RuoYiGenApplication
{
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static void printStartupBanner() {
        System.out.println();
        System.out.println("██╗    ██╗███████╗███╗   ██╗███████╗██╗  ██╗██╗   ██╗");
        System.out.println("██║    ██║██╔════╝████╗  ██║██╔════╝██║  ██║██║   ██║");
        System.out.println("██║ █╗ ██║█████╗  ██╔██╗ ██║███████╗███████║██║   ██║");
        System.out.println("██║███╗██║██╔══╝  ██║╚██╗██║╚════██║██╔══██║██║   ██║");
        System.out.println("╚███╔███╔╝███████╗██║ ╚████║███████║██║  ██║╚██████╔╝");
        System.out.println(" ╚══╝╚══╝ ╚══════╝╚═╝  ╚═══╝╚══════╝╚═╝  ╚═╝ ╚═════╝ ");
        System.out.println();
        System.out.println("WENSHU-GEN 代码生成模块");
        System.out.println("启动时间: " + LocalDateTime.now().format(FORMATTER));
        System.out.println("服务端口: 9202");
        System.out.println("服务功能: 代码生成服务");
        System.out.println();
    }

    public static void main(String[] args)
    {
        printStartupBanner();
        SpringApplication.run(RuoYiGenApplication.class, args);
    }
}
