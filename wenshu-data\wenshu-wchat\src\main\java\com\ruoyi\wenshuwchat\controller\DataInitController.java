//package com.ruoyi.wenshuwchat.controller;
//
//import com.ruoyi.wenshuwchat.init.DataInitializer;
//import com.ruoyi.wenshuwchat.service.ChatRecordService;
//import com.ruoyi.wenshuwchat.service.FriendListService;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * 数据初始化管理控制器
// * 提供手动触发数据初始化和清理的接口
// */
//@RestController
//@RequestMapping("/wenshu/wchat/admin/data")
//public class DataInitController {
//
//    private static final Logger logger = LoggerFactory.getLogger(DataInitController.class);
//
//    private final DataInitializer dataInitializer;
//    private final FriendListService friendListService;
//    private final ChatRecordService chatRecordService;
//
//    @Autowired
//    public DataInitController(DataInitializer dataInitializer,
//                             FriendListService friendListService,
//                             ChatRecordService chatRecordService) {
//        this.dataInitializer = dataInitializer;
//        this.friendListService = friendListService;
//        this.chatRecordService = chatRecordService;
//    }
//
//    /**
//     * 手动触发数据初始化
//     *
//     * @return 初始化结果响应
//     */
//    @PostMapping("/init")
//    public ResponseEntity<Map<String, Object>> initData() {
//        Map<String, Object> response = new HashMap<>();
//        try {
//            logger.info("手动触发数据初始化...");
//            dataInitializer.run(null);
//
//            response.put("success", true);
//            response.put("message", "数据初始化完成");
//            return ResponseEntity.ok(response);
//        } catch (Exception e) {
//            logger.error("手动数据初始化失败: {}", e.getMessage(), e);
//            response.put("success", false);
//            response.put("message", "数据初始化失败: " + e.getMessage());
//            return ResponseEntity.internalServerError().body(response);
//        }
//    }
//
//    /**
//     * 获取数据统计信息
//     *
//     * @return 数据统计响应
//     */
//    @GetMapping("/stats")
//    public ResponseEntity<Map<String, Object>> getDataStats() {
//        Map<String, Object> response = new HashMap<>();
//        try {
//            int friendCount = friendListService.getAllFriends().size();
//            // 注意：这里需要添加获取所有聊天记录数量的方法
//            // int chatCount = chatRecordService.getAllChatRecords().size();
//
//            Map<String, Object> stats = new HashMap<>();
//            stats.put("friendCount", friendCount);
//            // stats.put("chatCount", chatCount);
//            stats.put("chatCount", "需要实现getAllChatRecords方法");
//
//            response.put("success", true);
//            response.put("message", "获取统计信息成功");
//            response.put("data", stats);
//            return ResponseEntity.ok(response);
//        } catch (Exception e) {
//            logger.error("获取数据统计失败: {}", e.getMessage(), e);
//            response.put("success", false);
//            response.put("message", "获取统计信息失败: " + e.getMessage());
//            return ResponseEntity.internalServerError().body(response);
//        }
//    }
//
//    /**
//     * 清理所有测试数据
//     * 注意：这是危险操作，仅用于开发和测试环境
//     *
//     * @return 清理结果响应
//     */
//    @DeleteMapping("/clear")
//    public ResponseEntity<Map<String, Object>> clearTestData() {
//        Map<String, Object> response = new HashMap<>();
//        try {
//            logger.warn("开始清理测试数据...");
//
//            // 这里可以添加清理逻辑
//            // 注意：需要谨慎实现，避免误删生产数据
//
//            response.put("success", true);
//            response.put("message", "测试数据清理完成");
//            response.put("warning", "此功能需要谨慎实现，当前仅返回成功状态");
//            return ResponseEntity.ok(response);
//        } catch (Exception e) {
//            logger.error("清理测试数据失败: {}", e.getMessage(), e);
//            response.put("success", false);
//            response.put("message", "清理测试数据失败: " + e.getMessage());
//            return ResponseEntity.internalServerError().body(response);
//        }
//    }
//
//    /**
//     * 检查数据初始化状态
//     *
//     * @return 状态检查响应
//     */
//    @GetMapping("/status")
//    public ResponseEntity<Map<String, Object>> checkInitStatus() {
//        Map<String, Object> response = new HashMap<>();
//        try {
//            boolean hasData = !friendListService.getAllFriends().isEmpty();
//
//            Map<String, Object> status = new HashMap<>();
//            status.put("hasData", hasData);
//            status.put("dataInitialized", hasData);
//
//            response.put("success", true);
//            response.put("message", "状态检查完成");
//            response.put("data", status);
//            return ResponseEntity.ok(response);
//        } catch (Exception e) {
//            logger.error("检查初始化状态失败: {}", e.getMessage(), e);
//            response.put("success", false);
//            response.put("message", "状态检查失败: " + e.getMessage());
//            return ResponseEntity.internalServerError().body(response);
//        }
//    }
//}
