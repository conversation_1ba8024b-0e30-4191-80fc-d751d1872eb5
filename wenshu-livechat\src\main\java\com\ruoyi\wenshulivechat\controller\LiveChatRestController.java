package com.ruoyi.wenshulivechat.controller;

import com.ruoyi.wenshulivechat.model.NewMessageRequest;
import com.ruoyi.wenshulivechat.model.NewMessageResponse;
import com.ruoyi.wenshulivechat.service.LiveChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 实时聊天REST API控制器
 * 提供HTTP接口用于测试和管理
 * 
 * 统一接口路径前缀：/wenshu/livechat
 */
@RestController
@RequestMapping("/wenshu/livechat")
@Slf4j
public class LiveChatRestController {

    @Autowired
    private LiveChatService liveChatService;

    /**
     * 查询用户新消息（HTTP接口）
     * 
     * @param userId 用户ID
     * @param unreadOnly 是否只查询未读消息
     * @param limit 查询限制数量
     * @return 新消息响应
     */
    @GetMapping("/users/{userId}/new-messages")
    public ResponseEntity<NewMessageResponse> queryNewMessages(
            @PathVariable("userId") Long userId,
            @RequestParam(value = "unreadOnly", defaultValue = "true") Boolean unreadOnly,
            @RequestParam(value = "limit", defaultValue = "50") Integer limit) {
        
        try {
            log.info("HTTP接口查询用户{}的新消息，只查未读: {}, 限制: {}", userId, unreadOnly, limit);
            
            NewMessageRequest request = new NewMessageRequest();
            request.setUserId(userId);
            request.setUnreadOnly(unreadOnly);
            request.setLimit(limit);
            
            NewMessageResponse response = liveChatService.queryNewMessages(request);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            System.out.println("HTTP接口查询用户" + userId + "新消息异常: " + e.getMessage());
            NewMessageResponse errorResponse = NewMessageResponse.error("查询异常: " + e.getMessage(), userId, "notification");
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 订阅用户所有未读消息（HTTP接口）
     *
     * @param userId 用户ID
     * @return 所有未读消息响应
     */
    @GetMapping("/users/{userId}/all-unread-messages")
    public ResponseEntity<NewMessageResponse> subscribeAllUnreadMessages(@PathVariable("userId") Long userId) {
        try {
            System.out.println("HTTP接口订阅用户" + userId + "的所有未读消息");

            NewMessageResponse response = liveChatService.subscribeAllUnreadMessages(userId);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.out.println("HTTP接口订阅用户" + userId + "所有未读消息异常: " + e.getMessage());
            NewMessageResponse errorResponse = NewMessageResponse.error("订阅异常: " + e.getMessage(), userId, "subscription");
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 获取用户未读消息数量
     * 
     * @param userId 用户ID
     * @return 未读消息数量
     */
    @GetMapping("/users/{userId}/unread-count")
    public ResponseEntity<Map<String, Object>> getUnreadCount(@PathVariable("userId") Long userId) {
        try {
            log.info("HTTP接口查询用户{}的未读消息数量", userId);
            
            Integer count = liveChatService.getUnreadMessageCount(userId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "查询成功");
            response.put("userId", userId);
            response.put("unreadCount", count);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            System.out.println("HTTP接口查询用户" + userId + "未读消息数量异常: " + e.getMessage());

            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "查询异常: " + e.getMessage());
            response.put("userId", userId);
            response.put("unreadCount", 0);
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 健康检查接口
     * 
     * @return 服务状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "wenshu-livechat");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }

    /**
     * 获取WebSocket配置信息
     * 
     * @return WebSocket配置
     */
    @GetMapping("/websocket/info")
    public ResponseEntity<Map<String, Object>> getWebSocketInfo() {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "WebSocket配置信息");
        
        Map<String, Object> config = new HashMap<>();
        config.put("endpoint", "/ws");
        config.put("nativeEndpoint", "/ws-native");
        config.put("sendPrefix", "/send");
        config.put("recvPrefix", "/recv");
        config.put("systemPrefix", "/system");
        
        Map<String, String> routes = new HashMap<>();
        routes.put("查询新消息", "/send/{userId}/query-new-messages");
        routes.put("订阅所有未读消息", "/send/{userId}/subscribe-all-unread");
        routes.put("心跳检测", "/send/{userId}/heartbeat");
        routes.put("接收新消息", "/recv/{userId}/new-messages");
        routes.put("接收所有未读消息", "/recv/{userId}/all-unread-messages");
        routes.put("接收心跳响应", "/recv/{userId}/heartbeat");
        
        config.put("routes", routes);
        response.put("config", config);
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
}
