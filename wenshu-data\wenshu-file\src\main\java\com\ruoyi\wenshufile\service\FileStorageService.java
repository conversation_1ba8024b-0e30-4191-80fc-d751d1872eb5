package com.ruoyi.wenshufile.service;

import org.springframework.web.multipart.MultipartFile;

/**
 * 文件存储服务接口
 * 专门处理文件的物理存储、删除和访问等操作
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-28
 */
public interface FileStorageService {

    /**
     * 上传文件到服务器
     * 支持所有文件类型，单个文件不超过100MB
     * 
     * @param file 上传的文件
     * @param uploaderId 上传者ID，用于目录分类
     * @return 文件的相对存储路径
     * @throws Exception 上传失败时抛出异常
     */
    String uploadFile(MultipartFile file, Integer uploaderId) throws Exception;

    /**
     * 删除服务器上的文件
     * 
     * @param filePath 文件的相对存储路径
     * @return 删除是否成功
     */
    boolean deleteFile(String filePath);

    /**
     * 检查文件是否存在
     * 
     * @param filePath 文件的相对存储路径
     * @return 文件是否存在
     */
    boolean fileExists(String filePath);

    /**
     * 获取文件的完整访问URL
     * 
     * @param filePath 文件的相对存储路径
     * @return 完整的文件访问URL
     */
    String getFileAccessUrl(String filePath);

    /**
     * 验证文件是否符合上传要求
     * 检查文件大小、文件名等基本要求
     * 
     * @param file 上传的文件
     * @return 验证是否通过
     */
    boolean validateFile(MultipartFile file);

    /**
     * 获取文件的MIME类型
     * 
     * @param file 上传的文件
     * @return MIME类型字符串
     */
    String getFileMimeType(MultipartFile file);

    /**
     * 生成唯一的文件名
     * 保留原始文件扩展名，避免文件名冲突
     * 
     * @param originalFilename 原始文件名
     * @return 生成的唯一文件名
     */
    String generateUniqueFileName(String originalFilename);

    /**
     * 获取文件存储的绝对路径
     * 
     * @param relativePath 相对路径
     * @return 绝对路径
     */
    String getAbsolutePath(String relativePath);

    /**
     * 清理临时文件
     * 清理上传过程中产生的临时文件
     */
    void cleanupTempFiles();
}
