package com.ruoyi.wenshuapi.pojo.team;

import java.time.LocalDateTime;

public class TeamInfo {
    private Integer teamId;       // 团队ID
    private String teamName;      // 团队名称
    private Integer leaderId;     // 团队负责人ID
    private Integer creatorId;    // 团队创建者ID
    private LocalDateTime createTime; // 团队创建时间
    private String description;   // 团队描述
    private Byte status;          // 状态（0-禁用,1-启用）

    // 无参构造器
    public TeamInfo() {
    }

    // 全参构造器
    public TeamInfo(Integer teamId, String teamName, Integer leaderId, Integer creatorId,
                LocalDateTime createTime, String description, Byte status) {
        this.teamId = teamId;
        this.teamName = teamName;
        this.leaderId = leaderId;
        this.creatorId = creatorId;
        this.createTime = createTime;
        this.description = description;
        this.status = status;
    }

    // Getter 和 Setter 方法
    public Integer getTeamId() {
        return teamId;
    }

    public void setTeamId(Integer teamId) {
        this.teamId = teamId;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public Integer getLeaderId() {
        return leaderId;
    }

    public void setLeaderId(Integer leaderId) {
        this.leaderId = leaderId;
    }

    public Integer getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Integer creatorId) {
        this.creatorId = creatorId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    // toString() 方法
    @Override
    public String toString() {
        return "Team{" +
                "teamId=" + teamId +
                ", teamName='" + teamName + '\'' +
                ", leaderId=" + leaderId +
                ", creatorId=" + creatorId +
                ", createTime=" + createTime +
                ", description='" + description + '\'' +
                ", status=" + status +
                '}';
    }
}