package com.ruoyi.wenshuapi.pojo.team;

/**
 * 团队-用户关联实体类
 * 描述：存储团队与用户的关联关系信息
 */
public class TeamUserRelation {

    /**
     * 团队唯一标识符
     * 默认值：0（表示未分配团队）
     * 特殊值：-1（表示系统默认团队）
     */
    private int teamId;

    /**
     * 用户唯一标识符
     * 默认值：0（表示未分配用户）
     * 特殊值：-1（表示系统管理员用户）
     */
    private int userId;

    /**
     * 默认构造函数
     * 初始化字段为默认值（teamId=0, userId=0）
     */
    public TeamUserRelation() {
        this.teamId = 0;
        this.userId = 0;
    }

    /**
     * 全参数构造函数
     * @param teamId 团队ID（int类型）
     * @param userId 用户ID（int类型）
     */
    public TeamUserRelation(int teamId, int userId) {
        this.teamId = teamId;
        this.userId = userId;
    }

    /**
     * 获取团队ID
     * @return 当前团队ID值
     */
    public int getTeamId() {
        return teamId;
    }

    /**
     * 设置团队ID
     * @param teamId 要设置的团队ID值
     */
    public void setTeamId(int teamId) {
        this.teamId = teamId;
    }

    /**
     * 获取用户ID
     * @return 当前用户ID值
     */
    public int getUserId() {
        return userId;
    }

    /**
     * 设置用户ID
     * @param userId 要设置的用户ID值
     */
    public void setUserId(int userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return "TeamUserRelation{" +
                "teamId=" + teamId +
                ", userId=" + userId +
                '}';
    }
}