# Wenshu-Base 基础服务模块 - 详细API接口文档

## 📋 模块概述

**服务名称**: wenshu-base  
**服务端口**: 8700  
**版本**: v1.0.0  
**最后更新**: 2024-12-28  

**核心功能**: 基础服务模块，提供用户管理、权限控制、系统配置、数据字典、日志管理等基础功能

**技术栈**:
- Spring Boot 3.x
- Spring Cloud 2023.x
- Spring Security (权限控制)
- Redis (缓存管理)
- MySQL (数据存储)
- MyBatis-Plus (ORM框架)
- JWT (身份认证)

---

## 🏗️ 服务架构

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                Wenshu-Base 基础服务模块                      │
│                        (Port: 8700)                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│   用户管理     │ │权限控制  │ │ 系统配置   │
│ (User Mgmt)   │ │(Auth)  │ │(Config)   │
└───────────────┘ └────────┘ └───────────┘
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│ 数据字典       │ │日志管理  │ │ 缓存管理   │
│ (Dictionary)  │ │(Log)   │ │(Cache)    │
└───────────────┘ └────────┘ └───────────┘
```

### 服务依赖关系
- **MySQL**: 用户数据、权限数据、系统配置存储
- **Redis**: 缓存、会话管理、权限缓存
- **Nacos**: 服务注册与发现、配置管理

---

## 🌐 REST API接口

### 基础信息
- **Base URL**: `http://localhost:8700`
- **Content-Type**: `application/json`
- **鉴权方式**: JWT Token (Header: `Authorization: Bearer <token>`)

---

## 👤 用户管理API

### 1. 用户基础操作

#### 1.1 用户注册
**接口路径**: `POST /api/users/register`

**功能描述**: 用户注册接口

**请求参数**:
```http
POST /api/users/register
Content-Type: application/json

{
  "username": "zhangsan",
  "password": "123456",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "realName": "张三",
  "department": "技术部",
  "position": "软件工程师"
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "注册成功",
  "data": {
    "userId": 1001,
    "username": "zhangsan",
    "email": "<EMAIL>",
    "realName": "张三",
    "status": "active",
    "createTime": "2024-12-28T15:30:00"
  },
  "timestamp": 1640995200000
}
```

#### 1.2 用户登录
**接口路径**: `POST /api/users/login`

**功能描述**: 用户登录认证

**请求参数**:
```http
POST /api/users/login
Content-Type: application/json

{
  "username": "zhangsan",
  "password": "123456",
  "captcha": "ABCD",
  "captchaId": "captcha_12345"
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_here",
    "expiresIn": 7200,
    "userInfo": {
      "userId": 1001,
      "username": "zhangsan",
      "realName": "张三",
      "email": "<EMAIL>",
      "avatar": "/avatars/1001.jpg",
      "roles": ["user", "editor"],
      "permissions": ["user:read", "user:write"]
    }
  },
  "timestamp": 1640995200000
}
```

#### 1.3 获取用户信息
**接口路径**: `GET /api/users/{userId}`

**功能描述**: 获取指定用户的详细信息

**请求示例**:
```bash
curl -X GET "http://localhost:8700/api/users/1001" \
  -H "Authorization: Bearer <your-token>"
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "userId": 1001,
    "username": "zhangsan",
    "realName": "张三",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "avatar": "/avatars/1001.jpg",
    "department": "技术部",
    "position": "软件工程师",
    "status": "active",
    "lastLoginTime": "2024-12-28T15:30:00",
    "createTime": "2024-12-20T10:00:00",
    "updateTime": "2024-12-28T15:30:00"
  },
  "timestamp": 1640995200000
}
```

#### 1.4 更新用户信息
**接口路径**: `PUT /api/users/{userId}`

**功能描述**: 更新用户信息

**请求参数**:
```http
PUT /api/users/1001
Content-Type: application/json

{
  "realName": "张三丰",
  "email": "<EMAIL>",
  "phone": "13900139000",
  "department": "研发部",
  "position": "高级工程师"
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "更新成功",
  "data": {
    "userId": 1001,
    "username": "zhangsan",
    "realName": "张三丰",
    "email": "<EMAIL>",
    "updateTime": "2024-12-28T16:00:00"
  },
  "timestamp": 1640995200000
}
```

#### 1.5 用户列表查询
**接口路径**: `GET /api/users`

**功能描述**: 分页查询用户列表

**请求参数**:
```http
GET /api/users?page=1&size=20&keyword=张&department=技术部&status=active
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "users": [
      {
        "userId": 1001,
        "username": "zhangsan",
        "realName": "张三",
        "email": "<EMAIL>",
        "department": "技术部",
        "position": "软件工程师",
        "status": "active",
        "createTime": "2024-12-20T10:00:00"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 45,
      "totalPages": 3
    }
  },
  "timestamp": 1640995200000
}
```

---

## 🔐 权限管理API

### 2. 角色权限管理

#### 2.1 获取用户权限
**接口路径**: `GET /api/permissions/user/{userId}`

**功能描述**: 获取用户的所有权限信息

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "userId": 1001,
    "roles": [
      {
        "roleId": 1,
        "roleName": "普通用户",
        "roleCode": "user",
        "description": "系统普通用户"
      },
      {
        "roleId": 2,
        "roleName": "编辑者",
        "roleCode": "editor", 
        "description": "内容编辑权限"
      }
    ],
    "permissions": [
      {
        "permissionId": 1,
        "permissionName": "用户查看",
        "permissionCode": "user:read",
        "resourceType": "menu",
        "resourcePath": "/users"
      },
      {
        "permissionId": 2,
        "permissionName": "用户编辑",
        "permissionCode": "user:write",
        "resourceType": "button",
        "resourcePath": "/users/edit"
      }
    ]
  },
  "timestamp": 1640995200000
}
```

#### 2.2 分配用户角色
**接口路径**: `POST /api/permissions/user/{userId}/roles`

**功能描述**: 为用户分配角色

**请求参数**:
```http
POST /api/permissions/user/1001/roles
Content-Type: application/json

{
  "roleIds": [1, 2, 3]
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "角色分配成功",
  "data": {
    "userId": 1001,
    "assignedRoles": [
      {"roleId": 1, "roleName": "普通用户"},
      {"roleId": 2, "roleName": "编辑者"},
      {"roleId": 3, "roleName": "管理员"}
    ]
  },
  "timestamp": 1640995200000
}
```

#### 2.3 权限验证
**接口路径**: `POST /api/permissions/verify`

**功能描述**: 验证用户是否具有指定权限

**请求参数**:
```http
POST /api/permissions/verify
Content-Type: application/json

{
  "userId": 1001,
  "permission": "user:write",
  "resource": "/users/edit"
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "验证成功",
  "data": {
    "hasPermission": true,
    "permission": "user:write",
    "resource": "/users/edit",
    "grantedBy": ["editor", "admin"]
  },
  "timestamp": 1640995200000
}
```

---

---

## ⚙️ 系统配置API

### 3. 配置管理

#### 3.1 获取系统配置
**接口路径**: `GET /api/config/{configKey}`

**功能描述**: 获取指定的系统配置项

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "configKey": "system.title",
    "configValue": "文书智能办公系统",
    "configType": "string",
    "description": "系统标题配置",
    "updateTime": "2024-12-28T15:30:00"
  },
  "timestamp": 1640995200000
}
```

#### 3.2 更新系统配置
**接口路径**: `PUT /api/config/{configKey}`

**请求参数**:
```http
PUT /api/config/system.title
Content-Type: application/json

{
  "configValue": "文书智能办公平台",
  "description": "系统标题配置"
}
```

---

## 📚 数据字典API

### 4. 字典管理

#### 4.1 获取字典数据
**接口路径**: `GET /api/dict/{dictType}`

**功能描述**: 获取指定类型的字典数据

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "dictType": "user_status",
    "dictName": "用户状态",
    "items": [
      {
        "dictCode": "active",
        "dictLabel": "正常",
        "dictValue": "1",
        "cssClass": "success",
        "listClass": "primary",
        "isDefault": true,
        "status": "normal",
        "remark": "用户状态正常"
      },
      {
        "dictCode": "disabled",
        "dictLabel": "禁用",
        "dictValue": "0",
        "cssClass": "danger",
        "listClass": "danger",
        "isDefault": false,
        "status": "normal",
        "remark": "用户被禁用"
      }
    ]
  },
  "timestamp": 1640995200000
}
```

---

## 📊 日志管理API

### 5. 操作日志

#### 5.1 查询操作日志
**接口路径**: `GET /api/logs/operation`

**请求参数**:
```http
GET /api/logs/operation?page=1&size=20&userId=1001&operation=login&startDate=2024-12-01&endDate=2024-12-28
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "logs": [
      {
        "logId": 12345,
        "userId": 1001,
        "username": "zhangsan",
        "operation": "login",
        "method": "POST",
        "requestUrl": "/api/users/login",
        "requestIp": "*************",
        "userAgent": "Mozilla/5.0...",
        "operationTime": "2024-12-28T15:30:00",
        "costTime": 150,
        "status": "success"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 1250,
      "totalPages": 63
    }
  },
  "timestamp": 1640995200000
}
```

---

## 🔧 技术实现细节

### 安全机制
- **JWT认证**: 基于JWT的无状态认证
- **密码加密**: BCrypt加密存储
- **权限缓存**: Redis缓存权限信息
- **会话管理**: 支持单点登录和会话过期

### 缓存策略
- **用户信息缓存**: 30分钟过期
- **权限信息缓存**: 1小时过期
- **字典数据缓存**: 24小时过期
- **配置信息缓存**: 实时更新

---

## 📊 性能指标

### 处理性能
| 指标 | 数值 | 说明 |
|------|------|------|
| 登录响应时间 | < 500ms | 包含权限加载 |
| 权限验证时间 | < 50ms | 缓存命中 |
| 用户查询时间 | < 200ms | 分页查询 |
| 并发登录数 | 1000+ | 同时在线用户 |

---

*文档版本: v1.0.0 | 最后更新: 2024-12-28 | Wenshu-Base基础服务模块*
