package com.ruoyi.wenshuwchat.service;

import org.springframework.web.multipart.MultipartFile;

/**
 * 聊天文件存储服务接口
 * 专门处理聊天相关的文件上传、存储和访问
 */
public interface ChatFileService {

    /**
     * 上传聊天文件
     * 
     * @param file 上传的文件
     * @param contentType 内容类型 (image, video, file)
     * @return 文件访问路径
     * @throws Exception 上传失败时抛出异常
     */
    String uploadChatFile(MultipartFile file, String contentType) throws Exception;

    /**
     * 删除聊天文件
     * 
     * @param filePath 文件路径
     * @return 删除是否成功
     */
    boolean deleteChatFile(String filePath);

    /**
     * 检查文件是否存在
     * 
     * @param filePath 文件路径
     * @return 文件是否存在
     */
    boolean fileExists(String filePath);

    /**
     * 获取文件的完整访问URL
     * 
     * @param filePath 文件相对路径
     * @return 完整的访问URL
     */
    String getFileAccessUrl(String filePath);

    /**
     * 验证文件类型是否允许
     *
     * @param file 上传的文件
     * @param contentType 期望的内容类型
     * @return 是否允许上传
     */
    boolean isFileTypeAllowed(MultipartFile file, String contentType);

    /**
     * 获取文件的完整物理路径
     *
     * @param filePath 文件相对路径
     * @return 文件的完整物理路径
     */
    String getFullFilePath(String filePath);
}
