# Wenshu 智能协作平台

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen)](https://github.com/wenshu/wenshu-platform)
[![Version](https://img.shields.io/badge/version-v3.6.4-blue)](https://github.com/wenshu/wenshu-platform/releases)
[![License](https://img.shields.io/badge/license-MIT-green)](https://opensource.org/licenses/MIT)
[![Java](https://img.shields.io/badge/Java-17+-orange)](https://openjdk.java.net/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.3.3-brightgreen)](https://spring.io/projects/spring-boot)

## 项目概述

Wenshu是一个基于微服务架构的智能协作平台，集成了AI智能对话、语音处理、实时通信、文件管理、会议系统等多种功能。平台采用Spring Cloud微服务架构，支持高并发、高可用的企业级应用场景。

### 🚀 关键特性

- **🤖 AI智能对话**: 基于大语言模型的智能助手，支持意图识别和日程管理
- **🎙️ 智能语音处理**: 语音识别、语音交互，支持多种音频格式
- **💬 实时通信**: WebSocket实时聊天，支持文本、图片、文件等多媒体消息
- **📹 视频会议**: 基于WebRTC的多人视频会议系统
- **📁 文件管理**: 完整的文件上传、存储、管理解决方案
- **📅 日程管理**: 智能日程创建、查询、提醒功能
- **🔐 安全认证**: JWT身份认证，完善的权限控制体系
- **📊 数据管理**: 统一的数据服务，支持多种数据源

## 技术栈

### 后端框架
- **Spring Boot 3.3.3** - 主要应用框架
- **Spring Cloud 2023.0.1** - 微服务框架
- **Spring Cloud Alibaba 2023.0.1.0** - 阿里云微服务组件
- **Spring AI** - AI集成框架
- **MyBatis Plus** - 数据持久化框架

### 数据库与缓存
- **MySQL 8.0+** - 主数据库
- **Redis** - 缓存和会话存储
- **Nacos** - 服务注册与配置中心

### 基础设施
- **Docker** - 容器化部署
- **Nginx** - 反向代理和负载均衡
- **Maven 3.9+** - 项目构建工具

### 第三方服务
- **阿里云DashScope** - AI对话和语音识别
- **WebRTC** - 实时音视频通信
- **WebSocket/STOMP** - 实时消息推送

## 快速开始

### 环境准备

确保您的开发环境满足以下要求：

- **JDK 17+**
- **Maven 3.9+**
- **MySQL 8.0+**
- **Redis 6.0+**
- **Node.js 16+** (前端开发)

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/wenshu/wenshu-platform.git
cd wenshu-platform
```

2. **数据库初始化**
```bash
# 创建数据库
mysql -u root -p < sql/ry-cloud.sql
mysql -u root -p < sql/ry-config.sql
```

3. **启动基础服务**
```bash
# 启动Nacos (端口: 8848)
# 启动Redis (端口: 6379)
# 启动MySQL (端口: 3306)
```

4. **编译项目**
```bash
mvn clean install -DskipTests
```

5. **启动微服务**
```bash
# 启动网关服务
./bin/run-gateway.bat

# 启动认证服务
./bin/run-auth.bat

# 启动系统服务
./bin/run-modules-system.bat

# 启动业务服务
./bin/run-modules-file.bat

# 启动其他服务...
```

6. **启动前端**
```bash
cd ruoyi-ui
npm install
npm run dev
```

### 服务端口分配

| 服务名称 | 端口 | 说明 | 健康检查 |
|----------|------|------|----------|
| Gateway | 8080 | API网关 | http://localhost:8080/actuator/health |
| Auth | 9200 | 认证服务 | http://localhost:9200/actuator/health |
| System | 9201 | 系统服务 | http://localhost:9201/actuator/health |
| File | 9300 | 文件服务 | http://localhost:9300/actuator/health |
| wenshu-chat | 1013 | AI对话服务 | http://localhost:1013/actuator/health |
| wenshu-voice | 1014 | 语音处理服务 | http://localhost:1014/actuator/health |
| wenshu-file | 1016 | 文件管理服务 | http://localhost:1016/actuator/health |
| wenshu-meeting | 1018 | 会议服务 | http://localhost:1018/actuator/health |
| wenshu-livechat | 1020 | 实时聊天服务 | http://localhost:1020/actuator/health |
| programme-manage | 8605 | 日程管理服务 | http://localhost:8605/actuator/health |

### 配置说明

#### 环境变量配置

```bash
# 数据库配置
export DB_HOST=localhost
export DB_PORT=3306
export DB_NAME=ry-cloud
export DB_USERNAME=root
export DB_PASSWORD=password

# Redis配置
export REDIS_HOST=localhost
export REDIS_PORT=6379
export REDIS_PASSWORD=

# Nacos配置
export NACOS_HOST=localhost
export NACOS_PORT=8848
export NACOS_NAMESPACE=

# AI服务配置
export DASHSCOPE_API_KEY=your_api_key_here
```

#### 核心配置文件

**application.yml** (示例):
```yaml
spring:
  application:
    name: wenshu-platform
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_HOST:localhost}:${NACOS_PORT:8848}
      config:
        server-addr: ${NACOS_HOST:localhost}:${NACOS_PORT:8848}
        file-extension: yml
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:ry-cloud}
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
```

## 📖 API文档

### 离线文档

项目根目录下提供了完整的API接口文档：

**核心业务模块**:
- [Wenshu-Chat AI智能对话模块](./wenshu-chat-api.md)
- [Wenshu-Voice 语音处理模块](./wenshu-voice-api.md)
- [Wenshu-File 文件管理模块](./wenshu-file-api.md)
- [Wenshu-LiveChat 实时聊天模块](./wenshu-livechat-api.md)
- [Wenshu-Meeting 会议管理模块](./wenshu-meeting-api.md)
- [Programme-Manage 日程管理模块](./programme-manage-api.md)
- [Wenshu-Data 数据管理模块](./wenshu-data-api.md)

**系统基础模块**:
- [Wenshu-System 系统管理模块](./wenshu-system-api.md)
- [Wenshu 附加模块集合](./wenshu-additional-modules-api.md)

**通用规范文档**:
- [统一错误码和响应格式规范](./common-error-codes-api.md)
- [API文档完整索引](./API-DOCUMENTATION-INDEX.md)
- [安全最佳实践指南](./SECURITY-BEST-PRACTICES.md)

### 在线文档

- **Swagger UI**: http://localhost:8080/swagger-ui.html
- **Knife4j**: http://localhost:8080/doc.html

## 🏗️ 系统架构

### 整体架构图

```mermaid
graph TB
    subgraph "前端层"
        A[Vue.js 前端应用]
    end

    subgraph "网关层"
        B[Spring Cloud Gateway]
    end

    subgraph "认证层"
        C[认证服务 Auth]
    end

    subgraph "业务服务层"
        D[AI对话服务<br/>wenshu-chat:1013]
        E[语音处理服务<br/>wenshu-voice:1014]
        F[文件管理服务<br/>wenshu-file:1016]
        G[实时聊天服务<br/>wenshu-livechat:1020]
        H[会议服务<br/>wenshu-meeting:1018]
        I[日程管理服务<br/>programme-manage:8605]
        J[数据服务<br/>wenshu-data]
    end

    subgraph "基础设施层"
        K[Nacos 注册中心]
        L[MySQL 数据库]
        M[Redis 缓存]
        N[阿里云DashScope]
    end

    A --> B
    B --> C
    B --> D
    B --> E
    B --> F
    B --> G
    B --> H
    B --> I
    B --> J

    D --> K
    E --> K
    F --> K
    G --> K
    H --> K
    I --> K
    J --> K

    D --> L
    E --> L
    F --> L
    G --> L
    H --> L
    I --> L
    J --> L

    D --> M
    E --> M
    F --> M
    G --> M
    H --> M
    I --> M
    J --> M

    D --> N
    E --> N
```

### 典型业务流程图

#### AI智能对话流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant G as 网关
    participant C as Chat服务
    participant P as 日程服务
    participant AI as DashScope

    U->>G: 发送消息
    G->>C: 转发请求
    C->>AI: 意图识别
    AI-->>C: 返回意图类型

    alt 日程操作
        C->>P: 调用日程服务
        P-->>C: 返回操作结果
    else 普通聊天
        C->>AI: 发送对话请求
        AI-->>C: 返回AI回复
    end

    C-->>G: 返回响应
    G-->>U: 返回结果
```

#### 语音交互流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant V as Voice服务
    participant AI as DashScope
    participant C as Chat服务

    U->>V: 上传音频文件
    V->>V: 音频格式转换
    V->>AI: 语音识别
    AI-->>V: 返回文本
    V->>V: 文本去重处理
    V->>C: 发送文本到Chat服务
    C-->>V: 返回AI回复
    V-->>U: 返回完整结果
```

## 🚀 部署指南

### 生产环境配置建议

1. **JVM参数配置**
```bash
-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200
```

2. **数据库连接池配置**
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
```

3. **Redis集群配置**
```yaml
spring:
  redis:
    cluster:
      nodes: redis1:6379,redis2:6379,redis3:6379
```

### 容器化部署

1. **构建Docker镜像**
```bash
# 构建所有服务镜像
docker-compose build

# 启动所有服务
docker-compose up -d
```

2. **Docker Compose配置**
```yaml
version: '3.8'
services:
  nacos:
    image: nacos/nacos-server:latest
    ports:
      - "8848:8848"
    environment:
      - MODE=standalone
  
  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=password
  
  redis:
    image: redis:6.2
    ports:
      - "6379:6379"
  
  wenshu-gateway:
    build: ./ruoyi-gateway
    ports:
      - "8080:8080"
    depends_on:
      - nacos
      - mysql
      - redis
```

### Kubernetes部署

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wenshu-chat
spec:
  replicas: 3
  selector:
    matchLabels:
      app: wenshu-chat
  template:
    metadata:
      labels:
        app: wenshu-chat
    spec:
      containers:
      - name: wenshu-chat
        image: wenshu/wenshu-chat:latest
        ports:
        - containerPort: 1013
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
```

## 🤝 贡献指南

### 分支管理策略

- **main**: 主分支，用于生产环境
- **develop**: 开发分支，用于集成测试
- **feature/***: 功能分支，用于新功能开发
- **hotfix/***: 热修复分支，用于紧急修复

### PR规范要求

1. **代码规范**: 遵循阿里巴巴Java开发手册
2. **测试覆盖**: 新功能必须包含单元测试
3. **文档更新**: API变更需同步更新文档
4. **代码审查**: 所有PR需要至少一人审查

### 提交信息规范

```
<type>(<scope>): <subject>

<body>

<footer>
```

**Type类型**:
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 📊 性能基准测试

### QPS性能数据

| 服务 | 平均QPS | 95%响应时间 | 99%响应时间 |
|------|---------|-------------|-------------|
| AI对话服务 | 500+ | 2s | 5s |
| 语音处理服务 | 100+ | 3s | 8s |
| 文件管理服务 | 1000+ | 500ms | 1s |
| 实时聊天服务 | 2000+ | 100ms | 200ms |
| 日程管理服务 | 800+ | 300ms | 600ms |

### 系统容量

- **并发用户数**: 10,000+
- **文件存储**: 支持PB级存储
- **消息吞吐**: 100万条/分钟
- **视频会议**: 支持1000个并发会议室

## ❓ 常见问题排查 (FAQ)

### Q1: 服务启动失败怎么办？
**A**: 检查以下几点：
1. 确认JDK版本是否为17+
2. 检查Nacos是否正常启动
3. 确认数据库连接配置是否正确
4. 查看日志文件获取详细错误信息

### Q2: AI对话服务无响应？
**A**: 可能原因：
1. 阿里云DashScope API Key配置错误
2. 网络连接问题
3. API调用频率超限
4. 检查wenshu-chat服务日志

### Q3: 文件上传失败？
**A**: 检查项目：
1. 文件大小是否超过100MB限制
2. 磁盘空间是否充足
3. 文件存储路径权限是否正确
4. 检查wenshu-file服务状态

### Q4: WebSocket连接断开？
**A**: 排查步骤：
1. 检查网络连接稳定性
2. 确认JWT Token是否有效
3. 查看浏览器控制台错误信息
4. 检查服务器防火墙设置

## 📄 许可证

本项目采用 [MIT License](https://opensource.org/licenses/MIT) 开源协议。

## 📞 联系我们

- **技术支持**: <EMAIL>
- **商务合作**: <EMAIL>
- **问题反馈**: [GitHub Issues](https://github.com/wenshu/wenshu-platform/issues)
- **官方网站**: https://www.wenshu.com
- **技术文档**: https://docs.wenshu.com

---

**⭐ 如果这个项目对您有帮助，请给我们一个Star！**
