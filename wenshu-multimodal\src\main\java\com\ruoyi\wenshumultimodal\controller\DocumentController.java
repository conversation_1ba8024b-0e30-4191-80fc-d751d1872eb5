package com.ruoyi.wenshumultimodal.controller;

import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.dashscope.exception.UploadFileException;
import com.ruoyi.wenshumultimodal.model.DocumentRequest;
import com.ruoyi.wenshumultimodal.service.DocumentService;
import com.ruoyi.wenshumultimodal.util.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.tika.Tika;
import org.apache.tika.exception.TikaException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 文档生成控制器
 * 提供智能文档生成、格式转换与文档解析功能
 */
@Slf4j
@RestController
@RequestMapping("/document")
@RequiredArgsConstructor
public class DocumentController {

    private final DocumentService documentService;
    private final Tika tika = new Tika();

    /**
     * 生成Markdown格式的文档内容
     *
     * @param prompt 提示词
     * @param userId 用户ID
     * @param style 文档风格（可选：formal-正式, creative-创意, academic-学术, concise-简洁）
     * @param structureType 文档结构类型（可选：report-报告, article-文章, note-笔记, proposal-提案）
     * @return API响应
     */
    @GetMapping("/generate-markdown")
    public ApiResponse<String> generateMarkdown(
            @RequestParam("prompt") String prompt,
            @RequestParam("userId") String userId,
            @RequestParam(value = "style", required = false, defaultValue = "formal") String style,
            @RequestParam(value = "structureType", required = false, defaultValue = "article") String structureType) {

        log.info("收到生成Markdown文档请求，提示词: {}, 用户ID: {}, 风格: {}, 结构类型: {}", 
                prompt, userId, style, structureType);

        try {
            String markdownContent = documentService.generateMarkdownContent(prompt, userId, style, structureType);
            return ApiResponse.success("Markdown内容生成成功", markdownContent);
        } catch (Exception e) {
            log.error("生成Markdown内容失败: {}", e.getMessage(), e);
            return ApiResponse.error("生成Markdown内容失败: " + e.getMessage());
        }
    }

    /**
     * 生成并保存文档
     *
     * @param prompt 提示词
     * @param format 文档格式 (md, html, docx, pdf)
     * @param outputPath 输出路径 (可选)
     * @param userId 用户ID
     * @param style 文档风格（可选）
     * @param structureType 文档结构类型（可选）
     * @param includeTableOfContents 是否包含目录 (可选)
     * @return API响应
     */
    @PostMapping("/generate")
    public ApiResponse<Map<String, String>> generateDocument(
            @RequestParam("prompt") String prompt,
            @RequestParam("format") String format,
            @RequestParam(value = "outputPath", required = false) String outputPath,
            @RequestParam("userId") String userId,
            @RequestParam(value = "style", required = false, defaultValue = "formal") String style,
            @RequestParam(value = "structureType", required = false, defaultValue = "article") String structureType,
            @RequestParam(value = "includeTableOfContents", required = false, defaultValue = "false") Boolean includeTableOfContents) {

        log.info("收到生成文档请求，提示词: {}, 格式: {}, 用户ID: {}, 风格: {}, 结构类型: {}, 包含目录: {}", 
                prompt, format, userId, style, structureType, includeTableOfContents);

        try {
            Random random = new Random();
            int fileId = random.nextInt();
            
            // 构建增强的文档配置
            Map<String, Object> docConfig = new HashMap<>();
            docConfig.put("style", style);
            docConfig.put("structureType", structureType);
            docConfig.put("includeTableOfContents", includeTableOfContents);
            
            String filePath = documentService.generateEnhancedDocument(
                fileId, prompt, format, outputPath, userId, docConfig);

            Map<String, String> result = new HashMap<>();
            result.put("filePath", filePath);
            result.put("format", format);
            result.put("style", style);
            result.put("structureType", structureType);

            return ApiResponse.success("文档生成成功", result);
        } catch (Exception e) {
            log.error("生成文档失败: {}", e.getMessage(), e);
            return ApiResponse.error("生成文档失败: " + e.getMessage());
        }
    }

    /**
     * 使用请求对象生成文档（增强版）
     *
     * @param request 文档生成请求
     * @return API响应
     */
    @PostMapping("/generate-with-request")
    public ApiResponse<Map<String, String>> generateDocumentWithRequest(@RequestBody DocumentRequest request) {
        log.info("收到文档生成请求: {}", request);

        try {
            // 构建增强的文档配置
            Map<String, Object> docConfig = new HashMap<>();
            docConfig.put("style", request.getStyle());
            docConfig.put("structureType", request.getStructureType());
            docConfig.put("includeTableOfContents", request.isIncludeTableOfContents());
            docConfig.put("theme", request.getTheme());
            docConfig.put("fontSize", request.getFontSize());
            docConfig.put("lineSpacing", request.getLineSpacing());
            
            String filePath = documentService.generateEnhancedDocument(
                    request.getFileId(),
                    request.getPrompt(),
                    request.getFormat(),
                    request.getOutputPath(),
                    request.getUserId(),
                    docConfig
            );

            Map<String, String> result = new HashMap<>();
            result.put("filePath", filePath);
            result.put("format", request.getFormat());
            result.put("title", request.getTitle());
            result.put("style", request.getStyle());

            return ApiResponse.success("文档生成成功", result);
        } catch (Exception e) {
            log.error("生成文档失败: {}", e.getMessage(), e);
            return ApiResponse.error("生成文档失败: " + e.getMessage());
        }
    }

    /**
     * 将Markdown转换为HTML，支持主题和样式定制
     *
     * @param markdown Markdown内容
     * @param theme 主题风格 (可选: default, github, elegant, dark)
     * @param customCss 自定义CSS (可选)
     * @return API响应
     */
    @PostMapping("/markdown-to-html")
    public ApiResponse<String> markdownToHtml(
            @RequestParam("markdown") String markdown,
            @RequestParam(value = "theme", required = false, defaultValue = "default") String theme,
            @RequestParam(value = "customCss", required = false) String customCss) {
        try {
            String html = documentService.convertMarkdownToHtml(markdown, theme, customCss);
            return ApiResponse.success("转换成功", html);
        } catch (Exception e) {
            log.error("Markdown转HTML失败: {}", e.getMessage(), e);
            return ApiResponse.error("Markdown转HTML失败: " + e.getMessage());
        }
    }
    
    /**
     * 解析文档内容并进行智能分析
     * 支持多种文档格式，提供智能解析和内容总结
     *
     * @param file 上传的文件
     * @param prompt 提示词（可选，指导解析方向）
     * @param userId 用户ID
     * @param extractionType 提取类型（summary-摘要, structure-结构分析, full-全文解析）
     * @return API响应
     */
    @PostMapping("document-parse")
    public ApiResponse<Map<String, Object>> documentParse(
            @RequestParam("file") MultipartFile file,
            @RequestParam(required = false, value = "prompt") String prompt,
            @RequestParam(value = "userId") String userId,
            @RequestParam(required = false, value = "extractionType", defaultValue = "full") String extractionType) 
            throws NoApiKeyException, UploadFileException, IOException, TikaException {

        if(file.isEmpty()){
            return ApiResponse.error("上传文件为空");
        }

        log.info("接收到文件: {}, 大小: {} 字节, 提取类型: {}", 
                file.getOriginalFilename(), file.getSize(), extractionType);
        
        String fileContent;
        String filename = file.getOriginalFilename();

        try {
            // 根据文件类型选择解析方法
            if (filename != null && filename.toLowerCase().endsWith(".docx")) {
                // 使用POI处理Word文档
                try (XWPFDocument document = new XWPFDocument(file.getInputStream())) {
                    XWPFWordExtractor extractor = new XWPFWordExtractor(document);
                    fileContent = extractor.getText();
                    log.info("使用POI解析Word文档成功，内容长度: {} 字符", fileContent.length());
                }
            } else {
                // 使用Tika解析其他格式文件
                fileContent = tika.parseToString(file.getInputStream());
                log.info("使用Tika解析文件成功，内容长度: {} 字符", fileContent.length());
            }

            // 如果内容为空，记录警告
            if (fileContent == null || fileContent.trim().isEmpty()) {
                log.warn("文件解析结果为空");
                fileContent = "[文件内容为空或无法解析]";
            }
        } catch (Exception e) {
            log.error("文件解析失败: {}", e.getMessage(), e);
            return ApiResponse.error("文件解析失败: " + e.getMessage());
        }

        // 构建增强的提示词
        String finalPrompt;
        switch (extractionType) {
            case "summary":
                finalPrompt = "请提供以下文档内容的简洁摘要，突出关键信息和主要观点:\n";
                break;
            case "structure":
                finalPrompt = "请分析以下文档的结构，提取主要章节、标题和组织框架:\n";
                break;
            default:
                finalPrompt = "请解析以下文档内容，提供完整的信息分析:\n";
        }

        finalPrompt += prompt != null ? prompt + "\n" : "";
        finalPrompt += fileContent;

        // 调用文档解析服务
        Map<String, Object> result = new HashMap<>();
        String analysisResult = documentService.parseDocument(finalPrompt, userId, extractionType);
        
        result.put("filename", filename);
        result.put("extractionType", extractionType);
        result.put("contentLength", fileContent.length());
        result.put("analysis", analysisResult);
        
        return ApiResponse.success("文档解析成功", result);
    }
}
