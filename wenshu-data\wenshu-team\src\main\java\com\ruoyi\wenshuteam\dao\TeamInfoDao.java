package com.ruoyi.wenshuteam.dao;

import com.ruoyi.wenshuapi.pojo.team.TeamInfo;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 团队信息数据访问层 (DAO)
 * 使用 MyBatis 注解方式实现数据库操作
 * 对应数据库表：wenshu_team_info
 */
@Mapper
public interface TeamInfoDao {

    /**
     * 插入团队信息
     * 
     * @param teamInfo 团队信息实体对象
     * @return 受影响的行数
     */
    @Insert("INSERT INTO wenshu_team_info (" +
            "team_name, leader_id, creator_id, create_time, description, status" +
            ") VALUES (" +
            "#{teamName}, #{leaderId}, #{creatorId}, #{createTime}, #{description}, #{status}" +
            ")")
    @Options(useGeneratedKeys = true, keyProperty = "teamId", keyColumn = "team_id")
    int insertTeam(TeamInfo teamInfo);

    /**
     * 根据团队ID删除团队信息
     * 
     * @param teamId 团队ID
     * @return 受影响的行数
     */
    @Delete("DELETE FROM wenshu_team_info WHERE team_id = #{teamId}")
    int deleteByTeamId(@Param("teamId") Integer teamId);

    /**
     * 更新团队信息
     * 
     * @param teamInfo 团队信息实体对象
     * @return 受影响的行数
     */
    @Update("UPDATE wenshu_team_info SET " +
            "team_name = #{teamName}, " +
            "leader_id = #{leaderId}, " +
            "creator_id = #{creatorId}, " +
            "create_time = #{createTime}, " +
            "description = #{description}, " +
            "status = #{status} " +
            "WHERE team_id = #{teamId}")
    int updateTeam(TeamInfo teamInfo);

    /**
     * 根据团队ID查询团队信息
     * 
     * @param teamId 团队ID
     * @return 团队信息实体对象
     */
    @Select("SELECT " +
            "team_id, team_name, leader_id, creator_id, create_time, description, status " +
            "FROM wenshu_team_info " +
            "WHERE team_id = #{teamId}")
    @Results(id = "teamResultMap", value = {
            @Result(property = "teamId", column = "team_id", id = true),
            @Result(property = "teamName", column = "team_name"),
            @Result(property = "leaderId", column = "leader_id"),
            @Result(property = "creatorId", column = "creator_id"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "description", column = "description"),
            @Result(property = "status", column = "status")
    })
    TeamInfo selectByTeamId(@Param("teamId") Integer teamId);

    /**
     * 查询所有团队信息列表
     * 
     * @return 团队信息列表
     */
    @Select("SELECT * FROM wenshu_team_info")
    @ResultMap("teamResultMap")
    List<TeamInfo> selectAllTeams();

    /**
     * 根据状态查询团队列表
     * 
     * @param status 团队状态 (0-禁用, 1-启用)
     * @return 符合条件的团队列表
     */
    @Select("SELECT * FROM wenshu_team_info WHERE status = #{status}")
    @ResultMap("teamResultMap")
    List<TeamInfo> selectByStatus(@Param("status") Byte status);

    /**
     * 根据团队名称模糊查询
     * 
     * @param teamName 团队名称关键字
     * @return 符合条件的团队列表
     */
    @Select("SELECT * FROM wenshu_team_info WHERE team_name LIKE CONCAT('%', #{teamName}, '%')")
    @ResultMap("teamResultMap")
    List<TeamInfo> selectByTeamName(@Param("teamName") String teamName);
}