package com.ruoyi.wenshucommon.util.voiceutil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 语音助手提示词工具类
 * 提供不同场景下的 AI 提示词模板
 */
public class VoiceAgents {

    public static String agentSwitchPrompt =
            "当前时间：" + TimeNow() +
                    "\n请严格按规则判断语音识别内容意图（需处理错别字/语义联想）：" +
                    "\n\n【核心规则】" +
                    "\n1. 仅输出四字结果：普通对话、日程创建 或 日程查询" +
                    "\n2. 禁止任何其他文本/符号/解释" +
                    "\n\n【意图判断标准】" +
                    "\n- 日程创建：当内容包含以下任一特征时触发" +
                    "\n  a) 明确安排新事件（如：开会、见面、提醒）" +
                    "\n  b) 询问他人时间可用性（如：'你明天有空吗'）" +
                    "\n  c) 包含时间+地点+事件的组合（如：'明天下午3点302教室开会'）" +
                    "\n  d) 修改/调整现有日程" +
                    "\n" +
                    "\n- 日程查询：仅当内容为纯查询且不涉及新建/修改时触发" +
                    "\n  a) 查询已有日程（如：'我明天有什么安排'）" +
                    "\n  b) 不包含具体行动请求的单纯时间询问（如：'现在几点'）" +
                    "\n" +
                    "\n- 普通对话：不符合以上日程特征的其他内容" +
                    "\n\n【关键区别】" +
                    "\n• 询问时间可用性 → 日程创建（因隐含新建意图）" +
                    "\n• 询问现有日程 → 日程查询" +
                    "\n• 讨论日程但不操作 → 普通对话" +
                    "\n\n【待识别内容】：\n";
    
    /**
     * 添加日程的提示词
     */
    public static String agentAddSchedulePrompt="当前时间："+TimeNow()+
            "\n你会基于我以下的文本进行判断，以下文本为语音识别内容，请对错别字以及混乱字义进行联想" +
            "\n输出示例：" +
            "{\n" +
            "  \"title\": \"日程标题\",\n" +
            "  \"description\": \"描述信息，同时用于提取时间信息，如\\\"下午5点开会，地点在明行楼\\\"\",\n" +
            "  \"start_time\": \"日程开始时间（如果设置了此字段，则优先使用）\",\n" +
            "  \"owner_type\": \"所有者类型（个人、团队）\",\n" +
            "  \"repeat_rule\": \"重复规则（每天重复、一次性）\",\n" +
            "  \"remind_time\": \"提醒时间（默认提前十分钟，可修改）\",\n" +
            "  \"event_status\": \"日程状态(0-取消,1-正常)\"\n" +
            "}" +
            "\n输出要求：" +
            "\n1.基于给定识别文本内容对给定示例进行日程内容填充" +
            "\n2.未明确的内容可虚构，但要符合客观规律"+
            "\n3.除了给定示例内容，不允许输出多余内容" +
            "\n4.输出内容符合给定的json格式，不允许出现多余内容" +
            "\n5.去除特殊符合，只能使用 {} [] , : 等符合json格式的符号" +
            "\n6.不使用java的转义符号，去除示例中本身带有的转义符号" +
            "\n以下是我的识别内容：\n";
    
    /**
     * 查询日程的提示词
     */
    public static String agentLookSchedulePrompt="当前时间："+TimeNow()+
            "\n你会基于我以下的文本进行回复," +
            "以下文本为语音识别内容，请对错别字以及混乱字义进行联想" +
            "输出示例：" +
            "{" +
            "\"startTime\":\"开始时间戳\"" +
            "\"endTime\":\"结束时间戳\"" +
            "}\n" +
            "输出要求：" +
            "1.查询开始时间戳  格式为：yyyy-MM-dd HH:00:00" +
            "2.查询结束时间戳  格式为：yyyy-MM-dd HH:00:00" +
            "\n3.除了给定示例内容，不允许输出多余内容" +
            "\n4.输出内容符合给定的json格式，不允许出现多余内容" +
            "\n5.去除特殊符合，只能使用 {} [] , : 等符合json格式的符号" +
            "\n6.不使用java的转义符号，去除示例中本身带有的转义符号" +
            "\n7.不可以带多余符号和字符" +
            "\n8.不需要解释说明，只输出示例" +
            "\n以下是我的识别内容：\n";
    
    /**
     * 聊天的提示词
     */
    public static String agentChatPrompt="当前时间："+TimeNow()+
            "\n你会基于我以下的文本进行回复,满足基础聊天需求，" +
        "以下文本为语音识别内容，请对错别字以及混乱字义进行联想" +
        "输出要求：" +
        "1.分析识别内容进行回复" +
        "2.以聊天的方式进行回复" +
        "3.我不需要知道你怎么联想的，你只要对给定内容进行回复即可" +
            "\n以下是我的识别内容：\n";
    
    /**
     * 根据用户意图切换不同的提示词
     * 
     * @param switchPrompt 用户意图
     * @return 对应的提示词模板
     */
    public static String ifSwitch(String switchPrompt){
        if(switchPrompt!=null){
            if("普通对话".equals(switchPrompt)){
                return agentChatPrompt;
            }else if("日程创建".equals(switchPrompt)){
                return agentAddSchedulePrompt;
            }else if("日程查询".equals(switchPrompt)){
                return agentLookSchedulePrompt;
            }

        }
        return null;
    }
    
    /**
     * 获取当前时间的格式化字符串
     * 
     * @return 格式化的当前时间
     */
    static String TimeNow(){
        // 获取当前时间（包含年月日时分秒）
        LocalDateTime now = LocalDateTime.now();

        // 定义格式化模式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 格式化时间
        return now.format(formatter);
    }
} 