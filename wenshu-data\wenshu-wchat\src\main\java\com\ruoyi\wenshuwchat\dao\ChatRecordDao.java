package com.ruoyi.wenshuwchat.dao;

import com.ruoyi.wenshuapi.pojo.wchat.ChatRecord;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天记录数据访问层
 * 提供对wenshu-chat_records表的CRUD操作
 */
@Mapper
public interface ChatRecordDao {

    /**
     * 插入新的聊天记录
     *
     * @param chatRecord 聊天记录对象
     * @return 插入操作影响的行数
     */
    @Insert("INSERT INTO `wenshu-chat_records` " +
            "(session_id, sender_id, receiver_id, send_time, content, content_type, is_read) " +
            "VALUES (#{sessionId}, #{senderId}, #{receiverId}, #{sendTime}, #{content}, #{contentType}, #{isRead})")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insert(ChatRecord chatRecord);

    /**
     * 根据ID删除聊天记录
     *
     * @param id 消息ID
     * @return 删除操作影响的行数
     */
    @Delete("DELETE FROM `wenshu-chat_records` WHERE id = #{id}")
    int deleteById(@Param("id") Long id);

    /**
     * 批量删除会话的聊天记录
     *
     * @param sessionId 会话ID
     * @return 删除操作影响的行数
     */
    @Delete("DELETE FROM `wenshu-chat_records` WHERE session_id = #{sessionId}")
    int deleteBySessionId(@Param("sessionId") String sessionId);

    /**
     * 更新消息已读状态
     *
     * @param id 消息ID
     * @param isRead 是否已读
     * @return 更新操作影响的行数
     */
    @Update("UPDATE `wenshu-chat_records` SET is_read = #{isRead} WHERE id = #{id}")
    int updateReadStatus(@Param("id") Long id, @Param("isRead") Boolean isRead);

    /**
     * 批量更新会话中接收者的消息为已读
     *
     * @param sessionId 会话ID
     * @param receiverId 接收者ID
     * @return 更新操作影响的行数
     */
    @Update("UPDATE `wenshu-chat_records` SET is_read = 1 " +
            "WHERE session_id = #{sessionId} AND receiver_id = #{receiverId} AND is_read = 0")
    int markSessionMessagesAsRead(@Param("sessionId") String sessionId, @Param("receiverId") Long receiverId);

    /**
     * 根据ID查询聊天记录
     *
     * @param id 消息ID
     * @return 聊天记录对象
     */
    @Select("SELECT id, session_id, sender_id, receiver_id, send_time, content, content_type, is_read " +
            "FROM `wenshu-chat_records` WHERE id = #{id}")
    @Results(id = "chatRecordResultMap", value = {
            @Result(property = "id", column = "id", id = true),
            @Result(property = "sessionId", column = "session_id"),
            @Result(property = "senderId", column = "sender_id"),
            @Result(property = "receiverId", column = "receiver_id"),
            @Result(property = "sendTime", column = "send_time"),
            @Result(property = "content", column = "content"),
            @Result(property = "contentType", column = "content_type"),
            @Result(property = "isRead", column = "is_read")
    })
    ChatRecord selectById(@Param("id") Long id);

    /**
     * 根据会话ID查询聊天记录（按时间排序）
     *
     * @param sessionId 会话ID
     * @return 聊天记录列表
     */
    @Select("SELECT id, session_id, sender_id, receiver_id, send_time, content, content_type, is_read " +
            "FROM `wenshu-chat_records` WHERE session_id = #{sessionId} ORDER BY send_time ASC")
    @ResultMap("chatRecordResultMap")
    List<ChatRecord> selectBySessionId(@Param("sessionId") String sessionId);

    /**
     * 分页查询会话聊天记录
     *
     * @param sessionId 会话ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 聊天记录列表
     */
    @Select("SELECT id, session_id, sender_id, receiver_id, send_time, content, content_type, is_read " +
            "FROM `wenshu-chat_records` WHERE session_id = #{sessionId} " +
            "ORDER BY send_time DESC LIMIT #{offset}, #{limit}")
    @ResultMap("chatRecordResultMap")
    List<ChatRecord> selectBySessionIdWithPaging(@Param("sessionId") String sessionId, 
                                                 @Param("offset") int offset, 
                                                 @Param("limit") int limit);

    /**
     * 查询用户的未读消息数量
     *
     * @param receiverId 接收者ID
     * @return 未读消息数量
     */
    @Select("SELECT COUNT(*) FROM `wenshu-chat_records` " +
            "WHERE receiver_id = #{receiverId} AND is_read = 0")
    int countUnreadMessages(@Param("receiverId") Long receiverId);

    /**
     * 查询用户的未读消息列表
     *
     * @param receiverId 接收者ID
     * @return 未读消息列表
     */
    @Select("SELECT id, session_id, sender_id, receiver_id, send_time, content, content_type, is_read " +
            "FROM `wenshu-chat_records` WHERE receiver_id = #{receiverId} AND is_read = 0 " +
            "ORDER BY send_time DESC")
    @ResultMap("chatRecordResultMap")
    List<ChatRecord> selectUnreadMessages(@Param("receiverId") Long receiverId);

    /**
     * 查询指定会话的未读消息数量
     *
     * @param sessionId 会话ID
     * @param receiverId 接收者ID
     * @return 未读消息数量
     */
    @Select("SELECT COUNT(*) FROM `wenshu-chat_records` " +
            "WHERE session_id = #{sessionId} AND receiver_id = #{receiverId} AND is_read = 0")
    int countUnreadMessagesBySession(@Param("sessionId") String sessionId, @Param("receiverId") Long receiverId);

    /**
     * 查询会话的最新一条消息
     *
     * @param sessionId 会话ID
     * @return 最新的聊天记录
     */
    @Select("SELECT id, session_id, sender_id, receiver_id, send_time, content, content_type, is_read " +
            "FROM `wenshu-chat_records` WHERE session_id = #{sessionId} " +
            "ORDER BY send_time DESC LIMIT 1")
    @ResultMap("chatRecordResultMap")
    ChatRecord selectLatestBySessionId(@Param("sessionId") String sessionId);

    /**
     * 根据时间范围查询聊天记录
     *
     * @param sessionId 会话ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 聊天记录列表
     */
    @Select("SELECT id, session_id, sender_id, receiver_id, send_time, content, content_type, is_read " +
            "FROM `wenshu-chat_records` " +
            "WHERE session_id = #{sessionId} AND send_time BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY send_time ASC")
    @ResultMap("chatRecordResultMap")
    List<ChatRecord> selectBySessionIdAndTimeRange(@Param("sessionId") String sessionId,
                                                   @Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);
}
