package com.ruoyi.wenshucalebdar.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.wenshuapi.pojo.programme.CalendarParticipant;
import com.ruoyi.wenshuapi.pojo.programme.Programme;
import com.ruoyi.wenshucalebdar.service.CalendarParticipantService;
import com.ruoyi.wenshucalebdar.service.ProgrammeService;
import com.ruoyi.wenshucalebdar.common.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/programme")
public class ProgrammeController {
    private final ProgrammeService programmeService;
    private final CalendarParticipantService cal;
    
    @PostMapping("/add/{userId}")
    public ApiResponse<?> addProgramme(@PathVariable("userId") Long userId, @RequestBody Programme programme) {
        try {
            // 确保新记录的ID为null或0
            if (programme.getEventId() <= 0) {
                programme.setEventId(0);
            }
            
            // 保存日程
            boolean success = programmeService.save(programme);
            
            if (success) {
                // 获取新生成的ID
                int eventId = programme.getEventId();
                
                // 创建参与者记录
                CalendarParticipant c = new CalendarParticipant();
                c.setEventId(eventId);
                c.setUserId(userId);
                cal.save(c);
                
                return ApiResponse.success(programme, "添加日程成功");
            } else {
                return ApiResponse.failed("添加日程失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ApiResponse.failed("添加日程失败: " + e.getMessage());
        }
    }
    /**
     * 删除日程
     * @param event_id 日程ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{event_id}")
    public ApiResponse<?> deleteProgramme(@PathVariable("event_id") int event_id) {
        // 使用服务层方法删除日程
        boolean success = programmeService.removeById(event_id);
        if (success) {
            return ApiResponse.success(null, "删除日程成功");
        } else {
            return ApiResponse.failed("删除日程失败");
        }
    }
    /*
     * 根据时间范围查询所有日程
     * @param eventIds 事件ID列表
     * @param startTime 开始时间，格式：yyyy-MM-dd HH:mm:ss
     * @param endTime 结束时间，格式：yyyy-MM-dd HH:mm:ss
     * @return 日程列表
     */
    @GetMapping("/getByTime")
    public ApiResponse<?> listProgramme(@RequestParam(required = false, value = "userId") Long userId,
                                       @RequestParam(required = false, value = "startTime") String startTime,
                                       @RequestParam(required = false, value = "endTime") String endTime) {
        try {
            LocalDateTime startDateTime = null;
            LocalDateTime endDateTime = null;
            
            // 转换时间字符串为LocalDateTime
            if (startTime != null && !startTime.isEmpty()) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                try {
                    startDateTime = LocalDateTime.parse(startTime, formatter);
                } catch (Exception e) {
                    return ApiResponse.failed("开始时间格式错误，请使用yyyy-MM-dd HH:mm:ss格式");
                }
            }
            
            if (endTime != null && !endTime.isEmpty()) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                try {
                    endDateTime = LocalDateTime.parse(endTime, formatter);
                } catch (Exception e) {
                    return ApiResponse.failed("结束时间格式错误，请使用yyyy-MM-dd HH:mm:ss格式");
                }
            }
            
            // 查询日程
            List<Programme> programmes = programmeService.getProgrammesByTimeRange(startDateTime, endDateTime, userId);
            
            return ApiResponse.success(programmes, "查询日程成功");
        } catch (Exception e) {
            e.printStackTrace();
            return ApiResponse.failed("查询日程失败: " + e.getMessage());
        }
    }

    @GetMapping("/getById/{event_id}")
    public ApiResponse<?> getById(@PathVariable("event_id") int event_id) {
        Programme programme = programmeService.getOne(new QueryWrapper<Programme>().eq("event_id", event_id));

        return ApiResponse.success(programme, "查询日程成功");
    }

    @PutMapping("/update/{event_id}")
    public ApiResponse<?> updateProgramme(@PathVariable("event_id") int event_id, @RequestBody Programme programme) {
        programme.setEventId(event_id);
        boolean success = programmeService.updateById(programme); // 更新日程

        if (success) {
            return ApiResponse.success("", "修改成功");
        } else {
            return ApiResponse.failed("修改失败");
        }
    }

    @GetMapping("/getAll")
    public ApiResponse<?> getAll(@RequestParam(required = false,value = "userId") int userId) {
        List<Programme> programmes;
        programmes = programmeService.getProgrammesByUserId((long)userId);
        return ApiResponse.success(programmes, "查询日程成功");
    }
    
    @GetMapping("/list")
    public List<CalendarParticipant> list(@RequestParam(required = false,value = "userId") int userId) {
        List<CalendarParticipant> userId1 = cal.list(new QueryWrapper<CalendarParticipant>().eq("user_id", userId));
        return userId1;
    }
    
    @GetMapping("/getAllProgrammes")
    public List<Programme> ProgetAll(@RequestParam(required = false,value = "userId") Long userId) {
        return programmeService.getProgrammesByUserId(userId);
    }
}
