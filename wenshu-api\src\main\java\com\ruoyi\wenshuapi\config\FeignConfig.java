package com.ruoyi.wenshuapi.config;


import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ruoyi.wenshuapi.fallback.file.FileInfoClientFallback;
import com.ruoyi.wenshuapi.fallback.file.FileUserTeamClientFallback;
import com.ruoyi.wenshuapi.fallback.remind.EmailServiceClientFallback;
import com.ruoyi.wenshuapi.fallback.team.TeamInfoClientFallbackFactory;
import com.ruoyi.wenshuapi.fallback.team.TeamMemberClientFallback;
import com.ruoyi.wenshuapi.fallback.wchat.ChatRecordClientFallback;
import com.ruoyi.wenshuapi.fallback.wchat.FriendListClientFallback;
import feign.Logger;
import feign.Response;
import feign.codec.Decoder;
import feign.codec.Encoder;
import feign.codec.ErrorDecoder;
import feign.form.spring.SpringFormEncoder;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Configuration
public class FeignConfig {
    private static final org.slf4j.Logger log = LoggerFactory.getLogger(FeignConfig.class);

    //交给ioc管理
    @Bean
    public FileUserTeamClientFallback fileUserTeamClientFallback(){
        return  new FileUserTeamClientFallback();
    }
    @Bean
    public FileInfoClientFallback fileInfoClientFallback(){
        return  new FileInfoClientFallback();
    }
    @Bean
    public TeamInfoClientFallbackFactory teamInfoClientFallbackFactory(){
        return  new TeamInfoClientFallbackFactory();
    }
    @Bean
    public EmailServiceClientFallback emailServiceClientFallback(){
        return  new EmailServiceClientFallback();
    }
    @Bean
    public TeamMemberClientFallback teamMemberClientFallback(){
        return  new TeamMemberClientFallback();
    }
    @Bean
    public ChatRecordClientFallback chatRecordClientFallback(){
        return  new ChatRecordClientFallback();
    }
    @Bean
    public FriendListClientFallback friendListClientFallback(){
        return  new FriendListClientFallback();
    }
    /**
     * 自定义线程池解决关闭冲突
     */
    @Bean(name = "applicationTaskExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(25);
        executor.setThreadNamePrefix("Feign-Executor-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        return executor;
    }
    
    @Bean
    Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }
    
    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        return objectMapper;
    }
    
    /**
     * 自定义编码器，支持更大的请求体
     */
    @Bean
    public Encoder feignEncoder() {
        MappingJackson2HttpMessageConverter jacksonConverter = new MappingJackson2HttpMessageConverter(objectMapper());
        // 设置更大的缓冲区
        jacksonConverter.setDefaultCharset(StandardCharsets.UTF_8);
        
        ObjectFactory<HttpMessageConverters> objectFactory = () -> {
            List<HttpMessageConverter<?>> converters = new ArrayList<>();
            converters.add(jacksonConverter);
            converters.add(new StringHttpMessageConverter(StandardCharsets.UTF_8));
            return new HttpMessageConverters(converters);
        };
        
        return new SpringFormEncoder(new SpringEncoder(objectFactory));
    }
    
    /**
     * 自定义解码器，支持更大的响应体和text/plain类型
     */
    @Bean
    public Decoder feignDecoder() {
        MappingJackson2HttpMessageConverter jacksonConverter = new MappingJackson2HttpMessageConverter(objectMapper());
        // 设置更大的缓冲区
        jacksonConverter.setDefaultCharset(StandardCharsets.UTF_8);

        // 创建支持text/plain类型的StringHttpMessageConverter
        StringHttpMessageConverter stringConverter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
        // 添加对text/plain类型的支持
        stringConverter.setSupportedMediaTypes(Arrays.asList(
            MediaType.TEXT_PLAIN,
            MediaType.APPLICATION_JSON,
            MediaType.ALL
        ));

        ObjectFactory<HttpMessageConverters> objectFactory = () -> {
            List<HttpMessageConverter<?>> converters = new ArrayList<>();
            // 先添加String转换器，确保text/plain类型能被正确处理
            converters.add(stringConverter);
            converters.add(jacksonConverter);
            return new HttpMessageConverters(converters);
        };

        return new ResponseEntityDecoder(new SpringDecoder(objectFactory));
    }
    
    @Bean
    public ErrorDecoder errorDecoder() {
        return new CustomErrorDecoder();
    }
    
    public static class CustomErrorDecoder implements ErrorDecoder {
        private final ErrorDecoder defaultErrorDecoder = new Default();
        
        @Override
        public Exception decode(String methodKey, Response response) {
            Exception exception = defaultErrorDecoder.decode(methodKey, response);
            
            log.error("Feign client error: {} - {} - {}", 
                methodKey, 
                response.status(), 
                exception.getMessage());
            
            return exception;
        }
    }
}
