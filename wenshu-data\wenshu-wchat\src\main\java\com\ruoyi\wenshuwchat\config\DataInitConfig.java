//package com.ruoyi.wenshuwchat.config;
//
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Configuration;
//
///**
// * 数据初始化配置
// */
//@Configuration
//@ConfigurationProperties(prefix = "wchat.data.init")
//public class DataInitConfig {
//
//    /**
//     * 是否启用数据初始化
//     */
//    private boolean enabled = true;
//
//    /**
//     * 好友关系数量
//     */
//    private int friendCount = 30;
//
//    /**
//     * 聊天记录数量
//     */
//    private int chatCount = 70;
//
//    /**
//     * 用户ID范围 - 最小值
//     */
//    private Long minUserId = 1001L;
//
//    /**
//     * 用户ID范围 - 最大值
//     */
//    private Long maxUserId = 1020L;
//
//    public boolean isEnabled() {
//        return enabled;
//    }
//
//    public void setEnabled(boolean enabled) {
//        this.enabled = enabled;
//    }
//
//    public int getFriendCount() {
//        return friendCount;
//    }
//
//    public void setFriendCount(int friendCount) {
//        this.friendCount = friendCount;
//    }
//
//    public int getChatCount() {
//        return chatCount;
//    }
//
//    public void setChatCount(int chatCount) {
//        this.chatCount = chatCount;
//    }
//
//    public Long getMinUserId() {
//        return minUserId;
//    }
//
//    public void setMinUserId(Long minUserId) {
//        this.minUserId = minUserId;
//    }
//
//    public Long getMaxUserId() {
//        return maxUserId;
//    }
//
//    public void setMaxUserId(Long maxUserId) {
//        this.maxUserId = maxUserId;
//    }
//}
