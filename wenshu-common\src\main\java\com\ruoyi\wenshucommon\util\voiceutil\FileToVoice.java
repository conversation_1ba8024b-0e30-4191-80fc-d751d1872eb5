package com.ruoyi.wenshucommon.util.voiceutil;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * 音频文件转语音文本工具类
 * 支持从资源目录或指定路径加载音频文件并进行语音识别
 */
public class FileToVoice {
    /**
     * 通过文件名调用语音识别
     * 
     * @param filename 资源目录下的文件名
     * @param form 音频格式
     * @return [0]:语音识别结果, [1]:翻译结果
     */
    public String[] consFileName(String filename, String form) throws Exception {
        Path tempFile = loadResourceToTempFile("temp/" + filename); // filename="xxx.wav"
        ExecutorService executorService = Executors.newSingleThreadExecutor();

        Future<String[]> future = executorService.submit(new RealtimeTranslateTask(tempFile, form));
        String[] results = future.get();

        executorService.shutdown();
        return results;
    }
    
    /**
     * 通过文件路径调用语音识别
     * 
     * @param filepath 音频文件路径
     * @param form 音频格式
     * @return [0]:语音识别结果, [1]:翻译结果
     */
    public String[] consFilePath(Path filepath, String form) throws Exception {
        ExecutorService executorService = Executors.newSingleThreadExecutor();

        Future<String[]> future = executorService.submit(new RealtimeTranslateTask(filepath, form));
        String[] results = future.get();

        executorService.shutdown();
        return results;
    }

    /**
     * 将资源文件加载到临时文件
     */
    private static Path loadResourceToTempFile(String resourcePath) throws IOException {
        InputStream inputStream = FileToVoice.class.getClassLoader().getResourceAsStream(resourcePath);
        if (inputStream == null) {
            throw new FileNotFoundException("Resource not found: " + resourcePath);
        }

        String fileName = resourcePath.substring(resourcePath.lastIndexOf('/') + 1);
        Path tempFile = Files.createTempFile("audio-", "-" + fileName);
        Files.copy(inputStream, tempFile, StandardCopyOption.REPLACE_EXISTING);
        tempFile.toFile().deleteOnExit();
        return tempFile;
    }
} 