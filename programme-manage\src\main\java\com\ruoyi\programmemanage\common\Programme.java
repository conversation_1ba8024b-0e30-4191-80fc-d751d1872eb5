package com.ruoyi.programmemanage.common;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 日程实体类
 */
@Data
public class Programme {
    /**
     * 主键id
     */
    private int eventId;
    
    /**
     * 日程标题
     */
    private String title;
    
    /**
     * 日程描述
     */
    private String description;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 日程开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 创建者ID (外键sys_user.id)
     */
    private int creatorId;
    
    /**
     * 所有者类型（个人、团队）
     */
    private String ownerType;
    
    /**
     * 重复规则（每天重复、一次性)
     */
    private String repeatRule;
    
    /**
     * 提醒时间（默认提前十分钟，可修改）
     */
    private LocalDateTime remindTime;
    
    /**
     * 日程状态(0-取消,1-正常)
     */
    private int eventStatus;
} 