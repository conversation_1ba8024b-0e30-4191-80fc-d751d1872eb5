//package com.ruoyi.wenshuteam.init;
//
//import com.ruoyi.wenshuapi.pojo.team.TeamUserRelation;
//import com.ruoyi.wenshuteam.dao.TeamMemberDao;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.List;
//import java.util.Random;
//
///**
// * 团队数据初始化器
// * 在应用启动时自动注入虚拟的团队-成员关系数据
// */
//@Component
//public class TeamDataInitializers implements CommandLineRunner {
//
//    private final TeamMemberDao teamMemberDao;
//
//    // 总成员数（用户ID范围）
//    private static final int TOTAL_USERS = 100;
//    // 总团队数
//    private static final int TOTAL_TEAMS = 100;
//    // 每个团队最小成员数
//    private static final int MIN_MEMBERS_PER_TEAM = 5;
//    // 每个团队最大成员数
//    private static final int MAX_MEMBERS_PER_TEAM = 6;
//
//    @Autowired
//    public TeamDataInitializers(TeamMemberDao teamMemberDao) {
//        this.teamMemberDao = teamMemberDao;
//    }
//
//    @Override
//    public void run(String... args) throws Exception {
//        // 检查是否已有数据
//        if (hasExistingData()) {
//            System.out.println("团队-成员关系表中已有数据，跳过虚拟数据注入");
//            return;
//        }
//
//        // 生成虚拟数据
//        List<TeamUserRelation> relations = generateDummyData();
//
//        // 批量插入数据库
//        insertData(relations);
//
//        System.out.println("成功注入 " + relations.size() + " 条虚拟团队-成员关系数据");
//    }
//
//    /**
//     * 检查数据库中是否已有团队-成员关系数据
//     */
//    private boolean hasExistingData() {
//        try {
//            // 尝试获取团队1的成员数量
//            return teamMemberDao.countMembersByTeamId(1) > 0;
//        } catch (Exception e) {
//            // 表可能不存在或其他异常，视为无数据
//            return false;
//        }
//    }
//
//    /**
//     * 生成虚拟的团队-成员关系数据
//     *
//     * @return 生成的虚拟数据列表
//     */
//    private List<TeamUserRelation> generateDummyData() {
//        List<TeamUserRelation> relations = new ArrayList<>();
//        Random random = new Random();
//
//        // 创建所有可能的用户ID列表 (1到100)
//        List<Integer> allUserIds = new ArrayList<>();
//        for (int i = 1; i <= TOTAL_USERS; i++) {
//            allUserIds.add(i);
//        }
//
//        // 为每个团队生成成员
//        for (int teamId = 1; teamId <= TOTAL_TEAMS; teamId++) {
//            // 随机决定该团队成员数量 (5或6)
//            int memberCount = MIN_MEMBERS_PER_TEAM + random.nextInt(2);
//
//            // 打乱所有用户ID列表
//            Collections.shuffle(allUserIds);
//
//            // 为该团队选择成员
//            for (int i = 0; i < memberCount; i++) {
//                // 循环使用用户ID，避免耗尽
//                int userIdIndex = (teamId * memberCount + i) % allUserIds.size();
//                int userId = allUserIds.get(userIdIndex);
//
//                relations.add(new TeamUserRelation(teamId, userId));
//            }
//        }
//
//        return relations;
//    }
//
//    /**
//     * 将生成的虚拟数据插入数据库
//     *
//     * @param relations 要插入的关系列表
//     */
//    private void insertData(List<TeamUserRelation> relations) {
//        for (TeamUserRelation relation : relations) {
//            try {
//                teamMemberDao.insert(relation);
//            } catch (Exception e) {
//                System.err.println("插入虚拟数据失败: " + relation);
//                // 在实际应用中可能需要更详细的错误处理
//            }
//        }
//    }
//}