package com.ruoyi.wenshufile.service.impl;

import com.ruoyi.wenshufile.exception.FileStorageException;
import com.ruoyi.wenshufile.service.FileStorageService;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 文件存储服务实现类
 * 提供本地文件存储的具体实现
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-28
 */
@Service
public class FileStorageServiceImpl implements FileStorageService {

    private static final Logger logger = LoggerFactory.getLogger(FileStorageServiceImpl.class);

    /**
     * 文件存储根路径
     */
    @Value("${wenshu.file.storage.path:D:/wenshu/file-storage}")
    private String fileStorageBasePath;

    /**
     * 文件访问域名
     */
    @Value("${wenshu.file.domain:http://localhost:1016}")
    private String fileDomain;

    /**
     * 文件访问前缀
     */
    @Value("${wenshu.file.prefix:/wenshu-files}")
    private String filePrefix;

    /**
     * 最大文件大小（10GB）
     */
    @Value("${wenshu.file.max-size:10737418240}")
    private long maxFileSize;

    /**
     * 最大文件名长度
     */
    private static final int MAX_FILENAME_LENGTH = 255;

    @Override
    public String uploadFile(MultipartFile file, Integer uploaderId) throws Exception {
        logger.info("开始上传文件: {}, 上传者ID: {}, 文件大小: {} bytes", 
                   file.getOriginalFilename(), uploaderId, file.getSize());

        // 1. 验证文件
        if (!validateFile(file)) {
            throw new FileStorageException(FileStorageException.FILE_NAME_INVALID, "文件验证失败");
        }

        try {
            // 2. 生成文件存储路径
            String relativePath = generateStoragePath(file, uploaderId);
            
            // 3. 创建完整路径
            Path fullPath = Paths.get(fileStorageBasePath, relativePath);
            
            // 4. 确保目录存在
            Files.createDirectories(fullPath.getParent());
            
            // 5. 保存文件
            file.transferTo(fullPath.toFile());
            
            logger.info("文件上传成功: {} -> {}", file.getOriginalFilename(), relativePath);
            return relativePath;
            
        } catch (IOException e) {
            logger.error("文件上传失败: {}", file.getOriginalFilename(), e);
            throw new FileStorageException(FileStorageException.UPLOAD_FAILED,
                                         "文件上传失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean deleteFile(String filePath) {
        if (!StringUtils.hasText(filePath)) {
            logger.warn("删除文件失败: 文件路径为空");
            return false;
        }

        try {
            Path fullPath = Paths.get(fileStorageBasePath, filePath);
            boolean deleted = Files.deleteIfExists(fullPath);
            
            if (deleted) {
                logger.info("文件删除成功: {}", filePath);
                // 尝试删除空的父目录
                cleanupEmptyDirectories(fullPath.getParent());
            } else {
                logger.warn("文件删除失败: 文件不存在 - {}", filePath);
            }
            
            return deleted;
        } catch (IOException e) {
            logger.error("文件删除失败: {}", filePath, e);
            return false;
        }
    }

    @Override
    public boolean fileExists(String filePath) {
        if (!StringUtils.hasText(filePath)) {
            return false;
        }
        
        Path fullPath = Paths.get(fileStorageBasePath, filePath);
        return Files.exists(fullPath) && Files.isRegularFile(fullPath);
    }

    @Override
    public String getFileAccessUrl(String filePath) {
        if (!StringUtils.hasText(filePath)) {
            return null;
        }
        
        // 确保路径以正斜杠开头
        String normalizedPath = filePath.startsWith("/") ? filePath : "/" + filePath;
        return fileDomain + filePrefix + normalizedPath;
    }

    @Override
    public boolean validateFile(MultipartFile file) {
        // 1. 检查文件是否为空
        if (file == null || file.isEmpty()) {
            logger.warn("文件验证失败: 文件为空");
            return false;
        }

        // 2. 检查文件大小
        if (file.getSize() > maxFileSize) {
            logger.warn("文件验证失败: 文件大小超过限制 - {} bytes > {} bytes",
                       file.getSize(), maxFileSize);
            throw new FileStorageException(FileStorageException.FILE_TOO_LARGE,
                                         String.format("文件大小超过限制，最大允许 %d MB，当前文件 %.2f MB",
                                                      maxFileSize / 1024 / 1024,
                                                      file.getSize() / 1024.0 / 1024.0));
        }

        // 3. 检查文件名
        String originalFilename = file.getOriginalFilename();
        if (!StringUtils.hasText(originalFilename)) {
            logger.warn("文件验证失败: 文件名为空");
            return false;
        }

        if (originalFilename.length() > MAX_FILENAME_LENGTH) {
            logger.warn("文件验证失败: 文件名过长 - {} > {}", 
                       originalFilename.length(), MAX_FILENAME_LENGTH);
            return false;
        }

        // 4. 检查文件名中的非法字符
        if (containsIllegalCharacters(originalFilename)) {
            logger.warn("文件验证失败: 文件名包含非法字符 - {}", originalFilename);
            return false;
        }

        return true;
    }

    @Override
    public String getFileMimeType(MultipartFile file) {
        String contentType = file.getContentType();
        if (StringUtils.hasText(contentType)) {
            return contentType;
        }
        
        // 如果无法获取MIME类型，根据文件扩展名推断
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        return getMimeTypeByExtension(extension);
    }

    @Override
    public String generateUniqueFileName(String originalFilename) {
        if (!StringUtils.hasText(originalFilename)) {
            return UUID.randomUUID().toString();
        }
        
        String extension = FilenameUtils.getExtension(originalFilename);
        String uuid = UUID.randomUUID().toString().replace("-", "");
        
        if (StringUtils.hasText(extension)) {
            return uuid + "." + extension.toLowerCase();
        } else {
            return uuid;
        }
    }

    @Override
    public String getAbsolutePath(String relativePath) {
        if (!StringUtils.hasText(relativePath)) {
            return null;
        }
        
        return Paths.get(fileStorageBasePath, relativePath).toString();
    }

    @Override
    public void cleanupTempFiles() {
        // 实现临时文件清理逻辑
        // 这里可以添加定时清理临时文件的逻辑
        logger.info("执行临时文件清理");
    }

    /**
     * 生成文件存储路径
     * 格式: users/{uploaderId}/yyyy/MM/dd/{uniqueFileName}
     * 
     * @param file 上传的文件
     * @param uploaderId 上传者ID
     * @return 相对存储路径
     */
    private String generateStoragePath(MultipartFile file, Integer uploaderId) {
        // 生成唯一文件名
        String uniqueFileName = generateUniqueFileName(file.getOriginalFilename());
        
        // 创建目录结构：users/{uploaderId}/年月日/
        String userDir = uploaderId != null ? "users/" + uploaderId : "anonymous";
        String dateDir = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        
        return userDir + "/" + dateDir + "/" + uniqueFileName;
    }

    /**
     * 检查文件名是否包含非法字符
     * 
     * @param filename 文件名
     * @return 是否包含非法字符
     */
    private boolean containsIllegalCharacters(String filename) {
        // Windows和Linux系统的非法字符
        String illegalChars = "<>:\"|?*";
        for (char c : illegalChars.toCharArray()) {
            if (filename.indexOf(c) >= 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据文件扩展名获取MIME类型
     * 
     * @param extension 文件扩展名
     * @return MIME类型
     */
    private String getMimeTypeByExtension(String extension) {
        if (!StringUtils.hasText(extension)) {
            return "application/octet-stream";
        }
        
        switch (extension.toLowerCase()) {
            case "txt": return "text/plain";
            case "pdf": return "application/pdf";
            case "doc": return "application/msword";
            case "docx": return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xls": return "application/vnd.ms-excel";
            case "xlsx": return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "jpg": case "jpeg": return "image/jpeg";
            case "png": return "image/png";
            case "gif": return "image/gif";
            case "mp4": return "video/mp4";
            case "mp3": return "audio/mpeg";
            default: return "application/octet-stream";
        }
    }

    /**
     * 清理空的父目录
     * 
     * @param directory 目录路径
     */
    private void cleanupEmptyDirectories(Path directory) {
        try {
            if (Files.exists(directory) && Files.isDirectory(directory)) {
                // 检查目录是否为空
                if (Files.list(directory).findAny().isEmpty()) {
                    Files.delete(directory);
                    logger.debug("删除空目录: {}", directory);
                    // 递归删除父目录（如果也为空）
                    cleanupEmptyDirectories(directory.getParent());
                }
            }
        } catch (IOException e) {
            logger.debug("清理空目录失败: {}", directory, e);
        }
    }
}
