package com.ruoyi.wenshuwchat.dto;

import java.time.LocalDateTime;

/**
 * 发送消息响应DTO
 */
public class SendMessageResponse {
    
    private Long messageId;         // 消息ID
    private String contentType;     // 内容类型
    private String content;         // 消息内容（文本内容或文件路径）
    private String accessUrl;       // 文件访问URL（仅文件类型消息有）
    private String fileName;        // 原始文件名（仅文件类型消息有）
    private Long fileSize;          // 文件大小（仅文件类型消息有）
    private LocalDateTime sendTime; // 发送时间

    public SendMessageResponse() {
    }

    public SendMessageResponse(Long messageId, String contentType, String content) {
        this.messageId = messageId;
        this.contentType = contentType;
        this.content = content;
        this.sendTime = LocalDateTime.now();
    }

    public SendMessageResponse(Long messageId, String contentType, String content, 
                              String accessUrl, String fileName, Long fileSize) {
        this.messageId = messageId;
        this.contentType = contentType;
        this.content = content;
        this.accessUrl = accessUrl;
        this.fileName = fileName;
        this.fileSize = fileSize;
        this.sendTime = LocalDateTime.now();
    }

    public Long getMessageId() {
        return messageId;
    }

    public void setMessageId(Long messageId) {
        this.messageId = messageId;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getAccessUrl() {
        return accessUrl;
    }

    public void setAccessUrl(String accessUrl) {
        this.accessUrl = accessUrl;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public LocalDateTime getSendTime() {
        return sendTime;
    }

    public void setSendTime(LocalDateTime sendTime) {
        this.sendTime = sendTime;
    }

    @Override
    public String toString() {
        return "SendMessageResponse{" +
                "messageId=" + messageId +
                ", contentType='" + contentType + '\'' +
                ", content='" + content + '\'' +
                ", accessUrl='" + accessUrl + '\'' +
                ", fileName='" + fileName + '\'' +
                ", fileSize=" + fileSize +
                ", sendTime=" + sendTime +
                '}';
    }
}
