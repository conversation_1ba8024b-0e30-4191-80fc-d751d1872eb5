package com.ruoyi.wenshucalebdar.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.wenshuapi.pojo.programme.CalendarParticipant;
import com.ruoyi.wenshuapi.pojo.programme.Programme;
import com.ruoyi.wenshucalebdar.dao.ProgrammeDao;
import com.ruoyi.wenshucalebdar.service.CalendarParticipantService;
import com.ruoyi.wenshucalebdar.service.ProgrammeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 日程服务实现类
 */
@Service
@RequiredArgsConstructor
public class ProgrammeServiceImpl extends ServiceImpl<ProgrammeDao, Programme> implements ProgrammeService {
    private final CalendarParticipantService participantService;

    /**
     * 根据时间范围查询日程
     */
    @Override
    public List<Programme> getProgrammesByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Long userId) {
        // 如果提供了用户ID，使用getProgrammesByUserIdAndTimeRange方法
        if (userId != null) {
            return getProgrammesByUserIdAndTimeRange(userId, startTime, endTime);
        }
        
        // 如果未提供用户ID，只按时间范围查询
        QueryWrapper<Programme> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .ge(startTime != null, Programme::getStartTime, startTime)
                .le(endTime != null, Programme::getStartTime, endTime);

        return this.list(queryWrapper);
    }
    
    /**
     * 根据用户ID查询日程
     */
    @Override
    public List<Programme> getProgrammesByUserId(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }
        
        // 查询用户参与的所有日程ID
        QueryWrapper<CalendarParticipant> participantQuery = new QueryWrapper<>();
        participantQuery.lambda().eq(CalendarParticipant::getUserId, userId);
        List<CalendarParticipant> participants = participantService.list(participantQuery);
        
        if (participants.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 获取所有事件ID
        List<Integer> eventIds = participants.stream()
                .map(CalendarParticipant::getEventId)
                .collect(Collectors.toList());
        
        // 查询对应的日程
        QueryWrapper<Programme> programmeQuery = new QueryWrapper<>();
        programmeQuery.lambda().in(Programme::getEventId, eventIds);
        
        return this.list(programmeQuery);
    }

    /**
     * 根据用户ID和时间范围查询日程
     */
    @Override
    public List<Programme> getProgrammesByUserIdAndTimeRange(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        if (userId == null) {
            return new ArrayList<>();
        }
        
        // 查询用户参与的所有日程ID
        QueryWrapper<CalendarParticipant> participantQuery = new QueryWrapper<>();
        participantQuery.lambda().eq(CalendarParticipant::getUserId, userId);
        List<CalendarParticipant> participants = participantService.list(participantQuery);

        if (participants.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取所有事件ID
        List<Integer> eventIds = participants.stream()
                .map(CalendarParticipant::getEventId)
                .collect(Collectors.toList());

        // 查询这些日程，并添加时间范围条件
        QueryWrapper<Programme> programmeQuery = new QueryWrapper<>();
        programmeQuery.lambda()
                .in(Programme::getEventId, eventIds)
                .ge(startTime != null, Programme::getStartTime, startTime)
                .le(endTime != null, Programme::getStartTime, endTime);

        return this.list(programmeQuery);
    }
}
