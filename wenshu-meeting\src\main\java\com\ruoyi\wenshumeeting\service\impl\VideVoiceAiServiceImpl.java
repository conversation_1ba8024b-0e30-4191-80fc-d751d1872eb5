package com.ruoyi.wenshumeeting.service.impl;

import com.ruoyi.wenshuapi.client.voice.RestResultL;
import com.ruoyi.wenshuapi.client.voice.VoiceClient;
import com.ruoyi.wenshumeeting.service.VideVoiceAiService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service
@Transactional
public class VideVoiceAiServiceImpl implements VideVoiceAiService {
    private static final Logger logger = LoggerFactory.getLogger(VideVoiceAiServiceImpl.class);

    @Autowired
    VoiceClient voiceClient;

    @Override
    public String speechRecognition(MultipartFile file) {
        try {
            logger.info("开始语音识别，文件名: {}, 文件大小: {} bytes",
                       file.getOriginalFilename(), file.getSize());

            RestResultL<String[]> restResultL = voiceClient.handleAudioUpload(file);

            // 检查响应结果
            if (restResultL == null) {
                logger.error("语音识别服务返回null响应");
                throw new RuntimeException("语音识别服务无响应");
            }

            // 检查响应码
            if (restResultL.getCode() != 200) {
                logger.error("语音识别失败，错误码: {}, 错误信息: {}",
                           restResultL.getCode(), restResultL.getMessage());
                throw new RuntimeException("语音识别失败: " + restResultL.getMessage());
            }

            // 检查数据是否为空
            String[] data = restResultL.getData();
            if (data == null) {
                logger.error("语音识别返回数据为空");
                throw new RuntimeException("语音识别返回数据为空，可能是音频格式不支持或音频质量问题");
            }

            if (data.length == 0) {
                logger.error("语音识别返回数据数组为空");
                throw new RuntimeException("语音识别返回数据数组为空");
            }

            if (data[0] == null || data[0].trim().isEmpty()) {
                logger.warn("语音识别结果为空，可能是音频中没有可识别的语音内容");
                return ""; // 返回空字符串而不是null
            }

            logger.info("语音识别成功，识别结果长度: {} 字符", data[0].length());
            return data[0];

        } catch (Exception e) {
            logger.error("语音识别过程中发生异常: {}", e.getMessage(), e);
            throw new RuntimeException("语音识别失败: " + e.getMessage(), e);
        }
    }
}
