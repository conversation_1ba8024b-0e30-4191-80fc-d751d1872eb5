#!/bin/bash

# 更新Nacos到v2.2.0版本并重启服务
echo "=== 更新Nacos到v2.2.0版本 ==="

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ 错误: Docker未运行，请先启动Docker"
    exit 1
fi

echo "✅ Docker运行正常"

# 1. 停止现有的Nacos相关服务
echo ""
echo "1. 停止现有服务..."
docker compose -f docker-compose.env.yml down nacos 2>/dev/null || echo "   Nacos服务未在运行"
docker compose -f docker-compose.app.yml down auth gateway system 2>/dev/null || echo "   应用服务未在运行"

# 2. 拉取新的Nacos镜像
echo ""
echo "2. 拉取Nacos v2.2.0镜像..."
docker pull lcr.loongnix.cn/nacos/nacos-server:v2.2.0

if [ $? -eq 0 ]; then
    echo "✅ Nacos v2.2.0镜像拉取成功"
else
    echo "❌ Nacos镜像拉取失败，请检查网络连接"
    exit 1
fi

# 3. 删除旧的Nacos镜像（可选）
echo ""
echo "3. 清理旧的Nacos镜像..."
docker rmi lcr.loongnix.cn/nacos/nacos-server:v2.2.0-BETA 2>/dev/null || echo "   旧镜像不存在或已被使用"

# 4. 修复数据库表结构（如果需要）
echo ""
echo "4. 检查并修复数据库表结构..."
if [ -f "fix-nacos-db.sql" ]; then
    # 检查MySQL容器是否运行
    if docker ps --filter "name=wenshu-mysql" --format "{{.Names}}" | grep -q "wenshu-mysql"; then
        echo "   执行数据库修复..."
        docker exec -i wenshu-mysql mysql -uroot -p2313147023 < fix-nacos-db.sql
        if [ $? -eq 0 ]; then
            echo "✅ 数据库表结构修复成功"
        else
            echo "⚠️  数据库修复失败，但继续启动服务"
        fi
    else
        echo "   MySQL容器未运行，跳过数据库修复"
    fi
else
    echo "   修复脚本不存在，跳过数据库修复"
fi

# 5. 启动基础设施服务
echo ""
echo "5. 启动基础设施服务..."

# 启动MySQL（如果未运行）
if ! docker ps --filter "name=wenshu-mysql" --format "{{.Names}}" | grep -q "wenshu-mysql"; then
    echo "   启动MySQL..."
    docker compose -f docker-compose.env.yml up -d mysql
    sleep 10
fi

# 启动Redis（如果未运行）
if ! docker ps --filter "name=wenshu-redis" --format "{{.Names}}" | grep -q "wenshu-redis"; then
    echo "   启动Redis..."
    docker compose -f docker-compose.env.yml up -d redis
    sleep 5
fi

# 启动新版本的Nacos
echo "   启动Nacos v2.2.0..."
docker compose -f docker-compose.env.yml up -d nacos

# 6. 等待Nacos启动完成
echo ""
echo "6. 等待Nacos启动完成..."
for i in {1..30}; do
    if curl -f http://localhost:8848/nacos/actuator/health > /dev/null 2>&1; then
        echo "✅ Nacos启动成功"
        break
    fi
    echo "   等待Nacos启动... ($i/30)"
    sleep 5
done

# 检查Nacos是否成功启动
if ! curl -f http://localhost:8848/nacos/actuator/health > /dev/null 2>&1; then
    echo "❌ Nacos启动失败，请检查日志："
    echo "   docker compose -f docker-compose.env.yml logs nacos"
    exit 1
fi

# 7. 启动应用服务
echo ""
echo "7. 启动应用服务..."
echo "   启动认证服务..."
docker compose -f docker-compose.app.yml up -d auth
sleep 15

echo "   启动系统服务..."
docker compose -f docker-compose.app.yml up -d system
sleep 10

echo "   启动网关服务..."
docker compose -f docker-compose.app.yml up -d gateway
sleep 10

# 8. 验证服务状态
echo ""
echo "8. 验证服务状态..."
echo "=== 容器状态 ==="
docker ps --filter "name=wenshu-" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo "=== 服务健康检查 ==="
services=("nacos:8848" "auth:9200" "gateway:8080")
for service in "${services[@]}"; do
    name=$(echo $service | cut -d: -f1)
    port=$(echo $service | cut -d: -f2)
    if curl -f http://localhost:$port/actuator/health > /dev/null 2>&1; then
        echo "✅ $name 服务正常"
    else
        echo "❌ $name 服务异常"
    fi
done

echo ""
echo "=== 更新完成 ==="
echo "🎉 Nacos已成功更新到v2.2.0版本"
echo ""
echo "访问地址："
echo "  - Nacos控制台: http://localhost:8848/nacos"
echo "  - 网关服务: http://localhost:8080"
echo ""
echo "如果服务异常，请查看日志："
echo "  docker compose -f docker-compose.env.yml logs nacos"
echo "  docker compose -f docker-compose.app.yml logs auth"
echo "  docker compose -f docker-compose.app.yml logs gateway"
