<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

		<parent>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi</artifactId>
                <version>3.6.4</version>
            </parent>
<modelVersion>4.0.0</modelVersion>
 <modules>

        <module>wenshu-audit</module>
        <module>wenshu-base</module>
         <module>wenshu-calebdar</module>
          <module>wenshu-file</module>
           <module>wenshu-team</module>
             <module>wenshu-wchat</module>
    </modules>
    <dependencies>
    		<dependency>
    			<groupId>com.ruoyi</groupId>
    			<artifactId>wenshu-api</artifactId>
    			<version>0.0.1-SNAPSHOT</version>
    		</dependency>

    	</dependencies>
  <!-- Generated by https://start.springboot.io -->
  <!-- 优质的 spring/boot/data/security/cloud 框架中文文档尽在 => https://springdoc.cn -->

	<artifactId>wenshu-data</artifactId>
     <packaging>pom</packaging>

</project>
