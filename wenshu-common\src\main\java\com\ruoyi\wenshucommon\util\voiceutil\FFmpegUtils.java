package com.ruoyi.wenshucommon.util.voiceutil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStreamReader;

/**
 * FFmpeg工具类
 * 用于检测FFmpeg是否可用以及执行FFmpeg命令
 */
public class FFmpegUtils {
    private static final Logger logger = LoggerFactory.getLogger(FFmpegUtils.class);
    
    private static Boolean ffmpegAvailable = null;
    
    /**
     * 检查FFmpeg是否可用
     * @return true如果FFmpeg可用，false否则
     */
    public static boolean isFFmpegAvailable() {
        if (ffmpegAvailable != null) {
            return ffmpegAvailable;
        }
        
        try {
            ProcessBuilder pb = new ProcessBuilder("ffmpeg", "-version");
            Process process = pb.start();
            
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line = reader.readLine();
                if (line != null && line.contains("ffmpeg version")) {
                    ffmpegAvailable = true;
                    logger.info("FFmpeg可用: {}", line);
                    return true;
                }
            }
            
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                ffmpegAvailable = true;
                return true;
            }
            
        } catch (Exception e) {
            logger.debug("FFmpeg不可用: {}", e.getMessage());
        }
        
        ffmpegAvailable = false;
        logger.warn("FFmpeg不可用，某些音频格式转换功能将受限");
        return false;
    }
    
    /**
     * 获取FFmpeg不可用时的建议信息
     * @return 安装建议
     */
    public static String getFFmpegInstallationAdvice() {
        String os = System.getProperty("os.name").toLowerCase();
        
        if (os.contains("windows")) {
            return "请下载FFmpeg并添加到系统PATH环境变量中。下载地址: https://ffmpeg.org/download.html#build-windows";
        } else if (os.contains("mac")) {
            return "请使用Homebrew安装FFmpeg: brew install ffmpeg";
        } else if (os.contains("linux")) {
            return "请使用包管理器安装FFmpeg: sudo apt-get install ffmpeg 或 sudo yum install ffmpeg";
        } else {
            return "请访问 https://ffmpeg.org/download.html 下载并安装FFmpeg";
        }
    }
    
    /**
     * 重置FFmpeg可用性检测缓存
     */
    public static void resetFFmpegAvailabilityCache() {
        ffmpegAvailable = null;
    }
}
