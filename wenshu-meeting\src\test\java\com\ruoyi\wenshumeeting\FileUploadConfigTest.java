package com.ruoyi.wenshumeeting;

import com.ruoyi.wenshumeeting.config.FileUploadConfig;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.multipart.MultipartResolver;

import jakarta.servlet.MultipartConfigElement;

/**
 * 文件上传配置测试类
 * 验证文件上传配置是否正确加载
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@SpringBootTest
@ActiveProfiles("analysisController")
public class FileUploadConfigTest {
    
    @Autowired(required = false)
    private FileUploadConfig fileUploadConfig;
    
    @Autowired(required = false)
    private MultipartConfigElement multipartConfigElement;
    
    @Autowired(required = false)
    private MultipartResolver multipartResolver;
    
    @Test
    public void testFileUploadConfigExists() {
        // 测试文件上传配置类是否正确注入
        System.out.println("FileUploadConfig注入状态: " + (fileUploadConfig != null ? "成功" : "失败"));
        assert fileUploadConfig != null : "FileUploadConfig应该被正确注入";
    }
    
    @Test
    public void testMultipartConfigElement() {
        // 测试文件上传配置元素
        System.out.println("MultipartConfigElement注入状态: " + (multipartConfigElement != null ? "成功" : "失败"));
        
        if (multipartConfigElement != null) {
            long maxFileSize = multipartConfigElement.getMaxFileSize();
            long maxRequestSize = multipartConfigElement.getMaxRequestSize();
            int fileSizeThreshold = multipartConfigElement.getFileSizeThreshold();
            String location = multipartConfigElement.getLocation();
            
            System.out.println("最大文件大小: " + maxFileSize + " bytes (" + (maxFileSize / 1024 / 1024) + " MB)");
            System.out.println("最大请求大小: " + maxRequestSize + " bytes (" + (maxRequestSize / 1024 / 1024) + " MB)");
            System.out.println("文件大小阈值: " + fileSizeThreshold + " bytes");
            System.out.println("临时文件位置: " + location);
            
            // 验证配置值是否符合预期（500MB = 524288000 bytes）
            long expectedSize = 500L * 1024 * 1024; // 500MB in bytes
            assert maxFileSize == expectedSize : "最大文件大小应该是500MB";
            assert maxRequestSize == expectedSize : "最大请求大小应该是500MB";
            assert fileSizeThreshold == 2048 : "文件大小阈值应该是2KB";
        }
    }
    
    @Test
    public void testMultipartResolver() {
        // 测试文件上传解析器
        System.out.println("MultipartResolver注入状态: " + (multipartResolver != null ? "成功" : "失败"));
        
        if (multipartResolver != null) {
            System.out.println("MultipartResolver类型: " + multipartResolver.getClass().getSimpleName());
        }
    }
    
    @Test
    public void testFileSizeCalculation() {
        // 测试文件大小计算
        long oneMB = 1024 * 1024;
        long fiveHundredMB = 500 * oneMB;
        
        System.out.println("1MB = " + oneMB + " bytes");
        System.out.println("500MB = " + fiveHundredMB + " bytes");
        
        // 验证计算是否正确
        assert oneMB == 1048576 : "1MB应该等于1048576字节";
        assert fiveHundredMB == 524288000 : "500MB应该等于524288000字节";
        
        System.out.println("文件大小计算测试通过");
    }
    
    @Test
    public void testSystemTempDirectory() {
        // 测试系统临时目录
        String tempDir = System.getProperty("java.io.tmpdir");
        System.out.println("系统临时目录: " + tempDir);
        
        assert tempDir != null && !tempDir.isEmpty() : "系统临时目录应该存在";
        
        System.out.println("系统临时目录测试通过");
    }
}
