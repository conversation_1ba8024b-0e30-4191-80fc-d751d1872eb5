package com.ruoyi.wenshucalebdar.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通用API响应结构
 *
 * @param <T> 响应数据类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> {
    /**
     * 状态码
     */
    private long code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 成功响应
     *
     * @param data    响应数据
     * @param message 响应消息
     * @param <T>     数据类型
     * @return 成功响应对象
     */
    public static <T> ApiResponse<T> success(T data, String message) {
        return new ApiResponse<>(200L, message, data);
    }
    
    /**
     * 成功响应
     *
     * @param <T> 数据类型
     * @return 成功响应对象（无数据）
     */
    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>(200L, "操作成功", null);
    }
    
    /**
     * 失败响应
     *
     * @param message 错误消息
     * @param <T>     数据类型
     * @return 失败响应对象
     */
    public static <T> ApiResponse<T> failed(String message) {
        return new ApiResponse<>(500L, message, null);
    }
    
    /**
     * 参数验证失败响应
     *
     * @param message 错误消息
     * @param <T>     数据类型
     * @return 失败响应对象
     */
    public static <T> ApiResponse<T> validateFailed(String message) {
        return new ApiResponse<>(400L, message, null);
    }
    
    /**
     * 未登录响应
     *
     * @param <T> 数据类型
     * @return 未登录响应对象
     */
    public static <T> ApiResponse<T> unauthorized() {
        return new ApiResponse<>(401L, "暂未登录或token已经过期", null);
    }
} 