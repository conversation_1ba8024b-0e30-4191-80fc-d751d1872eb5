# Wenshu-EditFile 在线文件编辑服务模块 - 详细API接口文档

## 📋 模块概述

**服务名称**: wenshu-editfile  
**服务端口**: 8080  
**版本**: v1.0.0  
**最后更新**: 2024-12-28  

**核心功能**: 在线文件编辑服务，提供多格式文件在线编辑、实时协作、版本控制、文件管理等功能

**技术栈**:
- Spring Boot 3.x
- Spring Cloud 2023.x
- WebSocket (实时协作)
- Spring Data JPA (数据持久化)
- Redis (缓存和会话管理)
- Apache Tika (文件格式检测)
- Apache POI (Office文档处理)
- iText (PDF处理)
- Monaco Editor (代码编辑器)
- CKEditor (富文本编辑器)

---

## 🏗️ 服务架构

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                Wenshu-EditFile 在线文件编辑服务              │
│                        (Port: 8080)                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│   文件编辑     │ │实时协作  │ │ 版本控制   │
│ (File Edit)   │ │(Collab) │ │(Version)  │
└───────────────┘ └────────┘ └───────────┘
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│ 多格式支持     │ │WebSocket│ │ Git集成   │
│ Office/PDF/   │ │实时同步  │ │ 历史记录   │
│ Code/Markdown │ │        │ │          │
└───────────────┘ └────────┘ └───────────┘
```

### 服务依赖关系
- **wenshu-api**: 通用API服务和文件存储
- **wenshu-chat**: 协作聊天服务
- **wenshu-voice**: 语音注释服务
- **Redis**: 实时协作状态管理
- **MySQL**: 文件元数据和版本信息存储

---

## 🌐 REST API接口

### 基础信息
- **Base URL**: `http://localhost:8080`
- **Content-Type**: `application/json` / `multipart/form-data`
- **鉴权方式**: JWT Token (Header: `Authorization: Bearer <token>`)

### 文件处理限制
- **单文件最大**: 100MB
- **支持格式**: 
  - 文档: DOC, DOCX, PDF, TXT, MD, RTF
  - 表格: XLS, XLSX, CSV
  - 演示: PPT, PPTX
  - 代码: JS, TS, Java, Python, C++, HTML, CSS等
  - 图片: JPG, PNG, GIF, SVG (查看模式)

---

## 📚 文件管理API

### 1. 文件基础操作

#### 1.1 上传文件
**接口路径**: `POST /api/files/upload`

**功能描述**: 上传文件到编辑服务，支持多种格式的文件上传

**请求参数**:
```http
POST /api/files/upload
Content-Type: multipart/form-data

file: [文件] (必填)
userId: [用户ID] (必填)
folderId: [文件夹ID] (可选)
description: [文件描述] (可选)
```

**请求示例**:
```bash
curl -X POST http://localhost:8080/api/files/upload \
  -H "Authorization: Bearer <your-token>" \
  -F "file=@document.docx" \
  -F "userId=1001" \
  -F "folderId=folder_123" \
  -F "description=项目需求文档"
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "文件上传成功",
  "data": {
    "fileId": "file_12345",
    "fileName": "document.docx",
    "fileSize": 2048576,
    "fileType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "mimeType": "docx",
    "uploadTime": "2024-12-28T15:30:00",
    "uploadUser": 1001,
    "folderId": "folder_123",
    "editUrl": "/edit/file_12345",
    "previewUrl": "/preview/file_12345",
    "downloadUrl": "/download/file_12345",
    "isEditable": true,
    "supportedEditors": ["office", "text"]
  },
  "timestamp": 1640995200000
}
```

#### 1.2 获取文件列表
**接口路径**: `GET /api/files`

**功能描述**: 获取用户的文件列表，支持分页和筛选

**请求参数**:
```http
GET /api/files?userId=1001&folderId=folder_123&page=1&size=20&fileType=docx&keyword=项目
```

**查询参数**:
- `userId`: 用户ID (必填)
- `folderId`: 文件夹ID (可选)
- `page`: 页码 (可选，默认1)
- `size`: 每页大小 (可选，默认20)
- `fileType`: 文件类型过滤 (可选)
- `keyword`: 关键词搜索 (可选)

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "files": [
      {
        "fileId": "file_12345",
        "fileName": "项目需求文档.docx",
        "fileSize": 2048576,
        "fileType": "docx",
        "createTime": "2024-12-28T15:30:00",
        "updateTime": "2024-12-28T16:45:00",
        "creator": 1001,
        "lastEditor": 1002,
        "isEditable": true,
        "isShared": false,
        "collaborators": [1001, 1002],
        "status": "normal"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 45,
      "totalPages": 3
    }
  },
  "timestamp": 1640995200000
}
```

#### 1.3 获取文件详情
**接口路径**: `GET /api/files/{fileId}`

**功能描述**: 获取指定文件的详细信息

**请求示例**:
```bash
curl -X GET "http://localhost:8080/api/files/file_12345" \
  -H "Authorization: Bearer <your-token>"
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "fileId": "file_12345",
    "fileName": "项目需求文档.docx",
    "originalName": "requirements.docx",
    "fileSize": 2048576,
    "fileType": "docx",
    "mimeType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "createTime": "2024-12-28T15:30:00",
    "updateTime": "2024-12-28T16:45:00",
    "creator": {
      "userId": 1001,
      "userName": "张三",
      "email": "<EMAIL>"
    },
    "lastEditor": {
      "userId": 1002,
      "userName": "李四",
      "email": "<EMAIL>"
    },
    "folderId": "folder_123",
    "description": "项目需求文档",
    "tags": ["需求", "项目", "文档"],
    "isEditable": true,
    "isShared": false,
    "shareSettings": {
      "shareType": "private",
      "shareUrl": null,
      "expireTime": null
    },
    "collaborators": [
      {
        "userId": 1001,
        "userName": "张三",
        "role": "owner",
        "permissions": ["read", "write", "share", "delete"]
      },
      {
        "userId": 1002,
        "userName": "李四",
        "role": "editor",
        "permissions": ["read", "write"]
      }
    ],
    "versionInfo": {
      "currentVersion": "1.3",
      "totalVersions": 5,
      "lastSaveTime": "2024-12-28T16:45:00"
    },
    "editHistory": [
      {
        "version": "1.3",
        "editor": "李四",
        "editTime": "2024-12-28T16:45:00",
        "changes": "修改了第三章内容"
      }
    ],
    "status": "normal"
  },
  "timestamp": 1640995200000
}
```

#### 1.4 删除文件
**接口路径**: `DELETE /api/files/{fileId}`

**功能描述**: 删除指定文件

**请求示例**:
```bash
curl -X DELETE "http://localhost:8080/api/files/file_12345" \
  -H "Authorization: Bearer <your-token>"
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "文件删除成功",
  "data": {
    "fileId": "file_12345",
    "fileName": "项目需求文档.docx",
    "deleteTime": "2024-12-28T17:00:00"
  },
  "timestamp": 1640995200000
}
```

---

## ✏️ 文件编辑API

### 2. 在线编辑功能

#### 2.1 打开文件编辑器
**接口路径**: `GET /api/edit/{fileId}`

**功能描述**: 打开文件编辑器，返回编辑器配置和文件内容

**请求参数**:
```http
GET /api/edit/{fileId}?mode=edit&editor=auto
```

**查询参数**:
- `mode`: 编辑模式 (edit/view/comment，默认edit)
- `editor`: 编辑器类型 (auto/office/code/markdown，默认auto)

**请求示例**:
```bash
curl -X GET "http://localhost:8080/api/edit/file_12345?mode=edit&editor=office" \
  -H "Authorization: Bearer <your-token>"
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "编辑器加载成功",
  "data": {
    "fileId": "file_12345",
    "fileName": "项目需求文档.docx",
    "editorType": "office",
    "editorConfig": {
      "mode": "edit",
      "language": "zh-CN",
      "theme": "default",
      "toolbar": ["bold", "italic", "underline", "font", "paragraph", "insert"],
      "plugins": ["collaboration", "comments", "track-changes"],
      "autosave": {
        "enabled": true,
        "interval": 30000
      }
    },
    "fileContent": {
      "format": "docx",
      "content": "base64编码的文件内容或HTML格式",
      "metadata": {
        "wordCount": 1250,
        "pageCount": 5,
        "lastModified": "2024-12-28T16:45:00"
      }
    },
    "collaborationInfo": {
      "sessionId": "session_abc123",
      "websocketUrl": "ws://localhost:8080/ws/collaboration/file_12345",
      "currentUsers": [
        {
          "userId": 1001,
          "userName": "张三",
          "cursor": {"line": 10, "column": 25},
          "selection": {"start": {"line": 10, "column": 20}, "end": {"line": 10, "column": 30}},
          "color": "#FF5733"
        }
      ]
    },
    "permissions": {
      "canEdit": true,
      "canComment": true,
      "canShare": true,
      "canDownload": true
    }
  },
  "timestamp": 1640995200000
}
```

#### 2.2 保存文件内容
**接口路径**: `POST /api/edit/{fileId}/save`

**功能描述**: 保存编辑后的文件内容

**请求参数**:
```http
POST /api/edit/{fileId}/save
Content-Type: application/json

{
  "content": "文件内容",
  "format": "docx",
  "saveType": "manual",
  "versionComment": "修改了第三章内容",
  "createNewVersion": true
}
```

**请求示例**:
```bash
curl -X POST "http://localhost:8080/api/edit/file_12345/save" \
  -H "Authorization: Bearer <your-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "更新后的文档内容...",
    "format": "docx",
    "saveType": "manual",
    "versionComment": "添加了新的需求章节",
    "createNewVersion": true
  }'
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "文件保存成功",
  "data": {
    "fileId": "file_12345",
    "saveTime": "2024-12-28T17:15:00",
    "version": "1.4",
    "fileSize": 2156789,
    "changes": {
      "wordsAdded": 125,
      "wordsDeleted": 23,
      "charactersAdded": 756,
      "charactersDeleted": 134
    },
    "nextAutosaveTime": "2024-12-28T17:15:30"
  },
  "timestamp": 1640995200000
}
```

#### 2.3 获取文件预览
**接口路径**: `GET /api/preview/{fileId}`

**功能描述**: 获取文件预览内容，支持多种格式的预览

**请求参数**:
```http
GET /api/preview/{fileId}?format=html&page=1&zoom=100
```

**查询参数**:
- `format`: 预览格式 (html/pdf/image，默认html)
- `page`: 页码 (可选，用于多页文档)
- `zoom`: 缩放比例 (可选，默认100)

**响应格式**:
```json
{
  "code": 200,
  "msg": "预览生成成功",
  "data": {
    "fileId": "file_12345",
    "previewFormat": "html",
    "content": "HTML格式的预览内容",
    "pageInfo": {
      "currentPage": 1,
      "totalPages": 5,
      "pageSize": "A4"
    },
    "previewUrl": "/preview/file_12345/html",
    "thumbnailUrl": "/preview/file_12345/thumbnail",
    "downloadUrl": "/download/file_12345"
  },
  "timestamp": 1640995200000
}
```

---

---

## 🤝 实时协作API

### 3. 协作编辑功能

#### 3.1 加入协作会话
**接口路径**: `POST /api/collaboration/{fileId}/join`

**功能描述**: 加入文件的协作编辑会话

**请求参数**:
```http
POST /api/collaboration/{fileId}/join
Content-Type: application/json

{
  "userId": 1002,
  "userName": "李四",
  "role": "editor"
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "加入协作成功",
  "data": {
    "sessionId": "session_abc123",
    "fileId": "file_12345",
    "websocketUrl": "ws://localhost:8080/ws/collaboration/file_12345",
    "userInfo": {
      "userId": 1002,
      "userName": "李四",
      "role": "editor",
      "color": "#33FF57",
      "cursor": {"line": 1, "column": 1}
    },
    "currentCollaborators": [
      {
        "userId": 1001,
        "userName": "张三",
        "role": "owner",
        "color": "#FF5733",
        "isOnline": true,
        "lastActivity": "2024-12-28T17:20:00"
      }
    ],
    "permissions": {
      "canEdit": true,
      "canComment": true,
      "canViewHistory": true
    }
  },
  "timestamp": 1640995200000
}
```

#### 3.2 离开协作会话
**接口路径**: `POST /api/collaboration/{fileId}/leave`

**功能描述**: 离开文件的协作编辑会话

**请求示例**:
```bash
curl -X POST "http://localhost:8080/api/collaboration/file_12345/leave" \
  -H "Authorization: Bearer <your-token>" \
  -H "Content-Type: application/json" \
  -d '{"userId": 1002}'
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "离开协作成功",
  "data": {
    "userId": 1002,
    "leaveTime": "2024-12-28T17:30:00",
    "sessionDuration": "00:10:25"
  },
  "timestamp": 1640995200000
}
```

#### 3.3 获取协作状态
**接口路径**: `GET /api/collaboration/{fileId}/status`

**功能描述**: 获取文件当前的协作状态

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "fileId": "file_12345",
    "isCollaborationActive": true,
    "totalCollaborators": 3,
    "onlineCollaborators": 2,
    "collaborators": [
      {
        "userId": 1001,
        "userName": "张三",
        "role": "owner",
        "isOnline": true,
        "joinTime": "2024-12-28T17:00:00",
        "lastActivity": "2024-12-28T17:25:00",
        "currentPosition": {"line": 15, "column": 30},
        "color": "#FF5733"
      },
      {
        "userId": 1002,
        "userName": "李四",
        "role": "editor",
        "isOnline": true,
        "joinTime": "2024-12-28T17:10:00",
        "lastActivity": "2024-12-28T17:24:00",
        "currentPosition": {"line": 8, "column": 12},
        "color": "#33FF57"
      }
    ],
    "recentChanges": [
      {
        "changeId": "change_789",
        "userId": 1002,
        "userName": "李四",
        "changeType": "insert",
        "position": {"line": 8, "column": 12},
        "content": "新增的文本内容",
        "timestamp": "2024-12-28T17:24:00"
      }
    ]
  },
  "timestamp": 1640995200000
}
```

---

## 📝 版本控制API

### 4. 版本管理功能

#### 4.1 获取版本历史
**接口路径**: `GET /api/versions/{fileId}`

**功能描述**: 获取文件的版本历史记录

**请求参数**:
```http
GET /api/versions/{fileId}?page=1&size=10&startDate=2024-12-01&endDate=2024-12-28
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "fileId": "file_12345",
    "currentVersion": "1.4",
    "versions": [
      {
        "versionId": "version_1.4",
        "versionNumber": "1.4",
        "createTime": "2024-12-28T17:15:00",
        "creator": {
          "userId": 1002,
          "userName": "李四"
        },
        "comment": "添加了新的需求章节",
        "fileSize": 2156789,
        "changes": {
          "wordsAdded": 125,
          "wordsDeleted": 23,
          "linesAdded": 15,
          "linesDeleted": 3
        },
        "isCurrent": true,
        "downloadUrl": "/download/file_12345/version_1.4"
      },
      {
        "versionId": "version_1.3",
        "versionNumber": "1.3",
        "createTime": "2024-12-28T16:45:00",
        "creator": {
          "userId": 1002,
          "userName": "李四"
        },
        "comment": "修改了第三章内容",
        "fileSize": 2048576,
        "changes": {
          "wordsAdded": 45,
          "wordsDeleted": 12,
          "linesAdded": 8,
          "linesDeleted": 2
        },
        "isCurrent": false,
        "downloadUrl": "/download/file_12345/version_1.3"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 10,
      "total": 4,
      "totalPages": 1
    }
  },
  "timestamp": 1640995200000
}
```

#### 4.2 版本比较
**接口路径**: `GET /api/versions/{fileId}/compare`

**功能描述**: 比较两个版本之间的差异

**请求参数**:
```http
GET /api/versions/{fileId}/compare?fromVersion=1.2&toVersion=1.4&format=html
```

**查询参数**:
- `fromVersion`: 源版本号 (必填)
- `toVersion`: 目标版本号 (必填)
- `format`: 比较结果格式 (html/json，默认html)

**响应格式**:
```json
{
  "code": 200,
  "msg": "版本比较成功",
  "data": {
    "fileId": "file_12345",
    "fromVersion": "1.2",
    "toVersion": "1.4",
    "compareResult": {
      "format": "html",
      "content": "HTML格式的差异对比内容",
      "summary": {
        "totalChanges": 25,
        "additions": 18,
        "deletions": 7,
        "modifications": 12,
        "wordsChanged": 156,
        "linesChanged": 23
      },
      "changes": [
        {
          "type": "addition",
          "position": {"line": 8, "column": 1},
          "content": "新增的段落内容",
          "author": "李四",
          "timestamp": "2024-12-28T16:45:00"
        },
        {
          "type": "deletion",
          "position": {"line": 15, "column": 10},
          "content": "删除的文本",
          "author": "李四",
          "timestamp": "2024-12-28T17:15:00"
        }
      ]
    }
  },
  "timestamp": 1640995200000
}
```

#### 4.3 恢复到指定版本
**接口路径**: `POST /api/versions/{fileId}/restore`

**功能描述**: 将文件恢复到指定版本

**请求参数**:
```http
POST /api/versions/{fileId}/restore
Content-Type: application/json

{
  "versionId": "version_1.3",
  "createNewVersion": true,
  "restoreComment": "恢复到版本1.3，撤销最新修改"
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "版本恢复成功",
  "data": {
    "fileId": "file_12345",
    "restoredFromVersion": "1.3",
    "newVersion": "1.5",
    "restoreTime": "2024-12-28T17:45:00",
    "restoreComment": "恢复到版本1.3，撤销最新修改"
  },
  "timestamp": 1640995200000
}
```

---

## 💬 评论和注释API

### 5. 评论功能

#### 5.1 添加评论
**接口路径**: `POST /api/comments/{fileId}`

**功能描述**: 在文件中添加评论或注释

**请求参数**:
```http
POST /api/comments/{fileId}
Content-Type: application/json

{
  "content": "这里需要补充更多的技术细节",
  "position": {
    "line": 10,
    "column": 15,
    "selection": {
      "start": {"line": 10, "column": 10},
      "end": {"line": 10, "column": 25}
    }
  },
  "commentType": "suggestion",
  "isPrivate": false,
  "mentionUsers": [1003]
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "评论添加成功",
  "data": {
    "commentId": "comment_456",
    "fileId": "file_12345",
    "content": "这里需要补充更多的技术细节",
    "author": {
      "userId": 1002,
      "userName": "李四",
      "avatar": "/avatars/1002.jpg"
    },
    "createTime": "2024-12-28T17:50:00",
    "position": {
      "line": 10,
      "column": 15,
      "selection": {
        "start": {"line": 10, "column": 10},
        "end": {"line": 10, "column": 25}
      }
    },
    "commentType": "suggestion",
    "isPrivate": false,
    "mentionUsers": [
      {
        "userId": 1003,
        "userName": "王五"
      }
    ],
    "status": "open",
    "replies": []
  },
  "timestamp": 1640995200000
}
```

#### 5.2 获取文件评论
**接口路径**: `GET /api/comments/{fileId}`

**功能描述**: 获取文件的所有评论

**请求参数**:
```http
GET /api/comments/{fileId}?status=open&commentType=all&page=1&size=20
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "fileId": "file_12345",
    "comments": [
      {
        "commentId": "comment_456",
        "content": "这里需要补充更多的技术细节",
        "author": {
          "userId": 1002,
          "userName": "李四",
          "avatar": "/avatars/1002.jpg"
        },
        "createTime": "2024-12-28T17:50:00",
        "updateTime": "2024-12-28T17:50:00",
        "position": {
          "line": 10,
          "column": 15
        },
        "commentType": "suggestion",
        "status": "open",
        "replies": [
          {
            "replyId": "reply_789",
            "content": "已经补充了相关内容",
            "author": {
              "userId": 1001,
              "userName": "张三"
            },
            "createTime": "2024-12-28T18:00:00"
          }
        ],
        "replyCount": 1
      }
    ],
    "summary": {
      "totalComments": 5,
      "openComments": 3,
      "resolvedComments": 2,
      "suggestionComments": 4,
      "questionComments": 1
    },
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 5,
      "totalPages": 1
    }
  },
  "timestamp": 1640995200000
}
```

#### 5.3 回复评论
**接口路径**: `POST /api/comments/{commentId}/reply`

**功能描述**: 回复指定评论

**请求参数**:
```http
POST /api/comments/comment_456/reply
Content-Type: application/json

{
  "content": "已经补充了相关内容，请查看",
  "mentionUsers": [1002]
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "回复成功",
  "data": {
    "replyId": "reply_789",
    "commentId": "comment_456",
    "content": "已经补充了相关内容，请查看",
    "author": {
      "userId": 1001,
      "userName": "张三",
      "avatar": "/avatars/1001.jpg"
    },
    "createTime": "2024-12-28T18:00:00",
    "mentionUsers": [
      {
        "userId": 1002,
        "userName": "李四"
      }
    ]
  },
  "timestamp": 1640995200000
}
```

#### 5.4 解决评论
**接口路径**: `PUT /api/comments/{commentId}/resolve`

**功能描述**: 标记评论为已解决

**请求示例**:
```bash
curl -X PUT "http://localhost:8080/api/comments/comment_456/resolve" \
  -H "Authorization: Bearer <your-token>" \
  -H "Content-Type: application/json" \
  -d '{"resolveComment": "问题已解决"}'
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "评论已解决",
  "data": {
    "commentId": "comment_456",
    "status": "resolved",
    "resolveTime": "2024-12-28T18:10:00",
    "resolver": {
      "userId": 1001,
      "userName": "张三"
    },
    "resolveComment": "问题已解决"
  },
  "timestamp": 1640995200000
}
```

---

---

## 🔗 文件分享API

### 6. 分享功能

#### 6.1 创建分享链接
**接口路径**: `POST /api/share/{fileId}`

**功能描述**: 为文件创建分享链接

**请求参数**:
```http
POST /api/share/{fileId}
Content-Type: application/json

{
  "shareType": "public",
  "permissions": ["read", "comment"],
  "expireTime": "2024-12-30T23:59:59",
  "password": "share123",
  "allowDownload": false,
  "allowCopy": true,
  "description": "项目需求文档分享"
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "分享链接创建成功",
  "data": {
    "shareId": "share_abc123",
    "fileId": "file_12345",
    "shareUrl": "https://edit.wenshu.com/share/share_abc123",
    "shareCode": "ABCD1234",
    "shareType": "public",
    "permissions": ["read", "comment"],
    "expireTime": "2024-12-30T23:59:59",
    "hasPassword": true,
    "allowDownload": false,
    "allowCopy": true,
    "createTime": "2024-12-28T18:15:00",
    "creator": {
      "userId": 1001,
      "userName": "张三"
    },
    "accessCount": 0,
    "maxAccess": null
  },
  "timestamp": 1640995200000
}
```

#### 6.2 访问分享文件
**接口路径**: `GET /api/share/{shareId}/access`

**功能描述**: 通过分享链接访问文件

**请求参数**:
```http
GET /api/share/{shareId}/access?password=share123
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "访问成功",
  "data": {
    "shareId": "share_abc123",
    "fileInfo": {
      "fileId": "file_12345",
      "fileName": "项目需求文档.docx",
      "fileSize": 2156789,
      "fileType": "docx"
    },
    "shareInfo": {
      "shareType": "public",
      "permissions": ["read", "comment"],
      "expireTime": "2024-12-30T23:59:59",
      "allowDownload": false,
      "allowCopy": true,
      "description": "项目需求文档分享"
    },
    "accessInfo": {
      "accessToken": "access_token_xyz",
      "accessExpire": "2024-12-28T20:15:00",
      "viewerInfo": {
        "viewerId": "visitor_789",
        "viewerName": "访客",
        "accessTime": "2024-12-28T18:20:00"
      }
    },
    "editorUrl": "/share/edit/share_abc123?token=access_token_xyz"
  },
  "timestamp": 1640995200000
}
```

#### 6.3 获取分享统计
**接口路径**: `GET /api/share/{fileId}/statistics`

**功能描述**: 获取文件的分享统计信息

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "fileId": "file_12345",
    "totalShares": 3,
    "activeShares": 2,
    "expiredShares": 1,
    "totalAccess": 156,
    "uniqueVisitors": 89,
    "shares": [
      {
        "shareId": "share_abc123",
        "shareType": "public",
        "createTime": "2024-12-28T18:15:00",
        "expireTime": "2024-12-30T23:59:59",
        "accessCount": 45,
        "lastAccess": "2024-12-28T18:20:00",
        "status": "active"
      }
    ],
    "accessHistory": [
      {
        "accessTime": "2024-12-28T18:20:00",
        "viewerInfo": "访客",
        "ipAddress": "*************",
        "userAgent": "Chrome/120.0.0.0",
        "duration": "00:05:30"
      }
    ]
  },
  "timestamp": 1640995200000
}
```

---

## 🔐 权限管理API

### 7. 权限控制

#### 7.1 设置文件权限
**接口路径**: `PUT /api/permissions/{fileId}`

**功能描述**: 设置文件的访问权限

**请求参数**:
```http
PUT /api/permissions/{fileId}
Content-Type: application/json

{
  "permissions": [
    {
      "userId": 1002,
      "role": "editor",
      "permissions": ["read", "write", "comment"]
    },
    {
      "userId": 1003,
      "role": "viewer",
      "permissions": ["read", "comment"]
    }
  ],
  "defaultPermission": "private",
  "inheritFromFolder": false
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "权限设置成功",
  "data": {
    "fileId": "file_12345",
    "owner": {
      "userId": 1001,
      "userName": "张三",
      "permissions": ["read", "write", "comment", "share", "delete", "manage"]
    },
    "collaborators": [
      {
        "userId": 1002,
        "userName": "李四",
        "role": "editor",
        "permissions": ["read", "write", "comment"],
        "grantTime": "2024-12-28T18:25:00",
        "grantedBy": 1001
      },
      {
        "userId": 1003,
        "userName": "王五",
        "role": "viewer",
        "permissions": ["read", "comment"],
        "grantTime": "2024-12-28T18:25:00",
        "grantedBy": 1001
      }
    ],
    "defaultPermission": "private",
    "inheritFromFolder": false
  },
  "timestamp": 1640995200000
}
```

#### 7.2 检查用户权限
**接口路径**: `GET /api/permissions/{fileId}/check`

**功能描述**: 检查当前用户对文件的权限

**请求参数**:
```http
GET /api/permissions/{fileId}/check?userId=1002&action=write
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "权限检查完成",
  "data": {
    "fileId": "file_12345",
    "userId": 1002,
    "hasAccess": true,
    "userRole": "editor",
    "permissions": {
      "read": true,
      "write": true,
      "comment": true,
      "share": false,
      "delete": false,
      "manage": false
    },
    "restrictions": {
      "canDownload": true,
      "canCopy": true,
      "canPrint": true,
      "canExport": false
    }
  },
  "timestamp": 1640995200000
}
```

---

## 📁 文件夹管理API

### 8. 文件夹操作

#### 8.1 创建文件夹
**接口路径**: `POST /api/folders`

**功能描述**: 创建新的文件夹

**请求参数**:
```http
POST /api/folders
Content-Type: application/json

{
  "folderName": "项目文档",
  "parentFolderId": "folder_root",
  "description": "存放项目相关文档",
  "isPublic": false
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "文件夹创建成功",
  "data": {
    "folderId": "folder_456",
    "folderName": "项目文档",
    "parentFolderId": "folder_root",
    "folderPath": "/项目文档",
    "description": "存放项目相关文档",
    "createTime": "2024-12-28T18:30:00",
    "creator": {
      "userId": 1001,
      "userName": "张三"
    },
    "isPublic": false,
    "fileCount": 0,
    "subFolderCount": 0
  },
  "timestamp": 1640995200000
}
```

#### 8.2 获取文件夹内容
**接口路径**: `GET /api/folders/{folderId}/contents`

**功能描述**: 获取文件夹中的文件和子文件夹

**请求参数**:
```http
GET /api/folders/{folderId}/contents?sortBy=name&sortOrder=asc&page=1&size=20
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "folderId": "folder_456",
    "folderName": "项目文档",
    "folderPath": "/项目文档",
    "contents": {
      "folders": [
        {
          "folderId": "folder_789",
          "folderName": "需求文档",
          "createTime": "2024-12-28T18:35:00",
          "fileCount": 3,
          "subFolderCount": 0
        }
      ],
      "files": [
        {
          "fileId": "file_12345",
          "fileName": "项目需求文档.docx",
          "fileSize": 2156789,
          "fileType": "docx",
          "createTime": "2024-12-28T15:30:00",
          "updateTime": "2024-12-28T17:15:00",
          "isShared": false
        }
      ]
    },
    "summary": {
      "totalFiles": 5,
      "totalFolders": 2,
      "totalSize": 10485760
    },
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 7,
      "totalPages": 1
    }
  },
  "timestamp": 1640995200000
}
```

---

## 🔧 技术实现细节

### 编辑器引擎
- **Office文档**: OnlyOffice Document Server / Microsoft Office Online
- **代码编辑**: Monaco Editor (VS Code内核)
- **富文本编辑**: CKEditor 5 / TinyMCE
- **Markdown编辑**: StackEdit / Typora Web
- **PDF编辑**: PDF.js + 自定义编辑层

### 实时协作机制
- **协议**: WebSocket + Operational Transformation (OT)
- **冲突解决**: 基于时间戳的三路合并算法
- **状态同步**: Redis Pub/Sub + 事件驱动架构
- **光标同步**: 实时位置广播和颜色标识
- **变更追踪**: 细粒度的操作记录和回放

### 文件格式支持
- **文档格式**: DOCX, DOC, PDF, TXT, MD, RTF, ODT
- **表格格式**: XLSX, XLS, CSV, ODS
- **演示格式**: PPTX, PPT, ODP
- **代码格式**: 支持100+编程语言语法高亮
- **图片格式**: JPG, PNG, GIF, SVG, WebP (查看模式)

### 版本控制系统
- **存储方式**: 增量存储 + 快照备份
- **版本算法**: 基于内容哈希的去重机制
- **分支管理**: 支持分支创建和合并
- **回滚机制**: 快速回滚到任意历史版本

---

## 📊 性能指标

### 处理性能
| 指标 | 数值 | 说明 |
|------|------|------|
| 文件加载时间 | < 3秒 | 100MB以内文件 |
| 编辑响应延迟 | < 100ms | 本地编辑操作 |
| 协作同步延迟 | < 200ms | 多用户协作 |
| 并发编辑用户 | 50+ | 单文件同时编辑 |
| 文件上传速度 | 10MB/s | 依赖网络带宽 |
| 版本切换时间 | < 2秒 | 历史版本加载 |

### 资源消耗
- **内存使用**: 平均 1GB (大文件编辑时)
- **CPU使用**: 平均 < 40%
- **磁盘空间**: 版本文件增量存储
- **网络带宽**: 依赖文件大小和协作频率

---

---

## 🛠️ 配置说明

### 服务配置
```yaml
server:
  port: 8080
  servlet:
    context-path: /
  tomcat:
    max-http-form-post-size: 100MB
    max-swallow-size: 100MB
    connection-timeout: 300000    # 5分钟
    max-connections: 8192
    max-threads: 200

spring:
  application:
    name: wenshu-editfile
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      file-size-threshold: 2KB
      location: ${java.io.tmpdir}
```

### 编辑器配置
```yaml
wenshu:
  editfile:
    # 文件存储配置
    storage:
      base-path: /data/editfile
      temp-path: /tmp/editfile
      max-file-size: 100MB
      allowed-types:
        - "docx,doc,pdf,txt,md,rtf"
        - "xlsx,xls,csv"
        - "pptx,ppt"
        - "js,ts,java,py,cpp,html,css"

    # 编辑器配置
    editors:
      office:
        server-url: "http://onlyoffice:8080"
        jwt-secret: "your-jwt-secret"
        timeout: 300000

      code:
        theme: "vs-dark"
        font-size: 14
        tab-size: 4
        word-wrap: true
        minimap: true

      markdown:
        theme: "default"
        preview-mode: "side-by-side"
        math-support: true
        mermaid-support: true

    # 协作配置
    collaboration:
      max-collaborators: 50
      sync-interval: 1000          # 同步间隔(毫秒)
      heartbeat-interval: 30000    # 心跳间隔(毫秒)
      session-timeout: 1800000     # 会话超时(30分钟)

    # 版本控制配置
    version:
      max-versions: 100            # 最大版本数
      auto-save-interval: 30000    # 自动保存间隔(毫秒)
      compress-old-versions: true  # 压缩旧版本
      retention-days: 365          # 版本保留天数

    # 权限配置
    permissions:
      default-role: "viewer"
      allow-anonymous: false
      max-share-duration: 30       # 最大分享天数
```

### Redis配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password:
    database: 0
    timeout: 5000
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5

wenshu:
  editfile:
    redis:
      key-prefix: "editfile:"
      collaboration-prefix: "collab:"
      session-ttl: 1800            # 会话TTL(秒)
      lock-ttl: 300               # 锁TTL(秒)
```

---

## 🔍 错误处理

### 常见错误码
| 错误码 | 错误信息 | 解决方案 |
|-------|---------|----------|
| 400 | 文件格式不支持 | 检查文件格式是否在支持列表中 |
| 400 | 文件大小超限 | 文件大小不能超过100MB |
| 401 | 未授权访问 | 提供有效的JWT Token |
| 403 | 权限不足 | 检查用户对文件的访问权限 |
| 404 | 文件不存在 | 确认文件ID正确且文件未被删除 |
| 409 | 文件正在编辑 | 文件被其他用户锁定编辑 |
| 500 | 编辑器加载失败 | 检查编辑器服务状态 |
| 500 | 协作同步失败 | 检查WebSocket连接和Redis状态 |

### 错误响应格式
```json
{
  "code": 403,
  "msg": "权限不足：您没有编辑此文件的权限",
  "data": {
    "fileId": "file_12345",
    "requiredPermission": "write",
    "userPermissions": ["read", "comment"],
    "suggestion": "请联系文件所有者获取编辑权限"
  },
  "timestamp": 1640995200000
}
```

---

## 🔒 安全措施

### 文件安全
- **格式验证**: 严格验证文件格式和内容
- **病毒扫描**: 上传文件安全检查
- **大小限制**: 防止大文件攻击
- **访问控制**: 基于角色的权限管理

### 编辑安全
- **内容过滤**: 防止XSS和恶意脚本注入
- **操作审计**: 记录所有编辑操作
- **版本保护**: 防止恶意删除历史版本
- **会话管理**: 安全的协作会话控制

### 数据保护
- **传输加密**: HTTPS/WSS加密传输
- **存储加密**: 敏感文件内容加密存储
- **备份机制**: 定期数据备份和恢复
- **隐私保护**: 用户数据隔离和匿名化

---

## 💻 开发集成指南

### 客户端集成示例

#### JavaScript/Web集成
```javascript
// 文件编辑客户端
class WenshuEditFileClient {
  constructor(baseUrl, token) {
    this.baseUrl = baseUrl;
    this.token = token;
    this.currentFile = null;
    this.editor = null;
    this.collaborationWs = null;
  }

  // 上传文件
  async uploadFile(file, folderId = null, description = null) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('userId', this.getCurrentUserId());
    if (folderId) formData.append('folderId', folderId);
    if (description) formData.append('description', description);

    try {
      const response = await fetch(`${this.baseUrl}/api/files/upload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`
        },
        body: formData
      });

      const result = await response.json();
      if (result.code === 200) {
        console.log('文件上传成功:', result.data);
        return result.data;
      } else {
        throw new Error(result.msg);
      }
    } catch (error) {
      console.error('文件上传失败:', error);
      throw error;
    }
  }

  // 打开文件编辑器
  async openEditor(fileId, mode = 'edit', editorType = 'auto') {
    try {
      const response = await fetch(
        `${this.baseUrl}/api/edit/${fileId}?mode=${mode}&editor=${editorType}`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.token}`
          }
        }
      );

      const result = await response.json();
      if (result.code === 200) {
        this.currentFile = result.data;
        await this.initializeEditor(result.data);
        await this.setupCollaboration(result.data.collaborationInfo);
        return result.data;
      } else {
        throw new Error(result.msg);
      }
    } catch (error) {
      console.error('编辑器加载失败:', error);
      throw error;
    }
  }

  // 初始化编辑器
  async initializeEditor(fileData) {
    const { editorType, editorConfig, fileContent } = fileData;

    switch (editorType) {
      case 'office':
        await this.initOfficeEditor(editorConfig, fileContent);
        break;
      case 'code':
        await this.initCodeEditor(editorConfig, fileContent);
        break;
      case 'markdown':
        await this.initMarkdownEditor(editorConfig, fileContent);
        break;
      default:
        throw new Error(`不支持的编辑器类型: ${editorType}`);
    }
  }

  // 初始化Office编辑器
  async initOfficeEditor(config, content) {
    // 使用OnlyOffice或其他Office编辑器
    this.editor = new DocsAPI.DocEditor("editor-container", {
      document: {
        fileType: content.format,
        key: this.currentFile.fileId,
        title: this.currentFile.fileName,
        url: content.downloadUrl
      },
      editorConfig: {
        mode: config.mode,
        lang: config.language,
        callbackUrl: `${this.baseUrl}/api/edit/${this.currentFile.fileId}/callback`,
        user: {
          id: this.getCurrentUserId(),
          name: this.getCurrentUserName()
        },
        customization: {
          autosave: config.autosave.enabled,
          toolbar: config.toolbar
        }
      },
      events: {
        onDocumentReady: () => {
          console.log('Office编辑器加载完成');
        },
        onDocumentStateChange: (event) => {
          if (event.data) {
            this.handleDocumentChange(event.data);
          }
        }
      }
    });
  }

  // 初始化代码编辑器
  async initCodeEditor(config, content) {
    // 使用Monaco Editor
    require.config({ paths: { vs: '/monaco-editor/min/vs' } });

    require(['vs/editor/editor.main'], () => {
      this.editor = monaco.editor.create(document.getElementById('editor-container'), {
        value: content.content,
        language: this.detectLanguage(content.format),
        theme: config.theme,
        fontSize: config.fontSize,
        tabSize: config.tabSize,
        wordWrap: config.wordWrap,
        minimap: { enabled: config.minimap }
      });

      // 监听内容变化
      this.editor.onDidChangeModelContent((event) => {
        this.handleContentChange(event);
      });

      // 监听光标位置变化
      this.editor.onDidChangeCursorPosition((event) => {
        this.handleCursorChange(event);
      });

      console.log('代码编辑器加载完成');
    });
  }

  // 设置协作功能
  async setupCollaboration(collaborationInfo) {
    if (!collaborationInfo || !collaborationInfo.websocketUrl) {
      return;
    }

    this.collaborationWs = new WebSocket(collaborationInfo.websocketUrl);

    this.collaborationWs.onopen = () => {
      console.log('协作连接已建立');
      this.sendCollaborationMessage({
        type: 'join',
        sessionId: collaborationInfo.sessionId,
        userInfo: {
          userId: this.getCurrentUserId(),
          userName: this.getCurrentUserName()
        }
      });
    };

    this.collaborationWs.onmessage = (event) => {
      const message = JSON.parse(event.data);
      this.handleCollaborationMessage(message);
    };

    this.collaborationWs.onclose = () => {
      console.log('协作连接已断开');
      this.attemptReconnection();
    };
  }

  // 处理协作消息
  handleCollaborationMessage(message) {
    switch (message.type) {
      case 'user_joined':
        this.showUserJoined(message.data);
        break;
      case 'user_left':
        this.showUserLeft(message.data);
        break;
      case 'content_change':
        this.applyRemoteChange(message.data);
        break;
      case 'cursor_change':
        this.updateRemoteCursor(message.data);
        break;
      case 'comment_added':
        this.showNewComment(message.data);
        break;
    }
  }

  // 保存文件
  async saveFile(content, saveType = 'manual', versionComment = null) {
    try {
      const response = await fetch(`${this.baseUrl}/api/edit/${this.currentFile.fileId}/save`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content: content,
          format: this.currentFile.fileContent.format,
          saveType: saveType,
          versionComment: versionComment,
          createNewVersion: saveType === 'manual'
        })
      });

      const result = await response.json();
      if (result.code === 200) {
        console.log('文件保存成功:', result.data);
        this.showSaveSuccess(result.data);
        return result.data;
      } else {
        throw new Error(result.msg);
      }
    } catch (error) {
      console.error('文件保存失败:', error);
      this.showSaveError(error);
      throw error;
    }
  }

  // 添加评论
  async addComment(content, position, commentType = 'suggestion') {
    try {
      const response = await fetch(`${this.baseUrl}/api/comments/${this.currentFile.fileId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content: content,
          position: position,
          commentType: commentType,
          isPrivate: false
        })
      });

      const result = await response.json();
      if (result.code === 200) {
        console.log('评论添加成功:', result.data);
        this.showComment(result.data);
        return result.data;
      } else {
        throw new Error(result.msg);
      }
    } catch (error) {
      console.error('评论添加失败:', error);
      throw error;
    }
  }

  // 获取文件版本历史
  async getVersionHistory(page = 1, size = 10) {
    try {
      const response = await fetch(
        `${this.baseUrl}/api/versions/${this.currentFile.fileId}?page=${page}&size=${size}`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.token}`
          }
        }
      );

      const result = await response.json();
      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg);
      }
    } catch (error) {
      console.error('版本历史查询失败:', error);
      throw error;
    }
  }

  // 辅助方法
  getCurrentUserId() {
    // 从JWT Token或其他地方获取用户ID
    return 1001;
  }

  getCurrentUserName() {
    // 从JWT Token或其他地方获取用户名
    return '张三';
  }

  detectLanguage(fileFormat) {
    const languageMap = {
      'js': 'javascript',
      'ts': 'typescript',
      'java': 'java',
      'py': 'python',
      'cpp': 'cpp',
      'html': 'html',
      'css': 'css',
      'md': 'markdown',
      'json': 'json',
      'xml': 'xml'
    };
    return languageMap[fileFormat] || 'plaintext';
  }

  showSaveSuccess(data) {
    // 显示保存成功提示
    console.log('保存成功:', data);
  }

  showSaveError(error) {
    // 显示保存失败提示
    console.error('保存失败:', error);
  }

  showComment(comment) {
    // 显示新评论
    console.log('新评论:', comment);
  }

  // 清理资源
  cleanup() {
    if (this.collaborationWs) {
      this.collaborationWs.close();
    }
    if (this.editor) {
      this.editor.dispose();
    }
  }
}

// 使用示例
const editClient = new WenshuEditFileClient('http://localhost:8080', 'your-jwt-token');

// 上传文件
document.getElementById('fileInput').addEventListener('change', async (event) => {
  const file = event.target.files[0];
  if (file) {
    try {
      const result = await editClient.uploadFile(file, 'folder_123', '项目文档');
      console.log('上传成功:', result);

      // 上传成功后打开编辑器
      await editClient.openEditor(result.fileId, 'edit', 'auto');
    } catch (error) {
      alert('上传失败: ' + error.message);
    }
  }
});

// 页面关闭时清理资源
window.addEventListener('beforeunload', () => {
  editClient.cleanup();
});
```

---

---

## 🚀 部署指南

### Docker部署
```dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app

# 安装必要的工具和字体
RUN apt-get update && apt-get install -y \
    curl \
    fonts-dejavu-core \
    fontconfig \
    libreoffice \
    && rm -rf /var/lib/apt/lists/*

# 复制应用程序
COPY target/wenshu-editfile-0.0.1-SNAPSHOT.jar app.jar

# 创建必要的目录
RUN mkdir -p /app/logs/wenshu-editfile
RUN mkdir -p /data/editfile
RUN mkdir -p /tmp/editfile

# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' > /etc/timezone

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", "-jar", "-Xmx2g", "-Xms1g", "app.jar"]
```

### Docker Compose部署
```yaml
version: '3.8'

services:
  wenshu-editfile:
    build: .
    container_name: wenshu-editfile
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - MYSQL_URL=********************************
      - MYSQL_USERNAME=root
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - ONLYOFFICE_URL=http://onlyoffice:8080
    volumes:
      - ./logs:/app/logs
      - ./data:/data/editfile
      - ./temp:/tmp/editfile
    depends_on:
      - mysql
      - redis
      - onlyoffice
    networks:
      - wenshu-network
    restart: unless-stopped

  onlyoffice:
    image: onlyoffice/documentserver:latest
    container_name: onlyoffice
    ports:
      - "8081:80"
    environment:
      - JWT_ENABLED=true
      - JWT_SECRET=${ONLYOFFICE_JWT_SECRET}
    volumes:
      - onlyoffice_data:/var/www/onlyoffice/Data
      - onlyoffice_logs:/var/log/onlyoffice
    networks:
      - wenshu-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - wenshu-network
    command: redis-server --appendonly yes

  mysql:
    image: mysql:8.0
    container_name: mysql
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_DATABASE=ry-cloud
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - wenshu-network

volumes:
  mysql_data:
  redis_data:
  onlyoffice_data:
  onlyoffice_logs:

networks:
  wenshu-network:
    driver: bridge
```

### Kubernetes部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wenshu-editfile
  labels:
    app: wenshu-editfile
spec:
  replicas: 3
  selector:
    matchLabels:
      app: wenshu-editfile
  template:
    metadata:
      labels:
        app: wenshu-editfile
    spec:
      containers:
      - name: wenshu-editfile
        image: wenshu/editfile:1.0.0
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: MYSQL_URL
          value: "****************************************"
        - name: REDIS_HOST
          value: "redis-service"
        - name: ONLYOFFICE_URL
          value: "http://onlyoffice-service:80"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: data
          mountPath: /data/editfile
        - name: temp
          mountPath: /tmp/editfile
      volumes:
      - name: logs
        emptyDir: {}
      - name: data
        persistentVolumeClaim:
          claimName: editfile-data-pvc
      - name: temp
        emptyDir:
          sizeLimit: 10Gi

---
apiVersion: v1
kind: Service
metadata:
  name: wenshu-editfile-service
spec:
  selector:
    app: wenshu-editfile
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: wenshu-editfile-ingress
  annotations:
    nginx.ingress.kubernetes.io/websocket-services: "wenshu-editfile-service"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
spec:
  rules:
  - host: edit.wenshu.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: wenshu-editfile-service
            port:
              number: 8080
```

---

## 📞 技术支持

### 常见问题解答

**Q1: 支持哪些文件格式？**
A: 支持Office文档(DOCX/XLSX/PPTX)、PDF、文本文件、代码文件等多种格式。

**Q2: 多人协作时如何处理冲突？**
A: 使用Operational Transformation算法自动解决编辑冲突，确保数据一致性。

**Q3: 文件大小有限制吗？**
A: 单个文件最大支持100MB，可根据服务器配置调整。

**Q4: 如何保证文件安全？**
A: 采用多层安全机制，包括权限控制、传输加密、内容过滤等。

**Q5: 支持离线编辑吗？**
A: 目前主要支持在线编辑，离线功能在开发计划中。

**Q6: 版本历史保存多久？**
A: 默认保存365天，可根据配置调整保留期限。

### 性能优化建议

1. **服务器配置**
   - 推荐内存: 4GB+
   - 推荐CPU: 4核+
   - 推荐存储: SSD
   - 网络带宽: 100Mbps+

2. **数据库优化**
   - 使用MySQL 8.0+
   - 配置适当的连接池
   - 定期清理过期数据
   - 建立合适的索引

3. **缓存优化**
   - 使用Redis集群
   - 配置合理的TTL
   - 启用数据压缩
   - 监控缓存命中率

4. **文件存储优化**
   - 使用对象存储(如MinIO)
   - 启用CDN加速
   - 配置文件压缩
   - 实现分层存储

### 故障排查指南

#### 编辑器加载问题
```bash
# 检查编辑器服务状态
curl http://localhost:8080/actuator/health

# 检查OnlyOffice服务
curl http://onlyoffice:8080/healthcheck

# 查看应用日志
tail -f logs/wenshu-editfile/editfile.log | grep "editor"
```

#### 协作同步问题
```bash
# 检查WebSocket连接
wscat -c ws://localhost:8080/ws/collaboration/file_12345

# 检查Redis连接
redis-cli ping

# 查看协作日志
tail -f logs/wenshu-editfile/editfile.log | grep "collaboration"
```

#### 文件上传问题
```bash
# 检查磁盘空间
df -h

# 检查文件权限
ls -la /data/editfile

# 查看上传日志
tail -f logs/wenshu-editfile/editfile.log | grep "upload"
```

### 联系方式
- **技术支持**: <EMAIL>
- **API文档**: https://docs.wenshu.com/editfile
- **问题反馈**: https://github.com/wenshu/editfile/issues
- **在线帮助**: https://help.wenshu.com/editfile

### 版本更新日志

#### v1.0.0 (2024-12-28)
- ✨ 新增多格式文件在线编辑
- ✨ 新增实时协作功能
- ✨ 新增版本控制系统
- ✨ 新增评论和注释功能
- ✨ 新增文件分享功能
- ✨ 新增权限管理系统
- 🔧 优化编辑器性能
- 🔧 优化协作同步机制
- 🐛 修复文件上传问题
- 🐛 修复版本比较异常

#### 即将发布
- 🚀 支持移动端编辑
- 🚀 支持离线编辑功能
- 🚀 支持更多文件格式
- 🚀 支持AI辅助编辑
- 🚀 支持插件扩展系统

---

*文档版本: v1.0.0 | 最后更新: 2024-12-28 | Wenshu-EditFile在线文件编辑服务*
