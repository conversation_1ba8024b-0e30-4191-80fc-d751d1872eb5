# Tomcat
server:
  port: 8701

# Spring
spring:
  main:
    allow-bean-definition-overriding: true
  datasource:
    url: jdbc:mysql://************:3306/ry-cloud?&serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=UTF8&autoReconnect=true&useSSL=false&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true
    username: root
    password: 2313147023
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
  ai:
    dashscope:
      api-key: sk-574f46304e5c4405aa5bbe26af6489b0
      chat:
        model: deepseek-r1-distill-llama-70b
      embedding:
        options:
            model: text-embedding-v1
    vectorstore:
      milvus:
        client:
          host: "************"
          port: 19530
        databaseName: "default"
        collectionName: "vector_store"
        embeddingDimension: 1536
        indexType: IVF_FLAT
        metricType: COSINE
        initialize-schema: true
        indexParams:
          nlist: 1024    # 聚类中心数 (典型值：4√N，N=向量总数)
        searchParams:
          nprobe: 128     # 查询时扫描的聚类中心数 (典型值：5-10% of nlist)
        consistencyLevel: "Bounded" # 一致性级别


  application:
    # 应用名称
    name: wenshu-chat
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: ************:8848
      config:
        # 配置中心地址
        server-addr: ************:8848
        # 配置文件格式
        file-extension: yml

# Feign配置
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 5000
  httpclient:
    enabled: true

# 日志配置
logging:
  level:
    root: INFO
    com.ruoyi.wenshuchat: DEBUG
    org.springframework.ai: DEBUG
    com.ruoyi.wenshuapi.client: DEBUG

# MyBatis-Plus 配置
mybatis-plus:
  type-aliases-package: com.ruoyi.wenshucommon.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

# 程序管理服务URL配置
programme:
  service:
    url: http://localhost:8080