# Wenshu-Voice 语音处理模块 - 详细API接口文档

## 📋 模块概述

**服务名称**: wenshu-voice  
**服务端口**: 1014  
**版本**: v1.0.0  
**最后更新**: 2024-12-28  

**核心功能**: 智能语音处理服务，提供语音识别、语音交互、AI对话集成等功能

**技术栈**:
- Spring Boot 3.x
- Spring Cloud 2023.x
- 阿里云DashScope语音识别
- Spring AI集成
- Nacos服务注册与发现
- OpenFeign微服务调用

---

## 🏗️ 服务架构

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    Wenshu-Voice 语音处理服务                  │
│                        (Port: 1014)                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│  语音识别模块   │ │AI交互模块│ │ 文件处理模块 │
│ (ASR Engine)  │ │(AI Chat)│ │(File Proc)│
└───────────────┘ └────────┘ └───────────┘
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│ 阿里云DashScope│ │wenshu-chat│ │音频格式转换│
│   语音服务     │ │  服务   │ │   工具    │
└───────────────┘ └────────┘ └───────────┘
```

### 服务依赖关系
- **wenshu-chat**: AI智能对话服务
- **wenshu-calebdar**: 日程管理服务  
- **wenshu-api**: 通用API服务
- **wenshu-common**: 公共工具库
- **阿里云DashScope**: 语音识别引擎

---

## 🌐 REST API接口

### 基础信息
- **Base URL**: `http://localhost:1014`
- **API前缀**: `/api/voice`
- **Content-Type**: `multipart/form-data` (文件上传) / `application/json`
- **鉴权方式**: JWT Token (Header: `Authorization: Bearer <token>`)

### 文件上传限制
- **单文件最大**: 1000MB (1GB)
- **支持格式**: WAV, MP3, M4A, FLAC等主流音频格式
- **推荐格式**: WAV (单声道, 16kHz采样率)
- **连接超时**: 1小时
- **处理超时**: 1小时

---

## 📚 API接口详情

### 1. 语音识别接口

#### 1.1 音频转文字
**接口路径**: `POST /api/voice/SpeechRecognition`

**功能描述**: 将上传的音频文件转换为文字，支持文本去重处理

**请求参数**:
```http
POST /api/voice/SpeechRecognition
Content-Type: multipart/form-data

file: [音频文件] (必填)
```

**请求示例**:
```bash
curl -X POST http://localhost:1014/api/voice/SpeechRecognition \
  -F "file=@meeting_audio.wav"
```

**响应格式**:
```json
{
  "code": 200,
  "message": "音频处理成功",
  "data": [
    "去重后的识别文本内容",
    "识别置信度分数"
  ]
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "音频处理成功", 
  "data": [
    "明天下午三点在会议室A开项目讨论会",
    "0.95"
  ]
}
```

**错误响应**:
```json
{
  "code": 500,
  "message": "音频格式错误：仅支持单声道音频，请转换音频格式后重试",
  "data": null
}
```

**处理流程**:
1. 🎵 音频文件上传和格式检测
2. 🔄 音频格式转换和优化
3. 🤖 阿里云DashScope语音识别
4. 🧹 智能文本去重处理
5. 📝 返回处理结果
6. 🗑️ 自动清理临时文件

---

### 2. 语音交互接口

#### 2.1 智能语音交互
**接口路径**: `POST /api/voice/VoiceInteraction`

**功能描述**: 完整的语音交互流程，包括语音识别、意图识别、AI处理和响应生成

**请求参数**:
```http
POST /api/voice/VoiceInteraction
Content-Type: multipart/form-data

file: [音频文件] (必填)
conversationId: [会话ID] (必填)
userId: [用户ID] (必填)
```

**请求示例**:
```bash
curl -X POST http://localhost:1014/api/voice/VoiceInteraction \
  -F "file=@voice_command.wav" \
  -F "conversationId=conv_123456" \
  -F "userId=1001"
```

**响应格式**:
```json
{
  "code": 200,
  "message": "语音交互处理成功",
  "data": {
    "mode": "create|query|chat",
    "jsonResponse": {},
    "chatResponse": "AI回复内容"
  }
}
```

#### 2.1.1 日程创建模式响应
```json
{
  "code": 200,
  "message": "语音交互处理成功",
  "data": {
    "mode": "create",
    "jsonResponse": {
      "title": "项目讨论会",
      "description": "在会议室A讨论项目进展",
      "startTime": "2024-12-29T15:00:00",
      "creatorId": 1001,
      "ownerType": "个人",
      "repeatRule": "一次性",
      "remindTime": "2024-12-29T14:50:00",
      "eventStatus": 1
    },
    "chatResponse": "好的，我已经为您安排了明天下午3点在会议室A的项目讨论会。"
  }
}
```

#### 2.1.2 日程查询模式响应
```json
{
  "code": 200,
  "message": "语音交互处理成功",
  "data": {
    "mode": "query",
    "jsonResponse": [
      {
        "eventId": 1,
        "title": "项目会议",
        "startTime": "2024-12-29T15:00:00",
        "description": "讨论项目进展"
      }
    ],
    "chatResponse": "您明天有1个安排：下午3点的项目会议。"
  }
}
```

#### 2.1.3 普通聊天模式响应
```json
{
  "code": 200,
  "message": "语音交互处理成功",
  "data": {
    "mode": "chat",
    "chatResponse": "今天天气不错，适合外出活动。有什么我可以帮助您的吗？"
  }
}
```

**处理流程**:
1. 🎵 音频文件处理和语音识别
2. 🧠 AI意图识别（日程创建/查询/普通聊天）
3. 📅 根据意图执行相应操作
4. 🤖 生成智能回复
5. 📝 返回结构化结果

---

### 3. 测试接口

#### 3.1 日期格式测试
**接口路径**: `GET /api/voice/test`

**功能描述**: 测试日期时间格式解析和转换功能

**请求参数**:
```http
GET /api/voice/test?a=2024-12-29 15:00:00&b=2024-12-29 18:00:00
```

**响应示例**:
```json
{
  "code": 200,
  "message": "日期格式化成功",
  "data": [
    "2024-12-29 15:00:00",
    "2024-12-29 18:00:00"
  ]
}
```

---

## 🔧 技术实现细节

### 语音识别引擎
- **服务提供商**: 阿里云DashScope
- **识别模型**: 通用语音识别模型
- **支持语言**: 中文普通话
- **识别精度**: > 95%
- **实时性**: 支持流式识别

### 音频处理能力
- **格式支持**: WAV, MP3, M4A, FLAC, AAC
- **自动转换**: 智能音频格式转换
- **采样率**: 自动检测和调整
- **声道处理**: 自动转换为单声道
- **降噪处理**: 基础音频降噪

### AI交互集成
- **意图识别**: 智能识别用户意图类型
- **上下文记忆**: 支持多轮对话上下文
- **日程操作**: 自动创建和查询日程
- **自然语言**: 自然语言理解和生成

---

## 📊 性能指标

### 处理性能
| 指标 | 数值 | 说明 |
|------|------|------|
| 音频识别延迟 | < 5秒 | 1分钟音频 |
| 并发处理能力 | 50+ | 同时处理请求数 |
| 文件大小限制 | 1GB | 单个音频文件 |
| 识别准确率 | > 95% | 标准普通话 |
| 服务可用性 | > 99.9% | 月度统计 |

### 资源消耗
- **内存使用**: 平均 512MB
- **CPU使用**: 平均 < 50%
- **磁盘空间**: 临时文件自动清理
- **网络带宽**: 依赖音频文件大小

---

## 🛠️ 配置说明

### 服务配置
```yaml
server:
  port: 1014
  tomcat:
    max-http-post-size: 1048576000  # 1GB
    connection-timeout: 3600000     # 1小时
    max-connections: 8192
    max-threads: 200

spring:
  servlet:
    multipart:
      max-file-size: 1000MB
      max-request-size: 1000MB
      enabled: true
```

### AI服务配置
```yaml
spring:
  ai:
    dashscope:
      api-key: ${DASHSCOPE_API_KEY}
      chat:
        options:
          model: qwen-plus
```

### Feign客户端配置
```yaml
feign:
  client:
    config:
      default:
        connect-timeout: 30000   # 30秒
        read-timeout: 3600000    # 1小时
        logger-level: basic
  hystrix:
    enabled: true
```

---

## 🔍 错误处理

### 常见错误码
| 错误码 | 错误信息 | 解决方案 |
|-------|---------|----------|
| 500001 | 音频格式错误：仅支持单声道音频 | 转换为单声道音频格式 |
| 500002 | 音频采样率不匹配 | 系统自动调整，请重试 |
| 500003 | 音频重采样失败 | 检查音频文件完整性 |
| 500004 | MP3解码失败 | 建议使用WAV格式 |
| 500005 | 音频解码失败 | 音频文件可能损坏 |
| 500006 | 语音识别服务状态异常 | 稍后重试 |
| 500007 | 网络连接错误 | 检查网络连接 |
| 500008 | API密钥错误 | 检查服务配置 |
| 500009 | 文件大小超限 | 压缩音频文件 |
| 500010 | 处理超时 | 尝试分段处理 |

### 错误处理策略
1. **自动重试**: 网络错误自动重试3次
2. **格式转换**: 自动转换音频格式
3. **降级处理**: AI服务不可用时返回基础识别结果
4. **资源清理**: 异常情况下自动清理临时文件

---

## 🔒 安全措施

### 文件安全
- **格式验证**: 严格验证音频文件格式
- **大小限制**: 限制单文件最大1GB
- **病毒扫描**: 上传文件安全检查
- **临时存储**: 处理完成后自动删除

### 接口安全
- **JWT认证**: 所有接口需要有效Token
- **限流控制**: 防止恶意大量请求
- **参数验证**: 严格验证所有输入参数
- **日志记录**: 完整的操作日志记录

### 数据保护
- **传输加密**: HTTPS加密传输
- **存储加密**: 敏感数据加密存储
- **访问控制**: 基于角色的访问控制
- **审计日志**: 完整的审计追踪

---

## 📈 监控指标

### 业务指标
- 语音识别成功率
- 平均处理时间
- 用户活跃度
- 错误率统计

### 技术指标
- 服务响应时间
- 内存使用率
- CPU使用率
- 网络吞吐量

### 告警规则
- 错误率 > 5% 触发告警
- 响应时间 > 10秒 触发告警
- 服务不可用立即告警
- 资源使用率 > 80% 预警

---

---

## 💻 开发集成指南

### 客户端集成示例

#### JavaScript/Web集成
```javascript
// 语音识别接口调用
async function speechRecognition(audioFile) {
  const formData = new FormData();
  formData.append('file', audioFile);

  try {
    const response = await fetch('/api/voice/SpeechRecognition', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + getToken()
      },
      body: formData
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('识别结果:', result.data[0]);
      console.log('置信度:', result.data[1]);
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('语音识别失败:', error);
    throw error;
  }
}

// 语音交互接口调用
async function voiceInteraction(audioFile, conversationId, userId) {
  const formData = new FormData();
  formData.append('file', audioFile);
  formData.append('conversationId', conversationId);
  formData.append('userId', userId);

  try {
    const response = await fetch('/api/voice/VoiceInteraction', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + getToken()
      },
      body: formData
    });

    const result = await response.json();
    if (result.code === 200) {
      const { mode, jsonResponse, chatResponse } = result.data;

      switch (mode) {
        case 'create':
          console.log('创建日程:', jsonResponse);
          break;
        case 'query':
          console.log('查询结果:', jsonResponse);
          break;
        case 'chat':
          console.log('聊天回复:', chatResponse);
          break;
      }

      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('语音交互失败:', error);
    throw error;
  }
}
```

#### Python集成示例
```python
import requests
import json

class WenshuVoiceClient:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {token}'
        }

    def speech_recognition(self, audio_file_path):
        """语音识别"""
        url = f"{self.base_url}/api/voice/SpeechRecognition"

        with open(audio_file_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(url, headers=self.headers, files=files)

        result = response.json()
        if result['code'] == 200:
            return {
                'text': result['data'][0],
                'confidence': result['data'][1]
            }
        else:
            raise Exception(f"语音识别失败: {result['message']}")

    def voice_interaction(self, audio_file_path, conversation_id, user_id):
        """语音交互"""
        url = f"{self.base_url}/api/voice/VoiceInteraction"

        with open(audio_file_path, 'rb') as f:
            files = {'file': f}
            data = {
                'conversationId': conversation_id,
                'userId': user_id
            }
            response = requests.post(url, headers=self.headers, files=files, data=data)

        result = response.json()
        if result['code'] == 200:
            return result['data']
        else:
            raise Exception(f"语音交互失败: {result['message']}")

# 使用示例
client = WenshuVoiceClient('http://localhost:1014', 'your-jwt-token')

# 语音识别
recognition_result = client.speech_recognition('audio.wav')
print(f"识别文本: {recognition_result['text']}")

# 语音交互
interaction_result = client.voice_interaction('command.wav', 'conv_123', 1001)
print(f"交互模式: {interaction_result['mode']}")
print(f"AI回复: {interaction_result['chatResponse']}")
```

#### Java集成示例
```java
@Service
public class WenshuVoiceService {

    @Value("${wenshu.voice.base-url}")
    private String baseUrl;

    private final RestTemplate restTemplate;

    public WenshuVoiceService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    /**
     * 语音识别
     */
    public SpeechRecognitionResult speechRecognition(MultipartFile audioFile, String token) {
        String url = baseUrl + "/api/voice/SpeechRecognition";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.setBearerAuth(token);

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", audioFile.getResource());

        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        ResponseEntity<ApiResponse> response = restTemplate.postForEntity(url, requestEntity, ApiResponse.class);

        if (response.getBody().getCode() == 200) {
            List<String> data = (List<String>) response.getBody().getData();
            return new SpeechRecognitionResult(data.get(0), data.get(1));
        } else {
            throw new RuntimeException("语音识别失败: " + response.getBody().getMessage());
        }
    }

    /**
     * 语音交互
     */
    public VoiceInteractionResult voiceInteraction(MultipartFile audioFile, String conversationId,
                                                  Integer userId, String token) {
        String url = baseUrl + "/api/voice/VoiceInteraction";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.setBearerAuth(token);

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", audioFile.getResource());
        body.add("conversationId", conversationId);
        body.add("userId", userId);

        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        ResponseEntity<ApiResponse> response = restTemplate.postForEntity(url, requestEntity, ApiResponse.class);

        if (response.getBody().getCode() == 200) {
            return objectMapper.convertValue(response.getBody().getData(), VoiceInteractionResult.class);
        } else {
            throw new RuntimeException("语音交互失败: " + response.getBody().getMessage());
        }
    }
}
```

---

## 🎯 使用场景示例

### 场景1: 智能会议助手
```javascript
// 会议录音处理流程
async function processMeetingAudio(audioFile, meetingId) {
  try {
    // 1. 语音识别转文字
    const recognition = await speechRecognition(audioFile);
    console.log('会议内容转录:', recognition[0]);

    // 2. 智能提取会议要点
    const interaction = await voiceInteraction(
      audioFile,
      `meeting_${meetingId}`,
      getCurrentUserId()
    );

    // 3. 自动创建后续日程
    if (interaction.mode === 'create') {
      console.log('自动创建日程:', interaction.jsonResponse);
      await createSchedule(interaction.jsonResponse);
    }

    // 4. 生成会议纪要
    const summary = interaction.chatResponse;
    await saveMeetingSummary(meetingId, {
      transcript: recognition[0],
      summary: summary,
      schedules: interaction.jsonResponse
    });

  } catch (error) {
    console.error('会议音频处理失败:', error);
  }
}
```

### 场景2: 语音日程助手
```javascript
// 语音创建日程
async function createScheduleByVoice(audioFile, userId) {
  try {
    const result = await voiceInteraction(
      audioFile,
      `schedule_${Date.now()}`,
      userId
    );

    if (result.mode === 'create') {
      // 显示创建的日程信息
      displayScheduleInfo(result.jsonResponse);

      // 确认创建
      const confirmed = await confirmScheduleCreation(result.jsonResponse);
      if (confirmed) {
        await saveSchedule(result.jsonResponse);
        showNotification('日程创建成功', result.chatResponse);
      }
    } else {
      showMessage(result.chatResponse);
    }
  } catch (error) {
    showError('语音日程创建失败', error.message);
  }
}

// 语音查询日程
async function queryScheduleByVoice(audioFile, userId) {
  try {
    const result = await voiceInteraction(
      audioFile,
      `query_${Date.now()}`,
      userId
    );

    if (result.mode === 'query') {
      // 显示查询结果
      displayScheduleList(result.jsonResponse);

      // 语音播报结果
      speakText(result.chatResponse);
    } else {
      showMessage(result.chatResponse);
    }
  } catch (error) {
    showError('语音日程查询失败', error.message);
  }
}
```

### 场景3: 智能客服助手
```javascript
// 语音客服交互
async function handleCustomerVoice(audioFile, customerId, sessionId) {
  try {
    const result = await voiceInteraction(
      audioFile,
      `customer_${sessionId}`,
      customerId
    );

    // 记录客服对话
    await logCustomerInteraction({
      customerId: customerId,
      sessionId: sessionId,
      audioFile: audioFile.name,
      response: result.chatResponse,
      mode: result.mode,
      timestamp: new Date()
    });

    // 返回AI回复
    return {
      text: result.chatResponse,
      needHumanService: result.mode === 'complex_query',
      suggestedActions: extractActions(result.jsonResponse)
    };

  } catch (error) {
    console.error('客服语音处理失败:', error);
    return {
      text: '抱歉，我暂时无法理解您的问题，请稍后重试或联系人工客服。',
      needHumanService: true
    };
  }
}
```

---

## 🔧 高级配置

### 自定义音频处理参数
```yaml
# application.yml
wenshu:
  voice:
    audio:
      # 音频处理配置
      max-duration: 3600        # 最大音频时长(秒)
      sample-rate: 16000        # 目标采样率
      channels: 1               # 声道数
      format: wav               # 输出格式

      # 降噪配置
      noise-reduction: true     # 启用降噪
      volume-normalization: true # 音量标准化

      # 分段处理配置
      segment-duration: 60      # 分段时长(秒)
      overlap-duration: 5       # 重叠时长(秒)

    recognition:
      # 识别引擎配置
      engine: dashscope         # 识别引擎
      language: zh-CN           # 识别语言
      model: general            # 识别模型

      # 识别参数
      enable-punctuation: true  # 启用标点符号
      enable-word-timestamp: true # 启用词级时间戳
      confidence-threshold: 0.8  # 置信度阈值

    ai:
      # AI交互配置
      max-context-length: 4000  # 最大上下文长度
      temperature: 0.7          # 创造性参数
      max-tokens: 1000          # 最大输出长度
```

### 自定义错误处理
```java
@ControllerAdvice
public class VoiceExceptionHandler {

    @ExceptionHandler(AudioProcessingException.class)
    public ResponseEntity<RestResultL> handleAudioProcessingException(AudioProcessingException e) {
        log.error("音频处理异常", e);

        String userFriendlyMessage = getUserFriendlyMessage(e);
        return ResponseEntity.ok(RestResultL.error(500, userFriendlyMessage));
    }

    @ExceptionHandler(SpeechRecognitionException.class)
    public ResponseEntity<RestResultL> handleSpeechRecognitionException(SpeechRecognitionException e) {
        log.error("语音识别异常", e);

        // 根据错误类型返回不同的提示
        if (e.getErrorCode().equals("AUDIO_FORMAT_ERROR")) {
            return ResponseEntity.ok(RestResultL.error(500001, "音频格式不支持，请使用WAV格式"));
        } else if (e.getErrorCode().equals("NETWORK_ERROR")) {
            return ResponseEntity.ok(RestResultL.error(500007, "网络连接异常，请检查网络后重试"));
        } else {
            return ResponseEntity.ok(RestResultL.error(500, "语音识别失败，请重试"));
        }
    }

    private String getUserFriendlyMessage(Exception e) {
        // 将技术错误转换为用户友好的提示
        String message = e.getMessage();
        if (message.contains("sample rate")) {
            return "音频采样率不匹配，系统正在自动调整，请稍候";
        } else if (message.contains("channel")) {
            return "音频声道格式不支持，请使用单声道音频";
        } else {
            return "音频处理失败，请检查音频文件格式";
        }
    }
}
```

### 性能优化配置
```java
@Configuration
public class VoicePerformanceConfig {

    @Bean
    @ConfigurationProperties(prefix = "wenshu.voice.thread-pool")
    public ThreadPoolTaskExecutor voiceProcessingExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("voice-processing-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean
    public CacheManager voiceCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(Duration.ofMinutes(30))
            .recordStats());
        return cacheManager;
    }
}
```

---

## 📊 监控和运维

### 健康检查端点
```http
GET /actuator/health
GET /actuator/metrics
GET /actuator/info
```

### 自定义监控指标
```java
@Component
public class VoiceMetrics {

    private final MeterRegistry meterRegistry;
    private final Counter speechRecognitionCounter;
    private final Timer speechRecognitionTimer;
    private final Gauge activeProcessingGauge;

    public VoiceMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.speechRecognitionCounter = Counter.builder("voice.speech.recognition.total")
            .description("Total speech recognition requests")
            .register(meterRegistry);
        this.speechRecognitionTimer = Timer.builder("voice.speech.recognition.duration")
            .description("Speech recognition processing time")
            .register(meterRegistry);
        this.activeProcessingGauge = Gauge.builder("voice.processing.active")
            .description("Active voice processing tasks")
            .register(meterRegistry, this, VoiceMetrics::getActiveProcessingCount);
    }

    public void recordSpeechRecognition(Duration duration, boolean success) {
        speechRecognitionCounter.increment(
            Tags.of("status", success ? "success" : "failure")
        );
        speechRecognitionTimer.record(duration);
    }

    private double getActiveProcessingCount() {
        // 返回当前活跃的处理任务数
        return ProcessingTaskManager.getActiveTaskCount();
    }
}
```

### 日志配置
```yaml
logging:
  level:
    com.ruoyi.wenshuvoice: DEBUG
    com.ruoyi.wenshucommon.util.voiceutil: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: logs/wenshu-voice/voice.log
    max-size: 100MB
    max-history: 30
```

---

## 🚀 部署指南

### Docker部署
```dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app

COPY target/wenshu-voice-0.0.1-SNAPSHOT.jar app.jar

# 安装音频处理依赖
RUN apt-get update && apt-get install -y \
    ffmpeg \
    sox \
    && rm -rf /var/lib/apt/lists/*

# 创建音频处理目录
RUN mkdir -p /app/uploads /app/temp

EXPOSE 1014

ENTRYPOINT ["java", "-jar", "app.jar"]
```

### Kubernetes部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wenshu-voice
spec:
  replicas: 3
  selector:
    matchLabels:
      app: wenshu-voice
  template:
    metadata:
      labels:
        app: wenshu-voice
    spec:
      containers:
      - name: wenshu-voice
        image: wenshu/voice:1.0.0
        ports:
        - containerPort: 1014
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: NACOS_SERVER_ADDR
          value: "nacos-service:8848"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "2000m"
        volumeMounts:
        - name: temp-storage
          mountPath: /app/temp
      volumes:
      - name: temp-storage
        emptyDir:
          sizeLimit: 10Gi
---
apiVersion: v1
kind: Service
metadata:
  name: wenshu-voice-service
spec:
  selector:
    app: wenshu-voice
  ports:
  - port: 1014
    targetPort: 1014
  type: ClusterIP
```

---

## 📞 技术支持

### 常见问题解答

**Q1: 为什么音频识别失败？**
A: 请检查音频格式是否支持，推荐使用WAV格式，单声道，16kHz采样率。

**Q2: 如何提高识别准确率？**
A: 确保音频清晰，减少背景噪音，使用标准普通话录音。

**Q3: 支持哪些音频格式？**
A: 支持WAV、MP3、M4A、FLAC等格式，系统会自动转换为最佳格式。

**Q4: 如何处理大文件？**
A: 系统支持最大1GB文件，建议将长音频分段处理以提高效率。

**Q5: API调用频率限制？**
A: 建议控制在每分钟不超过60次调用，避免触发限流。

### 联系方式
- **技术支持**: <EMAIL>
- **API文档**: https://docs.wenshu.com/voice
- **问题反馈**: https://github.com/wenshu/voice/issues

---

*文档版本: v1.0.0 | 最后更新: 2024-12-28 | Wenshu-Voice语音处理服务*
