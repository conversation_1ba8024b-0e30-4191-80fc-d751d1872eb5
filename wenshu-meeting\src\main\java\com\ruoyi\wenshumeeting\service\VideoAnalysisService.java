package com.ruoyi.wenshumeeting.service;

import com.ruoyi.wenshumeeting.pojo.VideoAnalysis;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 视频分析任务服务层接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface VideoAnalysisService {
    
    /**
     * 保存视频分析任务
     * 
     * @param videoAnalysis 视频分析任务对象
     * @return 保存后的视频分析任务对象
     */
    VideoAnalysis save(VideoAnalysis videoAnalysis);
    
    /**
     * 根据ID查询视频分析任务
     * 
     * @param id 任务ID
     * @return 视频分析任务对象
     */
    Optional<VideoAnalysis> findById(Long id);
    
    /**
     * 查询所有视频分析任务
     * 
     * @return 视频分析任务列表
     */
    List<VideoAnalysis> findAll();
    
    /**
     * 根据用户ID查询视频分析任务列表
     * 
     * @param userId 用户ID
     * @return 视频分析任务列表
     */
    List<VideoAnalysis> findByUserId(Integer userId);
    
    /**
     * 根据文件ID查询视频分析任务
     * 
     * @param fileId 文件ID
     * @return 视频分析任务对象
     */
    Optional<VideoAnalysis> findByFileId(String fileId);
    
    /**
     * 根据状态查询视频分析任务列表
     * 
     * @param status 任务状态
     * @return 视频分析任务列表
     */
    List<VideoAnalysis> findByStatus(Integer status);
    
    /**
     * 根据用户ID和状态查询视频分析任务列表
     * 
     * @param userId 用户ID
     * @param status 任务状态
     * @return 视频分析任务列表
     */
    List<VideoAnalysis> findByUserIdAndStatus(Integer userId, Integer status);
    
    /**
     * 根据上传时间范围查询视频分析任务列表
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 视频分析任务列表
     */
    List<VideoAnalysis> findByUploadTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据用户ID和上传时间范围查询视频分析任务列表
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 视频分析任务列表
     */
    List<VideoAnalysis> findByUserIdAndUploadTimeBetween(Integer userId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 更新视频分析任务
     * 
     * @param videoAnalysis 视频分析任务对象
     * @return 更新后的视频分析任务对象
     */
    VideoAnalysis update(VideoAnalysis videoAnalysis);
    
    /**
     * 更新任务状态
     * 
     * @param id 任务ID
     * @param status 新状态
     * @return 是否更新成功
     */
    boolean updateStatus(Long id, Integer status);
    
    /**
     * 开始任务处理
     * 
     * @param id 任务ID
     * @return 是否更新成功
     */
    boolean startTask(Long id);
    
    /**
     * 完成任务
     * 
     * @param id 任务ID
     * @param analysisText 分析文本
     * @return 是否更新成功
     */
    boolean completeTask(Long id, String analysisText);
    
    /**
     * 任务失败
     * 
     * @param id 任务ID
     * @param failureReason 失败原因
     * @return 是否更新成功
     */
    boolean failTask(Long id, String failureReason);
    
    /**
     * 根据ID删除视频分析任务
     * 
     * @param id 任务ID
     * @return 是否删除成功
     */
    boolean deleteById(Long id);
    
    /**
     * 根据用户ID删除视频分析任务
     * 
     * @param userId 用户ID
     * @return 删除的记录数
     */
    int deleteByUserId(Integer userId);
    
    /**
     * 根据文件ID删除视频分析任务
     * 
     * @param fileId 文件ID
     * @return 删除的记录数
     */
    int deleteByFileId(String fileId);
    
    /**
     * 判断任务是否存在
     * 
     * @param id 任务ID
     * @return 是否存在
     */
    boolean existsById(Long id);
    
    /**
     * 判断文件ID对应的任务是否存在
     * 
     * @param fileId 文件ID
     * @return 是否存在
     */
    boolean existsByFileId(String fileId);
    
    /**
     * 统计用户的任务数量
     * 
     * @param userId 用户ID
     * @return 任务数量
     */
    long countByUserId(Integer userId);
    
    /**
     * 统计指定状态的任务数量
     * 
     * @param status 任务状态
     * @return 任务数量
     */
    long countByStatus(Integer status);
    
    /**
     * 统计用户指定状态的任务数量
     * 
     * @param userId 用户ID
     * @param status 任务状态
     * @return 任务数量
     */
    long countByUserIdAndStatus(Integer userId, Integer status);
    
    /**
     * 创建新的视频分析任务
     * 
     * @param userId 用户ID
     * @param fileId 文件ID
     * @param initialText 初始文本
     * @return 创建的视频分析任务对象
     */
    VideoAnalysis createTask(Integer userId, String fileId, String initialText);
}
