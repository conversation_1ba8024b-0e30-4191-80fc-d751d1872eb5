package com.ruoyi.wenshuremind.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.wenshuapi.pojo.programme.Programme;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Programme数据传输对象
 * 专门用于WebSocket响应，解决LocalDateTime序列化问题
 */
@Data
public class ProgrammeDTO {
    
    private int eventId;
    private String title;
    private String description;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime createTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime startTime;
    
    private int creatorId;
    private String ownerType;
    private String repeatRule;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime remindTime;
    
    private int eventStatus;
    
    /**
     * 从Programme对象创建ProgrammeDTO
     */
    public static ProgrammeDTO fromProgramme(Programme programme) {
        if (programme == null) {
            return null;
        }
        
        ProgrammeDTO dto = new ProgrammeDTO();
        dto.setEventId(programme.getEventId());
        dto.setTitle(programme.getTitle());
        dto.setDescription(programme.getDescription());
        dto.setCreateTime(programme.getCreateTime());
        dto.setStartTime(programme.getStartTime());
        dto.setCreatorId(programme.getCreatorId());
        dto.setOwnerType(programme.getOwnerType());
        dto.setRepeatRule(programme.getRepeatRule());
        dto.setRemindTime(programme.getRemindTime());
        dto.setEventStatus(programme.getEventStatus());
        
        return dto;
    }
    
    /**
     * 转换为Programme对象
     */
    public Programme toProgramme() {
        Programme programme = new Programme();
        programme.setEventId(this.eventId);
        programme.setTitle(this.title);
        programme.setDescription(this.description);
        programme.setCreateTime(this.createTime);
        programme.setStartTime(this.startTime);
        programme.setCreatorId(this.creatorId);
        programme.setOwnerType(this.ownerType);
        programme.setRepeatRule(this.repeatRule);
        programme.setRemindTime(this.remindTime);
        programme.setEventStatus(this.eventStatus);
        
        return programme;
    }
}
