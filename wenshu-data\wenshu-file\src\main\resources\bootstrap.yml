# Tomcat
server:
  port: 1016
  tomcat:
    max-http-form-post-size: 10GB
    max-swallow-size: 10GB

# Spring
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 2313147023
    url: jdbc:mysql://************:3306/ry-cloud?&serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=UTF8&autoReconnect=true&useSSL=false&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true

  application:
    # 应用名称
    name: wenshu-file
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: ************:8848
      config:
        # 配置中心地址
        server-addr: ************:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

  # 文件上传配置
  servlet:
    multipart:
      # 启用文件上传
      enabled: true
      # 单个文件最大大小（10GB）
      max-file-size: 10GB
      # 总上传数据最大大小（10GB）
      max-request-size: 10GB
      # 文件大小阈值，超过此值将写入临时文件（2KB）
      file-size-threshold: 2KB
      # 临时文件存储位置
      location: ${java.io.tmpdir}

  # Web MVC配置
  mvc:
    # 当找不到处理器时抛出异常，而不是返回404页面
    throw-exception-if-no-handler-found: true

  # 静态资源配置
  web:
    resources:
      # 禁用默认的静态资源处理，让NoHandlerFoundException能够被抛出
      add-mappings: false

# 文件存储配置
wenshu:
  file:
    # 文件存储根路径
    storage:
      path: D:/wenshu/file-storage
    # 文件访问域名
    domain: http://localhost:1016
    # 文件访问前缀
    prefix: /wenshu-files
    # 最大文件大小（10GB，单位：字节）
    max-size: 10737418240
    # 最大请求大小（10GB，单位：字节）
    max-request-size: 10737418240
    # 文件大小阈值（2KB，单位：字节）
    file-size-threshold: 2048
