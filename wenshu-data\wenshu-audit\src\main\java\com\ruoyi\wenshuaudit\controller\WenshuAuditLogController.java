package com.ruoyi.wenshuaudit.controller;

import com.ruoyi.wenshuapi.pojo.audit.WenshuAuditLog;
import com.ruoyi.wenshuaudit.common.RestResult;
import com.ruoyi.wenshuaudit.service.WenshuAuditLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 审计日志控制器
 */
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
@Slf4j
public class WenshuAuditLogController {

    private final WenshuAuditLogService wenshuAuditLogService;

    /**
     * 添加审计日志
     */
    @PostMapping("/add")
    public RestResult<Void> addLog(@RequestBody WenshuAuditLog log) {
        return wenshuAuditLogService.addLog(log);
    }

    /**
     * 获取审计日志列表
     */
    @GetMapping("/list")
    public RestResult<List<WenshuAuditLog>> getLogList(@RequestParam("auditId") Integer auditId) {
        return wenshuAuditLogService.getLogList(auditId);
    }

    /**
     * 删除审计日志
     */
    @DeleteMapping("/delete")
    public RestResult<Void> deleteLog(@RequestParam("logId") Integer logId) {
        return wenshuAuditLogService.deleteLog(logId);
    }
}
