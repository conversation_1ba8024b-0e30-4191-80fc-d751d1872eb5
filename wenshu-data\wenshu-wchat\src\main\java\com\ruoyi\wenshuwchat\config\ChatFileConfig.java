package com.ruoyi.wenshuwchat.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.File;

/**
 * 聊天文件配置类
 * 配置文件访问路径和跨域支持
 */
@Configuration
public class ChatFileConfig implements WebMvcConfigurer {

    /**
     * 聊天文件存储根路径
     */
    @Value("${chat.file.path:D:/wenshu/chat-files}")
    private String chatFileBasePath;

    /**
     * 文件访问前缀
     */
    @Value("${chat.file.prefix:/chat-files}")
    private String filePrefix;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置聊天文件访问路径
        registry.addResourceHandler(filePrefix + "/**")
                .addResourceLocations("file:" + chatFileBasePath + File.separator);
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // 配置聊天文件跨域访问
        registry.addMapping(filePrefix + "/**")
                .allowedOrigins("*")
                .allowedMethods("GET")
                .allowedHeaders("*")
                .maxAge(3600);
    }
}
