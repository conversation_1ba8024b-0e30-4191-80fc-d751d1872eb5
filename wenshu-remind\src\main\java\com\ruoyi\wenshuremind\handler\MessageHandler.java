package com.ruoyi.wenshuremind.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ruoyi.wenshuapi.client.chat.ChatFeignClientInter;
import com.ruoyi.wenshuapi.client.programme.ProgrammeFeignClientInter;
import com.ruoyi.wenshuapi.pojo.chat.RequestBodyDTO;
import com.ruoyi.wenshuapi.pojo.programme.Programme;
import com.ruoyi.wenshuapi.util.remind.Weather;
import com.ruoyi.wenshuremind.dto.ProgrammeDTO;
import com.ruoyi.wenshuremind.model.Message;
import com.ruoyi.wenshuremind.service.EmailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;


import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Controller("/api/remind")
public class MessageHandler {

    private static final Logger logger = LoggerFactory.getLogger(MessageHandler.class);
    private static final Logger performanceLogger = LoggerFactory.getLogger("PERFORMANCE");

    // 日期时间格式化器
    private static final DateTimeFormatter DETAILED_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    private static final DateTimeFormatter SHORT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    private static final DateTimeFormatter DATE_ONLY_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");



    // 响应常量
    private static final String KEY_REMINDER_REQUIRED = "reminderRequired";
    private static final String KEY_EVENT = "event";
    private static final String KEY_AI_RESPONSE = "aiChatResponse";
    private static final String KEY_TODAY_EVENTS = "todayEventsCount";
    private static final String KEY_REASON = "reason";

    private final ObjectMapper objectMapper;

    @Autowired
    private ProgrammeFeignClientInter programmeFeignClientInter;
    @Autowired
    private ChatFeignClientInter chatFeignClientInter;
    @Autowired
    private EmailService emailService;

    public MessageHandler() {
        this.objectMapper = JsonMapper.builder()
                .addModule(new JavaTimeModule())
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .build();
    }



    /**
     * 获取用户所有有效日程
     *
     * @param userId 用户ID
     * @return 日程列表（可能为空）
     *
     * @throws RuntimeException 当Feign客户端调用失败时抛出
     */
    private List<Programme> getUserProgrammes(Long userId) {
        logger.info("=== 开始获取用户日程数据 ===");
        logger.info("用户ID: {}", userId);

        try {
            logger.info("调用Feign客户端获取日程数据...");
            List<Programme> programmes = programmeFeignClientInter.ProgetAll(userId);

            if (programmes == null) {
                logger.warn("⚠️ 从数据库获取到空日程列表");
                logger.warn("用户ID: {}", userId);
                logger.warn("可能原因: 1.用户不存在 2.用户没有日程 3.数据库连接异常");
                return null;
            }

            logger.info("✅ 成功从数据库获取到 {} 条日程数据", programmes.size());

            // 详细输出每个日程的基本信息
            for (int i = 0; i < programmes.size(); i++) {
                Programme programme = programmes.get(i);
                logger.info("日程[{}]: ID={}, 标题={}, 描述={}, 开始时间={}, 提醒时间={}, 状态={}",
                        i + 1,
                        programme.getEventId(),
                        programme.getTitle(),
                        programme.getDescription(),
                        programme.getStartTime(),
                        programme.getRemindTime(),
                        programme.getEventStatus()
                );
            }

            return programmes;
        } catch (Exception e) {
            logger.error("❌ 获取日程数据异常");
            logger.error("用户ID: {}", userId);
            logger.error("异常类型: {}", e.getClass().getSimpleName());
            logger.error("异常信息: {}", e.getMessage());
            logger.error("异常堆栈: ", e);
            throw new RuntimeException("获取日程数据异常: " + e.getMessage(), e);
        }
    }

    /**
     * 检查日程是否需要提醒
     *
     * @param programme 日程对象
     * @param currentTime 当前时间（格式: yyyy-MM-dd HH:mm）
     * @return 是否匹配提醒条件
     *
     * @throws NullPointerException 当参数为空时抛出
     */
    private boolean shouldRemind(Programme programme, String currentTime) {
        logger.info("--- 检查日程是否需要提醒 ---");
        logger.info("日程ID: {}", programme != null ? programme.getEventId() : "null");
        logger.info("当前时间: {}", currentTime);

        // 参数校验
        if (programme == null || currentTime == null) {
            logger.error("❌ 参数校验失败: programme={}, currentTime={}", programme, currentTime);
            throw new NullPointerException("参数不能为空");
        }

        // 检查提醒时间是否为空
        if (programme.getRemindTime() == null) {
            logger.info("⏭️ 跳过日程[ID:{}] - 提醒时间为空", programme.getEventId());
            logger.info("日程详情: 标题={}, 描述={}", programme.getTitle(), programme.getDescription());
            return false;
        }

        // 检查日程状态
        if (programme.getEventStatus() != 1) {
            logger.info("⏭️ 跳过日程[ID:{}] - 状态禁用(状态值:{})", programme.getEventId(), programme.getEventStatus());
            logger.info("日程详情: 标题={}, 描述={}", programme.getTitle(), programme.getDescription());
            return false;
        }

        String remindTime = programme.getRemindTime().format(SHORT_FORMATTER);
        boolean shouldRemind = currentTime.equals(remindTime);

        logger.info("🔍 时间匹配检查:");
        logger.info("  - 日程提醒时间: {}", remindTime);
        logger.info("  - 当前系统时间: {}", currentTime);
        logger.info("  - 时间匹配结果: {}", shouldRemind ? "✅ 匹配" : "❌ 不匹配");

        if (shouldRemind) {
            logger.info("🎯 找到需要提醒的日程!");
            logger.info("日程详情: ID={}, 标题={}, 描述={}",
                    programme.getEventId(), programme.getTitle(), programme.getDescription());
        }

        return shouldRemind;
    }

    /**
     * 统计当天日程数量
     *
     * @param programmes 日程列表
     * @return 当天日程数量
     */
    private int countTodayEvents(List<Programme> programmes) {
        logger.info("=== 统计今日日程数量 ===");

        if (programmes == null || programmes.isEmpty()) {
            logger.info("📋 日程列表为空，今日事件数为0");
            return 0;
        }

        LocalDate today = LocalDate.now();
        logger.info("今日日期: {}", today);

        int count = 0;
        logger.info("开始遍历 {} 个日程，统计今日事件...", programmes.size());

        for (int i = 0; i < programmes.size(); i++) {
            Programme programme = programmes.get(i);
            logger.info("检查日程[{}]: ID={}, 标题={}", i + 1, programme.getEventId(), programme.getTitle());

            if (programme.getStartTime() == null) {
                logger.info("  - 开始时间为空，跳过");
                continue;
            }

            LocalDate programmeDate = programme.getStartTime().toLocalDate();
            boolean isToday = programmeDate.isEqual(today);

            logger.info("  - 日程日期: {}", programmeDate);
            logger.info("  - 是否今日: {}", isToday ? "✅ 是" : "❌ 否");

            if (isToday) {
                count++;
                logger.info("  - 今日事件计数: {}", count);
            }
        }

        logger.info("📊 统计完成: 今日[{}]共有 {} 个日程事件", today, count);
        return count;
    }

    /**
     * 生成AI提醒内容
     *
     * @param programme 日程对象
     * @param todayEventsCount 今日事件数
     * @param weather 天气信息
     * @param userId 用户ID
     * @return AI生成的提醒内容
     */
    private String generateAiReminder(Programme programme, int todayEventsCount, String weather, String userId) {
        logger.info("=== 生成AI提醒内容 ===");
        logger.info("事件ID: {}", programme.getEventId());
        logger.info("今日事件总数: {}", todayEventsCount);
        logger.info("天气信息: {}", weather);
        logger.info("用户ID: {}", userId);

        String prompt;
        String promptType;
        if (todayEventsCount > 3) {
            promptType = "日程较多型";
            prompt = String.format("用户有日程提醒：%s\n今日日程较多（%d项），请提醒注意休息\n天气：%s\n请贴心提醒，100字以内",
                    programme, todayEventsCount, weather);
        } else {
            promptType = "日程较少型";
            prompt = String.format("用户有日程提醒：%s\n今日日程较少（%d项），可推荐放松活动\n天气：%s\n请贴心提醒，100字以内",
                    programme, todayEventsCount, weather);
        }

        logger.info("🤖 AI提示词类型: {}", promptType);
        logger.info("🤖 AI提示词内容: {}", prompt);

        try {
            logger.info("调用AI服务生成提醒内容...");
            // 创建RequestBodyDTO对象
            RequestBodyDTO requestBodyDTO = new RequestBodyDTO(prompt, userId);
            String aiResponse = chatFeignClientInter.getChatResponse(requestBodyDTO).getBody();
            logger.info("✅ AI服务调用成功");
            logger.info("🤖 AI生成的提醒内容: {}", aiResponse);
            return aiResponse;
        } catch (Exception e) {
            logger.error("❌ AI服务调用失败");
            logger.error("异常类型: {}", e.getClass().getSimpleName());
            logger.error("异常信息: {}", e.getMessage());
            logger.error("异常堆栈: ", e);

            String fallbackMessage = "温馨提醒：您的日程【" + programme.getDescription()+ "】即将开始！";
            logger.info("🔄 使用备用提醒内容: {}", fallbackMessage);
            return fallbackMessage;
        }
    }

    /**
     * 处理日程提醒请求
     *
     * @param userIdStr 用户ID字符串
     * @param province 省份名称
     * @param city 城市名称
     * @return 包含提醒状态和当天日程数量的消息对象
     *
     * @apiNote 返回JSON结构:
     * {
     *   "code": 状态码,
     *   "msg": 消息,
     *   "data": {
     *     "reminderRequired": 是否需要提醒,
     *     "todayEventsCount": 当日事件数,
     *     "event": 日程对象(如果需要提醒),
     *     "aiChatResponse": AI响应内容(如果需要提醒),
     *     "reason": 原因说明(如果不需要提醒)
     *   }
     * }
     */
    @MessageMapping("/programme/{userid}/{sheng}/{place}/{email}")
    @SendTo("/topic/programme/{userid}/{sheng}/{place}")
    public com.ruoyi.wenshuremind.model.Message<Map<String, Object>> handleProgrammeReminder(
            @DestinationVariable("userid") String userIdStr,
            @DestinationVariable("sheng") String province,
            @DestinationVariable("place") String city,
            @DestinationVariable("email") String email

    ) {

        // 生成唯一请求ID
        final String requestId = UUID.randomUUID().toString().substring(0, 8);
        MDC.put("requestId", requestId);

        final long startTime = System.currentTimeMillis();
        final String currentTime = LocalDateTime.now().format(SHORT_FORMATTER);
        final Map<String, Object> responseData = new HashMap<>();

        logger.info("收到日程提醒请求 | 用户:{} 地区:{}/{} 邮箱:{} 时间:{}",
                userIdStr, province, city, email, currentTime);

        try {
            logger.info("🚀 ========== 开始处理日程提醒请求 ==========");
            logger.info("📨 接收到WebSocket消息:");
            logger.info("  - 消息路径: /programme/{}/{}/{}/{}", userIdStr, province, city, email);

            logger.info("📋 解析后的请求参数:");
            logger.info("  - 用户ID字符串: {}", userIdStr);
            logger.info("  - 省份: {}", province);
            logger.info("  - 城市: {}", city);
            logger.info("  - 邮箱: {}", email);
            logger.info("  - 邮箱长度: {} 字符", email != null ? email.length() : 0);
            logger.info("  - 当前时间: {}", currentTime);

            // 参数转换
            Long userId = Long.parseLong(userIdStr);
            logger.info("✅ 用户ID转换成功: {}", userId);

            // 获取用户日程
            List<Programme> programmes = getUserProgrammes(userId);

            // 统计当日事件
            int todayEvents = countTodayEvents(programmes);
            responseData.put(KEY_TODAY_EVENTS, todayEvents);
            logger.info("📊 今日事件统计完成，数量: {}", todayEvents);

            if (programmes == null || programmes.isEmpty()) {
                logger.info("⚠️ 未找到有效日程数据，结束处理");
                responseData.put(KEY_REMINDER_REQUIRED, false);
                responseData.put(KEY_REASON, "未获取到用户日程数据");
                return com.ruoyi.wenshuremind.model.Message.success("无日程数据", responseData);
            }

            logger.info("🔍 开始检查 {} 个日程的提醒条件...", programmes.size());

            // 遍历日程检查提醒条件
            for (int i = 0; i < programmes.size(); i++) {
                Programme programme = programmes.get(i);
                logger.info("检查第 {}/{} 个日程...", i + 1, programmes.size());

                if (shouldRemind(programme, currentTime)) {
                    logger.info("🎯 发现需要提醒的日程，开始处理提醒流程...");

                    // 获取天气信息
                    logger.info("🌤️ 获取天气信息...");
                    String weather = Weather.getWeather(province, city);
                    logger.info("🌤️ 天气信息获取完成: {}", weather);

                    // 生成AI提醒
                    String aiResponse = generateAiReminder(programme, todayEvents, weather, userIdStr);

                    // 发送邮件提醒
                    logger.info("📧 发送邮件提醒...");
                    logger.info("  - 收件人: {}", email);
                    logger.info("  - 主题: {}", programme.getDescription());
                    logger.info("  - 内容: {}", aiResponse);

                    try {
                        emailService.sendSimpleMail(email, programme.getDescription(), aiResponse);
                        logger.info("✅ 邮件发送成功");
                    } catch (Exception e) {
                        logger.error("❌ 邮件发送失败: {}", e.getMessage(), e);
                    }

                    // 构建响应 - 使用DTO避免LocalDateTime序列化问题
                    ProgrammeDTO programmeDTO = ProgrammeDTO.fromProgramme(programme);
                    logger.info("📦 转换Programme为DTO以避免序列化问题");

                    responseData.put(KEY_REMINDER_REQUIRED, true);
                    responseData.put(KEY_EVENT, programmeDTO);
                    responseData.put(KEY_AI_RESPONSE, aiResponse);

                    logger.info("🎉 提醒处理完成，返回成功响应");
                    return com.ruoyi.wenshuremind.model.Message.success("发现待提醒日程", responseData);
                }
            }

            logger.info("ℹ️ 当前无符合提醒条件的日程");
            responseData.put(KEY_REMINDER_REQUIRED, false);
            responseData.put(KEY_REASON, "当前无符合提醒条件的日程");
            return com.ruoyi.wenshuremind.model.Message.success("无待提醒日程", responseData);

        } catch (NumberFormatException e) {
            logger.error("❌ 用户ID格式错误");
            logger.error("输入的用户ID字符串: {}", userIdStr);
            logger.error("异常信息: {}", e.getMessage());
            logger.error("异常堆栈: ", e);
            responseData.put(KEY_REMINDER_REQUIRED, false);
            responseData.put(KEY_REASON, "用户ID格式错误: " + userIdStr);
            return com.ruoyi.wenshuremind.model.Message.error(400, "参数错误", responseData);
        } catch (Exception e) {
            logger.error("❌ 处理过程中发生未预期异常");
            logger.error("异常类型: {}", e.getClass().getSimpleName());
            logger.error("异常信息: {}", e.getMessage());
            logger.error("异常堆栈: ", e);
            responseData.put(KEY_REMINDER_REQUIRED, false);
            responseData.put(KEY_REASON, "处理异常: " + e.getClass().getSimpleName());
            return com.ruoyi.wenshuremind.model.Message.error(500, "服务异常", responseData);
        } finally {
            // 性能日志记录
            long duration = System.currentTimeMillis() - startTime;
            logger.info("⏱️ 请求处理完成，总耗时: {}ms", duration);
            performanceLogger.info("请求处理完成 | 耗时:{}ms", duration);

            // 清理MDC
            MDC.clear();
            logger.info("🏁 ========== 日程提醒请求处理结束 ==========\n");
        }
    }
}