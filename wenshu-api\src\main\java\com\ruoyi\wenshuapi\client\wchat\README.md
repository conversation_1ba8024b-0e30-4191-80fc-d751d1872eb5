# wenshu-wchat 微服务客户端

本目录包含了用于调用 wenshu-wchat 微服务的 Feign 客户端接口和降级处理组件。

## 组件说明

### 1. 客户端接口 (Client)

#### ChatRecordClient
- **功能**: 聊天记录管理的远程调用接口
- **服务名**: `wenshu-wchat`
- **路径前缀**: `/wenshu/wchat/chat`
- **主要功能**:
  - 发送各种类型消息（文本、图片、视频、文件等所有类型）
  - 获取聊天记录（支持分页、时间范围查询）
  - 消息状态管理（已读标记、删除）
  - 会话管理（生成会话ID、根据会话查询）
  - 文件管理（上传、下载、信息获取）
  - 消息搜索
  - 支持所有文件类型上传（无文件类型限制）

#### FriendListClient
- **功能**: 好友关系管理的远程调用接口
- **服务名**: `wenshu-wchat`
- **路径前缀**: `/wenshu/wchat/friend`
- **主要功能**:
  - 好友关系增删改查
  - 好友状态管理
  - 好友关系检查
  - 好友搜索
  - 批量操作支持

### 2. 降级处理 (Fallback)

#### ChatRecordClientFallback
- **功能**: 聊天记录服务不可用时的降级处理
- **策略**:
  - 写操作：返回503错误，记录详细日志
  - 查询操作：返回空数据集，避免NPE
  - 文件操作：返回空字节数组或错误响应

#### FriendListClientFallback
- **功能**: 好友关系服务不可用时的降级处理
- **策略**:
  - 写操作：返回503错误，记录详细日志
  - 查询操作：返回空数据集或默认值
  - 关系检查：返回未知状态

## 使用方法

### 1. 依赖注入

在需要使用的服务中注入客户端：

```java
@Service
public class YourService {
    
    @Autowired
    private ChatRecordClient chatRecordClient;
    
    @Autowired
    private FriendListClient friendListClient;
    
    // 使用客户端进行远程调用
}
```

### 2. 启用 Feign 客户端

在启动类上添加 `@EnableFeignClients` 注解：

```java
@SpringBootApplication
@EnableFeignClients(basePackages = "com.ruoyi.wenshuapi.client")
public class YourApplication {
    public static void main(String[] args) {
        SpringApplication.run(YourApplication.class, args);
    }
}
```

### 3. 配置文件

确保在 `application.yml` 或 `bootstrap.yml` 中配置了服务发现：

```yaml
spring:
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
```

## API 接口说明

### 聊天记录接口

#### 发送消息
```java
// 发送文本消息
ResponseEntity<Map<String, Object>> response = chatRecordClient.sendTextMessage(
    1001L,  // 发送者ID
    1002L,  // 接收者ID
    "Hello World!"  // 消息内容
);

// 统一发送消息接口
ResponseEntity<Map<String, Object>> response = chatRecordClient.sendMessage(
    1001L,      // 发送者ID
    1002L,      // 接收者ID
    "text",     // 内容类型
    "Hello!",   // 文本内容
    null        // 文件
);
```

#### 获取聊天记录
```java
// 根据发送者和接收者获取
ResponseEntity<Map<String, Object>> response = chatRecordClient.getMessagesBySenderAndReceiver(
    1001L,  // 发送者ID
    1002L   // 接收者ID
);

// 根据会话ID获取
ResponseEntity<Map<String, Object>> response = chatRecordClient.getMessagesBySessionId(
    "session_1001_1002"  // 会话ID
);
```

#### 会话管理
```java
// 生成会话ID
ResponseEntity<Map<String, Object>> response = chatRecordClient.generateSessionId(
    1001L,  // 用户1 ID
    1002L   // 用户2 ID
);
```

### 好友关系接口

#### 添加好友
```java
FriendList friendList = new FriendList();
friendList.setUserId(1001L);
friendList.setFriendId(1002L);
friendList.setStatus("active");

ResponseEntity<Map<String, Object>> response = friendListClient.addFriend(friendList);
```

#### 获取好友列表
```java
ResponseEntity<Map<String, Object>> response = friendListClient.getFriendsByUserId(1001L);
```

#### 检查好友关系
```java
ResponseEntity<Map<String, Object>> response = friendListClient.checkFriendship(
    1001L,  // 用户1 ID
    1002L   // 用户2 ID
);
```

## 错误处理

### 降级响应格式

当服务不可用时，fallback 会返回统一格式的降级响应：

```json
{
    "success": false,
    "message": "聊天记录服务暂时不可用，请稍后重试",
    "data": null,
    "fallback": true
}
```

### 查询操作降级

查询操作在降级时会返回空数据：

```json
{
    "success": true,
    "message": "（降级数据）聊天记录服务暂时不可用，请稍后重试",
    "data": [],
    "fallback": true
}
```

## 注意事项

1. **参数注解**: 使用 `@PathVariable("参数名")` 和 `@RequestParam("参数名")` 明确指定参数名
2. **文件上传**: 文件上传接口需要使用 `MultipartFile` 类型
3. **响应格式**: 所有接口返回 `ResponseEntity<Map<String, Object>>` 格式
4. **服务名称**: 确保 `wenshu-wchat` 服务已注册到服务发现中心
5. **路径匹配**: 客户端路径必须与服务端 Controller 路径完全匹配
6. **降级处理**: 降级时会记录详细日志，便于问题排查

## 示例代码

完整的使用示例请参考：`com.ruoyi.wenshuapi.example.WchatClientExample`

该示例展示了所有主要功能的调用方法和错误处理。
