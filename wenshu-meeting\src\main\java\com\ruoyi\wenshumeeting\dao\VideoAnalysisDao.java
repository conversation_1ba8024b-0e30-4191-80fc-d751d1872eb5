package com.ruoyi.wenshumeeting.dao;

import com.ruoyi.wenshumeeting.pojo.VideoAnalysis;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 视频分析任务数据访问层接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Repository
public interface VideoAnalysisDao extends JpaRepository<VideoAnalysis, Long> {
    
    /**
     * 根据用户ID查询视频分析任务列表
     * 
     * @param userId 用户ID
     * @return 视频分析任务列表
     */
    List<VideoAnalysis> findByUserId(Integer userId);
    
    /**
     * 根据文件ID查询视频分析任务
     * 
     * @param fileId 文件ID
     * @return 视频分析任务
     */
    Optional<VideoAnalysis> findByFileId(String fileId);
    
    /**
     * 根据状态查询视频分析任务列表
     * 
     * @param status 任务状态
     * @return 视频分析任务列表
     */
    List<VideoAnalysis> findByStatus(Integer status);
    
    /**
     * 根据用户ID和状态查询视频分析任务列表
     * 
     * @param userId 用户ID
     * @param status 任务状态
     * @return 视频分析任务列表
     */
    List<VideoAnalysis> findByUserIdAndStatus(Integer userId, Integer status);
    
    /**
     * 根据上传时间范围查询视频分析任务列表
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 视频分析任务列表
     */
    @Query("SELECT v FROM VideoAnalysis v WHERE v.uploadTime BETWEEN :startTime AND :endTime")
    List<VideoAnalysis> findByUploadTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                               @Param("endTime") LocalDateTime endTime);
    
    /**
     * 根据用户ID和上传时间范围查询视频分析任务列表
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 视频分析任务列表
     */
    @Query("SELECT v FROM VideoAnalysis v WHERE v.userId = :userId AND v.uploadTime BETWEEN :startTime AND :endTime")
    List<VideoAnalysis> findByUserIdAndUploadTimeBetween(@Param("userId") Integer userId,
                                                        @Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);
    
    /**
     * 更新任务状态
     * 
     * @param id 任务ID
     * @param status 新状态
     * @return 更新的记录数
     */
    @Modifying
    @Transactional
    @Query("UPDATE VideoAnalysis v SET v.status = :status WHERE v.id = :id")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);
    
    /**
     * 更新任务状态和开始时间
     * 
     * @param id 任务ID
     * @param status 新状态
     * @param startTime 开始时间
     * @return 更新的记录数
     */
    @Modifying
    @Transactional
    @Query("UPDATE VideoAnalysis v SET v.status = :status, v.startTime = :startTime WHERE v.id = :id")
    int updateStatusAndStartTime(@Param("id") Long id, @Param("status") Integer status, 
                                @Param("startTime") LocalDateTime startTime);
    
    /**
     * 更新任务完成信息
     * 
     * @param id 任务ID
     * @param status 新状态
     * @param analysisText 分析文本
     * @param finishTime 完成时间
     * @return 更新的记录数
     */
    @Modifying
    @Transactional
    @Query("UPDATE VideoAnalysis v SET v.status = :status, v.analysisText = :analysisText, v.finishTime = :finishTime WHERE v.id = :id")
    int updateTaskCompletion(@Param("id") Long id, @Param("status") Integer status, 
                           @Param("analysisText") String analysisText, @Param("finishTime") LocalDateTime finishTime);
    
    /**
     * 更新任务失败信息
     * 
     * @param id 任务ID
     * @param status 新状态
     * @param failureReason 失败原因
     * @param finishTime 完成时间
     * @return 更新的记录数
     */
    @Modifying
    @Transactional
    @Query("UPDATE VideoAnalysis v SET v.status = :status, v.failureReason = :failureReason, v.finishTime = :finishTime WHERE v.id = :id")
    int updateTaskFailure(@Param("id") Long id, @Param("status") Integer status, 
                         @Param("failureReason") String failureReason, @Param("finishTime") LocalDateTime finishTime);
    
    /**
     * 根据用户ID删除视频分析任务
     * 
     * @param userId 用户ID
     * @return 删除的记录数
     */
    @Modifying
    @Transactional
    int deleteByUserId(Integer userId);
    
    /**
     * 根据文件ID删除视频分析任务
     * 
     * @param fileId 文件ID
     * @return 删除的记录数
     */
    @Modifying
    @Transactional
    int deleteByFileId(String fileId);
    
    /**
     * 统计用户的任务数量
     * 
     * @param userId 用户ID
     * @return 任务数量
     */
    long countByUserId(Integer userId);
    
    /**
     * 统计指定状态的任务数量
     * 
     * @param status 任务状态
     * @return 任务数量
     */
    long countByStatus(Integer status);
    
    /**
     * 统计用户指定状态的任务数量
     * 
     * @param userId 用户ID
     * @param status 任务状态
     * @return 任务数量
     */
    long countByUserIdAndStatus(Integer userId, Integer status);
}
