package com.ruoyi.wenshuwchat.service.impl;

import com.ruoyi.wenshuwchat.service.ChatFileService;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 聊天文件存储服务实现类
 */
@Service
public class ChatFileServiceImpl implements ChatFileService {

    /**
     * 聊天文件存储根路径
     */
    @Value("${chat.file.path:D:/wenshu/chat-files}")
    private String chatFileBasePath;

    /**
     * 文件访问域名
     */
    @Value("${chat.file.domain:http://localhost:8080}")
    private String fileDomain;

    /**
     * 文件访问前缀
     */
    @Value("${chat.file.prefix:/chat-files}")
    private String filePrefix;

    // 常见图片文件扩展名（用于内容类型识别，不限制上传）
    private static final List<String> IMAGE_EXTENSIONS = Arrays.asList(
            "jpg", "jpeg", "png", "gif", "bmp", "webp", "svg", "ico", "tiff", "tif"
    );

    // 常见视频文件扩展名（用于内容类型识别，不限制上传）
    private static final List<String> VIDEO_EXTENSIONS = Arrays.asList(
            "mp4", "avi", "mov", "wmv", "flv", "webm", "mkv", "m4v", "3gp", "ogv"
    );

    // 常见文档文件扩展名（用于内容类型识别，不限制上传）
    private static final List<String> DOCUMENT_EXTENSIONS = Arrays.asList(
            "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "rtf", "odt", "ods", "odp"
    );

    // 常见压缩文件扩展名（用于内容类型识别，不限制上传）
    private static final List<String> ARCHIVE_EXTENSIONS = Arrays.asList(
            "zip", "rar", "7z", "tar", "gz", "bz2", "xz", "tar.gz", "tar.bz2"
    );

    // 文件大小限制：100MB
    private static final long MAX_FILE_SIZE = 100 * 1024 * 1024L;

    @Override
    public String uploadChatFile(MultipartFile file, String contentType) throws Exception {
        // 验证文件
        if (file.isEmpty()) {
            throw new IllegalArgumentException("上传文件不能为空");
        }

        // 验证文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new IllegalArgumentException("文件大小超过限制，最大支持100MB");
        }

        if (!isFileTypeAllowed(file, contentType)) {
            throw new IllegalArgumentException("不支持的文件类型");
        }

        // 生成文件名
        String originalFilename = file.getOriginalFilename();
        String extension = FilenameUtils.getExtension(originalFilename);
        String fileName = generateFileName(extension);

        // 创建目录结构：contentType/年月日/
        String dateDir = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String relativePath = contentType + "/" + dateDir + "/" + fileName;
        
        // 创建完整路径
        Path fullPath = Paths.get(chatFileBasePath, relativePath);
        
        // 确保目录存在
        Files.createDirectories(fullPath.getParent());
        
        // 保存文件
        file.transferTo(fullPath.toFile());
        
        return relativePath;
    }

    @Override
    public boolean deleteChatFile(String filePath) {
        try {
            Path fullPath = Paths.get(chatFileBasePath, filePath);
            return Files.deleteIfExists(fullPath);
        } catch (IOException e) {
            return false;
        }
    }

    @Override
    public boolean fileExists(String filePath) {
        Path fullPath = Paths.get(chatFileBasePath, filePath);
        return Files.exists(fullPath);
    }

    @Override
    public String getFileAccessUrl(String filePath) {
        return fileDomain + filePrefix + "/" + filePath;
    }

    @Override
    public boolean isFileTypeAllowed(MultipartFile file, String contentType) {
        // 检查文件是否为空
        if (file == null || file.isEmpty()) {
            return false;
        }

        // 检查文件名是否有效
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            return false;
        }

        // 获取文件扩展名
        String extension = FilenameUtils.getExtension(originalFilename).toLowerCase();

        // 允许所有类型的文件上传，只需要有扩展名即可
        // 对于特定的内容类型，仍然进行基本的验证以确保分类正确
        switch (contentType.toLowerCase()) {
            case "image":
                // 如果指定为图片类型，验证是否为常见图片格式，但不限制其他格式
                return extension.isEmpty() || IMAGE_EXTENSIONS.contains(extension) || isValidFileExtension(extension);
            case "video":
                // 如果指定为视频类型，验证是否为常见视频格式，但不限制其他格式
                return extension.isEmpty() || VIDEO_EXTENSIONS.contains(extension) || isValidFileExtension(extension);
            case "file":
            case "document":
                // 文件/文档类型允许所有格式
                return isValidFileExtension(extension);
            case "archive":
                // 压缩文件类型
                return extension.isEmpty() || ARCHIVE_EXTENSIONS.contains(extension) || isValidFileExtension(extension);
            default:
                // 其他类型也允许，只要有有效的扩展名
                return isValidFileExtension(extension);
        }
    }

    /**
     * 验证文件扩展名是否有效
     * 只要不是空的且不包含危险字符即可
     */
    private boolean isValidFileExtension(String extension) {
        if (extension == null) {
            return true; // 允许没有扩展名的文件
        }

        // 检查扩展名长度（一般不超过10个字符）
        if (extension.length() > 10) {
            return false;
        }

        // 检查是否包含危险字符
        String dangerousChars = "\\/:*?\"<>|";
        for (char c : dangerousChars.toCharArray()) {
            if (extension.indexOf(c) >= 0) {
                return false;
            }
        }

        return true;
    }

    @Override
    public String getFullFilePath(String filePath) {
        return Paths.get(chatFileBasePath, filePath).toString();
    }

    /**
     * 生成唯一文件名
     */
    private String generateFileName(String extension) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String timestamp = String.valueOf(System.currentTimeMillis());
        return timestamp + "_" + uuid + "." + extension;
    }
}
