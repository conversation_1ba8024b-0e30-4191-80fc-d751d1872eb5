# Wenshu-File 文件管理模块 - 详细API接口文档

## 📋 模块概述

**服务名称**: wenshu-file  
**服务端口**: 8704  
**版本**: v1.0.0  
**最后更新**: 2024-12-28  

**核心功能**: 文件管理服务，提供文件上传、下载、存储、分享、版本控制、权限管理等功能

**技术栈**:
- Spring Boot 3.x
- Spring Cloud 2023.x
- MinIO (对象存储)
- Redis (缓存管理)
- MySQL (元数据存储)
- Apache Tika (文件类型检测)
- ImageIO (图片处理)

---

## 🏗️ 服务架构

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                Wenshu-File 文件管理模块                      │
│                        (Port: 8704)                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│   文件存储     │ │文件分享  │ │ 权限控制   │
│ (Storage)     │ │(Share)  │ │(Access)   │
└───────────────┘ └────────┘ └───────────┘
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│ MinIO对象存储  │ │版本控制  │ │ 缩略图     │
│ (Object Store)│ │(Version)│ │(Thumbnail)│
└───────────────┘ └────────┘ └───────────┘
```

### 服务依赖关系
- **MinIO**: 对象存储服务
- **MySQL**: 文件元数据存储
- **Redis**: 缓存和临时存储
- **wenshu-base**: 用户权限管理

---

## 🌐 REST API接口

### 基础信息
- **Base URL**: `http://localhost:8704`
- **Content-Type**: `multipart/form-data` / `application/json`
- **鉴权方式**: JWT Token (Header: `Authorization: Bearer <token>`)

### 文件处理限制
- **单文件最大**: 5GB
- **支持格式**: 所有常见文件格式
- **存储策略**: 自动分片存储大文件

---

## 📁 文件管理API

### 1. 文件基础操作

#### 1.1 文件上传
**接口路径**: `POST /api/files/upload`

**功能描述**: 上传文件到系统

**请求参数**:
```http
POST /api/files/upload
Content-Type: multipart/form-data

file: [文件] (必填)
folderId: [文件夹ID] (可选)
description: [文件描述] (可选)
tags: [标签，逗号分隔] (可选)
isPublic: [是否公开] (可选，默认false)
```

**请求示例**:
```bash
curl -X POST "http://localhost:8704/api/files/upload" \
  -H "Authorization: Bearer <your-token>" \
  -F "file=@document.pdf" \
  -F "folderId=folder_123" \
  -F "description=重要文档" \
  -F "tags=文档,重要" \
  -F "isPublic=false"
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "文件上传成功",
  "data": {
    "fileId": "file_12345",
    "fileName": "document.pdf",
    "originalName": "重要文档.pdf",
    "fileSize": 2048576,
    "fileType": "pdf",
    "mimeType": "application/pdf",
    "filePath": "/files/2024/12/28/file_12345.pdf",
    "downloadUrl": "/api/files/download/file_12345",
    "previewUrl": "/api/files/preview/file_12345",
    "thumbnailUrl": "/api/files/thumbnail/file_12345",
    "uploadTime": "2024-12-28T15:30:00",
    "uploader": {
      "userId": "1001",
      "userName": "张三"
    },
    "folderId": "folder_123",
    "description": "重要文档",
    "tags": ["文档", "重要"],
    "isPublic": false,
    "md5": "d41d8cd98f00b204e9800998ecf8427e",
    "status": "uploaded"
  },
  "timestamp": 1640995200000
}
```

#### 1.2 文件下载
**接口路径**: `GET /api/files/download/{fileId}`

**功能描述**: 下载指定文件

**请求示例**:
```bash
curl -X GET "http://localhost:8704/api/files/download/file_12345" \
  -H "Authorization: Bearer <your-token>" \
  -o "downloaded_file.pdf"
```

**响应**: 直接返回文件流，包含以下响应头：
```http
Content-Type: application/pdf
Content-Disposition: attachment; filename="document.pdf"
Content-Length: 2048576
Cache-Control: max-age=3600
```

#### 1.3 文件预览
**接口路径**: `GET /api/files/preview/{fileId}`

**功能描述**: 在线预览文件

**请求参数**:
```http
GET /api/files/preview/{fileId}?page=1&zoom=100&format=html
```

**查询参数**:
- `page`: 页码 (可选，用于多页文档)
- `zoom`: 缩放比例 (可选，默认100)
- `format`: 预览格式 (html/pdf/image，默认html)

**响应格式**:
```json
{
  "code": 200,
  "msg": "预览生成成功",
  "data": {
    "fileId": "file_12345",
    "fileName": "document.pdf",
    "previewType": "html",
    "previewUrl": "/preview/file_12345.html",
    "pageInfo": {
      "currentPage": 1,
      "totalPages": 10,
      "pageSize": "A4"
    },
    "supportedFormats": ["html", "pdf", "image"],
    "downloadUrl": "/api/files/download/file_12345"
  },
  "timestamp": 1640995200000
}
```

#### 1.4 获取文件信息
**接口路径**: `GET /api/files/{fileId}`

**功能描述**: 获取文件的详细信息

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "fileId": "file_12345",
    "fileName": "document.pdf",
    "originalName": "重要文档.pdf",
    "fileSize": 2048576,
    "fileType": "pdf",
    "mimeType": "application/pdf",
    "filePath": "/files/2024/12/28/file_12345.pdf",
    "uploadTime": "2024-12-28T15:30:00",
    "updateTime": "2024-12-28T15:30:00",
    "uploader": {
      "userId": "1001",
      "userName": "张三",
      "email": "<EMAIL>"
    },
    "folderId": "folder_123",
    "folderPath": "/工作文档/项目资料",
    "description": "重要文档",
    "tags": ["文档", "重要"],
    "isPublic": false,
    "downloadCount": 5,
    "viewCount": 15,
    "shareCount": 2,
    "md5": "d41d8cd98f00b204e9800998ecf8427e",
    "status": "normal",
    "versions": [
      {
        "versionId": "v1.0",
        "versionNumber": "1.0",
        "uploadTime": "2024-12-28T15:30:00",
        "fileSize": 2048576,
        "isCurrent": true
      }
    ],
    "permissions": {
      "canRead": true,
      "canWrite": true,
      "canDelete": true,
      "canShare": true
    }
  },
  "timestamp": 1640995200000
}
```

#### 1.5 文件列表查询
**接口路径**: `GET /api/files`

**功能描述**: 查询文件列表

**请求参数**:
```http
GET /api/files?folderId=folder_123&page=1&size=20&fileType=pdf&keyword=文档&sortBy=uploadTime&sortOrder=desc
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "files": [
      {
        "fileId": "file_12345",
        "fileName": "document.pdf",
        "fileSize": 2048576,
        "fileType": "pdf",
        "uploadTime": "2024-12-28T15:30:00",
        "uploader": "张三",
        "downloadCount": 5,
        "isPublic": false,
        "thumbnailUrl": "/api/files/thumbnail/file_12345"
      }
    ],
    "summary": {
      "totalFiles": 156,
      "totalSize": 1073741824,
      "fileTypes": {
        "pdf": 45,
        "docx": 32,
        "xlsx": 28,
        "image": 51
      }
    },
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 156,
      "totalPages": 8
    }
  },
  "timestamp": 1640995200000
}
```

#### 1.6 删除文件
**接口路径**: `DELETE /api/files/{fileId}`

**功能描述**: 删除指定文件

**请求参数**:
```http
DELETE /api/files/{fileId}?permanent=false
```

**查询参数**:
- `permanent`: 是否永久删除 (可选，默认false，移入回收站)

**响应格式**:
```json
{
  "code": 200,
  "msg": "文件删除成功",
  "data": {
    "fileId": "file_12345",
    "fileName": "document.pdf",
    "deleteTime": "2024-12-28T16:00:00",
    "isPermanent": false,
    "recycleId": "recycle_789"
  },
  "timestamp": 1640995200000
}
```

---

## 📂 文件夹管理API

### 2. 文件夹操作

#### 2.1 创建文件夹
**接口路径**: `POST /api/folders`

**功能描述**: 创建新文件夹

**请求参数**:
```http
POST /api/folders
Content-Type: application/json

{
  "folderName": "项目资料",
  "parentFolderId": "folder_root",
  "description": "存放项目相关资料",
  "isPublic": false
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "文件夹创建成功",
  "data": {
    "folderId": "folder_456",
    "folderName": "项目资料",
    "parentFolderId": "folder_root",
    "folderPath": "/项目资料",
    "description": "存放项目相关资料",
    "createTime": "2024-12-28T16:00:00",
    "creator": {
      "userId": "1001",
      "userName": "张三"
    },
    "isPublic": false,
    "fileCount": 0,
    "subFolderCount": 0,
    "totalSize": 0
  },
  "timestamp": 1640995200000
}
```

#### 2.2 获取文件夹内容
**接口路径**: `GET /api/folders/{folderId}/contents`

**功能描述**: 获取文件夹中的文件和子文件夹

**响应格式**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "folderId": "folder_456",
    "folderName": "项目资料",
    "folderPath": "/项目资料",
    "contents": {
      "folders": [
        {
          "folderId": "folder_789",
          "folderName": "设计文档",
          "createTime": "2024-12-28T16:05:00",
          "fileCount": 8,
          "subFolderCount": 2,
          "totalSize": 52428800
        }
      ],
      "files": [
        {
          "fileId": "file_12345",
          "fileName": "需求文档.pdf",
          "fileSize": 2048576,
          "fileType": "pdf",
          "uploadTime": "2024-12-28T15:30:00",
          "thumbnailUrl": "/api/files/thumbnail/file_12345"
        }
      ]
    },
    "summary": {
      "totalFiles": 25,
      "totalFolders": 5,
      "totalSize": 104857600
    }
  },
  "timestamp": 1640995200000
}
```

---

*文档版本: v1.0.0 | 最后更新: 2024-12-28 | Wenshu-File文件管理模块*
