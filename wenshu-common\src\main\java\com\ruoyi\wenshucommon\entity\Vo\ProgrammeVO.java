package com.ruoyi.wenshucommon.entity.Vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 日程管理VO类，用于接收前端请求参数
 * 从description字段中提取时间信息，如果设置了start_time则优先使用
 */
@Data
public class ProgrammeVO {
    private String title;
    private String description; // 描述信息，同时用于提取时间信息，如"下午5点开会，地点在明行楼"
    private LocalDateTime start_time; // 如果设置了此字段，则优先使用
    private Integer creator_id;
    private String owner_type;
    private String repeat_rule;
    private LocalDateTime remind_time;
    private Integer event_status;
} 