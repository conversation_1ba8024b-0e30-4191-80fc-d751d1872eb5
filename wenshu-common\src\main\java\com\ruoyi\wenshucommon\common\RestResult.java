package com.ruoyi.wenshucommon.common;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import lombok.Data;

/**
 * 统一响应结果类
 */
@Data
public class RestResult<T> {

    @Schema(description = "请求是否成功", requiredMode = RequiredMode.REQUIRED)
    private boolean success = true;

    @Schema(description = "提示信息", requiredMode = RequiredMode.REQUIRED)
    private String msg = "";

    @Schema(description = "业务代码", requiredMode = RequiredMode.REQUIRED)
    private String code = "0000";

    @Schema(description = "返回内容")
    private T content;

    /**
     * 快速构建无内容成功结果
     */
    public static <T> RestResult<T> buildSuccessResult() {
        return new RestResult<>();
    }

    /**
     * 快速构建带内容成功结果
     */
    public static <T> RestResult<T> buildSuccessResult(T content) {
        RestResult<T> result = new RestResult<>();
        result.setContent(content);
        return result;
    }
    
    /**
     * 构建错误结果
     */
    public static <T> RestResult<T> buildErrorResult(String code, String msg) {
        RestResult<T> result = new RestResult<>();
        result.setSuccess(false);
        result.setCode(code);
        result.setMsg(msg);
        return result;
    }
} 