# Wenshu-Multimodal 多模态处理模块 - 详细API接口文档

## 📋 模块概述

**服务名称**: wenshu-multimodal  
**服务端口**: 8702  
**版本**: v1.0.0  
**最后更新**: 2024-12-28  

**核心功能**: 多模态内容处理服务，提供OCR图像识别、智能文档生成、格式转换等功能

**技术栈**:
- Spring Boot 3.x
- Spring Cloud 2023.x
- 阿里云DashScope (qwen-vl-ocr-latest)
- Apache POI (Word文档处理)
- iText 7 (PDF生成)
- CommonMark (Markdown处理)
- JSoup (HTML解析)
- OpenFeign微服务调用

---

## 🏗️ 服务架构

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                Wenshu-Multimodal 多模态处理服务               │
│                        (Port: 8702)                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│   OCR识别     │ │文档生成  │ │ 格式转换   │
│ (Image OCR)   │ │(Doc Gen)│ │(Converter) │
└───────────────┘ └────────┘ └───────────┘
        │             │             │
┌───────▼───────┐ ┌───▼────┐ ┌─────▼─────┐
│阿里云DashScope│ │wenshu-chat│ │多格式支持  │
│qwen-vl-ocr   │ │ AI服务  │ │MD/HTML/   │
│   模型       │ │        │ │DOCX/PDF   │
└───────────────┘ └────────┘ └───────────┘
```

### 服务依赖关系
- **wenshu-chat**: AI智能对话服务
- **wenshu-api**: 通用API服务和文件存储
- **阿里云DashScope**: OCR图像识别引擎
- **文件存储服务**: 图片和文档存储

---

## 🌐 REST API接口

### 基础信息
- **Base URL**: `http://localhost:8702`
- **Content-Type**: `multipart/form-data` (文件上传) / `application/json`
- **鉴权方式**: JWT Token (Header: `Authorization: Bearer <token>`)

### 文件上传限制
- **单文件最大**: 10MB
- **支持格式**: JPG, PNG, GIF, BMP, TIFF等主流图片格式
- **连接超时**: 5秒
- **读取超时**: 2分钟

---

## 📚 OCR图像识别API

### 1. 图像内容解析

#### 1.1 智能图像OCR识别
**接口路径**: `POST /api/ocr/parse/imag`

**功能描述**: 使用阿里云qwen-vl-ocr-latest模型对上传的图片进行OCR识别，支持自定义提示词

**请求参数**:
```http
POST /api/ocr/parse/imag
Content-Type: multipart/form-data

file: [图片文件] (必填)
userId: [用户ID] (必填)
prompt: [自定义提示词] (可选)
```

**请求示例**:
```bash
curl -X POST http://localhost:8702/api/ocr/parse/imag \
  -H "Authorization: Bearer <your-token>" \
  -F "file=@document.jpg" \
  -F "userId=1001" \
  -F "prompt=请提取图片中的所有文字内容，保持原有格式"
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": "图片解析出来的内容为: 这是一份技术文档，包含以下内容：\n1. 系统架构设计\n2. API接口规范\n3. 数据库设计方案",
  "timestamp": 1640995200000
}
```

**高级功能参数**:
- **max_pixels**: 6422528 (图像最大像素阈值)
- **min_pixels**: 3136 (图像最小像素阈值)
- **enable_rotate**: true (自动图像转正)

**支持的提示词类型**:
- **通用OCR**: "请提取图片中的所有文字内容"
- **表格识别**: "请识别图片中的表格，保持表格格式"
- **公式识别**: "请识别图片中的数学公式和符号"
- **手写识别**: "请识别图片中的手写文字"
- **多语言识别**: "请识别图片中的中英文混合内容"

**错误响应**:
```json
{
  "code": 500,
  "msg": "图像识别失败: 不支持的图片格式",
  "data": null,
  "timestamp": 1640995200000
}
```

---

## 📄 智能文档生成API

### 2. Markdown文档生成

#### 2.1 生成Markdown内容
**接口路径**: `GET /document/generate-markdown`

**功能描述**: 基于提示词生成结构化的Markdown格式文档内容

**请求参数**:
```http
GET /document/generate-markdown?prompt=[提示词]&userId=[用户ID]
```

**请求示例**:
```bash
curl -X GET "http://localhost:8702/document/generate-markdown?prompt=Spring Boot微服务架构设计&userId=1001" \
  -H "Authorization: Bearer <your-token>"
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "Markdown内容生成成功",
  "data": "# Spring Boot微服务架构设计\n\n## 概述\n\nSpring Boot微服务架构是现代企业级应用开发的主流选择...\n\n## 核心组件\n\n### 1. 服务注册与发现\n- Nacos\n- Eureka\n- Consul\n\n### 2. 配置管理\n- Spring Cloud Config\n- Nacos Config\n\n## 最佳实践\n\n```java\n@SpringBootApplication\n@EnableDiscoveryClient\npublic class Application {\n    public static void main(String[] args) {\n        SpringApplication.run(Application.class, args);\n    }\n}\n```\n\n生成时间: 2024-12-28T15:30:00",
  "timestamp": 1640995200000
}
```

---

### 3. 多格式文档生成

#### 3.1 生成指定格式文档
**接口路径**: `POST /document/generate`

**功能描述**: 生成并保存指定格式的文档文件，支持MD、HTML、DOCX、PDF格式

**请求参数**:
```http
POST /document/generate
Content-Type: application/x-www-form-urlencoded

prompt: [提示词] (必填)
format: [文档格式] (必填: md/html/docx/pdf)
userId: [用户ID] (必填)
outputPath: [输出路径] (可选)
```

**请求示例**:
```bash
curl -X POST http://localhost:8702/document/generate \
  -H "Authorization: Bearer <your-token>" \
  -d "prompt=API接口设计规范" \
  -d "format=docx" \
  -d "userId=1001" \
  -d "outputPath=/tmp/api_design_spec.docx"
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "文档生成成功",
  "data": {
    "filePath": "/tmp/api_design_spec.docx",
    "format": "docx"
  },
  "timestamp": 1640995200000
}
```

#### 3.2 使用请求对象生成文档
**接口路径**: `POST /document/generate-with-request`

**功能描述**: 使用JSON请求对象生成文档，支持更丰富的参数配置

**请求参数**:
```http
POST /document/generate-with-request
Content-Type: application/json

{
  "prompt": "数据库设计最佳实践",
  "format": "pdf",
  "title": "数据库设计指南",
  "userId": "1001",
  "outputPath": "/documents/db_design_guide.pdf"
}
```

**请求示例**:
```bash
curl -X POST http://localhost:8702/document/generate-with-request \
  -H "Authorization: Bearer <your-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "微服务安全架构设计",
    "format": "html",
    "title": "微服务安全指南",
    "userId": "1001"
  }'
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "文档生成成功",
  "data": {
    "filePath": "/tmp/12345678-1234-1234-1234-123456789abc.html",
    "format": "html",
    "title": "微服务安全指南"
  },
  "timestamp": 1640995200000
}
```

---

### 4. 格式转换API

#### 4.1 Markdown转HTML
**接口路径**: `POST /document/markdown-to-html`

**功能描述**: 将Markdown格式内容转换为HTML格式，支持表格、代码块等扩展语法

**请求参数**:
```http
POST /document/markdown-to-html
Content-Type: application/x-www-form-urlencoded

markdown: [Markdown内容] (必填)
```

**请求示例**:
```bash
curl -X POST http://localhost:8702/document/markdown-to-html \
  -H "Authorization: Bearer <your-token>" \
  -d "markdown=# 标题\n\n这是一个**粗体**文本示例。\n\n## 代码示例\n\n\`\`\`java\npublic class Hello {\n    public static void main(String[] args) {\n        System.out.println(\"Hello World\");\n    }\n}\n\`\`\`"
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "转换成功",
  "data": "<h1>标题</h1>\n<p>这是一个<strong>粗体</strong>文本示例。</p>\n<h2>代码示例</h2>\n<pre><code class=\"language-java\">public class Hello {\n    public static void main(String[] args) {\n        System.out.println(&quot;Hello World&quot;);\n    }\n}\n</code></pre>",
  "timestamp": 1640995200000
}
```

---

## 🔧 技术实现细节

### OCR识别引擎
- **模型**: qwen-vl-ocr-latest
- **提供商**: 阿里云DashScope
- **识别精度**: > 98%
- **支持语言**: 中文、英文、数字、符号
- **特殊功能**: 自动图像转正、像素自适应

### 文档生成引擎
- **AI模型**: 通过wenshu-chat服务调用
- **重试机制**: 最大3次重试
- **回退策略**: AI服务不可用时使用预设模板
- **格式支持**: Markdown、HTML、Word、PDF

### 格式转换能力
- **Markdown解析**: CommonMark标准
- **HTML生成**: 支持GFM表格扩展
- **Word文档**: Apache POI XWPF
- **PDF生成**: iText 7 HTML2PDF

---

## 📊 性能指标

### 处理性能
| 指标 | 数值 | 说明 |
|------|------|------|
| OCR识别延迟 | < 5秒 | 标准图片(2MB以内) |
| 文档生成延迟 | < 30秒 | 1000字文档 |
| 格式转换延迟 | < 3秒 | Markdown转HTML |
| 并发处理能力 | 20+ | 同时处理请求数 |
| 文件大小限制 | 10MB | 单个图片文件 |
| 识别准确率 | > 98% | 清晰印刷体文字 |

### 资源消耗
- **内存使用**: 平均 512MB
- **CPU使用**: 平均 < 40%
- **磁盘空间**: 临时文件自动清理
- **网络带宽**: 依赖文件大小

---

## 🛠️ 配置说明

### 服务配置
```yaml
server:
  port: 8702

spring:
  application:
    name: wenshu-multimodal
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      enabled: true
  
  # AI服务配置
  ai:
    dashscope:
      api-key: ${DASHSCOPE_API_KEY}
      chat:
        model: qwen-max
```

### OpenFeign配置
```yaml
spring:
  cloud:
    openfeign:
      client:
        config:
          default:
            connectTimeout: 5000
            readTimeout: 60000
          wenshu-chat:
            connectTimeout: 5000
            readTimeout: 120000
      compression:
        request:
          enabled: true
        response:
          enabled: true
      circuitbreaker:
        enabled: true
```

### 文档生成配置
```yaml
wenshu:
  multimodal:
    document:
      # 文档生成配置
      max-retry: 3
      output-dir: ${java.io.tmpdir}
      
      # AI提示词模板
      system-prompt: |
        请生成一份关于[主题]的技术文档，要求：
        1. 使用Markdown格式
        2. 包含完整的文档结构
        3. 内容详实，逻辑清晰
        4. 包含代码示例和最佳实践
        
    ocr:
      # OCR配置
      max-pixels: 6422528
      min-pixels: 3136
      enable-rotate: true
      timeout: 30000
```

---

---

## 🔍 错误处理

### 常见错误码
| 错误码 | 错误信息 | 解决方案 |
|-------|---------|----------|
| 400 | 参数错误 | 检查请求参数格式 |
| 400 | 文件不能为空 | 确保上传了有效文件 |
| 400 | 不支持的文档格式 | 使用支持的格式(md/html/docx/pdf) |
| 401 | 未授权 | 提供有效的JWT Token |
| 500 | 图像识别失败 | 检查图片格式和网络连接 |
| 500 | 文档生成失败 | 检查AI服务状态 |
| 500 | 格式转换失败 | 检查输入内容格式 |
| 500 | 文件保存失败 | 检查磁盘空间和权限 |

### 错误处理策略
1. **OCR识别失败**: 自动重试，检查图片格式
2. **AI服务不可用**: 使用预设模板生成回退内容
3. **文件上传失败**: 验证文件大小和格式
4. **格式转换失败**: 返回详细错误信息
5. **磁盘空间不足**: 自动清理临时文件

---

## 🔒 安全措施

### 文件安全
- **格式验证**: 严格验证上传文件格式
- **大小限制**: 限制单文件最大10MB
- **病毒扫描**: 上传文件安全检查
- **临时存储**: 处理完成后自动删除

### 接口安全
- **JWT认证**: 所有接口需要有效Token
- **参数验证**: 严格验证所有输入参数
- **限流控制**: 防止恶意大量请求
- **日志记录**: 完整的操作日志记录

### 数据保护
- **传输加密**: HTTPS加密传输
- **API密钥**: 安全存储DashScope API密钥
- **访问控制**: 基于用户权限的访问控制
- **审计日志**: 完整的审计追踪

---

## 💻 开发集成指南

### 客户端集成示例

#### JavaScript/Web集成
```javascript
// 多模态处理客户端
class WenshuMultimodalClient {
  constructor(baseUrl, token) {
    this.baseUrl = baseUrl;
    this.token = token;
  }

  // OCR图像识别
  async ocrParseImage(file, userId, prompt = null) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('userId', userId);
    if (prompt) {
      formData.append('prompt', prompt);
    }

    try {
      const response = await fetch(`${this.baseUrl}/api/ocr/parse/imag`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`
        },
        body: formData
      });

      const result = await response.json();
      if (result.code === 200) {
        console.log('OCR识别成功:', result.data);
        return result.data;
      } else {
        throw new Error(result.msg);
      }
    } catch (error) {
      console.error('OCR识别失败:', error);
      throw error;
    }
  }

  // 生成Markdown文档
  async generateMarkdown(prompt, userId) {
    try {
      const response = await fetch(
        `${this.baseUrl}/document/generate-markdown?prompt=${encodeURIComponent(prompt)}&userId=${userId}`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.token}`
          }
        }
      );

      const result = await response.json();
      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg);
      }
    } catch (error) {
      console.error('Markdown生成失败:', error);
      throw error;
    }
  }

  // 生成多格式文档
  async generateDocument(prompt, format, userId, outputPath = null) {
    const formData = new FormData();
    formData.append('prompt', prompt);
    formData.append('format', format);
    formData.append('userId', userId);
    if (outputPath) {
      formData.append('outputPath', outputPath);
    }

    try {
      const response = await fetch(`${this.baseUrl}/document/generate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`
        },
        body: formData
      });

      const result = await response.json();
      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg);
      }
    } catch (error) {
      console.error('文档生成失败:', error);
      throw error;
    }
  }

  // 使用请求对象生成文档
  async generateDocumentWithRequest(documentRequest) {
    try {
      const response = await fetch(`${this.baseUrl}/document/generate-with-request`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(documentRequest)
      });

      const result = await response.json();
      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg);
      }
    } catch (error) {
      console.error('文档生成失败:', error);
      throw error;
    }
  }

  // Markdown转HTML
  async markdownToHtml(markdown) {
    const formData = new FormData();
    formData.append('markdown', markdown);

    try {
      const response = await fetch(`${this.baseUrl}/document/markdown-to-html`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`
        },
        body: formData
      });

      const result = await response.json();
      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg);
      }
    } catch (error) {
      console.error('Markdown转HTML失败:', error);
      throw error;
    }
  }
}

// 使用示例
const multimodalClient = new WenshuMultimodalClient('http://localhost:8702', 'your-jwt-token');

// OCR识别示例
document.getElementById('imageInput').addEventListener('change', async (event) => {
  const file = event.target.files[0];
  if (file) {
    try {
      const result = await multimodalClient.ocrParseImage(
        file,
        1001,
        '请提取图片中的所有文字内容，保持原有格式'
      );
      document.getElementById('ocrResult').textContent = result;
    } catch (error) {
      alert('OCR识别失败: ' + error.message);
    }
  }
});

// 文档生成示例
async function generateTechDoc() {
  try {
    const documentRequest = {
      prompt: 'Spring Boot微服务最佳实践',
      format: 'html',
      title: 'Spring Boot微服务指南',
      userId: '1001'
    };

    const result = await multimodalClient.generateDocumentWithRequest(documentRequest);
    console.log('文档生成成功:', result);

    // 下载生成的文档
    window.open(`/download?filePath=${encodeURIComponent(result.filePath)}`);
  } catch (error) {
    alert('文档生成失败: ' + error.message);
  }
}
```

#### Python集成示例
```python
import requests
import json
from typing import Optional, Dict, Any

class WenshuMultimodalClient:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {token}'
        }

    def ocr_parse_image(self, file_path: str, user_id: int, prompt: Optional[str] = None) -> str:
        """OCR图像识别"""
        url = f"{self.base_url}/api/ocr/parse/imag"

        with open(file_path, 'rb') as f:
            files = {'file': f}
            data = {'userId': user_id}
            if prompt:
                data['prompt'] = prompt

            response = requests.post(url, headers=self.headers, files=files, data=data)

        result = response.json()
        if result['code'] == 200:
            return result['data']
        else:
            raise Exception(f"OCR识别失败: {result['msg']}")

    def generate_markdown(self, prompt: str, user_id: str) -> str:
        """生成Markdown文档"""
        url = f"{self.base_url}/document/generate-markdown"
        params = {
            'prompt': prompt,
            'userId': user_id
        }

        response = requests.get(url, headers=self.headers, params=params)
        result = response.json()

        if result['code'] == 200:
            return result['data']
        else:
            raise Exception(f"Markdown生成失败: {result['msg']}")

    def generate_document(self, prompt: str, format: str, user_id: str,
                         output_path: Optional[str] = None) -> Dict[str, str]:
        """生成多格式文档"""
        url = f"{self.base_url}/document/generate"
        data = {
            'prompt': prompt,
            'format': format,
            'userId': user_id
        }
        if output_path:
            data['outputPath'] = output_path

        response = requests.post(url, headers=self.headers, data=data)
        result = response.json()

        if result['code'] == 200:
            return result['data']
        else:
            raise Exception(f"文档生成失败: {result['msg']}")

    def generate_document_with_request(self, document_request: Dict[str, Any]) -> Dict[str, str]:
        """使用请求对象生成文档"""
        url = f"{self.base_url}/document/generate-with-request"
        headers = {**self.headers, 'Content-Type': 'application/json'}

        response = requests.post(url, headers=headers, json=document_request)
        result = response.json()

        if result['code'] == 200:
            return result['data']
        else:
            raise Exception(f"文档生成失败: {result['msg']}")

    def markdown_to_html(self, markdown: str) -> str:
        """Markdown转HTML"""
        url = f"{self.base_url}/document/markdown-to-html"
        data = {'markdown': markdown}

        response = requests.post(url, headers=self.headers, data=data)
        result = response.json()

        if result['code'] == 200:
            return result['data']
        else:
            raise Exception(f"Markdown转HTML失败: {result['msg']}")

# 使用示例
if __name__ == "__main__":
    client = WenshuMultimodalClient('http://localhost:8702', 'your-jwt-token')

    # OCR识别示例
    try:
        ocr_result = client.ocr_parse_image(
            'document.jpg',
            1001,
            '请识别图片中的表格内容，保持表格格式'
        )
        print(f"OCR识别结果: {ocr_result}")
    except Exception as e:
        print(f"OCR识别失败: {e}")

    # 生成技术文档
    try:
        document_request = {
            'prompt': 'Docker容器化部署最佳实践',
            'format': 'pdf',
            'title': 'Docker部署指南',
            'userId': '1001',
            'outputPath': '/tmp/docker_guide.pdf'
        }

        result = client.generate_document_with_request(document_request)
        print(f"文档生成成功: {result}")
    except Exception as e:
        print(f"文档生成失败: {e}")

    # Markdown转HTML示例
    try:
        markdown_content = """
        # API文档

        ## 接口列表

        | 接口名称 | 方法 | 路径 |
        |---------|------|------|
        | 用户登录 | POST | /api/login |
        | 获取用户信息 | GET | /api/user |

        ## 代码示例

        ```python
        import requests

        response = requests.post('/api/login', {
            'username': 'admin',
            'password': 'password'
        })
        ```
        """

        html_result = client.markdown_to_html(markdown_content)
        print("HTML转换成功")

        # 保存HTML文件
        with open('api_doc.html', 'w', encoding='utf-8') as f:
            f.write(html_result)

    except Exception as e:
        print(f"Markdown转HTML失败: {e}")
```

---

## 🎯 使用场景示例

### 场景1: 智能文档扫描系统
```javascript
// 文档扫描处理器
class DocumentScanProcessor {
  constructor(multimodalClient) {
    this.client = multimodalClient;
    this.scanHistory = [];
  }

  // 批量处理扫描文档
  async processScanBatch(files, userId) {
    const results = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      console.log(`处理第${i + 1}/${files.length}个文件: ${file.name}`);

      try {
        // 根据文件类型选择不同的提示词
        const prompt = this.getPromptByFileType(file.name);

        // OCR识别
        const ocrResult = await this.client.ocrParseImage(file, userId, prompt);

        // 保存结果
        const scanRecord = {
          fileName: file.name,
          scanTime: new Date(),
          content: ocrResult,
          status: 'success'
        };

        results.push(scanRecord);
        this.scanHistory.push(scanRecord);

        // 显示进度
        this.updateProgress((i + 1) / files.length * 100);

      } catch (error) {
        console.error(`文件${file.name}处理失败:`, error);
        results.push({
          fileName: file.name,
          scanTime: new Date(),
          error: error.message,
          status: 'failed'
        });
      }
    }

    return results;
  }

  // 根据文件类型获取提示词
  getPromptByFileType(fileName) {
    const extension = fileName.toLowerCase().split('.').pop();

    switch (extension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
        if (fileName.includes('table') || fileName.includes('表格')) {
          return '请识别图片中的表格内容，保持表格格式，按行列输出';
        } else if (fileName.includes('form') || fileName.includes('表单')) {
          return '请识别图片中的表单内容，提取所有字段和对应的值';
        } else {
          return '请提取图片中的所有文字内容，保持原有格式和段落结构';
        }
      default:
        return '请提取图片中的所有文字内容';
    }
  }

  // 生成扫描报告
  async generateScanReport(scanResults, userId) {
    const reportContent = this.buildReportContent(scanResults);

    try {
      // 生成HTML报告
      const htmlReport = await this.client.markdownToHtml(reportContent);

      // 生成PDF报告
      const pdfResult = await this.client.generateDocument(
        `请基于以下扫描结果生成专业的文档扫描报告：\n\n${reportContent}`,
        'pdf',
        userId,
        `/reports/scan_report_${Date.now()}.pdf`
      );

      return {
        htmlReport: htmlReport,
        pdfPath: pdfResult.filePath,
        summary: this.generateSummary(scanResults)
      };

    } catch (error) {
      console.error('报告生成失败:', error);
      throw error;
    }
  }

  buildReportContent(scanResults) {
    const successCount = scanResults.filter(r => r.status === 'success').length;
    const failedCount = scanResults.filter(r => r.status === 'failed').length;

    let content = `# 文档扫描报告\n\n`;
    content += `## 扫描概要\n\n`;
    content += `- 总文件数: ${scanResults.length}\n`;
    content += `- 成功处理: ${successCount}\n`;
    content += `- 处理失败: ${failedCount}\n`;
    content += `- 成功率: ${(successCount / scanResults.length * 100).toFixed(2)}%\n\n`;

    content += `## 扫描详情\n\n`;

    scanResults.forEach((result, index) => {
      content += `### ${index + 1}. ${result.fileName}\n\n`;
      content += `- 状态: ${result.status === 'success' ? '✅ 成功' : '❌ 失败'}\n`;
      content += `- 时间: ${result.scanTime.toLocaleString()}\n`;

      if (result.status === 'success') {
        content += `- 内容预览: ${result.content.substring(0, 100)}...\n\n`;
      } else {
        content += `- 错误信息: ${result.error}\n\n`;
      }
    });

    content += `\n---\n\n*报告生成时间: ${new Date().toLocaleString()}*`;

    return content;
  }

  generateSummary(scanResults) {
    const totalFiles = scanResults.length;
    const successFiles = scanResults.filter(r => r.status === 'success').length;
    const totalCharacters = scanResults
      .filter(r => r.status === 'success')
      .reduce((sum, r) => sum + r.content.length, 0);

    return {
      totalFiles,
      successFiles,
      failedFiles: totalFiles - successFiles,
      successRate: (successFiles / totalFiles * 100).toFixed(2),
      totalCharacters,
      averageCharacters: Math.round(totalCharacters / successFiles)
    };
  }

  updateProgress(percentage) {
    const progressBar = document.getElementById('scanProgress');
    if (progressBar) {
      progressBar.style.width = `${percentage}%`;
      progressBar.textContent = `${percentage.toFixed(1)}%`;
    }
  }
}
```

### 场景2: 智能知识库构建
```javascript
// 知识库构建器
class KnowledgeBaseBuilder {
  constructor(multimodalClient) {
    this.client = multimodalClient;
    this.knowledgeBase = new Map();
  }

  // 从多种来源构建知识库
  async buildKnowledgeBase(sources, userId) {
    const knowledgeItems = [];

    for (const source of sources) {
      try {
        let content = '';

        switch (source.type) {
          case 'image':
            // 从图片提取知识
            content = await this.extractKnowledgeFromImage(source.file, userId);
            break;
          case 'text':
            // 从文本生成结构化知识
            content = await this.structureTextKnowledge(source.content, userId);
            break;
          case 'url':
            // 从URL生成知识文档
            content = await this.generateKnowledgeFromUrl(source.url, userId);
            break;
        }

        const knowledgeItem = {
          id: this.generateId(),
          title: source.title || this.extractTitle(content),
          content: content,
          source: source,
          createdAt: new Date(),
          tags: this.extractTags(content)
        };

        knowledgeItems.push(knowledgeItem);
        this.knowledgeBase.set(knowledgeItem.id, knowledgeItem);

      } catch (error) {
        console.error(`处理知识源失败:`, error);
      }
    }

    // 生成知识库索引
    await this.generateKnowledgeIndex(knowledgeItems, userId);

    return knowledgeItems;
  }

  async extractKnowledgeFromImage(imageFile, userId) {
    const prompt = `请分析这张图片中的知识内容，提取关键信息并整理成结构化的知识点：
    1. 主要概念和定义
    2. 重要步骤或流程
    3. 关键数据和指标
    4. 注意事项和最佳实践
    请使用Markdown格式输出，包含适当的标题和列表。`;

    return await this.client.ocrParseImage(imageFile, userId, prompt);
  }

  async structureTextKnowledge(textContent, userId) {
    const prompt = `请将以下文本内容整理成结构化的知识文档：

${textContent}

要求：
1. 使用Markdown格式
2. 提取核心概念和要点
3. 组织成清晰的层次结构
4. 添加适当的示例和说明
5. 包含相关的最佳实践建议`;

    return await this.client.generateMarkdown(prompt, userId);
  }

  async generateKnowledgeIndex(knowledgeItems, userId) {
    const indexContent = this.buildIndexContent(knowledgeItems);

    const documentRequest = {
      prompt: `请基于以下知识库内容生成一份完整的知识库索引文档：\n\n${indexContent}`,
      format: 'html',
      title: '知识库索引',
      userId: userId
    };

    const result = await this.client.generateDocumentWithRequest(documentRequest);

    console.log('知识库索引生成成功:', result.filePath);
    return result;
  }

  buildIndexContent(knowledgeItems) {
    let content = `# 知识库索引\n\n`;
    content += `## 概览\n\n`;
    content += `- 知识条目总数: ${knowledgeItems.length}\n`;
    content += `- 最后更新: ${new Date().toLocaleString()}\n\n`;

    // 按标签分类
    const tagGroups = this.groupByTags(knowledgeItems);

    content += `## 分类索引\n\n`;
    for (const [tag, items] of tagGroups) {
      content += `### ${tag}\n\n`;
      items.forEach(item => {
        content += `- [${item.title}](#${item.id})\n`;
      });
      content += `\n`;
    }

    content += `## 详细内容\n\n`;
    knowledgeItems.forEach(item => {
      content += `### ${item.title} {#${item.id}}\n\n`;
      content += `**标签**: ${item.tags.join(', ')}\n\n`;
      content += `**创建时间**: ${item.createdAt.toLocaleString()}\n\n`;
      content += `**内容预览**:\n\n`;
      content += item.content.substring(0, 200) + '...\n\n';
      content += `---\n\n`;
    });

    return content;
  }

  groupByTags(knowledgeItems) {
    const tagGroups = new Map();

    knowledgeItems.forEach(item => {
      item.tags.forEach(tag => {
        if (!tagGroups.has(tag)) {
          tagGroups.set(tag, []);
        }
        tagGroups.get(tag).push(item);
      });
    });

    return tagGroups;
  }

  extractTitle(content) {
    // 从内容中提取标题
    const lines = content.split('\n');
    for (const line of lines) {
      if (line.startsWith('# ')) {
        return line.substring(2).trim();
      }
    }
    return '未命名知识条目';
  }

  extractTags(content) {
    // 简单的标签提取逻辑
    const tags = [];
    const keywords = ['技术', '方法', '工具', '概念', '流程', '最佳实践'];

    keywords.forEach(keyword => {
      if (content.includes(keyword)) {
        tags.push(keyword);
      }
    });

    return tags.length > 0 ? tags : ['通用'];
  }

  generateId() {
    return 'kb_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}
```

---

---

## 🔧 高级配置

### 文档生成高级配置
```yaml
# application.yml
wenshu:
  multimodal:
    document:
      # AI文档生成配置
      ai:
        max-retry: 3
        retry-delay: 1000
        timeout: 120000
        fallback-enabled: true

      # 文档模板配置
      templates:
        system-prompt: |
          请生成一份关于[主题]的专业技术文档，要求：
          1. 使用标准Markdown格式
          2. 包含完整的文档结构
          3. 内容详实，逻辑清晰
          4. 包含代码示例和最佳实践
          5. 文档长度控制在800-1200字

        fallback-template: |
          # {topic}

          ## 概述
          很抱歉，AI服务暂时无法生成完整的文档内容。

          ## 建议
          - 请稍后再试
          - 检查网络连接
          - 联系系统管理员

      # 文件输出配置
      output:
        base-dir: ${java.io.tmpdir}
        auto-cleanup: true
        cleanup-delay: 3600000  # 1小时后清理
        max-file-size: 50MB

    ocr:
      # OCR识别配置
      dashscope:
        model: qwen-vl-ocr-latest
        max-pixels: 6422528
        min-pixels: 3136
        enable-rotate: true
        timeout: 30000

      # 图片预处理配置
      preprocessing:
        enabled: true
        max-width: 4096
        max-height: 4096
        quality: 0.9
        format: jpg
```

### 性能优化配置
```java
@Configuration
public class MultimodalPerformanceConfig {

    @Bean
    @ConfigurationProperties(prefix = "wenshu.multimodal.thread-pool")
    public ThreadPoolTaskExecutor multimodalTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("multimodal-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean
    public CacheManager multimodalCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(500)
            .expireAfterWrite(Duration.ofMinutes(30))
            .recordStats());
        return cacheManager;
    }

    @Bean
    public RestTemplate ocrRestTemplate() {
        RestTemplate restTemplate = new RestTemplate();

        // 设置连接超时和读取超时
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        factory.setConnectTimeout(5000);
        factory.setReadTimeout(30000);

        restTemplate.setRequestFactory(factory);
        return restTemplate;
    }
}
```

### 文件处理配置
```java
@Configuration
public class FileProcessingConfig {

    @Value("${wenshu.multimodal.document.output.base-dir}")
    private String baseOutputDir;

    @Value("${wenshu.multimodal.document.output.auto-cleanup:true}")
    private boolean autoCleanup;

    @Bean
    public FileStorageService fileStorageService() {
        return new FileStorageService(baseOutputDir, autoCleanup);
    }

    @Bean
    public ImageProcessor imageProcessor() {
        return new ImageProcessor();
    }

    @Bean
    public DocumentConverter documentConverter() {
        return new DocumentConverter();
    }

    @Scheduled(fixedRate = 3600000) // 每小时执行一次
    public void cleanupTempFiles() {
        if (autoCleanup) {
            try {
                Path tempDir = Paths.get(baseOutputDir);
                Files.walk(tempDir)
                    .filter(Files::isRegularFile)
                    .filter(path -> {
                        try {
                            FileTime lastModified = Files.getLastModifiedTime(path);
                            return lastModified.toInstant()
                                .isBefore(Instant.now().minus(1, ChronoUnit.HOURS));
                        } catch (IOException e) {
                            return false;
                        }
                    })
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                            log.debug("清理临时文件: {}", path);
                        } catch (IOException e) {
                            log.warn("清理文件失败: {}", path, e);
                        }
                    });
            } catch (IOException e) {
                log.error("清理临时文件失败", e);
            }
        }
    }
}
```

---

## 📊 监控和运维

### 健康检查配置
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
      show-components: always
  health:
    diskspace:
      enabled: true
      threshold: 1GB
  metrics:
    export:
      prometheus:
        enabled: true
```

### 自定义健康检查
```java
@Component
public class MultimodalHealthIndicator implements HealthIndicator {

    private final MultiModalService multiModalService;
    private final DocumentService documentService;

    @Override
    public Health health() {
        try {
            // 检查OCR服务
            boolean ocrHealthy = checkOcrService();

            // 检查文档生成服务
            boolean docHealthy = checkDocumentService();

            // 检查磁盘空间
            boolean diskHealthy = checkDiskSpace();

            if (ocrHealthy && docHealthy && diskHealthy) {
                return Health.up()
                    .withDetail("ocr", "正常")
                    .withDetail("document", "正常")
                    .withDetail("disk", "正常")
                    .build();
            } else {
                return Health.down()
                    .withDetail("ocr", ocrHealthy ? "正常" : "异常")
                    .withDetail("document", docHealthy ? "正常" : "异常")
                    .withDetail("disk", diskHealthy ? "正常" : "异常")
                    .build();
            }
        } catch (Exception e) {
            return Health.down()
                .withDetail("error", e.getMessage())
                .build();
        }
    }

    private boolean checkOcrService() {
        try {
            // 简单的OCR服务检查
            return true; // 实际实现中可以调用一个测试接口
        } catch (Exception e) {
            return false;
        }
    }

    private boolean checkDocumentService() {
        try {
            // 检查文档生成服务
            return true; // 实际实现中可以生成一个测试文档
        } catch (Exception e) {
            return false;
        }
    }

    private boolean checkDiskSpace() {
        try {
            File tempDir = new File(System.getProperty("java.io.tmpdir"));
            long freeSpace = tempDir.getFreeSpace();
            long threshold = 1024 * 1024 * 1024; // 1GB
            return freeSpace > threshold;
        } catch (Exception e) {
            return false;
        }
    }
}
```

### 监控指标
```java
@Component
public class MultimodalMetrics {

    private final MeterRegistry meterRegistry;
    private final Counter ocrRequestCounter;
    private final Counter documentGenerationCounter;
    private final Timer ocrProcessingTimer;
    private final Timer documentGenerationTimer;
    private final Gauge tempFileGauge;

    public MultimodalMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;

        this.ocrRequestCounter = Counter.builder("multimodal.ocr.requests.total")
            .description("Total OCR requests")
            .register(meterRegistry);

        this.documentGenerationCounter = Counter.builder("multimodal.document.generation.total")
            .description("Total document generation requests")
            .register(meterRegistry);

        this.ocrProcessingTimer = Timer.builder("multimodal.ocr.processing.duration")
            .description("OCR processing time")
            .register(meterRegistry);

        this.documentGenerationTimer = Timer.builder("multimodal.document.generation.duration")
            .description("Document generation time")
            .register(meterRegistry);

        this.tempFileGauge = Gauge.builder("multimodal.temp.files.count")
            .description("Number of temporary files")
            .register(meterRegistry, this, MultimodalMetrics::getTempFileCount);
    }

    public void recordOcrRequest(boolean success) {
        ocrRequestCounter.increment(Tags.of("status", success ? "success" : "failure"));
    }

    public void recordDocumentGeneration(String format, boolean success) {
        documentGenerationCounter.increment(
            Tags.of("format", format, "status", success ? "success" : "failure")
        );
    }

    public Timer.Sample startOcrTimer() {
        return Timer.start(meterRegistry);
    }

    public void stopOcrTimer(Timer.Sample sample) {
        sample.stop(ocrProcessingTimer);
    }

    public Timer.Sample startDocumentTimer() {
        return Timer.start(meterRegistry);
    }

    public void stopDocumentTimer(Timer.Sample sample, String format) {
        sample.stop(documentGenerationTimer.tag("format", format));
    }

    private double getTempFileCount() {
        try {
            Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"));
            return Files.walk(tempDir, 1)
                .filter(Files::isRegularFile)
                .filter(path -> path.getFileName().toString().contains("wenshu"))
                .count();
        } catch (IOException e) {
            return 0;
        }
    }
}
```

### 日志配置
```yaml
logging:
  level:
    com.ruoyi.wenshumultimodal: DEBUG
    com.alibaba.dashscope: INFO
    org.apache.poi: WARN
    com.itextpdf: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: logs/wenshu-multimodal/multimodal.log
    max-size: 100MB
    max-history: 30

# 自定义日志配置
wenshu:
  logging:
    ocr:
      enabled: true
      level: INFO
      file: logs/wenshu-multimodal/ocr.log
    document:
      enabled: true
      level: INFO
      file: logs/wenshu-multimodal/document.log
    performance:
      enabled: true
      level: DEBUG
      file: logs/wenshu-multimodal/performance.log
```

---

## 🚀 部署指南

### Docker部署
```dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app

# 安装字体和图像处理依赖
RUN apt-get update && apt-get install -y \
    fonts-dejavu-core \
    fontconfig \
    libfreetype6 \
    && rm -rf /var/lib/apt/lists/*

# 复制应用程序
COPY target/wenshu-multimodal-0.0.1-SNAPSHOT.jar app.jar

# 创建必要的目录
RUN mkdir -p /app/logs/wenshu-multimodal
RUN mkdir -p /app/temp
RUN mkdir -p /app/documents

# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' > /etc/timezone

# 暴露端口
EXPOSE 8702

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8702/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", "-jar", "-Xmx1g", "-Xms512m", "app.jar"]
```

### Docker Compose部署
```yaml
version: '3.8'

services:
  wenshu-multimodal:
    build: .
    container_name: wenshu-multimodal
    ports:
      - "8702:8702"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - NACOS_SERVER_ADDR=nacos:8848
      - DASHSCOPE_API_KEY=${DASHSCOPE_API_KEY}
      - MYSQL_URL=********************************
      - MYSQL_USERNAME=root
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
    volumes:
      - ./logs:/app/logs
      - ./temp:/app/temp
      - ./documents:/app/documents
    depends_on:
      - nacos
      - mysql
    networks:
      - wenshu-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8702/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  nacos:
    image: nacos/nacos-server:v2.2.3
    container_name: nacos
    ports:
      - "8848:8848"
    environment:
      - MODE=standalone
    networks:
      - wenshu-network

  mysql:
    image: mysql:8.0
    container_name: mysql
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_DATABASE=ry-cloud
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - wenshu-network

volumes:
  mysql_data:

networks:
  wenshu-network:
    driver: bridge
```

### Kubernetes部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wenshu-multimodal
  labels:
    app: wenshu-multimodal
spec:
  replicas: 2
  selector:
    matchLabels:
      app: wenshu-multimodal
  template:
    metadata:
      labels:
        app: wenshu-multimodal
    spec:
      containers:
      - name: wenshu-multimodal
        image: wenshu/multimodal:1.0.0
        ports:
        - containerPort: 8702
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: NACOS_SERVER_ADDR
          value: "nacos-service:8848"
        - name: DASHSCOPE_API_KEY
          valueFrom:
            secretKeyRef:
              name: dashscope-secret
              key: api-key
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8702
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8702
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: temp
          mountPath: /app/temp
      volumes:
      - name: logs
        emptyDir: {}
      - name: temp
        emptyDir:
          sizeLimit: 5Gi

---
apiVersion: v1
kind: Service
metadata:
  name: wenshu-multimodal-service
spec:
  selector:
    app: wenshu-multimodal
  ports:
  - port: 8702
    targetPort: 8702
  type: ClusterIP

---
apiVersion: v1
kind: Secret
metadata:
  name: dashscope-secret
type: Opaque
data:
  api-key: <base64-encoded-api-key>
```

---

## 📞 技术支持

### 常见问题解答

**Q1: OCR识别准确率低怎么办？**
A: 确保图片清晰度足够，尝试使用不同的提示词，或者对图片进行预处理。

**Q2: 文档生成失败怎么处理？**
A: 检查AI服务状态，确认网络连接正常，查看错误日志获取详细信息。

**Q3: 支持哪些图片格式？**
A: 支持JPG、PNG、GIF、BMP、TIFF等主流格式，推荐使用JPG或PNG。

**Q4: 如何提高文档生成质量？**
A: 使用更详细和具体的提示词，指定文档结构和内容要求。

**Q5: 临时文件如何管理？**
A: 系统会自动清理1小时前的临时文件，也可以手动配置清理策略。

### 性能优化建议

1. **OCR优化**
   - 使用适当的图片分辨率
   - 启用图片预处理
   - 合理设置像素阈值

2. **文档生成优化**
   - 使用缓存减少重复生成
   - 异步处理大批量任务
   - 优化提示词减少AI处理时间

3. **系统优化**
   - 配置适当的线程池大小
   - 启用文件自动清理
   - 监控磁盘空间使用

### 联系方式
- **技术支持**: <EMAIL>
- **API文档**: https://docs.wenshu.com/multimodal
- **问题反馈**: https://github.com/wenshu/multimodal/issues

---

*文档版本: v1.0.0 | 最后更新: 2024-12-28 | Wenshu-Multimodal多模态处理服务*
