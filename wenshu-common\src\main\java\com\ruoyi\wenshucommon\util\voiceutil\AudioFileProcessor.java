package com.ruoyi.wenshucommon.util.voiceutil;

import lombok.Getter;
import org.apache.tika.exception.TikaException;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.parser.AutoDetectParser;
import org.apache.tika.parser.ParseContext;
import org.apache.tika.parser.Parser;
import org.springframework.web.multipart.MultipartFile;
import org.xml.sax.SAXException;
import org.xml.sax.helpers.DefaultHandler;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * 音频文件处理工具类
 * 用于处理音频文件上传、保存和提取元数据
 */
public class AudioFileProcessor {
    // 支持的音频格式列表
    private static final List<String> AUDIO_EXTENSIONS = Arrays.asList(
            "pcm", "wav", "mp3", "opus", "speex", "aac", "amr"
    );

    public static ProcessedAudioFile processUpload(
            MultipartFile file,
            String uploadDir
    ) throws IOException {
        // 1. 验证文件
        if (file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !isValidAudioFile(originalFilename)) {
            throw new IllegalArgumentException("仅支持音频文件（pcm/wav/mp3/opus/speex/aac/amr）");
        }

        // 2. 创建存储目录
        Path targetDir = Paths.get(uploadDir);
        if (!Files.exists(targetDir)) {
            Files.createDirectories(targetDir);
        }

        // 3. 生成安全文件名
        String sanitizedFilename = originalFilename.replace("..", "");
        String newFilename = new Random().nextInt(10000, 10000000) + "_" + sanitizedFilename;
        Path targetPath = targetDir.resolve(newFilename);

        // 4. 保存文件
        file.transferTo(targetPath);

        // 5. 提取元数据
        String audioFormat = null;
        Integer sampleRate = null;

        try (InputStream input = Files.newInputStream(targetPath)) {
            Metadata metadata = new Metadata();
            Parser parser = new AutoDetectParser();
            parser.parse(input, new DefaultHandler(), metadata, new ParseContext());

            String ext = getFileExtension(originalFilename);
            audioFormat = AUDIO_EXTENSIONS.contains(ext) ? ext : metadata.get("audio_format");
            String srStr = metadata.get("sample_rate");
            sampleRate = srStr != null ? Integer.parseInt(srStr) : null;
        } catch (TikaException | SAXException e) {
            throw new RuntimeException(e);
        }

        return new ProcessedAudioFile(targetPath, audioFormat, sampleRate);
    }

    private static boolean isValidAudioFile(String filename) {
        String ext = getFileExtension(filename);
        return AUDIO_EXTENSIONS.contains(ext);
    }

    private static String getFileExtension(String filename) {
        return filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
    }

    @Getter
    public static class ProcessedAudioFile {
        private final Path filePath;
        private final String audioFormat;
        private final Integer sampleRate;

        public ProcessedAudioFile(Path filePath, String audioFormat, Integer sampleRate) {
            this.filePath = filePath;
            this.audioFormat = audioFormat;
            this.sampleRate = sampleRate;
        }
    }
} 