package com.ruoyi.wenshufile.exception;

import com.ruoyi.wenshuapi.util.file.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理文件模块的异常情况，提供精确的HTTP状态码和错误信息
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2024-06-28
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    // ==================== HTTP相关异常 ====================

    /**
     * 处理HTTP方法不支持异常
     * 例如：对POST接口使用GET请求
     *
     * @param e HTTP方法不支持异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<ApiResponse<String>> handleHttpRequestMethodNotSupported(
            HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        String method = request.getMethod();
        String supportedMethods = String.join(", ", e.getSupportedMethods());
        String message = String.format("请求方法 '%s' 不支持，支持的方法: %s", method, supportedMethods);

        logger.warn("HTTP方法不支持: URI={}, 请求方法={}, 支持的方法={}", requestUri, method, supportedMethods);

        ApiResponse<String> response = ApiResponse.failed(message, 405);
        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).body(response);
    }

    /**
     * 处理媒体类型不支持异常
     * 例如：Content-Type不正确
     *
     * @param e 媒体类型不支持异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ResponseEntity<ApiResponse<String>> handleHttpMediaTypeNotSupported(
            HttpMediaTypeNotSupportedException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        String contentType = request.getContentType();
        String supportedTypes = e.getSupportedMediaTypes().stream()
                .map(Object::toString)
                .collect(Collectors.joining(", "));
        String message = String.format("不支持的媒体类型 '%s'，支持的类型: %s", contentType, supportedTypes);

        logger.warn("媒体类型不支持: URI={}, Content-Type={}, 支持的类型={}", requestUri, contentType, supportedTypes);

        ApiResponse<String> response = ApiResponse.failed(message, 415);
        return ResponseEntity.status(HttpStatus.UNSUPPORTED_MEDIA_TYPE).body(response);
    }

    /**
     * 处理资源未找到异常
     * 例如：访问不存在的静态资源
     *
     * @param e 资源未找到异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(NoResourceFoundException.class)
    public ResponseEntity<ApiResponse<String>> handleNoResourceFound(
            NoResourceFoundException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        String message = String.format("请求的资源 '%s' 不存在", requestUri);

        // 对于actuator等监控端点的访问，使用DEBUG级别日志
        if (requestUri.contains("/actuator/")) {
            logger.debug("监控端点访问失败: URI={}", requestUri);
        } else {
            logger.warn("资源未找到: URI={}", requestUri);
        }

        ApiResponse<String> response = ApiResponse.failed(message, 404);
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    }

    /**
     * 处理处理器未找到异常
     * 例如：访问不存在的API端点
     *
     * @param e 处理器未找到异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseEntity<ApiResponse<String>> handleNoHandlerFound(
            NoHandlerFoundException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        String method = request.getMethod();
        String message = String.format("未找到 %s %s 对应的处理器", method, requestUri);

        logger.warn("处理器未找到: {} {}", method, requestUri);

        ApiResponse<String> response = ApiResponse.failed(message, 404);
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    }

    // ==================== 参数验证异常 ====================

    /**
     * 处理缺少请求参数异常
     *
     * @param e 缺少请求参数异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<ApiResponse<String>> handleMissingServletRequestParameter(
            MissingServletRequestParameterException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        String message = String.format("缺少必需的请求参数: %s", e.getParameterName());

        logger.warn("缺少请求参数: URI={}, 参数名={}", requestUri, e.getParameterName());

        ApiResponse<String> response = ApiResponse.validateFailed(message);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理缺少请求部分异常（文件上传）
     *
     * @param e 缺少请求部分异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(MissingServletRequestPartException.class)
    public ResponseEntity<ApiResponse<String>> handleMissingServletRequestPart(
            MissingServletRequestPartException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        String message = String.format("缺少必需的文件参数: %s", e.getRequestPartName());

        logger.warn("缺少文件参数: URI={}, 参数名={}", requestUri, e.getRequestPartName());

        ApiResponse<String> response = ApiResponse.validateFailed(message);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理方法参数类型不匹配异常
     *
     * @param e 方法参数类型不匹配异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ApiResponse<String>> handleMethodArgumentTypeMismatch(
            MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        String message = String.format("参数 '%s' 的值 '%s' 类型不正确，期望类型: %s",
                                      e.getName(), e.getValue(), e.getRequiredType().getSimpleName());

        logger.warn("参数类型不匹配: URI={}, 参数名={}, 参数值={}, 期望类型={}",
                   requestUri, e.getName(), e.getValue(), e.getRequiredType().getSimpleName());

        ApiResponse<String> response = ApiResponse.validateFailed(message);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理请求体不可读异常
     *
     * @param e 请求体不可读异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<ApiResponse<String>> handleHttpMessageNotReadable(
            HttpMessageNotReadableException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        String message = "请求体格式错误，请检查JSON格式是否正确";

        logger.warn("请求体不可读: URI={}, 错误信息={}", requestUri, e.getMessage());

        ApiResponse<String> response = ApiResponse.validateFailed(message);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理方法参数验证异常
     *
     * @param e 方法参数验证异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<String>> handleMethodArgumentNotValid(
            MethodArgumentNotValidException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();

        // 收集所有验证错误信息
        String errorMessages = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));

        String message = "参数验证失败: " + errorMessages;

        logger.warn("方法参数验证失败: URI={}, 错误信息={}", requestUri, errorMessages);

        ApiResponse<String> response = ApiResponse.validateFailed(message);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理绑定异常
     *
     * @param e 绑定异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<ApiResponse<String>> handleBindException(
            BindException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();

        // 收集所有绑定错误信息
        String errorMessages = e.getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));

        String message = "参数绑定失败: " + errorMessages;

        logger.warn("参数绑定失败: URI={}, 错误信息={}", requestUri, errorMessages);

        ApiResponse<String> response = ApiResponse.validateFailed(message);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理约束违反异常
     *
     * @param e 约束违反异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ApiResponse<String>> handleConstraintViolation(
            ConstraintViolationException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();

        // 收集所有约束违反信息
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        String errorMessages = violations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining("; "));

        String message = "约束验证失败: " + errorMessages;

        logger.warn("约束验证失败: URI={}, 错误信息={}", requestUri, errorMessages);

        ApiResponse<String> response = ApiResponse.validateFailed(message);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    // ==================== 文件相关异常 ====================

    /**
     * 处理文件存储异常
     *
     * @param e 文件存储异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(FileStorageException.class)
    public ResponseEntity<ApiResponse<String>> handleFileStorageException(
            FileStorageException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        String errorCode = e.getErrorCode();
        String message = e.getMessage();

        logger.error("文件存储异常: URI={}, 错误代码={}, 错误信息={}", requestUri, errorCode, message, e);

        // 根据错误代码返回不同的HTTP状态码
        ApiResponse<String> response;
        HttpStatus status;

        switch (errorCode) {
            case FileStorageException.FILE_TOO_LARGE:
                response = ApiResponse.failed(message, 413);
                status = HttpStatus.PAYLOAD_TOO_LARGE;
                break;
            case FileStorageException.FILE_TYPE_NOT_ALLOWED:
                response = ApiResponse.failed(message, 415);
                status = HttpStatus.UNSUPPORTED_MEDIA_TYPE;
                break;
            case FileStorageException.FILE_NOT_FOUND:
                response = ApiResponse.failed(message, 404);
                status = HttpStatus.NOT_FOUND;
                break;
            case FileStorageException.PERMISSION_DENIED:
                response = ApiResponse.failed(message, 403);
                status = HttpStatus.FORBIDDEN;
                break;
            case FileStorageException.DISK_SPACE_INSUFFICIENT:
                response = ApiResponse.failed(message, 507);
                status = HttpStatus.INSUFFICIENT_STORAGE;
                break;
            case FileStorageException.FILE_NAME_INVALID:
                response = ApiResponse.validateFailed(message);
                status = HttpStatus.BAD_REQUEST;
                break;
            case FileStorageException.UPLOAD_FAILED:
            default:
                response = ApiResponse.failed(message, 500);
                status = HttpStatus.INTERNAL_SERVER_ERROR;
                break;
        }

        return ResponseEntity.status(status).body(response);
    }

    /**
     * 处理文件上传大小超限异常
     *
     * @param e 文件大小超限异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<ApiResponse<String>> handleMaxUploadSizeExceededException(
            MaxUploadSizeExceededException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        String message = "文件大小超过限制，最大允许10GB";

        logger.warn("文件上传大小超限: URI={}, 错误信息={}", requestUri, e.getMessage());

        ApiResponse<String> response = ApiResponse.failed(message, 413);
        return ResponseEntity.status(HttpStatus.PAYLOAD_TOO_LARGE).body(response);
    }

    // ==================== 业务逻辑异常 ====================

    /**
     * 处理参数验证异常
     *
     * @param e 参数异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiResponse<String>> handleIllegalArgumentException(
            IllegalArgumentException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        String message = e.getMessage();

        logger.warn("参数验证失败: URI={}, 错误信息={}", requestUri, message);

        ApiResponse<String> response = ApiResponse.validateFailed(message);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理状态异常
     *
     * @param e 状态异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(IllegalStateException.class)
    public ResponseEntity<ApiResponse<String>> handleIllegalStateException(
            IllegalStateException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        String message = e.getMessage();

        logger.warn("状态异常: URI={}, 错误信息={}", requestUri, message);

        ApiResponse<String> response = ApiResponse.failed(message, 409);
        return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
    }

    // ==================== 系统级异常 ====================

    /**
     * 处理运行时异常
     *
     * @param e 运行时异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ApiResponse<String>> handleRuntimeException(
            RuntimeException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        String message = "系统内部错误，请稍后重试";

        logger.error("运行时异常: URI={}, 异常类型={}, 错误信息={}",
                    requestUri, e.getClass().getSimpleName(), e.getMessage(), e);

        ApiResponse<String> response = ApiResponse.failed(message, 500);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理通用异常（最后的兜底处理）
     *
     * @param e 异常
     * @param request HTTP请求
     * @return 错误响应
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<String>> handleException(Exception e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        String message = "系统发生未知错误，请联系管理员";

        // 对于一些特殊的异常，提供更友好的错误信息
        if (e.getMessage() != null) {
            if (e.getMessage().contains("Connection refused")) {
                message = "服务暂时不可用，请稍后重试";
            } else if (e.getMessage().contains("Timeout")) {
                message = "请求超时，请稍后重试";
            } else if (e.getMessage().contains("Access denied")) {
                message = "访问被拒绝，请检查权限";
            }
        }

        logger.error("未知异常: URI={}, 异常类型={}, 错误信息={}",
                    requestUri, e.getClass().getSimpleName(), e.getMessage(), e);

        ApiResponse<String> response = ApiResponse.failed(message, 500);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}
