package com.ruoyi.wenshuwchat.dao;

import com.ruoyi.wenshuapi.pojo.wchat.FriendList;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 好友关系数据访问层
 * 提供对wenshu-friend_list表的CRUD操作
 */
@Mapper
public interface FriendListDao {

    /**
     * 插入新的好友关系
     *
     * @param friendList 好友关系对象
     * @return 插入操作影响的行数
     */
    @Insert("INSERT INTO `wenshu-friend_list` (user_id, friend_id, status, created_at, updated_at) " +
            "VALUES (#{userId}, #{friendId}, #{status}, #{createdAt}, #{updatedAt})")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insert(FriendList friendList);

    /**
     * 根据ID删除好友关系
     *
     * @param id 好友关系ID
     * @return 删除操作影响的行数
     */
    @Delete("DELETE FROM `wenshu-friend_list` WHERE id = #{id}")
    int deleteById(@Param("id") Long id);

    /**
     * 根据用户ID和好友ID删除好友关系
     *
     * @param userId 用户ID
     * @param friendId 好友ID
     * @return 删除操作影响的行数
     */
    @Delete("DELETE FROM `wenshu-friend_list` WHERE user_id = #{userId} AND friend_id = #{friendId}")
    int deleteByUserAndFriend(@Param("userId") Long userId, @Param("friendId") Long friendId);

    /**
     * 更新好友关系状态
     *
     * @param friendList 好友关系对象
     * @return 更新操作影响的行数
     */
    @Update("UPDATE `wenshu-friend_list` SET " +
            "status = #{status}, updated_at = #{updatedAt} " +
            "WHERE id = #{id}")
    int updateStatus(FriendList friendList);

    /**
     * 根据ID查询好友关系
     *
     * @param id 好友关系ID
     * @return 好友关系对象
     */
    @Select("SELECT id, user_id, friend_id, status, created_at, updated_at " +
            "FROM `wenshu-friend_list` WHERE id = #{id}")
    @Results(id = "friendListResultMap", value = {
            @Result(property = "id", column = "id", id = true),
            @Result(property = "userId", column = "user_id"),
            @Result(property = "friendId", column = "friend_id"),
            @Result(property = "status", column = "status"),
            @Result(property = "createdAt", column = "created_at"),
            @Result(property = "updatedAt", column = "updated_at")
    })
    FriendList selectById(@Param("id") Long id);

    /**
     * 根据用户ID查询所有好友关系
     *
     * @param userId 用户ID
     * @return 好友关系列表
     */
    @Select("SELECT id, user_id, friend_id, status, created_at, updated_at " +
            "FROM `wenshu-friend_list` WHERE user_id = #{userId}")
    @ResultMap("friendListResultMap")
    List<FriendList> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和状态查询好友关系
     *
     * @param userId 用户ID
     * @param status 关系状态
     * @return 好友关系列表
     */
    @Select("SELECT id, user_id, friend_id, status, created_at, updated_at " +
            "FROM `wenshu-friend_list` WHERE user_id = #{userId} AND status = #{status}")
    @ResultMap("friendListResultMap")
    List<FriendList> selectByUserIdAndStatus(@Param("userId") Long userId, @Param("status") String status);

    /**
     * 检查两个用户之间是否存在好友关系
     *
     * @param userId 用户ID
     * @param friendId 好友ID
     * @return 好友关系对象，如果不存在则返回null
     */
    @Select("SELECT id, user_id, friend_id, status, created_at, updated_at " +
            "FROM `wenshu-friend_list` " +
            "WHERE (user_id = #{userId} AND friend_id = #{friendId}) " +
            "OR (user_id = #{friendId} AND friend_id = #{userId})")
    @ResultMap("friendListResultMap")
    FriendList selectByUserAndFriend(@Param("userId") Long userId, @Param("friendId") Long friendId);

    /**
     * 查询所有好友关系
     *
     * @return 所有好友关系列表
     */
    @Select("SELECT id, user_id, friend_id, status, created_at, updated_at " +
            "FROM `wenshu-friend_list`")
    @ResultMap("friendListResultMap")
    List<FriendList> selectAll();
}
