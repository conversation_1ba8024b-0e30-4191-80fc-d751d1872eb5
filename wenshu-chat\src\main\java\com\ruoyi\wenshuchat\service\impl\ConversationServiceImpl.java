package com.ruoyi.wenshuchat.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.wenshucommon.entity.BaseConversation;
import com.ruoyi.wenshuchat.dao.ChatConversationDao;
import com.ruoyi.wenshuchat.service.ConversationService;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service("wenshuChatConversationService")
public class ConversationServiceImpl extends ServiceImpl<ChatConversationDao, BaseConversation> implements ConversationService {
    /**
     * 获取单个对话，使用Optional包装
     */
    @Override
    public Optional<BaseConversation> getOneOpt(Wrapper<BaseConversation> queryWrapper) {
        return Optional.ofNullable(getOne(queryWrapper, false));
    }
}
