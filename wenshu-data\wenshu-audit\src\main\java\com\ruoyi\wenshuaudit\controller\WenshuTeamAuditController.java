package com.ruoyi.wenshuaudit.controller;

import com.ruoyi.wenshuapi.pojo.audit.WenshuTeamAudit;
import com.ruoyi.wenshuaudit.service.WenshuTeamAuditService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class WenshuTeamAuditController {
    private final WenshuTeamAuditService wenshuTeamAuditService;
    
    @GetMapping("/TeamInfogetAll")
    public List<WenshuTeamAudit> TeamInfogetAll() {
        return wenshuTeamAuditService.list();
    }
    
    @GetMapping("/TeamInfogetOne")
    public WenshuTeamAudit TeamInfogetOne(@RequestParam("teamId") int teamId) {
        return wenshuTeamAuditService.getById(teamId);
    }
    
    @PostMapping("/TeamInfoadd")
    public boolean TeamInfoadd(@RequestBody WenshuTeamAudit wenshuTeamAudit) {
        return wenshuTeamAuditService.save(wenshuTeamAudit);
    }
    
    @PutMapping("/TeamInfoupdate")
    public boolean TeamInfoupdate(@RequestBody WenshuTeamAudit wenshuTeamAudit) {
        return wenshuTeamAuditService.updateById(wenshuTeamAudit);
    }
    
    @DeleteMapping("/TeamInfodelete")
    public boolean TeamInfodelete(@RequestParam("teamId") int teamId) {
        return wenshuTeamAuditService.removeById(teamId);
    }
}
