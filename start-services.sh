#!/bin/bash

# 微服务启动脚本 - 按正确顺序启动服务
# 解决nacos连接问题

echo "=== 启动微服务系统 ==="

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "错误: Docker未运行，请先启动Docker"
    exit 1
fi

# 停止所有现有服务
echo "1. 停止现有服务..."
sudo docker compose -f docker-compose.app.yml down

# 清理网络和卷（可选）
echo "2. 清理Docker网络..."
sudo docker network prune -f

# 等待一下确保清理完成
sleep 2

# 启动基础设施服务（如果存在）
echo "3. 启动基础设施服务..."
if sudo docker compose -f docker-compose.yml ps wenshu-nacos > /dev/null 2>&1; then
    echo "   启动 Nacos..."
    sudo docker compose -f docker-compose.yml up -d wenshu-nacos
    sleep 10
fi

if sudo docker compose -f docker-compose.yml ps wenshu-mysql > /dev/null 2>&1; then
    echo "   启动 MySQL..."
    sudo docker compose -f docker-compose.yml up -d wenshu-mysql
    sleep 5
fi

if sudo docker compose -f docker-compose.yml ps wenshu-redis > /dev/null 2>&1; then
    echo "   启动 Redis..."
    sudo docker compose -f docker-compose.yml up -d wenshu-redis
    sleep 5
fi

# 等待基础服务完全启动
echo "4. 等待基础服务启动完成..."
sleep 15

# 启动认证服务
echo "5. 启动认证服务..."
sudo docker compose -f docker-compose.app.yml up -d auth
echo "   等待认证服务启动..."
sleep 20

# 检查认证服务状态
echo "6. 检查认证服务状态..."
if sudo docker compose -f docker-compose.app.yml ps auth | grep -q "Up"; then
    echo "   ✓ 认证服务启动成功"
else
    echo "   ✗ 认证服务启动失败，查看日志:"
    sudo docker compose -f docker-compose.app.yml logs auth --tail=20
    exit 1
fi

# 启动网关服务
echo "7. 启动网关服务..."
sudo docker compose -f docker-compose.app.yml up -d gateway
echo "   等待网关服务启动..."
sleep 20

# 检查网关服务状态
echo "8. 检查网关服务状态..."
if sudo docker compose -f docker-compose.app.yml ps gateway | grep -q "Up"; then
    echo "   ✓ 网关服务启动成功"
else
    echo "   ✗ 网关服务启动失败，查看日志:"
    sudo docker compose -f docker-compose.app.yml logs gateway --tail=20
    exit 1
fi

# 启动其他服务
echo "9. 启动其他服务..."
sudo docker compose -f docker-compose.app.yml up -d

echo "=== 启动完成 ==="
echo ""
echo "服务状态:"
sudo docker compose -f docker-compose.app.yml ps

echo ""
echo "如果有服务启动失败，可以使用以下命令查看日志:"
echo "sudo docker compose -f docker-compose.app.yml logs [服务名] --tail=50"
echo ""
echo "访问地址:"
echo "- 网关: http://localhost:8080"
echo "- 认证服务: http://localhost:9200"
