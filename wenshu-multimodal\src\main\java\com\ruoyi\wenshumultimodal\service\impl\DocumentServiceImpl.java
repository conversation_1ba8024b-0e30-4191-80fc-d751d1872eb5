package com.ruoyi.wenshumultimodal.service.impl;

import com.ruoyi.wenshuapi.client.file.FileInfoClient;
import com.ruoyi.wenshuapi.client.file.FileStorageClient;
import com.ruoyi.wenshuapi.client.file.FileUserTeamClient;
import com.ruoyi.wenshuapi.client.team.TeamMemberClient;
import com.ruoyi.wenshuapi.pojo.chat.RequestBodyDTO;
import com.ruoyi.wenshuapi.pojo.file.FileInfoPojo;
import com.ruoyi.wenshuapi.pojo.file.FileUserTeamPojo;
import com.ruoyi.wenshuapi.util.file.ApiResponse;
import com.ruoyi.wenshumultimodal.service.DocumentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.commonmark.Extension;
import org.commonmark.ext.gfm.tables.TablesExtension;
import org.commonmark.node.*;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.html.HtmlRenderer;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.ruoyi.wenshuapi.client.chat.ChatFeignClientInter;
import org.springframework.web.multipart.MultipartFile;
import org.xhtmlrenderer.pdf.ITextRenderer;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.math.BigInteger;

/**
 * 文档服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentServiceImpl implements DocumentService {

    private final ChatFeignClientInter chatFeignClient;
    private final FileInfoClient fileInfoClient;
    private final FileUserTeamClient  userTeamClient;
    private final TeamMemberClient teamMemberClient;
    private final FileStorageClient fileStorageClient;

    // 最大重试次数
    private static final int MAX_RETRY_ATTEMPTS = 3;
    
    // 系统提示词模板
    private static final String SYSTEM_PROMPT = "# 任务说明\n" +
            "请根据用户的具体需求创建一份完整文档，并以格式化的HTML代码形式输出（直接输出代码，不要包含解释说明）。文档需包含专业排版元素，确保浏览器可直接渲染。\n" +
            "\n" +
            "## 用户要求\n" +
            "用户将在此处描述具体内容需求（例如：产品说明/技术文档/商业报告等）\n" +
            "\n" +
            "## HTML格式要求\n" +
            "1. 使用HTML5标准\n" +
            "2. 包含响应式设计基础样式（内联在<style>标签中）\n" +
            "3. 必须包含以下结构元素：\n" +
            "   - 带导航标题的<header>\n" +
            "   - 内容分段<section>并添加ID锚点\n" +
            "   - 自适应表格（带斑马纹）\n" +
            "   - 层级清晰的多级标题（h1-h4）\n" +
            "   - 带项目符号/编号的列表\n" +
            "   - 页脚版权信息\n" +
            "4. 专业样式要求：\n" +
            "   - 主色系：#2563eb（标题/链接）\n" +
            "   - 辅色系：#f3f4f6（表格隔行色）\n" +
            "   - 字体：系统无衬线字体栈\n" +
            "   - 段落行高：1.6倍\n" +
            "   - 移动端适配（最大宽度100%）\n" +
            "\n" +
            "## 输出规范\n" +
            "1. 以<!DOCTYPE html>开头\n" +
            "2. 完整HTML结构，包含<html>、<head>和<body>标签\n" +
            "3. 标题添加ID属性用于锚点定位\n" +
            "4. 添加目录导航（带链接的列表）\n" +
            "5. 请确保代码可直接在浏览器中正确渲染";

    // 增强的Markdown提示词模板
    private static final String ENHANCED_MARKDOWN_PROMPT = "# 文档生成任务\n" +
            "请根据以下参数生成高质量的Markdown文档内容：\n" +
            "\n" +
            "## 主题\n" +
            "[主题]\n" +
            "\n" +
            "## 风格要求\n" +
            "- 文档风格：[风格]\n" +
            "- 结构类型：[结构类型]\n" +
            "- 语言：[语言]\n" +
            "\n" +
            "## Markdown格式要求\n" +
            "1. 使用标准Markdown语法\n" +
            "2. 结构清晰，层次分明\n" +
            "3. 使用适当的标题层级（# ## ###）\n" +
            "4. 包含丰富的格式元素：\n" +
            "   - 有序和无序列表\n" +
            "   - 表格（必要时）\n" +
            "   - 引用区块\n" +
            "   - 代码块（如适用）\n" +
            "   - 强调和高亮\n" +
            "5. 内容逻辑性强，层次清晰\n" +
            "\n" +
            "## 特殊要求\n" +
            "[特殊要求]\n" +
            "\n" +
            "请直接输出Markdown内容，不要包含解释说明。确保内容丰富、专业且易于阅读。";

    // HTML主题模板
    private static final Map<String, String> HTML_THEMES = new HashMap<>();
    
    static {
        // 默认主题
        HTML_THEMES.put("default", 
            "<style>\n" +
            "  body { font-family: system-ui, sans-serif; line-height: 1.6; max-width: 900px; margin: 0 auto; padding: 20px; }\n" +
            "  h1, h2, h3, h4 { color: #2563eb; }\n" +
            "  table { border-collapse: collapse; width: 100%; }\n" +
            "  th, td { border: 1px solid #ddd; padding: 8px; }\n" +
            "  tr:nth-child(even) { background-color: #f3f4f6; }\n" +
            "</style>");
        
        // GitHub风格主题
        HTML_THEMES.put("github", 
            "<style>\n" +
            "  body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif; line-height: 1.6; max-width: 900px; margin: 0 auto; padding: 20px; color: #24292e; }\n" +
            "  h1, h2, h3, h4 { border-bottom: 1px solid #eaecef; padding-bottom: 0.3em; }\n" +
            "  h1 { font-size: 2em; }\n" +
            "  h2 { font-size: 1.5em; }\n" +
            "  a { color: #0366d6; text-decoration: none; }\n" +
            "  table { border-collapse: collapse; width: 100%; }\n" +
            "  th, td { border: 1px solid #dfe2e5; padding: 6px 13px; }\n" +
            "  tr:nth-child(even) { background-color: #f6f8fa; }\n" +
            "</style>");
        
        // 优雅主题
        HTML_THEMES.put("elegant", 
            "<style>\n" +
            "  body { font-family: 'Georgia', serif; line-height: 1.7; max-width: 800px; margin: 0 auto; padding: 20px; color: #333; }\n" +
            "  h1, h2, h3, h4 { font-family: 'Palatino', 'Times New Roman', serif; margin-top: 1.5em; color: #444; }\n" +
            "  h1 { font-size: 2.2em; text-align: center; }\n" +
            "  h2 { font-size: 1.7em; }\n" +
            "  blockquote { border-left: 4px solid #ddd; margin-left: 0; padding-left: 20px; font-style: italic; color: #555; }\n" +
            "  table { border-collapse: collapse; width: 100%; margin: 20px 0; }\n" +
            "  th, td { border: 1px solid #e1e1e1; padding: 10px; }\n" +
            "  th { background-color: #f8f8f8; }\n" +
            "</style>");
        
        // 暗色主题
        HTML_THEMES.put("dark", 
            "<style>\n" +
            "  body { font-family: system-ui, sans-serif; line-height: 1.6; max-width: 900px; margin: 0 auto; padding: 20px; background-color: #1a1a1a; color: #eee; }\n" +
            "  h1, h2, h3, h4 { color: #58a6ff; }\n" +
            "  a { color: #58a6ff; }\n" +
            "  code { background-color: #2a2a2a; padding: 2px 4px; border-radius: 3px; color: #c9d1d9; }\n" +
            "  table { border-collapse: collapse; width: 100%; }\n" +
            "  th, td { border: 1px solid #333; padding: 8px; }\n" +
            "  tr:nth-child(even) { background-color: #2a2a2a; }\n" +
            "</style>");
    }

    @Override
    public String generateMarkdownContent(String prompt, String userId) {
        // 调用增强版方法，使用默认风格和结构类型
        return generateMarkdownContent(prompt, userId, "formal", "article");
    }

    @Override
    public String generateMarkdownContent(String prompt, String userId, String style, String structureType) {
        // 构建增强的提示词
        String enhancedPrompt = buildEnhancedPrompt(prompt, style, structureType, "zh", null);
        
        log.info("开始生成Markdown文档内容，主题: {}, 风格: {}, 结构类型: {}", prompt, style, structureType);
        
        // 重试机制
        int attempt = 0;
        while (attempt < MAX_RETRY_ATTEMPTS) {
            try {
                log.info("尝试第{}次调用AI服务生成文档内容", attempt + 1);
                
                // 调用AI服务生成内容
                RequestBodyDTO requestBodyDTO = new RequestBodyDTO(enhancedPrompt, userId);
                String response = chatFeignClient.sendDocmentSend(requestBodyDTO);
                
                if (response != null) {
                    log.info("成功获取AI生成的内容，内容长度: {}", response.length());
                    return response;
                }
                
                log.warn("AI服务返回空内容，尝试重试");
                attempt++;
            } catch (Exception e) {
                log.error("调用AI服务发生错误: {}", e.getMessage(), e);
                attempt++;
                
                if (attempt >= MAX_RETRY_ATTEMPTS) {
                    log.error("达到最大重试次数，使用回退内容");
                    return generateFallbackContent(prompt, style, structureType);
                }
                
                try {
                    // 等待一段时间后重试，使用指数退避策略
                    Thread.sleep(1000 * (long)Math.pow(2, attempt));
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.error("线程中断", ie);
                    break;
                }
            }
        }
        
        // 如果所有尝试都失败，返回回退内容
        return generateFallbackContent(prompt, style, structureType);
    }

    @Override
    public String convertMarkdownToHtml(String markdown) {
        return convertMarkdownToHtml(markdown, "default", null);
    }

    @Override
    public String convertMarkdownToHtml(String markdown, String theme, String customCss) {
        try {
            log.info("开始转换Markdown至HTML，使用主题: {}", theme);
            
            // 扩展解析器以支持表格
            List<Extension> extensions = Collections.singletonList(TablesExtension.create());
            Parser parser = Parser.builder().extensions(extensions).build();
            
            // 解析Markdown
            Node document = parser.parse(markdown);
            
            // 创建HTML渲染器
            HtmlRenderer renderer = HtmlRenderer.builder()
                    .extensions(extensions)
                    .build();
            
            // 渲染为HTML
            String htmlBody = renderer.render(document);
            
            // 获取选定的主题CSS或使用默认主题
            String themeCss = HTML_THEMES.getOrDefault(theme, HTML_THEMES.get("default"));
            
            // 添加自定义CSS（如果有）
            if (customCss != null && !customCss.isEmpty()) {
                themeCss += "\n<style>\n" + customCss + "\n</style>";
            }
            
            // 构建完整的HTML文档
            String html = "<!DOCTYPE html>\n" +
                    "<html>\n" +
                    "<head>\n" +
                    "<meta charset=\"UTF-8\">\n" +
                    "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                    "<title>Markdown转换文档</title>\n" +
                    themeCss +
                    "\n</head>\n" +
                    "<body>\n" +
                    htmlBody +
                    "\n</body>\n" +
                    "</html>";
            
            log.info("Markdown转HTML完成，HTML大小: {} 字节", html.getBytes(StandardCharsets.UTF_8).length);
            return html;
        } catch (Exception e) {
            log.error("转换Markdown至HTML失败: {}", e.getMessage(), e);
            throw new RuntimeException("Markdown转HTML失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String generateDocument(int fileId, String prompt, String format, String outputPath, String userId) {
        // 调用增强版方法，使用默认配置
        Map<String, Object> defaultConfig = new HashMap<>();
        return generateEnhancedDocument(fileId, prompt, format, outputPath, userId, defaultConfig);
    }

    /**
     * 将Markdown转换为Word文档（简化版，无配置）
     */
    private void convertToWord(String markdown, String outputPath) throws IOException {
        // 使用默认配置调用增强版方法
        convertToWord(markdown, outputPath, new HashMap<>());
    }
    
    /**
     * 将Markdown转换为Word文档，支持配置
     */
    private void convertToWord(String markdown, String outputPath, Map<String, Object> docConfig) throws IOException {
        log.info("开始转换Markdown到Word文档，Markdown内容长度: {} 字符", markdown.length());
        
        // 创建Word文档
        XWPFDocument document = new XWPFDocument();
        
        // 应用文档配置
        Object fontSizeObj = docConfig.getOrDefault("fontSize", 12);
        int fontSize = fontSizeObj instanceof Number ? ((Number) fontSizeObj).intValue() : 12;
        
        Object lineSpacingObj = docConfig.getOrDefault("lineSpacing", 1.5);
        double lineSpacing = lineSpacingObj instanceof Number ? ((Number) lineSpacingObj).doubleValue() : 1.5;
        
        String fontFamily = (String) docConfig.getOrDefault("fontFamily", "宋体");
        
        // 解析Markdown并转换为Word文档
        try {
            // 分割Markdown为行
            String[] lines = markdown.split("\n");
            
            // 当前标题级别
            int currentHeadingLevel = 0;
            
            // 处理每一行
            for (String line : lines) {
                line = line.trim();
                
                // 跳过空行
                if (line.isEmpty()) {
                    XWPFParagraph emptyPara = document.createParagraph();
                    emptyPara.setSpacingAfter(0);
                    continue;
                }
                
                // 处理标题
                if (line.startsWith("#")) {
                    int level = 0;
                    while (level < line.length() && line.charAt(level) == '#') {
                        level++;
                    }
                    
                    if (level <= 6 && (level == line.length() || Character.isWhitespace(line.charAt(level)))) {
                        currentHeadingLevel = level;
                        String headingText = line.substring(level).trim();
                        
                        XWPFParagraph heading = document.createParagraph();
                        heading.setStyle("Heading" + level);
                        
                        XWPFRun headingRun = heading.createRun();
                        headingRun.setText(headingText);
                        headingRun.setBold(true);
                        headingRun.setFontSize(fontSize + (6 - level) * 2); // 根据级别设置字体大小
                        headingRun.setFontFamily(fontFamily);
                        
                        continue;
                    }
                }
                
                // 处理无序列表
                if (line.startsWith("- ") || line.startsWith("* ") || line.startsWith("+ ")) {
                    XWPFParagraph listPara = document.createParagraph();
                    listPara.setIndentationLeft(500); // 缩进
                    listPara.setNumID(getOrCreateNumbering(document, 0, 1));
                    
                    XWPFRun listRun = listPara.createRun();
                    listRun.setText(line.substring(2).trim());
                    listRun.setFontSize(fontSize);
                    listRun.setFontFamily(fontFamily);
                    
                    continue;
                }
                
                // 处理有序列表
                if (line.matches("^\\d+\\.\\s.*")) {
                    XWPFParagraph listPara = document.createParagraph();
                    listPara.setIndentationLeft(500); // 缩进
                    listPara.setNumID(getOrCreateNumbering(document, 1, 1));
                    
                    String listText = line.replaceFirst("^\\d+\\.\\s+", "");
                    XWPFRun listRun = listPara.createRun();
                    listRun.setText(listText);
                    listRun.setFontSize(fontSize);
                    listRun.setFontFamily(fontFamily);
                    
                    continue;
                }
                
                // 处理普通段落
                XWPFParagraph para = document.createParagraph();
                para.setSpacingBetween(lineSpacing);
                
                XWPFRun run = para.createRun();
                run.setText(line);
                run.setFontSize(fontSize);
                run.setFontFamily(fontFamily);
            }
            
            log.info("Word文档内容生成完成，段落数量: {}", document.getParagraphs().size());
            
            // 保存文档
            try (FileOutputStream out = new FileOutputStream(outputPath)) {
                document.write(out);
                log.info("Word文档保存成功: {}", outputPath);
            }
        } catch (Exception e) {
            log.error("转换Markdown到Word文档失败: {}", e.getMessage(), e);
            throw new IOException("转换Word文档失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建或获取编号样式
     */
    private BigInteger getOrCreateNumbering(XWPFDocument document, int numFmt, int level) {
        try {
            // 简单实现，实际应用中可能需要更复杂的逻辑
            XWPFNumbering numbering = document.createNumbering();
            BigInteger abstractNumId = BigInteger.valueOf(0);
            BigInteger numId = numbering.addNum(abstractNumId);
            return numId;
        } catch (Exception e) {
            log.warn("创建编号样式失败: {}", e.getMessage());
            return BigInteger.ONE;
        }
    }
    
    @Override
    public String generateEnhancedDocument(int fileId, String prompt, String format, String outputPath, String userId, Map<String, Object> docConfig) {
        try {
            log.info("开始使用增强配置生成文档，格式: {}, 配置参数数量: {}", format, docConfig.size());
            
            // 提取文档配置参数
            String style = (String) docConfig.getOrDefault("style", "formal");
            String structureType = (String) docConfig.getOrDefault("structureType", "article");
            Boolean includeTableOfContents = (Boolean) docConfig.getOrDefault("includeTableOfContents", false);
            String theme = (String) docConfig.getOrDefault("theme", "default");
            Object fontSizeObj = docConfig.getOrDefault("fontSize", 12);
            int fontSize = fontSizeObj instanceof Number ? ((Number) fontSizeObj).intValue() : 12;
            String language = (String) docConfig.getOrDefault("language", "zh");
            
            // 1. 生成Markdown内容（根据风格和结构类型）
            String markdownContent = generateMarkdownContent(prompt, userId, style, structureType);
            log.info("成功生成Markdown内容，长度: {} 字符", markdownContent.length());
            
            // 如果启用了目录并且是大型文档，添加目录
            if (includeTableOfContents && markdownContent.length() > 1000) {
                markdownContent = addTableOfContents(markdownContent);
                log.info("已添加目录到文档");
            }

            // 如果没有指定输出路径，创建一个临时路径
            if (!StringUtils.hasText(outputPath)) {
                String fileName = UUID.randomUUID().toString();
                outputPath = System.getProperty("java.io.tmpdir") + "/" + fileName + "." + format.toLowerCase();
            }

            Path path = Paths.get(outputPath);
            
            // 2. 根据格式和配置生成不同类型的文档
            switch (format.toLowerCase()) {
                case "md":
                    // 直接保存Markdown内容
                    Files.write(path, markdownContent.getBytes(StandardCharsets.UTF_8));
                    log.info("已保存Markdown文件: {}", path);
                    break;
                    
                case "html":
                    // 转换为HTML并保存，应用所选主题
                    String htmlContent = convertMarkdownToHtml(markdownContent, theme, 
                            buildCustomCssFromConfig(docConfig));
                    Files.write(path, htmlContent.getBytes(StandardCharsets.UTF_8));
                    log.info("已保存HTML文件: {}, 大小: {} 字节", path, htmlContent.length());
                    break;
                    
                case "docx":
                    // 转换为Word文档，应用字体和样式配置
                    convertToWord(markdownContent, path.toString(), docConfig);
                    // 验证文件是否创建成功
                    if (!Files.exists(path) || Files.size(path) == 0) {
                        throw new IOException("Word文档创建失败或为空");
                    }
                    log.info("已保存Word文档: {}, 大小: {} 字节", path, Files.size(path));
                    break;
                    
                case "pdf":
                    // 转换为PDF，应用主题和样式
                    String htmlForPdf = convertMarkdownToHtml(markdownContent, theme, 
                            buildCustomCssFromConfig(docConfig));
                    convertToPdf(htmlForPdf, path.toString(), docConfig);
                    // 验证文件是否创建成功
                    if (!Files.exists(path) || Files.size(path) == 0) {
                        throw new IOException("PDF文档创建失败或为空");
                    }
                    log.info("已保存PDF文档: {}, 大小: {} 字节", path, Files.size(path));
                    break;
                    
                default:
                    throw new IllegalArgumentException("不支持的文档格式: " + format);
            }
            ApiResponse<Map<String, Object>> uploadResponse = null;
            // 3. 转换为MultipartFile并上传
            try {
                MultipartFile multipartFile = convertToMultipartFile(path);
                log.info("已将文件转换为MultipartFile, 大小: {} 字节", multipartFile.getSize());
                
                // 构建文件存储信息
                String fileTitle = prompt;
                if (fileTitle.length() > 50) {
                    fileTitle = fileTitle.substring(0, 47) + "...";
                }

                uploadResponse = fileStorageClient.uploadFile(
                        multipartFile,
                        Integer.parseInt(userId),
                        "个人",  // ownerType
                        "可读写" // fileStatus
                );
                
                if (uploadResponse != null && uploadResponse.getCode() == 200) {
                    log.info("文件上传成功，响应: {}", uploadResponse.getData());
                } else {
                    log.warn("文件上传响应异常: {}", uploadResponse);
                }
            } catch (Exception e) {
                log.error("文件上传失败: {}", e.getMessage(), e);
                // 上传失败不影响返回本地文件路径
            }
            
            log.info("文档生成成功，保存路径: {}, 格式: {}, 风格: {}", path, format, style);
            return "D:/wenshu/file-storage"+uploadResponse.getData().get("filePath").toString();
            
        } catch (Exception e) {
            log.error("生成增强文档失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成增强文档失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String praseDoucment(String prompt, String userId) {
        // 调用增强版方法，使用默认提取类型
        return parseDocument(prompt, userId, "full");
    }

    @Override
    public String parseDocument(String prompt, String userId, String extractionType) {
        log.info("开始解析文档，提取类型: {}, 用户ID: {}", extractionType, userId);
        
        try {
            // 根据提取类型定制提示词
            String enhancedPrompt = prompt;
            switch (extractionType) {
                case "summary":
                    enhancedPrompt = "请提供以下文档的精确摘要，突出核心观点和关键信息，保持简洁清晰：\n\n" + prompt;
                    break;
                case "structure":
                    enhancedPrompt = "请分析以下文档的结构，提供清晰的章节大纲，并标记重要组成部分：\n\n" + prompt;
                    break;
                case "entities":
                    enhancedPrompt = "请从以下文档中提取所有重要实体，包括人名、组织、日期、地点和关键术语：\n\n" + prompt;
                    break;
                default:
                    enhancedPrompt = "请全面解析以下文档内容，提供详细分析：\n\n" + prompt;
            }
            
            // 调用AI服务进行解析
            RequestBodyDTO requestBodyDTO = new RequestBodyDTO(enhancedPrompt, userId);
            String response = chatFeignClient.sendDocmentSend(requestBodyDTO);
            
            if (response != null && !response.isEmpty()) {
                log.info("文档解析成功，结果长度: {} 字符", response.length());
                return response;
            } else {
                log.warn("AI服务返回空解析结果");
                return "文档解析失败，未能获取有效结果。";
            }
        } catch (Exception e) {
            log.error("解析文档失败: {}", e.getMessage(), e);
            throw new RuntimeException("解析文档失败: " + e.getMessage(), e);
        }
    }

    // 将Path转换为MultipartFile
    private MultipartFile convertToMultipartFile(Path filePath) throws IOException {
        String fileName = filePath.getFileName().toString();
        String contentType = Files.probeContentType(filePath);
        byte[] content = Files.readAllBytes(filePath);
        return new MockMultipartFile(
                "file",
                fileName,
                contentType != null ? contentType : "application/octet-stream",
                content
        );
    }

    /**
     * 构建增强的AI提示词
     */
    private String buildEnhancedPrompt(String topic, String style, String structureType, String language, String specialRequirements) {
        String enhancedPrompt = ENHANCED_MARKDOWN_PROMPT
                .replace("[主题]", topic)
                .replace("[风格]", style)
                .replace("[结构类型]", structureType)
                .replace("[语言]", language);
        
        // 添加特殊要求（如果有）
        if (specialRequirements != null && !specialRequirements.isEmpty()) {
            enhancedPrompt = enhancedPrompt.replace("[特殊要求]", specialRequirements);
        } else {
            enhancedPrompt = enhancedPrompt.replace("[特殊要求]", "无");
        }
        
        return enhancedPrompt;
    }
    
    /**
     * 生成回退内容，根据风格和结构类型调整
     */
    private String generateFallbackContent(String topic, String style, String structureType) {
        String styleText;
        switch (style) {
            case "academic":
                styleText = "学术";
                break;
            case "creative":
                styleText = "创意";
                break;
            case "concise":
                styleText = "简洁";
                break;
            default:
                styleText = "正式";
        }
        
        String structureText;
        switch (structureType) {
            case "report":
                structureText = "报告";
                break;
            case "proposal":
                structureText = "提案";
                break;
            case "note":
                structureText = "笔记";
                break;
            default:
                structureText = "文章";
        }
        
        return "# " + topic + "\n\n" +
                "## 概述\n\n" +
                "很抱歉，AI服务暂时无法生成完整的" + styleText + "风格" + structureText + "内容。这是一个自动生成的回退内容。\n\n" +
                "## 建议\n\n" +
                "- 请稍后再试\n" +
                "- 检查网络连接\n" +
                "- 联系系统管理员\n\n" +
                "生成时间: " + LocalDateTime.now();
    }
    
    /**
     * 转换为PDF，支持配置
     */
    private void convertToPdf(String html, String outputPath, Map<String, Object> docConfig) {
        try {
            log.info("开始转换HTML到PDF: {}", outputPath);
            
            // 清理HTML，使其符合XHTML标准
            String cleanHtml = cleanHtmlForPdf(html);
            
            // 使用Flying Saucer + iText进行PDF转换
            ITextRenderer renderer = new ITextRenderer();
            
            // 设置HTML内容
            renderer.setDocumentFromString(cleanHtml);
            renderer.layout();
            
            // 创建输出文件
            try (FileOutputStream fos = new FileOutputStream(outputPath)) {
                renderer.createPDF(fos);
                log.info("PDF转换成功: {}", outputPath);
            }
            
        } catch (Exception e) {
            log.error("PDF转换失败: {}", e.getMessage(), e);
            throw new RuntimeException("PDF转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清理HTML使其符合XHTML标准
     */
    private String cleanHtmlForPdf(String html) {
        if (html == null) return "";
        
        // 修复自闭合标签
        html = html.replaceAll("<meta([^>]*?)>", "<meta$1/>");
        html = html.replaceAll("<br>", "<br/>");
        html = html.replaceAll("<hr>", "<hr/>");
        html = html.replaceAll("<img([^>]*?)>", "<img$1/>");
        html = html.replaceAll("<input([^>]*?)>", "<input$1/>");
        html = html.replaceAll("<link([^>]*?)>", "<link$1/>");
        
        // 确保HTML结构完整
        if (!html.contains("<!DOCTYPE")) {
            html = "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" " +
                   "\"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n" + html;
        }
        
        // 确保有html标签和xmlns属性
        if (!html.contains("<html")) {
            html = html.replace("<html", "<html xmlns=\"http://www.w3.org/1999/xhtml\"");
        }
        
        return html;
    }
    
    /**
     * 构建自定义CSS
     */
    private String buildCustomCssFromConfig(Map<String, Object> docConfig) {
        StringBuilder css = new StringBuilder();
        
        // 提取并应用字体大小
        Object fontSizeObj = docConfig.get("fontSize");
        if (fontSizeObj != null) {
            int fontSize = fontSizeObj instanceof Number ? ((Number) fontSizeObj).intValue() : 12;
            css.append("body { font-size: ").append(fontSize).append("px; }\n");
        }
        
        // 提取并应用行间距
        Object lineSpacingObj = docConfig.get("lineSpacing");
        if (lineSpacingObj != null) {
            double lineSpacing = lineSpacingObj instanceof Number ? ((Number) lineSpacingObj).doubleValue() : 1.5;
            css.append("body { line-height: ").append(lineSpacing).append("; }\n");
        }
        
        // 提取并应用字体
        String fontFamily = (String) docConfig.get("fontFamily");
        if (fontFamily != null) {
            css.append("body { font-family: \"").append(fontFamily).append("\", sans-serif; }\n");
        }
        
        // 页边距
        Object pageMarginObj = docConfig.get("pageMargin");
        if (pageMarginObj != null) {
            int pageMargin = pageMarginObj instanceof Number ? ((Number) pageMarginObj).intValue() : 20;
            css.append("body { padding: ").append(pageMargin).append("px; }\n");
        }
        
        return css.toString();
    }
    
    /**
     * 为Markdown文档添加目录
     */
    private String addTableOfContents(String markdown) {
        try {
            // 解析Markdown查找标题
            StringBuilder toc = new StringBuilder("## 目录\n\n");
            String[] lines = markdown.split("\n");
            int tocInsertPos = 0;
            
            // 找到第一个标题后的位置（作为目录插入点）
            for (int i = 0; i < lines.length; i++) {
                if (lines[i].startsWith("# ")) {
                    tocInsertPos = i + 1;
                    break;
                }
            }
            
            // 遍历查找所有标题并生成目录项
            for (int i = 0; i < lines.length; i++) {
                String line = lines[i];
                if (line.startsWith("# ") || line.startsWith("## ") || line.startsWith("### ")) {
                    // 计算缩进级别
                    int level = 0;
                    while (line.charAt(level) == '#') {
                        level++;
                    }
                    
                    String title = line.substring(level + 1).trim();
                    String anchor = title.toLowerCase().replaceAll("[^a-z0-9\\s]", "").replace(" ", "-");
                    
                    // 生成目录项（不同级别有不同缩进）
                    String indent = "  ".repeat(level - 1);
                    toc.append(indent).append("- [").append(title).append("](#").append(anchor).append(")\n");
                }
            }
            
            toc.append("\n");
            
            // 将目录插入到文档中
            StringBuilder enhancedMarkdown = new StringBuilder();
            for (int i = 0; i < lines.length; i++) {
                enhancedMarkdown.append(lines[i]).append("\n");
                
                // 在合适位置插入目录
                if (i == tocInsertPos) {
                    enhancedMarkdown.append(toc);
                }
            }
            
            return enhancedMarkdown.toString();
        } catch (Exception e) {
            log.error("添加目录失败: {}", e.getMessage());
            // 出错时返回原始Markdown
            return markdown;
        }
    }
}
