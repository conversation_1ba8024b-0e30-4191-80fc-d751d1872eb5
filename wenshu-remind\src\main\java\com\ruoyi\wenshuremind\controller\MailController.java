// src/main/java/com/example/maildemo/controller/MailController.java
package com.ruoyi.wenshuremind.controller;

import com.ruoyi.wenshuremind.service.EmailService;
import com.ruoyi.wenshuapi.util.file.ApiResponse;
import jakarta.mail.MessagingException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;

/**
 * 邮件服务控制器
 * 提供邮件发送功能的RESTful接口
 * 基础路径：/remind
 */
@RestController
@RequestMapping("/remind")
public class MailController {

    @Autowired
    private EmailService emailService;

    /**
     * 发送纯文本邮件
     *
     * @param to      收件人地址
     * @param subject 邮件主题
     * @param text    邮件正文内容
     * @return 统一API响应结果
     */
    @PostMapping("/send-simple")
    public ApiResponse<String> sendSimpleMail(
            @RequestParam("to") String to,
            @RequestParam("subject") String subject,
            @RequestParam("text") String text) {

        try {
            emailService.sendSimpleMail(to, subject, text);
            return ApiResponse.success("纯文本邮件发送成功", "邮件已发送至: " + to);
        } catch (Exception e) {
            return ApiResponse.failed("邮件发送失败: " + e.getMessage());
        }
    }

    /**
     * 发送HTML格式邮件
     *
     * @param to          收件人地址
     * @param subject     邮件主题
     * @param htmlContent HTML格式的邮件内容
     * @return 统一API响应结果
     */
    @PostMapping("/send-html")
    public ApiResponse<String> sendHtmlMail(
            @RequestParam("to") String to,
            @RequestParam("subject") String subject,
            @RequestParam("htmlContent") String htmlContent) {

        try {
            // 构建带样式的HTML邮件模板
            String html = "<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>" +
                    "<h2 style='color: #2c3e50;'>" + subject + "</h2>" +
                    "<div style='background-color: #f8f9fa; padding: 20px; border-radius: 5px;'>" +
                    htmlContent.replace("\n", "<br>") +
                    "</div>" +
                    "<p style='color: #7f8c8d; margin-top: 20px;'>此邮件由Spring Boot邮件系统发送</p>" +
                    "</div>";

            emailService.sendHtmlMail(to, subject, html);
            return ApiResponse.success("HTML邮件发送成功", "邮件已发送至: " + to);
        } catch (MessagingException e) {
            return ApiResponse.failed("HTML邮件发送失败: " + e.getMessage());
        }
    }

    /**
     * 发送带附件的邮件
     *
     * @param to        收件人地址
     * @param subject   邮件主题
     * @param text      邮件正文内容
     * @param attachment 邮件附件文件
     * @return 统一API响应结果
     */
    @PostMapping(value = "/send-attachment", consumes = "multipart/form-data")
    public ApiResponse<String> sendAttachmentMail(
            @RequestParam("to") String to,
            @RequestParam("subject") String subject,
            @RequestParam("text") String text,
            @RequestParam("attachment") MultipartFile attachment) {

        Path tempFile = null;
        try {
            // 校验附件是否为空
            if (attachment.isEmpty()) {
                return ApiResponse.validateFailed("附件不能为空");
            }

            // 创建临时文件保存附件
            tempFile = Files.createTempFile("mail_attachment_", "_" + attachment.getOriginalFilename());
            Files.copy(attachment.getInputStream(), tempFile, StandardCopyOption.REPLACE_EXISTING);

            // 发送带附件的邮件
            File attachmentFile = tempFile.toFile();
            emailService.sendAttachmentMail(to, subject, text, attachmentFile);

            return ApiResponse.success("附件邮件发送成功", "邮件已发送至: " + to);
        } catch (IOException | MessagingException e) {
            return ApiResponse.failed("附件邮件发送失败: " + e.getMessage());
        } finally {
            // 确保删除临时文件
            if (tempFile != null) {
                try {
                    Files.deleteIfExists(tempFile);
                } catch (IOException e) {
                    // 临时文件清理失败日志可在此处添加
                }
            }
        }
    }
}