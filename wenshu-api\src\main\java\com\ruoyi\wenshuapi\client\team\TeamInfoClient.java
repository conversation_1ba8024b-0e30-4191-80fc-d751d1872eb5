package com.ruoyi.wenshuapi.client.team;

import com.ruoyi.wenshuapi.pojo.team.TeamInfo;
import com.ruoyi.wenshuapi.util.file.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 团队信息管理服务 Feign 客户端接口
 *
 * <p>用于远程调用团队信息管理服务的 REST API，与 TeamInfoController 接口严格对应</p>
 *
 * <p><strong>服务名称:</strong> wenshu-team-service (需确保注册中心服务名一致)</p>
 * <p><strong>路径前缀:</strong> /wenshu/teaminfo</p>
 *
 * <p><strong>注意:</strong> 
 * 1. 所有参数绑定必须显式声明名称
 * 2. 路径变量使用 @PathVariable("name") 
 * 3. 查询参数使用 @RequestParam("name")
 * 4. 复杂对象使用 @RequestBody</p>
 */
@FeignClient(
        name = "wenshu-team",
        contextId = "teamInfoClient",
        path = "/wenshu/teaminfo"
)
public interface TeamInfoClient {

    /**
     * 创建新团队
     *
     * <p><strong>原始路径:</strong> POST /wenshu/teaminfo</p>
     *
     * @param teamInfo 团队信息实体 (必需, JSON格式)
     * @return 包含新团队ID的响应:
     *         - 成功: data=团队ID, message="团队创建成功"
     *         - 验证失败: code=400, message=错误信息
     *         - 系统错误: code=500, message=错误信息
     */
    @PostMapping
    ApiResponse<Integer> createTeam(@RequestBody TeamInfo teamInfo);

    /**
     * 删除团队
     *
     * <p><strong>原始路径:</strong> DELETE /wenshu/teaminfo/{teamId}</p>
     *
     * @param teamId 要删除的团队ID (必需, 路径变量)
     * @return 操作结果响应:
     *         - 成功: message="团队删除成功"
     *         - 未找到: message="团队删除失败: 未找到指定团队"
     *         - 验证失败: code=400, message=错误信息
     *         - 系统错误: code=500, message=错误信息
     */
    @DeleteMapping("/{teamId}")
    ApiResponse<Void> deleteTeam(@PathVariable("teamId") Integer teamId);

    /**
     * 更新团队信息
     *
     * <p><strong>原始路径:</strong> PUT /wenshu/teaminfo</p>
     *
     * @param teamInfo 更新后的团队信息实体 (必需, JSON格式)
     * @return 操作结果响应:
     *         - 成功: message="团队更新成功"
     *         - 未找到: message="团队更新失败: 未找到指定团队"
     *         - 验证失败: code=400, message=错误信息
     *         - 系统错误: code=500, message=错误信息
     */
    @PutMapping
    ApiResponse<Void> updateTeam(@RequestBody TeamInfo teamInfo);

    /**
     * 根据ID获取团队详情
     *
     * <p><strong>原始路径:</strong> GET /wenshu/teaminfo/{teamId}</p>
     *
     * @param teamId 要查询的团队ID (必需, 路径变量)
     * @return 团队详情响应:
     *         - 成功: data=TeamInfo对象, message="团队详情获取成功"
     *         - 未找到: code=404, message="未找到指定团队"
     *         - 验证失败: code=400, message=错误信息
     *         - 系统错误: code=500, message=错误信息
     */
    @GetMapping("/{teamId}")
    ApiResponse<TeamInfo> getTeamById(@PathVariable("teamId") Integer teamId);

    /**
     * 获取所有团队列表
     *
     * <p><strong>原始路径:</strong> GET /wenshu/teaminfo/all</p>
     *
     * @return 团队列表响应:
     *         - 成功: data=TeamInfo列表, message="团队列表获取成功"
     *         - 系统错误: code=500, message=错误信息
     */
    @GetMapping("/all")
    ApiResponse<List<TeamInfo>> getAllTeams();

    /**
     * 根据状态筛选团队
     *
     * <p><strong>原始路径:</strong> GET /wenshu/teaminfo/status/{status}</p>
     *
     * @param status 团队状态 (必需, 路径变量)
     *               - 0: 禁用
     *               - 1: 启用
     * @return 团队列表响应:
     *         - 成功: data=TeamInfo列表, message="团队列表获取成功"
     *         - 验证失败: code=400, message=错误信息
     *         - 系统错误: code=500, message=错误信息
     */
    @GetMapping("/status/{status}")
    ApiResponse<List<TeamInfo>> getTeamsByStatus(@PathVariable("status") Byte status);

    /**
     * 根据团队名称关键词搜索团队
     *
     * <p><strong>原始路径:</strong> GET /wenshu/teaminfo/search?name={name}</p>
     *
     * @param name 团队名称关键词 (必需, 查询参数)
     * @return 搜索结果响应:
     *         - 成功: data=TeamInfo列表, message="团队搜索完成"
     *         - 验证失败: code=400, message=错误信息
     *         - 系统错误: code=500, message=错误信息
     */
    @GetMapping("/search")
    ApiResponse<List<TeamInfo>> searchTeamsByName(@RequestParam("name") String name);
}