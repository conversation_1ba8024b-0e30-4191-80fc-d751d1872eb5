# ==================== 服务器配置 ====================
server:
  port: 1021  # 应用服务端口号，文书会议系统监听1021端口
  tomcat:
    max-http-form-post-size: 10000MB
    max-swallow-size: 10000MB
    connection-timeout: 3600000    # 连接超时时间（1小时）
    max-connections: 8192          # 最大连接数
    max-threads: 200               # 最大线程数
    accept-count: 100              # 等待队列长度
# ==================== Spring框架配置 ====================
spring:
  profiles:
    active: dev  # 激活的配置环境：dev(开发)、analysisController(测试)、prod(生产)
  application:
    name: wenshu-meeting  # 应用名称，用于服务注册和发现


  # ==================== Spring Cloud配置 ====================
  cloud:
    # Nacos服务注册与发现配置
    nacos:
      discovery:
        server-addr: ************:8848  # Nacos服务器地址和端口
        namespace: public           # 命名空间，用于环境隔离
        group: DEFAULT_GROUP        # 服务分组，用于逻辑分组
        health-check-enabled: true  # 启用健康检查，确保服务可用性

    # 负载均衡配置
    loadbalancer:
      enabled: true  # 启用Spring Cloud LoadBalancer
      health-check:  # 负载均衡健康检查配置
        enabled: true      # 启用健康检查
        interval: 30s      # 健康检查间隔时间
        initial-delay: 15s # 初始延迟时间
      # 负载均衡器配置
      configurations: default
      cache:
        enabled: true
        ttl: 35s
        capacity: 256

  # ==================== 文件上传配置 ====================
  servlet:
    multipart:
      enabled: true                    # 启用文件上传功能
      max-file-size: 10GB            # 单个文件最大大小（视频文件通常较大）
      max-request-size: 10GB         # 整个请求最大大小
      file-size-threshold: 2KB        # 文件写入磁盘的阈值
      location: ${java.io.tmpdir}     # 临时文件存储位置
      resolve-lazily: false           # 是否延迟解析文件

  # ==================== 数据源配置 ====================
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver  # MySQL 8.x驱动类
    # 数据库连接URL：指定服务器地址、端口、数据库名及连接参数
    url: jdbc:mysql://************:3306/ry-cloud?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=UTF8
    username: root        # 数据库用户名
    password: 2313147023  # 数据库密码
    # HikariCP连接池配置（Spring Boot默认连接池）
    hikari:
      connection-timeout: 30000  # 连接超时时间（毫秒）
      maximum-pool-size: 20      # 最大连接池大小

  # ==================== AI服务配置 ====================
  ai:
    dashscope:  # 阿里云DashScope AI服务配置
      api-key: sk-a4b80017093447aab1688acad39d24b6  # API密钥
      chat:
        options:
          model: qwen-plus  # 使用的AI模型：通义千问Plus

  # ==================== Redis缓存配置 ====================
  data:
    redis:
      database: 2        # Redis数据库索引（0-15）
      host: localhost    # Redis服务器地址
      port: 6379         # Redis服务器端口
      #password: 123456  # Redis密码（当前未设置密码）
      timeout: 6000ms    # 连接超时时间
      # Jedis连接池配置
      jedis:
        pool:
          max-active: 200  # 最大活跃连接数
          max-wait: -1     # 最大等待时间（-1表示无限等待）
          max-idle: 10     # 最大空闲连接数
          min-idle: 0      # 最小空闲连接数

  # ==================== Spring任务配置 ====================
  task:
    execution:
      pool:
        core-size: 8              # 核心线程数
        max-size: 16              # 最大线程数
        queue-capacity: 100       # 队列容量
        keep-alive: 60s           # 线程保活时间
      thread-name-prefix: "async-task-"  # 线程名前缀
    scheduling:
      pool:
        size: 4                   # 调度线程池大小

# ==================== Feign客户端配置 ====================
feign:
  client:
    config:
      default:  # 默认配置，适用于所有Feign客户端
        connect-timeout: 30000   # 连接超时时间（30秒）
        read-timeout: 3600000    # 读取超时时间（1小时）
        logger-level: basic      # 日志级别：NONE、BASIC、HEADERS、FULL
      # 语音服务专用配置 - 音频处理需要更长时间
      wenshu-voice:
        connect-timeout: 30000   # 语音服务连接超时（30秒）
        read-timeout: 3600000    # 语音服务读取超时（1小时）- 支持大型音频文件处理
        logger-level: full       # 详细日志，便于调试音频处理问题
  # 请求和响应压缩配置
  compression:
    request:
      enabled: true   # 启用请求压缩，减少网络传输数据量
    response:
      enabled: true   # 启用响应压缩，提高传输效率

# ==================== Hystrix熔断器配置 ====================
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 3600000  # 默认超时时间（1小时）
    # 语音服务专用配置
    VoiceClient#handleAudioUpload(MultipartFile):
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 3600000  # 语音识别超时时间（1小时）

# ==================== Ribbon负载均衡配置 ====================
ribbon:
  # 全局默认配置
  ConnectTimeout: 30000          # 连接超时时间（30秒）
  ReadTimeout: 3600000           # 读取超时时间（1小时）
  MaxAutoRetries: 0              # 同一服务器重试次数
  MaxAutoRetriesNextServer: 1    # 切换服务器重试次数
  OkToRetryOnAllOperations: false # 是否对所有操作重试

# 针对特定服务的Ribbon配置
wenshu-voice:
  ribbon:
    ConnectTimeout: 30000        # 语音服务连接超时（30秒）
    ReadTimeout: 3600000         # 语音服务读取超时（1小时）
    MaxAutoRetries: 0            # 不重试，避免重复处理音频
    MaxAutoRetriesNextServer: 0  # 不切换服务器重试

# ==================== 日志配置 ====================
logging:
  level:
    # 设置Spring Cloud OpenFeign的日志级别为DEBUG，便于调试
    org.springframework.cloud.openfeign: DEBUG
    # 设置Spring Cloud LoadBalancer的日志级别为DEBUG，便于调试
    org.springframework.cloud.loadbalancer: DEBUG