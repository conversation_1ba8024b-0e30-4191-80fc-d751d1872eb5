package com.ruoyi.wenshumeeting.controller;

import com.ruoyi.wenshuapi.client.file.FileInfoClient;

import com.ruoyi.wenshuapi.util.file.ApiResponse;
import com.ruoyi.wenshumeeting.pojo.VideoAnalysis;
import com.ruoyi.wenshumeeting.service.VideoAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 视频分析任务控制器
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@RequestMapping("/wenshu/meeting")
public class VideoAnalysisController {
    
    @Autowired
    private VideoAnalysisService videoAnalysisService;
    @Autowired
    private FileInfoClient fileInfoClient;

    /**
     * 健康检查端点
     * 用于验证视频分析服务是否正常运行
     *
     * @return 服务状态信息
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        return ResponseEntity.ok(Map.of(
            "status", "UP",
            "service", "video-analysis",
            "timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
            "message", "视频分析服务运行正常"
        ));
    }
    /**
     * 创建视频分析任务（带文件上传）
     * 必须上传文件，上传成功后自动创建分析任务
     *
     * @param file 上传的视频文件
     * @param userId 用户ID（必填）
     * @param initialText 初始文本（可选）
     * @param ownerType 所有者类型（默认为个人）
     * @param fileStatus 文件状态（默认为可读写）
     * @return 创建结果
     */
    @PostMapping(value = "/video-analysis", consumes = "multipart/form-data")
    public ResponseEntity<Map<String, Object>> createTaskWithFileUpload(
            @RequestPart(value = "file") MultipartFile file,
            @RequestParam(value = "userId") Integer userId,
            @RequestParam(value = "initialText", required = false) String initialText,
            @RequestParam(value = "ownerType", defaultValue = "个人") String ownerType,
            @RequestParam(value = "fileStatus", defaultValue = "可读写") String fileStatus) {
        try {
            // 1. 验证文件
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("error", "文件不能为空"));
            }

            // 2. 上传文件
            ApiResponse<Map<String, Object>> uploadResponse = fileInfoClient.uploadFile(file, userId, ownerType, fileStatus);

            if (uploadResponse == null || uploadResponse.getCode() != 200) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "文件上传失败", "details", uploadResponse != null ? uploadResponse.getMessage() : "未知错误"));
            }

            // 3. 获取上传后的文件ID
            Map<String, Object> uploadData = uploadResponse.getData();

            String fileId = uploadData.get("fileId").toString();

            // 4. 创建视频分析任务
            VideoAnalysis createdTask = videoAnalysisService.createTask(userId, fileId, initialText);

            // 5. 返回结果
            Map<String, Object> result = Map.of(
                "task", createdTask,
                "fileInfo", uploadData,
                "message", "视频分析任务创建成功"
            );

            return ResponseEntity.status(HttpStatus.CREATED).body(result);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "创建任务失败", "details", e.getMessage()));
        }
    }


    /**
     * 根据ID查询视频分析任务
     * 需要验证用户权限
     *
     * @param id 任务ID
     * @param userId 用户ID（必填）
     * @return 查询结果
     */
    @GetMapping("/video-analysis/{id}")
    public ResponseEntity<Map<String, Object>> getTaskById(
            @PathVariable(value = "id") Long id,
            @RequestParam(value = "userId") Integer userId) {
        try {
            Optional<VideoAnalysis> taskOpt = videoAnalysisService.findById(id);
            if (taskOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            VideoAnalysis task = taskOpt.get();
            // 验证用户权限：只能查看自己的任务
            if (!task.getUserId().equals(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", "无权限访问该任务"));
            }

            return ResponseEntity.ok(Map.of("task", task));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "查询失败", "details", e.getMessage()));
        }
    }

    /**
     * 查询用户的所有视频分析任务
     *
     * @param userId 用户ID（必填）
     * @return 查询结果
     */
    @GetMapping("/video-analysis")
    public ResponseEntity<Map<String, Object>> getAllTasksByUser(
            @RequestParam(value = "userId") Integer userId) {
        try {
            List<VideoAnalysis> tasks = videoAnalysisService.findByUserId(userId);
            return ResponseEntity.ok(Map.of("tasks", tasks, "count", tasks.size()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "查询失败", "details", e.getMessage()));
        }
    }
    
    /**
     * 根据文件ID查询视频分析任务
     * 需要验证用户权限
     *
     * @param fileId 文件ID
     * @param userId 用户ID（必填）
     * @return 查询结果
     */
    @GetMapping("/video-analysis/file/{fileId}")
    public ResponseEntity<Map<String, Object>> getTaskByFileId(
            @PathVariable(value = "fileId") String fileId,
            @RequestParam(value = "userId") Integer userId) {
        try {
            Optional<VideoAnalysis> taskOpt = videoAnalysisService.findByFileId(fileId);
            if (taskOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            VideoAnalysis task = taskOpt.get();
            // 验证用户权限：只能查看自己的任务
            if (!task.getUserId().equals(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", "无权限访问该任务"));
            }

            return ResponseEntity.ok(Map.of("task", task));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "查询失败", "details", e.getMessage()));
        }
    }

    /**
     * 根据状态查询用户的视频分析任务列表
     *
     * @param status 任务状态
     * @param userId 用户ID（必填）
     * @return 查询结果
     */
    @GetMapping("/video-analysis/status/{status}")
    public ResponseEntity<Map<String, Object>> getTasksByStatus(
            @PathVariable(value = "status") Integer status,
            @RequestParam(value = "userId") Integer userId) {
        try {
            List<VideoAnalysis> tasks = videoAnalysisService.findByUserIdAndStatus(userId, status);
            return ResponseEntity.ok(Map.of("tasks", tasks, "count", tasks.size(), "status", status));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "查询失败", "details", e.getMessage()));
        }
    }
    
    /**
     * 根据时间范围查询用户的视频分析任务列表
     *
     * @param userId 用户ID（必填）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 查询结果
     */
    @GetMapping("/video-analysis/time-range")
    public ResponseEntity<Map<String, Object>> getTasksByTimeRange(
            @RequestParam(value = "userId") Integer userId,
            @RequestParam(value = "startTime") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(value = "endTime") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            List<VideoAnalysis> tasks = videoAnalysisService.findByUserIdAndUploadTimeBetween(userId, startTime, endTime);
            return ResponseEntity.ok(Map.of(
                "tasks", tasks,
                "count", tasks.size(),
                "timeRange", Map.of("startTime", startTime, "endTime", endTime)
            ));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "查询失败", "details", e.getMessage()));
        }
    }
    
    /**
     * 更新视频分析任务
     * 需要验证用户权限
     *
     * @param id 任务ID
     * @param userId 用户ID（必填）
     * @param videoAnalysis 更新的任务对象
     * @return 更新结果
     */
    @PutMapping("/video-analysis/{id}")
    public ResponseEntity<Map<String, Object>> updateTask(
            @PathVariable(value = "id") Long id,
            @RequestParam(value = "userId") Integer userId,
            @RequestBody VideoAnalysis videoAnalysis) {
        try {
            Optional<VideoAnalysis> existingTaskOpt = videoAnalysisService.findById(id);
            if (existingTaskOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            VideoAnalysis existingTask = existingTaskOpt.get();
            // 验证用户权限：只能更新自己的任务
            if (!existingTask.getUserId().equals(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", "无权限更新该任务"));
            }

            videoAnalysis.setId(id);
            videoAnalysis.setUserId(userId); // 确保用户ID不被篡改
            VideoAnalysis updatedTask = videoAnalysisService.update(videoAnalysis);
            return ResponseEntity.ok(Map.of("task", updatedTask, "message", "任务更新成功"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "更新失败", "details", e.getMessage()));
        }
    }

    /**
     * 更新任务状态
     * 需要验证用户权限
     *
     * @param id 任务ID
     * @param userId 用户ID（必填）
     * @param status 新状态
     * @return 更新结果
     */
    @PutMapping("/video-analysis/{id}/status")
    public ResponseEntity<Map<String, Object>> updateTaskStatus(
            @PathVariable(value = "id") Long id,
            @RequestParam(value = "userId") Integer userId,
            @RequestParam(value = "status") Integer status) {
        try {
            Optional<VideoAnalysis> taskOpt = videoAnalysisService.findById(id);
            if (taskOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            VideoAnalysis task = taskOpt.get();
            // 验证用户权限：只能更新自己的任务
            if (!task.getUserId().equals(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", "无权限更新该任务"));
            }

            boolean updated = videoAnalysisService.updateStatus(id, status);
            if (updated) {
                return ResponseEntity.ok(Map.of("message", "状态更新成功", "newStatus", status));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "状态更新失败"));
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "更新失败", "details", e.getMessage()));
        }
    }

    /**
     * 开始任务处理
     * 需要验证用户权限
     *
     * @param id 任务ID
     * @param userId 用户ID（必填）
     * @return 处理结果
     */
    @PutMapping("/video-analysis/{id}/start")
    public ResponseEntity<Map<String, Object>> startTask(
            @PathVariable(value = "id") Long id,
            @RequestParam(value = "userId") Integer userId) {
        try {
            Optional<VideoAnalysis> taskOpt = videoAnalysisService.findById(id);
            if (taskOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            VideoAnalysis task = taskOpt.get();
            // 验证用户权限：只能操作自己的任务
            if (!task.getUserId().equals(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", "无权限操作该任务"));
            }

            boolean started = videoAnalysisService.startTask(id);
            if (started) {
                return ResponseEntity.ok(Map.of("message", "任务已开始处理"));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "任务启动失败"));
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "操作失败", "details", e.getMessage()));
        }
    }

    /**
     * 完成任务
     * 需要验证用户权限
     *
     * @param id 任务ID
     * @param userId 用户ID（必填）
     * @param analysisText 分析文本
     * @return 处理结果
     */
    @PutMapping("/video-analysis/{id}/complete")
    public ResponseEntity<Map<String, Object>> completeTask(
            @PathVariable(value = "id") Long id,
            @RequestParam(value = "userId") Integer userId,
            @RequestBody String analysisText) {
        try {
            Optional<VideoAnalysis> taskOpt = videoAnalysisService.findById(id);
            if (taskOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            VideoAnalysis task = taskOpt.get();
            // 验证用户权限：只能操作自己的任务
            if (!task.getUserId().equals(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", "无权限操作该任务"));
            }

            boolean completed = videoAnalysisService.completeTask(id, analysisText);
            if (completed) {
                return ResponseEntity.ok(Map.of("message", "任务已完成"));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "任务完成失败"));
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "操作失败", "details", e.getMessage()));
        }
    }

    /**
     * 任务失败
     * 需要验证用户权限
     *
     * @param id 任务ID
     * @param userId 用户ID（必填）
     * @param failureReason 失败原因
     * @return 处理结果
     */
    @PutMapping("/video-analysis/{id}/fail")
    public ResponseEntity<Map<String, Object>> failTask(
            @PathVariable(value = "id") Long id,
            @RequestParam(value = "userId") Integer userId,
            @RequestBody String failureReason) {
        try {
            Optional<VideoAnalysis> taskOpt = videoAnalysisService.findById(id);
            if (taskOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            VideoAnalysis task = taskOpt.get();
            // 验证用户权限：只能操作自己的任务
            if (!task.getUserId().equals(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", "无权限操作该任务"));
            }

            boolean failed = videoAnalysisService.failTask(id, failureReason);
            if (failed) {
                return ResponseEntity.ok(Map.of("message", "任务已标记为失败"));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "任务失败标记失败"));
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "操作失败", "details", e.getMessage()));
        }
    }
    
    /**
     * 根据ID删除视频分析任务
     * 需要验证用户权限
     *
     * @param id 任务ID
     * @param userId 用户ID（必填）
     * @return 删除结果
     */
    @DeleteMapping("/video-analysis/{id}")
    public ResponseEntity<Map<String, Object>> deleteTask(
            @PathVariable(value = "id") Long id,
            @RequestParam(value = "userId") Integer userId) {
        try {
            Optional<VideoAnalysis> taskOpt = videoAnalysisService.findById(id);
            if (taskOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            VideoAnalysis task = taskOpt.get();
            // 验证用户权限：只能删除自己的任务
            if (!task.getUserId().equals(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", "无权限删除该任务"));
            }

            boolean deleted = videoAnalysisService.deleteById(id);
            if (deleted) {
                return ResponseEntity.ok(Map.of("message", "任务删除成功"));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "任务删除失败"));
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "删除失败", "details", e.getMessage()));
        }
    }

    /**
     * 删除用户的所有视频分析任务
     * 只能删除自己的任务
     *
     * @param userId 用户ID（必填）
     * @return 删除结果
     */
    @DeleteMapping("/video-analysis/user")
    public ResponseEntity<Map<String, Object>> deleteAllTasksByUser(
            @RequestParam(value = "userId") Integer userId) {
        try {
            int deletedCount = videoAnalysisService.deleteByUserId(userId);
            return ResponseEntity.ok(Map.of(
                "message", "用户任务删除成功",
                "deletedCount", deletedCount
            ));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "删除失败", "details", e.getMessage()));
        }
    }

    /**
     * 根据文件ID删除视频分析任务
     * 需要验证用户权限
     *
     * @param fileId 文件ID
     * @param userId 用户ID（必填）
     * @return 删除结果
     */
    @DeleteMapping("/video-analysis/file/{fileId}")
    public ResponseEntity<Map<String, Object>> deleteTaskByFileId(
            @PathVariable(value = "fileId") String fileId,
            @RequestParam(value = "userId") Integer userId) {
        try {
            Optional<VideoAnalysis> taskOpt = videoAnalysisService.findByFileId(fileId);
            if (taskOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            VideoAnalysis task = taskOpt.get();
            // 验证用户权限：只能删除自己的任务
            if (!task.getUserId().equals(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", "无权限删除该任务"));
            }

            int deletedCount = videoAnalysisService.deleteByFileId(fileId);
            if (deletedCount > 0) {
                return ResponseEntity.ok(Map.of(
                    "message", "任务删除成功",
                    "deletedCount", deletedCount
                ));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "任务删除失败"));
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "删除失败", "details", e.getMessage()));
        }
    }

    /**
     * 统计用户的任务数量
     *
     * @param userId 用户ID（必填）
     * @return 统计结果
     */
    @GetMapping("/video-analysis/count")
    public ResponseEntity<Map<String, Object>> countTasksByUser(
            @RequestParam(value = "userId") Integer userId) {
        try {
            long count = videoAnalysisService.countByUserId(userId);
            return ResponseEntity.ok(Map.of("userId", userId, "totalCount", count));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "统计失败", "details", e.getMessage()));
        }
    }

    /**
     * 统计用户指定状态的任务数量
     *
     * @param userId 用户ID（必填）
     * @param status 任务状态
     * @return 统计结果
     */
    @GetMapping("/video-analysis/count/status/{status}")
    public ResponseEntity<Map<String, Object>> countTasksByUserAndStatus(
            @PathVariable(value = "status") Integer status,
            @RequestParam(value = "userId") Integer userId) {
        try {
            long count = videoAnalysisService.countByUserIdAndStatus(userId, status);
            return ResponseEntity.ok(Map.of(
                "userId", userId,
                "status", status,
                "count", count
            ));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "统计失败", "details", e.getMessage()));
        }
    }

    /**
     * 检查任务是否存在且用户有权限访问
     *
     * @param id 任务ID
     * @param userId 用户ID（必填）
     * @return 检查结果
     */
    @GetMapping("/video-analysis/{id}/exists")
    public ResponseEntity<Map<String, Object>> checkTaskExists(
            @PathVariable(value = "id") Long id,
            @RequestParam(value = "userId") Integer userId) {
        try {
            Optional<VideoAnalysis> taskOpt = videoAnalysisService.findById(id);
            boolean exists = taskOpt.isPresent();
            boolean hasPermission = exists && taskOpt.get().getUserId().equals(userId);

            return ResponseEntity.ok(Map.of(
                "exists", exists,
                "hasPermission", hasPermission,
                "accessible", exists && hasPermission
            ));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "检查失败", "details", e.getMessage()));
        }
    }

    /**
     * 检查文件ID对应的任务是否存在且用户有权限访问
     *
     * @param fileId 文件ID
     * @param userId 用户ID（必填）
     * @return 检查结果
     */
    @GetMapping("/video-analysis/file/{fileId}/exists")
    public ResponseEntity<Map<String, Object>> checkTaskExistsByFileId(
            @PathVariable(value = "fileId") String fileId,
            @RequestParam(value = "userId") Integer userId) {
        try {
            Optional<VideoAnalysis> taskOpt = videoAnalysisService.findByFileId(fileId);
            boolean exists = taskOpt.isPresent();
            boolean hasPermission = exists && taskOpt.get().getUserId().equals(userId);

            return ResponseEntity.ok(Map.of(
                "exists", exists,
                "hasPermission", hasPermission,
                "accessible", exists && hasPermission,
                "fileId", fileId
            ));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "检查失败", "details", e.getMessage()));
        }
    }

    /**
     * 处理文件上传大小超限异常
     *
     * @param ex 文件上传大小超限异常
     * @return 错误响应
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<Map<String, Object>> handleMaxUploadSizeExceeded(MaxUploadSizeExceededException ex) {
        return ResponseEntity.status(HttpStatus.PAYLOAD_TOO_LARGE)
            .body(Map.of(
                "error", "文件上传大小超限",
                "message", "上传的文件大小超过了系统限制（最大500MB）",
                "details", ex.getMessage(),
                "maxSize", "500MB"
            ));
    }

    /**
     * 处理通用异常
     *
     * @param ex 异常
     * @return 错误响应
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleGenericException(Exception ex) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(Map.of(
                "error", "服务器内部错误",
                "message", "处理请求时发生错误，请稍后重试",
                "details", ex.getMessage()
            ));
    }
}
