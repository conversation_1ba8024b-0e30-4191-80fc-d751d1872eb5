package com.ruoyi.wenshuchat.controller;

import com.ruoyi.wenshuapi.client.programme.WenshuCalebdarFeignClientInter;
import com.ruoyi.wenshuapi.pojo.vo.ProgrammeVO;
import com.ruoyi.wenshucommon.advisor.ChatMessageAdvisor;
import com.ruoyi.wenshuapi.pojo.programme.Programme;
import com.ruoyi.wenshuapi.common.ApiResponse;
import com.ruoyi.wenshuapi.pojo.chat.RequestBodyDTO;
import com.ruoyi.wenshuchat.advisor.DbChatMemory;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.QuestionAnswerAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.document.Document;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.vectorstore.SimpleVectorStore;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.milvus.MilvusVectorStore;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/chat")
@RequiredArgsConstructor
@Slf4j
public class ChatAPI {
    private final MilvusVectorStore vectorStore;
    private final ChatClient chatClient;
    private String systemPropt="";
    private final ChatMemory chatMemory;
    private final WenshuCalebdarFeignClientInter calebdarFeignClientInter;
    static final  String rchat= """
            你是一个意图识别专家，可以通过用户输入的文本内容识别用户意图，
            并返回，普通对话，日程操作，语音聊天,解析文档等等意图
            """;
    static final String rProgramme= """
            你是一个日程操作专家，可以通过用户输入的文本内容识别用户意图，
            并返回，用户需要对日程进行什么操作，请给我返回，查询、修改、删除、添加
            """;
    static final String law= """
请帮助我解析以下合同或法律文件，提取关键信息并进行法律风险评估。要求如下：

1. 提取以下核心信息：
   - 合同基本信息（编号、签订日期、类型）
   - 合同双方信息（甲方/乙方名称、地址、联系方式）
   - 核心条款（合作内容、金额支付、履约要求）
   - 法律条款（违约责任、争议解决）
   - 关键时间节点（生效、终止、付款时间）
   - 签署信息

2. 法律风险评估：
   - 识别潜在法律风险点（用★标注风险等级：★低风险 ★★中风险 ★★★高风险）
   - 分析条款是否存在歧义、缺失或矛盾
   - 检测权利义务是否对等
   - 验证关键条款是否符合相关法律法规

3. 修改建议：
   - 针对风险点提出具体修改意见
   - 建议补充的必要条款
   - 优化表述模糊的条款

输出格式要求：
{
  "status": "success" | "error",
  "content": {
    "basic_info": { ... },
    "parties": { ... },
    "clauses": { ... },
    "key_dates": { ... },
    "signatures": { ... },
    "legal_analysis": {
      "risk_assessment": [
        {
          "risk_point": "风险描述",
          "risk_level": "★",
          "clause_reference": "关联条款",
          "suggestion": "修改建议"
        }
      ],
      "missing_clauses": ["应补充条款1", "应补充条款2"],
      "ambiguous_terms": [
        {
          "term": "模糊术语",
          "context": "出现位置",
          "interpretation": "解释说明"
        }
      ]
    }
  }
}
""";
    /**
     * 添加日程的提示词
     */
    static final String systemPropt1= "你会基于我以下的文本进行判断，请对错别字以及混乱字义进行联想" +
            "\n输出示例：" +
            "{\n" +
            "  \"title\": \"日程标题\",\n" +
            "  \"description\": \"描述信息，同时用于提取时间信息，如\"下午5点开会，地点在明行楼\"\",\n" +
            "  \"start_time\": \"日程开始时间（如果设置了此字段，则优先使用）\",\n" +
            "  \"owner_type\": \"所有者类型（个人、团队）\",\n" +
            "  \"repeat_rule\": \"重复规则（每天重复、一次性）\",\n" +
            "  \"remind_time\": \"提醒时间（默认提前十分钟，可修改）\",\n" +
            "  \"event_status\": \"日程状态(0-取消,1-正常)\"\n" +
            "}" +
            "\n输出要求：" +
            "\n1.基于给定识别文本内容对给定示例进行日程内容填充" +
            "\n2.未明确的内容可虚构，但要符合客观规律"+
            "\n3.除了给定示例内容，不允许输出多余内容" +
            "\n4.输出内容符合给定的json格式，不允许出现多余内容" +
            "\n5.去除特殊符合，只能使用 {} [] , : 等符合json格式的符号" +
            "\n6.不使用java的转义符号，去除示例中本身带有的转义符号" +
            "\n以下是我的识别内容：\n";
    /**
     * 查询日程的提示词
     */
    public static String agentLookSchedulePrompt="当前时间："+TimeNow()+"\n你会基于我以下的文本进行回复," +
            "以下文本为语音识别内容，请对错别字以及混乱字义进行联想" +
            "输出示例：" +
            "{" +
            "\"startTime\":\"开始时间戳\"," +
            "\"endTime\":\"结束时间戳\"" +
            "}\n" +
            "输出要求：" +
            "1.查询开始时间戳  格式为：yyyy-MM-dd HH:00:00" +
            "2.查询结束时间戳  格式为：yyyy-MM-dd HH:00:00" +
            "\n3.除了给定示例内容，不允许输出多余内容" +
            "\n4.输出内容符合给定的json格式，不允许出现多余内容" +
            "\n5.去除特殊符合，只能使用 {} [] , : 等符合json格式的符号" +
            "\n6.不使用java的转义符号，去除示例中本身带有的转义符号" +
            "\n7.不可以带多余符号和字符" +
            "\n8.不需要解释说明，只输出示例" +
            "\n以下是我的识别内容：\n";

    /**
     * 聊天的提示词
     */
    public static String agentChatPrompt="当前时间："+TimeNow()+"\n你会基于我以下的文本进行回复,满足基础聊天需求，请对错别字以及混乱字义进行联想" +
            "输出要求：" +
            "1.分析识别内容进行回复" +
            "2.以聊天的方式进行回复"+
            "3.去除与问题无关的内容";
    /**
     * 语音聊天的提示词
     */
    public static String agentyuyingPrompt="你是一个语音聊天助手，你可以根据我的语音文字进行回答" +
            "，语音文字可能会有错别字，请对错别字以及混乱字义进行联想";

    /**
     * 识别自然语言中的日程信息提示词
     */
    static final String programmeIdentifyPrompt = """
            你是一个专业的日程识别助手。请分析用户输入的自然语言，提取与日程相关的所有信息，并按照Programme类的字段格式返回结果。
            
            请根据以下字段提取信息:
            - 日程标题(title): 提取事件的主要名称或主题
            - 日程描述(description): 提取事件的详细说明，包括地点、参与人等信息
            - 开始时间(startTime): 提取事件的开始时间，格式为yyyy-MM-dd HH:mm:ss
            - 创建者ID(creatorId): 若有提及创建者ID则提取，否则默认为当前用户ID
            - 所有者类型(ownerType): 判断是"个人"还是"团队"日程
            - 重复规则(repeatRule): 判断是"每天重复"还是"一次性"事件
            - 提醒时间(remindTime): 提取应在何时提醒，默认为开始时间前10分钟
            - 日程状态(eventStatus): 判断是正常(1)还是取消(0)状态

            输出格式必须严格为JSON，不要包含任何解释或前缀：
            {
              "title": "提取的标题",
              "description": "提取的描述",
              "startTime": "提取的开始时间",
              "creatorId": "提取的创建者ID或默认值",
              "ownerType": "个人或团队",
              "repeatRule": "每天重复或一次性",
              "remindTime": "提取的提醒时间或默认值",
              "eventStatus": "0或1"
            }
            
            重要：仅返回JSON格式，不要返回任何解释、前言或其他文本内容。你的回复必须能够被直接解析为JSON对象。
            如果无法提取某个字段的信息，请使用合理的默认值。
            
            请分析以下用户输入：
            """;
    //注释
    /**
     * 修改日程的提示词
     */
    static final String programmeEditPrompt = """
            你是一个专业的日程编辑助手。请分析用户输入的自然语言，提取用户想要修改的日程信息，并标识出要修改的字段和新值。
            
            请从以下方面提取信息:
            - 日程标题(title): 用户想修改成什么新标题
            - 日程描述(description): 用户想修改成什么新描述
            - 开始时间(startTime): 用户想修改成什么新开始时间，格式为yyyy-MM-dd HH:mm:ss
            - 所有者类型(ownerType): 用户想修改成什么新所有者类型("个人"或"团队")
            - 重复规则(repeatRule): 用户想修改成什么新重复规则("每天重复"或"一次性")
            - 提醒时间(remindTime): 用户想修改成什么新提醒时间
            - 日程状态(eventStatus): 用户想修改成什么新状态(0-取消或1-正常)
            
            同时，请提取用户可能提供的原始日程信息来帮助识别要修改的日程:
            - 原日程标题
            - 原日程时间
            - 原日程描述
            
            输出格式应为JSON:
            {
              "eventIdentifier": "识别日程的关键信息，如标题或时间",
              "fieldsToUpdate": {
                "field1": "新值",
                "field2": "新值"
              }
            }
            
            请分析以下用户输入：
            """;

    /**
     * 删除日程的提示词
     */
    static final String programmeDeletePrompt = """
            你是一个专业的日程管理助手。请分析用户输入的自然语言，提取用户想要删除的日程信息。
            
            请提取能够唯一标识日程的信息:
            - 日程标题
            - 日程时间
            - 其他能够唯一识别日程的描述信息
            
            输出格式应为JSON:
            {
              "eventIdentifier": {
                "title": "日程标题",
                "time": "日程时间",
                "description": "日程描述"
              }
            }
            
            请分析以下用户输入：
            """;


    @PostMapping(value = "/send")
    public ResponseEntity<String> sendMessage(@RequestBody RequestBodyDTO requestBodyDTO) {
        try {
            // 设置当前用户ID到DbChatMemory
            Long userId = Long.parseLong(requestBodyDTO.getUserId());
            DbChatMemory.setCurrentUserId(userId.intValue());
            
            // 处理用户消息，判断意图并执行相应操作
            String operationResult = processUserMessage(requestBodyDTO.getMessage(), userId);

            // 添加对话ID处理逻辑
            ChatClient.ChatClientRequestSpec promptSpec = chatClient.prompt()
                    .user(requestBodyDTO.getMessage())
                    .system(systemPropt)
                    .advisors(new ChatMessageAdvisor(chatMemory, requestBodyDTO.getConversationId()));
            if(isRelevantToKnowledgeBase(requestBodyDTO.getMessage())){
                promptSpec=promptSpec.advisors(new QuestionAnswerAdvisor(vectorStore));
            }

            String response =  promptSpec.call().content();
            // 仅在相关时添加知识库查询
            if (isRelevantToKnowledgeBase(requestBodyDTO.getMessage())) {
                promptSpec = promptSpec.advisors(new QuestionAnswerAdvisor(vectorStore));
            }

            // 如果有操作结果，将其添加到回复中
            if (operationResult != null && !operationResult.isEmpty()) {
                response = operationResult + "\n\n" + response;
            }
            
            // 清除当前用户ID
            DbChatMemory.clearCurrentUserId();

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            // 确保异常情况下也清除用户ID
            DbChatMemory.clearCurrentUserId();
            return ResponseEntity.internalServerError()
                    .body("处理消息时发生错误: " + e.getMessage());
        }
    }

    @PostMapping("/get-chat-response")
    public ResponseEntity<String> getChatResponse(@RequestBody RequestBodyDTO requestBodyDTO) {
        try {
            // 设置当前用户ID到DbChatMemory
            Long userId = Long.parseLong(requestBodyDTO.getUserId());
            DbChatMemory.setCurrentUserId(userId.intValue());
            
            // 处理用户消息，判断意图并执行相应操作
            String operationResult = processUserMessage(requestBodyDTO.getMessage(), userId);
            // 添加对话ID处理逻辑
            ChatClient.ChatClientRequestSpec promptSpec = chatClient.prompt()
                    .user(requestBodyDTO.getMessage())
                    .system(systemPropt);
            if(isRelevantToKnowledgeBase(requestBodyDTO.getMessage())){
                promptSpec=promptSpec.advisors(new QuestionAnswerAdvisor(vectorStore));
            }

            String response =  promptSpec.call().content();
            // 仅在相关时添加知识库查询
            // 如果有操作结果，将其添加到回复中
            if (operationResult != null && !operationResult.isEmpty()) {
                response = operationResult + "\n\n" + response;
            }
            
            // 清除当前用户ID
            DbChatMemory.clearCurrentUserId();

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            // 确保异常情况下也清除用户ID
            DbChatMemory.clearCurrentUserId();
            return ResponseEntity.internalServerError()
                    .body("处理消息时发生错误: " + e.getMessage());
        }
    }
    
    @PostMapping("/result")
    public ApiResponse<ResponseEntity<String>> result(@RequestBody RequestBodyDTO requestBodyDTO) {
        return ApiResponse.success(sendMessage(requestBodyDTO));
    }

    /**
     * 供内部调用的ai接口
     * @param requestBodyDTO
     * @return
     */
    @PostMapping("/send-easy")
    public String sendMessageEasy(@RequestBody RequestBodyDTO requestBodyDTO) {
        // 处理用户消息，判断意图并执行相应操作
        String operationResult = processUserMessage(requestBodyDTO.getMessage(), Long.parseLong(requestBodyDTO.getUserId()));
        return chatClient
                .prompt(requestBodyDTO.getMessage())
                .system(systemPropt)
                .call()
                .content();
    }

    @PostMapping("/docment-parse-send")
    public String sendDocmentParseSend(@RequestBody RequestBodyDTO requestBodyDTO) {
        // 处理用户消息，判断意图并执行相应操作
        systemPropt=law;
        return chatClient
                .prompt(requestBodyDTO.getMessage())
                .system(systemPropt)
                .call()
                .content();
    }
    @PostMapping("/docment-send")
    public String sendDocmentSend(@RequestBody RequestBodyDTO requestBodyDTO) {
        // 处理用户消息，判断意图并执行相应操作
        return chatClient
                .prompt(requestBodyDTO.getMessage())
                .call()
                .content();
    }

    @PostMapping("/send-easy-kowlage")
    public String sendMessageEasyMemory(@RequestBody RequestBodyDTO requestBodyDTO) {
        // 处理用户消息，判断意图并执行相应操作
        String operationResult = processUserMessage(requestBodyDTO.getMessage(), Long.parseLong(requestBodyDTO.getUserId()));
        ChatClient.ChatClientRequestSpec prompt = chatClient
                .prompt(requestBodyDTO.getMessage())
                .system(systemPropt);
        if(isRelevantToKnowledgeBase(requestBodyDTO.getMessage())){
            prompt=prompt.advisors(new QuestionAnswerAdvisor(vectorStore));
        }
        return prompt.call().content();
    }


    /**
     * 处理用户消息，识别意图并执行相应操作
     * @param message 用户消息
     * @param userId 用户ID
     * @return 操作结果的描述，如果没有具体操作则返回null
     */
    private String processUserMessage(String message, Long userId) {
        // 调用意图识别
        String userIntent = identifyUserIntent(message);

        // 根据不同意图执行不同操作
        if (userIntent.contains("日程操作")) {
            String operation = identifyOperation(message);

            switch (operation) {
                case "查询":
                    return handleQueryOperation(message, userId);
                case "添加":
                    return handleAddOperation(message, userId);
                case "修改":
                    return handleEditOperation(message, userId);
                case "删除":

                default:
                    systemPropt = agentChatPrompt;
                    return "无法识别具体的日程操作意图，请尝试更明确的表达。";
            }
        } else if (userIntent.contains("语音聊天")) {
            systemPropt = agentyuyingPrompt;
            return null; // 语音聊天不需要返回额外结果
        }else if(userIntent.contains("解析文档")){
            systemPropt=law;
            return null;
        }else {
            systemPropt = agentChatPrompt;
            return null; // 普通聊天不需要返回额外结果
        }
    }

    /**
     * 识别用户消息的总体意图
     */
    private String identifyUserIntent(String message) {
        return chatClient.prompt()
            .user(message)
            .system(rchat)
            .call()
            .content();
    }

    /**
     * 识别日程操作的具体类型
     */
    private String identifyOperation(String message) {
        return chatClient.prompt()
            .user(message)
            .system(rProgramme)
            .call()
            .content();
    }

    /**
     * 处理查询日程操作
     */
    private String handleQueryOperation(String message, Long userId) {
        try {
            systemPropt = agentLookSchedulePrompt;

            // 识别查询的时间范围
            String timeRangeJson = chatClient.prompt()
                .user(message)
                .system(agentLookSchedulePrompt)
                .call()
                .content();

            // 解析时间范围
            ObjectMapper mapper = new ObjectMapper();
            JsonNode jsonNode = mapper.readTree(timeRangeJson);

            String startTime = jsonNode.has("startTime") ? jsonNode.get("startTime").asText() : null;
            String endTime = jsonNode.has("endTime") ? jsonNode.get("endTime").asText() : null;

            // 如果无法识别时间范围，使用默认值（当天）
            if (startTime == null || endTime == null) {
                LocalDateTime now = LocalDateTime.now();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:00:00");
                startTime = now.withHour(0).withMinute(0).withSecond(0).format(formatter);
                endTime = now.withHour(23).withMinute(59).withSecond(59).format(formatter);
            }

            log.info("查询日程，用户ID: {}, 开始时间: {}, 结束时间: {}", userId, startTime, endTime);

            try {
                // 调用服务查询日程
                ApiResponse<?> response = calebdarFeignClientInter.listProgramme(userId, startTime, endTime);

                if (response == null || response.getData() == null) {
                    log.warn("查询日程返回为空，用户ID: {}", userId);
                    return "查询日程失败，请稍后重试。";
                }

                // 使用安全的方式处理响应数据
                List<ProgrammeVO> programmes = new ArrayList<>();
                if (response.getData() instanceof List<?>) {
                    List<?> dataList = (List<?>) response.getData();
                    for (Object item : dataList) {
                        if (item instanceof ProgrammeVO) {
                            programmes.add((ProgrammeVO) item);
                        } else if (item instanceof Map) {
                            // 处理返回的Map数据
                            try {
                                Map<?, ?> map = (Map<?, ?>) item;
                                ProgrammeVO vo = new ProgrammeVO();

                                if (map.containsKey("title")) {
                                    vo.setTitle(map.get("title").toString());
                                }

                                if (map.containsKey("description")) {
                                    vo.setDescription(map.get("description").toString());
                                }

                                if (map.containsKey("startTime")) {
                                    Object startTimeObj = map.get("startTime");
                                    if (startTimeObj instanceof String) {
                                        vo.setStartTime(LocalDateTime.parse((String) startTimeObj, DateTimeFormatter.ISO_DATE_TIME));
                                    } else if (startTimeObj != null) {
                                        vo.setStartTime(LocalDateTime.parse(startTimeObj.toString(), DateTimeFormatter.ISO_DATE_TIME));
                                    }
                                }

                                programmes.add(vo);
                            } catch (Exception e) {
                                log.error("解析日程数据失败", e);
                            }
                        }
                    }
                }

                // 格式化查询结果
                StringBuilder result = new StringBuilder("查询到以下日程：\n");

                if (programmes.isEmpty()) {
                    return "在指定时间范围内没有找到任何日程。";
                }

                for (ProgrammeVO programme : programmes) {
                    result.append("- ").append(programme.getTitle() != null ? programme.getTitle() : "未命名日程");

                    if (programme.getStartTime() != null) {
                        result.append("，时间：").append(programme.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
                    }

                    if (programme.getDescription() != null && !programme.getDescription().isEmpty()) {
                        result.append("，").append(programme.getDescription());
                    }

                    result.append("\n");
                }

                return result.toString();
            } catch (feign.FeignException e) {
                log.error("调用日程服务失败: {}", e.getMessage());
                return "查询日程服务暂时不可用，请稍后再试。错误详情: " + e.getMessage();
            }
        } catch (Exception e) {
            log.error("查询日程时出现错误", e);
            return "查询日程时出现错误：" + e.getMessage();
        }
    }
/**
 *判断是否使用知识库
 */
private boolean isRelevantToKnowledgeBase(String message) {
    // 1. 基础检查
    if (message == null || message.trim().isEmpty()) {
        return false;
    }

    // 2. 获取多个相关文档（前5个）
    List<Document> documents = vectorStore.similaritySearch(message);
    if (documents.isEmpty()) {
        return false;
    }

    // 3. 设置严格的距离阈值
    final double THRESHOLD = 0.35; // 更严格的阈值（0.3-0.4范围）

    // 4. 检查是否有文档超过阈值
    boolean hasRelevantDocument = false;
    double minDistance = Double.MAX_VALUE;
    double maxDistance = Double.MIN_VALUE;

    for (Document doc : documents) {
        if (doc.getMetadata().containsKey("distance")) {
            try {
                double distance = Double.parseDouble(doc.getMetadata().get("distance").toString());

                // 记录最小和最大距离用于日志
                minDistance = Math.min(minDistance, distance);
                maxDistance = Math.max(maxDistance, distance);

                // 距离小于阈值表示高相关性
                if (distance <= THRESHOLD) {
                    hasRelevantDocument = true;
                    log.debug("相关文档: 距离={}, 内容={}", distance, doc.getContent());
                }
            } catch (NumberFormatException e) {
                log.warn("无法解析距离值", e);
            }
        }
    }

    // 5. 记录距离分布
    log.debug("向量搜索: 查询='{}', 最小距离={}, 最大距离={}, 阈值={}",
            message, minDistance, maxDistance, THRESHOLD);

    // 6. 额外过滤条件
    if (hasRelevantDocument) {
        // 检查消息是否包含知识库相关关键词
        List<String> knowledgeKeywords = List.of("法律", "法规", "条款", "案例", "规定", "法条", "条例");
        for (String keyword : knowledgeKeywords) {
            if (message.contains(keyword)) {
                return true;
            }
        }

        // 检查用户是否明确要求知识库
        if (message.contains("根据知识库") || message.contains("参考文档")) {
            return true;
        }
    }

    return false;
}

    /**
     * 处理添加日程操作
     */
    private String handleAddOperation(String message, Long userId) {
        try {
            systemPropt = systemPropt1;
            log.info("开始处理添加日程操作，用户ID: {}", userId);

            // 从用户消息中提取日程信息
            Programme programme = extractProgrammeFromText(message, userId.intValue());
            log.info("提取到的日程信息: {}", programme);

            // 设置其他必要属性
            programme.setCreatorId(userId.intValue());
            programme.setCreateTime(LocalDateTime.now());
            
            // 确保设置eventId为0，这样会被当作新记录
            programme.setEventId(0);
            log.info("准备添加日程，设置后的Programme对象: {}", programme);

            // 调用服务添加日程
            log.info("调用Feign客户端添加日程");
            ApiResponse<?> response = calebdarFeignClientInter.addProgramme(userId, programme);
            log.info("添加日程响应: {}", response);

            boolean success = response != null && response.getCode() == 200;

            if (success) {
                log.info("成功添加日程: {}", programme.getTitle());
                return "已成功添加日程：" + programme.getTitle() + "\n时间：" +
                       programme.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } else {
                log.error("添加日程失败，响应: {}", response);
                return "添加日程失败，请稍后重试。";
            }
        } catch (Exception e) {
            log.error("添加日程时出现异常", e);
            e.printStackTrace();
            return "添加日程时出现错误：" + e.getMessage();
        }
    }

    /**
     * 处理修改日程操作
     */
    private String handleEditOperation(String message, Long userId) {
        try {
            // 使用专门的提示词识别修改信息
            String editInfoJson = chatClient.prompt()
                .user(message)
                .system(programmeEditPrompt)
                .call()
                .content();

            // 解析修改信息
            ObjectMapper mapper = new ObjectMapper();
            JsonNode editInfo = mapper.readTree(editInfoJson);

            String eventIdentifier = editInfo.has("eventIdentifier") ? editInfo.get("eventIdentifier").asText() : null;
            JsonNode fieldsToUpdate = editInfo.has("fieldsToUpdate") ? editInfo.get("fieldsToUpdate") : null;

            if (eventIdentifier == null || fieldsToUpdate == null) {
                return "无法识别要修改的日程或修改内容，请提供更明确的信息。";
            }

            // 先查询所有日程
            ApiResponse<?> allProgrammes = calebdarFeignClientInter.getAll(userId);

            if (allProgrammes == null || allProgrammes.getData() == null) {
                return "获取日程列表失败，请稍后重试。";
            }

            // 在所有日程中查找匹配的日程
            ProgrammeVO matchedVO = null;
            if (allProgrammes.getData() instanceof List<?>) {
                List<?> dataList = (List<?>) allProgrammes.getData();
                for (Object item : dataList) {
                    if (item instanceof ProgrammeVO) {
                        ProgrammeVO programmeVO = (ProgrammeVO) item;
                        if (programmeVO.getTitle() != null && programmeVO.getTitle().contains(eventIdentifier)) {
                            matchedVO = programmeVO;
                            break;
                        } else if (programmeVO.getDescription() != null && programmeVO.getDescription().contains(eventIdentifier)) {
                            matchedVO = programmeVO;
                            break;
                        }
                    }
                }
            }

            if (matchedVO == null) {
                return "未找到符合条件的日程，请确认日程信息是否正确。";
            }

            // 根据匹配的日程信息查询日程ID
            int eventId = -1;

            // 通过日程的其他信息（如标题、时间）查询具体日程ID
            ApiResponse<?> allEvents = calebdarFeignClientInter.getAll(userId);
            if (allEvents != null && allEvents.getData() != null && allEvents.getData() instanceof List<?>) {
                List<?> events = (List<?>) allEvents.getData();
                for (int i = 0; i < events.size(); i++) {
                    Object event = events.get(i);
                    if (event instanceof Map) {
                        Map<?, ?> eventMap = (Map<?, ?>) event;

                        // 检查这个日程的标题和描述是否匹配
                        Object titleObj = eventMap.get("title");
                        Object descObj = eventMap.get("description");

                        boolean titleMatches = titleObj != null &&
                            titleObj.toString().equals(matchedVO.getTitle());
                        boolean descMatches = descObj != null &&
                            matchedVO.getDescription() != null &&
                            descObj.toString().equals(matchedVO.getDescription());

                        if (titleMatches || descMatches) {
                            // 找到匹配的日程，获取ID
                            Object idObj = eventMap.get("eventId");
                            if (idObj != null) {
                                if (idObj instanceof Integer) {
                                    eventId = (Integer) idObj;
                                    break;
                                } else if (idObj instanceof Number) {
                                    eventId = ((Number) idObj).intValue();
                                    break;
                                } else {
                                    try {
                                        eventId = Integer.parseInt(idObj.toString());
                                        break;
                                    } catch (NumberFormatException e) {
                                        // 忽略解析错误，继续查找
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 如果还是找不到ID，尝试通过getById查询所有可能的日程
            if (eventId == -1) {
                // 查询最近添加的几个日程，从最新的开始检查
                for (int i = 1; i <= 10; i++) {
                    try {
                        ApiResponse<?> response = calebdarFeignClientInter.getById(i);
                        if (response != null && response.getCode() == 200 && response.getData() != null) {
                            Object data = response.getData();
                            // 检查返回的数据是否含有与我们匹配的日程信息
                            String dataStr = data.toString();
                            if (dataStr.contains(matchedVO.getTitle()) ||
                                (matchedVO.getDescription() != null && dataStr.contains(matchedVO.getDescription()))) {
                                eventId = i;
                                break;
                            }
                        }
                    } catch (Exception e) {
                        // 忽略单个查询错误，继续尝试
                    }
                }
            }

            // 如果仍然找不到ID
            if (eventId == -1) {
                return "无法获取日程ID，请提供更准确的日程信息。";
            }

            // 构建更新的Programme对象
            Programme updateProgramme = new Programme();
            updateProgramme.setEventId(eventId);
            updateProgramme.setTitle(matchedVO.getTitle());
            updateProgramme.setDescription(matchedVO.getDescription());
            updateProgramme.setStartTime(matchedVO.getStartTime());
            updateProgramme.setOwnerType(matchedVO.getOwnerType());
            updateProgramme.setRepeatRule(matchedVO.getRepeatRule());
            updateProgramme.setRemindTime(matchedVO.getRemindTime());
            updateProgramme.setEventStatus(matchedVO.getEventStatus());
            updateProgramme.setCreatorId(userId.intValue());
            updateProgramme.setCreateTime(LocalDateTime.now());

            // 更新字段
            if (fieldsToUpdate.has("title")) {
                updateProgramme.setTitle(fieldsToUpdate.get("title").asText());
            }

            if (fieldsToUpdate.has("description")) {
                updateProgramme.setDescription(fieldsToUpdate.get("description").asText());
            }

            if (fieldsToUpdate.has("startTime")) {
                String startTimeStr = fieldsToUpdate.get("startTime").asText();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime startTime = LocalDateTime.parse(startTimeStr, formatter);
                updateProgramme.setStartTime(startTime);
            }

            if (fieldsToUpdate.has("ownerType")) {
                updateProgramme.setOwnerType(fieldsToUpdate.get("ownerType").asText());
            }

            if (fieldsToUpdate.has("repeatRule")) {
                updateProgramme.setRepeatRule(fieldsToUpdate.get("repeatRule").asText());
            }

            if (fieldsToUpdate.has("remindTime")) {
                String remindTimeStr = fieldsToUpdate.get("remindTime").asText();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime remindTime = LocalDateTime.parse(remindTimeStr, formatter);
                updateProgramme.setRemindTime(remindTime);
            }

            if (fieldsToUpdate.has("eventStatus")) {
                updateProgramme.setEventStatus(Integer.parseInt(fieldsToUpdate.get("eventStatus").asText()));
            }

            // 调用服务更新日程
            ApiResponse<?> updateResponse = calebdarFeignClientInter.updateProgramme(eventId, updateProgramme);

            boolean success = updateResponse != null && updateResponse.getCode() == 200;

            if (success) {
                return "已成功修改日程：" + updateProgramme.getTitle();
            } else {
                return "修改日程失败，请稍后重试。";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "修改日程时出现错误：" + e.getMessage();
        }
    }

    /**
     * 处理删除日程操作
     */
    private String handleDeleteOperation(String message, Long userId) {
        try {
            // 使用专门的提示词识别要删除的日程
            String deleteInfoJson = chatClient.prompt()
                .user(message)
                .system(programmeDeletePrompt)
                .call()
                .content();

            // 解析删除信息
            ObjectMapper mapper = new ObjectMapper();
            JsonNode deleteInfo = mapper.readTree(deleteInfoJson);

            JsonNode eventIdentifier = deleteInfo.has("eventIdentifier") ? deleteInfo.get("eventIdentifier") : null;

            if (eventIdentifier == null) {
                return "无法识别要删除的日程，请提供更明确的信息。";
            }

            String title = eventIdentifier.has("title") ? eventIdentifier.get("title").asText() : null;
            String time = eventIdentifier.has("time") ? eventIdentifier.get("time").asText() : null;
            String description = eventIdentifier.has("description") ? eventIdentifier.get("description").asText() : null;

            // 搜索关键字优先使用标题，其次是描述
            String searchKeyword = title != null ? title : (description != null ? description : "");

            if (searchKeyword.isEmpty()) {
                return "无法识别要删除的日程，请提供更明确的信息。";
            }

            // 先查询所有日程
            ApiResponse<?> allProgrammes = calebdarFeignClientInter.getAll(userId);

            if (allProgrammes == null || allProgrammes.getData() == null) {
                return "获取日程列表失败，请稍后重试。";
            }

            // 在所有日程中查找匹配的日程
            ProgrammeVO matchedVO = null;
            if (allProgrammes.getData() instanceof List<?>) {
                List<?> dataList = (List<?>) allProgrammes.getData();
                for (Object item : dataList) {
                    if (item instanceof ProgrammeVO) {
                        ProgrammeVO programmeVO = (ProgrammeVO) item;
                        if (programmeVO.getTitle() != null && programmeVO.getTitle().contains(searchKeyword)) {
                            matchedVO = programmeVO;
                            break;
                        } else if (programmeVO.getDescription() != null && programmeVO.getDescription().contains(searchKeyword)) {
                            matchedVO = programmeVO;
                            break;
                        }
                    }
                }
            }

            if (matchedVO == null) {
                return "未找到符合条件的日程，请确认日程信息是否正确。";
            }

            // 根据匹配的日程信息查询日程ID
            int eventId = -1;

            // 通过日程的其他信息（如标题、时间）查询具体日程ID
            // 遍历所有日程，尝试找到匹配的ID
            ApiResponse<?> allEvents = calebdarFeignClientInter.getAll(userId);
            if (allEvents != null && allEvents.getData() != null && allEvents.getData() instanceof List<?>) {
                List<?> events = (List<?>) allEvents.getData();
                for (int i = 0; i < events.size(); i++) {
                    Object event = events.get(i);
                    if (event instanceof Map) {
                        Map<?, ?> eventMap = (Map<?, ?>) event;

                        // 检查这个日程的标题和描述是否匹配
                        Object titleObj = eventMap.get("title");
                        Object descObj = eventMap.get("description");

                        boolean titleMatches = titleObj != null &&
                            titleObj.toString().equals(matchedVO.getTitle());
                        boolean descMatches = descObj != null &&
                            matchedVO.getDescription() != null &&
                            descObj.toString().equals(matchedVO.getDescription());

                        if (titleMatches || descMatches) {
                            // 找到匹配的日程，获取ID
                            Object idObj = eventMap.get("eventId");
                            if (idObj != null) {
                                if (idObj instanceof Integer) {
                                    eventId = (Integer) idObj;
                                    break;
                                } else if (idObj instanceof Number) {
                                    eventId = ((Number) idObj).intValue();
                                    break;
                                } else {
                                    try {
                                        eventId = Integer.parseInt(idObj.toString());
                                        break;
                                    } catch (NumberFormatException e) {
                                        // 忽略解析错误，继续查找
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 如果还是找不到ID，尝试通过getById查询所有可能的日程
            if (eventId == -1) {
                // 查询最近添加的几个日程，从最新的开始检查
                for (int i = 1; i <= 10; i++) {
                    try {
                        ApiResponse<?> response = calebdarFeignClientInter.getById(i);
                        if (response != null && response.getCode() == 200 && response.getData() != null) {
                            Object data = response.getData();
                            // 检查返回的数据是否含有与我们匹配的日程信息
                            String dataStr = data.toString();
                            if (dataStr.contains(matchedVO.getTitle()) ||
                                (matchedVO.getDescription() != null && dataStr.contains(matchedVO.getDescription()))) {
                                eventId = i;
                                break;
                            }
                        }
                    } catch (Exception e) {
                        // 忽略单个查询错误，继续尝试
                    }
                }
            }

            // 如果仍然找不到ID
            if (eventId == -1) {
                return "无法获取日程ID，请提供更准确的日程信息。";
            }

            // 调用服务删除日程
            ApiResponse<?> deleteResponse = calebdarFeignClientInter.deleteProgramme(eventId);

            boolean success = deleteResponse != null && deleteResponse.getCode() == 200;

            if (success) {
                return "已成功删除日程：" + matchedVO.getTitle();
            } else {
                return "删除日程失败，请稍后重试。";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "删除日程时出现错误：" + e.getMessage();
        }
    }

    /**
     * 从自然语言文本中识别日程信息并转化为Programme对象
     */
    public Programme extractProgrammeFromText(String message, int userId) {
        try {
            message+="当前时间为"+LocalDateTime.now();
            // 调用AI模型识别日程信息
            String aiResponse = chatClient.prompt()
                    .user(message)
                    .system(programmeIdentifyPrompt)
                    .call()
                    .content();

            // 检查响应是否为有效JSON
            if (aiResponse.trim().startsWith("{") && aiResponse.trim().endsWith("}")) {
                // 解析AI返回的JSON
                ObjectMapper mapper = new ObjectMapper();
                JsonNode jsonNode = mapper.readTree(aiResponse);

                // 创建Programme对象并设置属性
                Programme programme = new Programme();

                // 设置日程标题
                if (jsonNode.has("title")) {
                    programme.setTitle(jsonNode.get("title").asText());
                }

                // 设置日程描述
                if (jsonNode.has("description")) {
                    programme.setDescription(jsonNode.get("description").asText());
                }

                // 设置开始时间
                if (jsonNode.has("startTime")) {
                    String startTimeStr = jsonNode.get("startTime").asText();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    LocalDateTime startTime = LocalDateTime.parse(startTimeStr, formatter);
                    programme.setStartTime(startTime);
                } else {
                    programme.setStartTime(LocalDateTime.now());
                }

                // 设置创建时间
                programme.setCreateTime(LocalDateTime.now());

                // 设置创建者ID
                if (jsonNode.has("creatorId")) {
                    programme.setCreatorId(jsonNode.get("creatorId").asInt());
                } else {
                    programme.setCreatorId(userId);
                }

                // 设置所有者类型
                if (jsonNode.has("ownerType")) {
                    programme.setOwnerType(jsonNode.get("ownerType").asText());
                } else {
                    programme.setOwnerType("个人");
                }

                // 设置重复规则
                if (jsonNode.has("repeatRule")) {
                    programme.setRepeatRule(jsonNode.get("repeatRule").asText());
                } else {
                    programme.setRepeatRule("一次性");
                }

                // 设置提醒时间
                if (jsonNode.has("remindTime")) {
                    String remindTimeStr = jsonNode.get("remindTime").asText();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    LocalDateTime remindTime = LocalDateTime.parse(remindTimeStr, formatter);
                    programme.setRemindTime(remindTime);
                } else {
                    // 默认提前10分钟提醒
                    programme.setRemindTime(programme.getStartTime().minusMinutes(10));
                }

                // 设置事件状态
                if (jsonNode.has("eventStatus")) {
                    programme.setEventStatus(jsonNode.get("eventStatus").asInt());
                } else {
                    programme.setEventStatus(1); // 默认为正常状态
                }

                return programme;
            } else {
                // 如果不是有效的JSON，尝试提取最可能的JSON部分
                int startIdx = aiResponse.indexOf('{');
                int endIdx = aiResponse.lastIndexOf('}');
                
                if (startIdx >= 0 && endIdx > startIdx) {
                    String extractedJson = aiResponse.substring(startIdx, endIdx + 1);
                    // 用提取的JSON重新尝试解析
                    ObjectMapper mapper = new ObjectMapper();
                    JsonNode jsonNode = mapper.readTree(extractedJson);
                    
                    // 创建Programme对象并设置属性
                    Programme programme = new Programme();
                    
                    // 设置日程标题
                    if (jsonNode.has("title")) {
                        programme.setTitle(jsonNode.get("title").asText());
                    }
                    
                    // 设置日程描述
                    if (jsonNode.has("description")) {
                        programme.setDescription(jsonNode.get("description").asText());
                    }
                    
                    // 设置开始时间
                    if (jsonNode.has("startTime")) {
                        String startTimeStr = jsonNode.get("startTime").asText();
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        LocalDateTime startTime = LocalDateTime.parse(startTimeStr, formatter);
                        programme.setStartTime(startTime);
                    } else {
                        programme.setStartTime(LocalDateTime.now());
                    }
                    
                    // 设置创建时间
                    programme.setCreateTime(LocalDateTime.now());
                    
                    // 设置创建者ID
                    if (jsonNode.has("creatorId")) {
                        programme.setCreatorId(jsonNode.get("creatorId").asInt());
                    } else {
                        programme.setCreatorId(userId);
                    }
                    
                    // 设置所有者类型
                    if (jsonNode.has("ownerType")) {
                        programme.setOwnerType(jsonNode.get("ownerType").asText());
                    } else {
                        programme.setOwnerType("个人");
                    }
                    
                    // 设置重复规则
                    if (jsonNode.has("repeatRule")) {
                        programme.setRepeatRule(jsonNode.get("repeatRule").asText());
                    } else {
                        programme.setRepeatRule("一次性");
                    }
                    
                    // 设置提醒时间
                    if (jsonNode.has("remindTime")) {
                        String remindTimeStr = jsonNode.get("remindTime").asText();
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        LocalDateTime remindTime = LocalDateTime.parse(remindTimeStr, formatter);
                        programme.setRemindTime(remindTime);
                    } else {
                        // 默认提前10分钟提醒
                        programme.setRemindTime(programme.getStartTime().minusMinutes(10));
                    }
                    
                    // 设置事件状态
                    if (jsonNode.has("eventStatus")) {
                        programme.setEventStatus(jsonNode.get("eventStatus").asInt());
                    } else {
                        programme.setEventStatus(1); // 默认为正常状态
                    }
                    
                    return programme;
                }
                
                // 如果无法解析JSON，使用默认值创建Programme对象
                log.error("无法从AI响应中解析JSON：{}", aiResponse);
                Programme defaultProgramme = new Programme();
                defaultProgramme.setTitle("未命名日程");
                defaultProgramme.setDescription("从消息中提取的日程");
                defaultProgramme.setStartTime(LocalDateTime.now().plusHours(1));
                defaultProgramme.setCreateTime(LocalDateTime.now());
                defaultProgramme.setCreatorId(userId);
                defaultProgramme.setOwnerType("个人");
                defaultProgramme.setRepeatRule("一次性");
                defaultProgramme.setRemindTime(LocalDateTime.now().plusHours(1).minusMinutes(10));
                defaultProgramme.setEventStatus(1);
                return defaultProgramme;
            }
        } catch (Exception e) {
            // 记录错误信息
            log.error("提取日程信息时出现异常", e);
            // 返回一个空的Programme对象或者抛出自定义异常
            Programme defaultProgramme = new Programme();
            defaultProgramme.setTitle("未命名日程");
            defaultProgramme.setDescription("从消息中提取的日程");
            defaultProgramme.setStartTime(LocalDateTime.now().plusHours(1));
            defaultProgramme.setCreateTime(LocalDateTime.now());
            defaultProgramme.setCreatorId(userId);
            defaultProgramme.setOwnerType("个人");
            defaultProgramme.setRepeatRule("一次性");
            defaultProgramme.setRemindTime(LocalDateTime.now().plusHours(1).minusMinutes(10));
            defaultProgramme.setEventStatus(1);
            return defaultProgramme;
        }
    }

    public void getUserMsg(String msg,Long id){
        // 设置当前用户ID
        DbChatMemory.setCurrentUserId(id.intValue());
        
        try {
              String userMsg  =chatClient
               .prompt()
               .user(msg)
                .system(rchat)
                .call()
                .content();
             if("日程操作".equals(userMsg)){
                 String content = chatClient.prompt()
                         .user(msg)
                         .system(rProgramme)
                         .call()
                         .content();
                 if("查询".equals( content)){
                     systemPropt=agentLookSchedulePrompt;
                     calebdarFeignClientInter.getAll(id);
                 }
                 if("添加".equals( content)){
                     systemPropt=systemPropt1;
                     Programme programme = extractProgrammeFromText(msg,id.intValue());
                     programme.setCreatorId(id.intValue());
                     programme.setCreateTime(LocalDateTime.now());
                     // 确保设置eventId为0，这样会被当作新记录
                     programme.setEventId(0);
                     calebdarFeignClientInter.addProgramme(id, programme);
                 }
                 if("修改".equals( content)||"删除".equals( content)){
                     systemPropt="屏蔽删除日程或修改日程的部分，针对其他部分做出回答";
                 }
//                 if(){
//                     systemPropt="请回复用户，无法识别意图";
//                 }

              } else if ("语音聊天".equals(userMsg)) {
                systemPropt=agentyuyingPrompt;
              }else{
                systemPropt=agentChatPrompt;
             }
        } finally {
            // 确保清除用户ID
            DbChatMemory.clearCurrentUserId();
        }
    }
    static String TimeNow(){
        // 获取当前时间（包含年月日时分秒）
        LocalDateTime now = LocalDateTime.now();

        // 定义格式化模式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 格式化时间
        return now.format(formatter);
    }
}