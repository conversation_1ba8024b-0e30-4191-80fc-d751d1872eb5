package com.ruoyi.wenshucommon.util.voiceutil;

import java.util.LinkedHashSet;
import java.util.Set;

/**
 * 文本去重工具类
 * 用于处理语音识别结果中可能出现的重复文本
 */
public class TextDeDuplication {
    /**
     * 处理文本结果，去除重复内容
     * 
     * @param rawText 原始文本
     * @return 处理后的文本
     */
    public static String processResult(String rawText) {
        // 1. 基础清理：移除空白字符和特殊符号
        String cleaned = rawText.replaceAll("[\\s\\u200B\\uFEFF\\u2028\\u2029]+", " ");

        // 2. 句子级去重（支持中英文标点）
        String[] sentences = cleaned.split("(?<=[。！？!.])\\s*");
        Set<String> uniqueSentences = new LinkedHashSet<>();
        StringBuilder deduped = new StringBuilder();

        for (String sentence : sentences) {
            String trimmed = sentence.replaceAll("^\\s+|\\s+$", "");
            if (!trimmed.isEmpty() && uniqueSentences.add(trimmed)) {
                deduped.append(trimmed).append("。");
            }
        }

        // 3. 智能换行处理
        String paragraphText = deduped.toString()
                .replaceAll("。+", "。")        // 合并连续句号
                .replaceAll("。\\s*。", "。")  // 修复错误分割
                .replaceAll("(?<=。)\\s*", "\n"); // 句子换行

        // 4. 最终格式化
        return paragraphText.replaceAll("[\n]+", "\n") // 合并空行
                .replaceAll("^\\s+|\\s+$", "")
                .trim();
    }
} 