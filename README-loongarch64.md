# 文书智能计算平台 - LoongArch64部署指南

本指南介绍如何在龙芯LoongArch64架构上部署文书智能计算平台。

## 一、环境准备

### 1. 安装Docker

```bash
# 更新软件包索引
sudo apt update

# 安装所需的包
sudo apt install -y ca-certificates curl gnupg lsb-release

# 添加龙芯Docker源
echo "deb http://pkg.loongnix.cn:8080/loongnix/repo loongnix main" | sudo tee /etc/apt/sources.list.d/loongnix.list

# 导入GPG密钥
curl -fsSL http://pkg.loongnix.cn:8080/loongnix/repo/apt.gpg | sudo apt-key add -

# 更新软件包索引
sudo apt update

# 安装Docker
sudo apt install -y docker-ce
```

### 2. 验证Docker安装

```bash
sudo docker --version
sudo docker run hello-world
```

## 二、修改Dockerfile适配LoongArch64

```bash
# 给脚本添加执行权限
chmod +x update-dockerfiles-loongarch64.sh

# 运行脚本修改所有Dockerfile
./update-dockerfiles-loongarch64.sh
```

## 三、构建服务镜像

```bash
# 给脚本添加执行权限
chmod +x build-all-docker.sh

# 构建所有服务的Docker镜像
./build-all-docker.sh
```

## 四、手动部署服务

由于LoongArch64缺乏Docker Compose的官方支持，我们使用纯Docker命令进行部署：

```bash
# 给脚本添加执行权限
chmod +x deploy-manual.sh

# 部署所有服务
./deploy-manual.sh
```

## 五、部署后验证

1. 检查服务状态：
```bash
docker ps
```

2. 查看服务日志：
```bash
# 例如查看网关服务日志
docker logs -f ruoyi-gateway
```

3. 访问服务：
   - Nacos控制台: http://localhost:8848/nacos (用户名/密码: nacos/nacos)
   - 网关服务: http://localhost:8080
   - 系统服务: http://localhost:9201
   - 多模态服务: http://localhost:8702

## 六、服务管理

部署脚本会自动创建以下管理脚本：

1. 停止所有服务：
```bash
./stop-services.sh
```

2. 停止并删除所有服务和网络：
```bash
./cleanup-all.sh
```

## 七、常见问题

### 1. 镜像拉取失败

如果从龙芯镜像源拉取镜像失败，可以尝试以下方法：

```bash
# 手动拉取MySQL镜像
docker pull cr.loongnix.cn/library/mysql:8.0.40

# 手动拉取Nacos镜像
docker pull cr.loongnix.cn/nacos/nacos-server:2.0.3
```

### 2. 数据库初始化问题

如果数据库初始化失败，可以手动执行SQL脚本：

```bash
# 将SQL文件复制到容器
docker cp sql/ry-cloud.sql mysql:/tmp/
docker cp sql/ry-config.sql mysql:/tmp/

# 在容器中执行SQL
docker exec -it mysql bash -c "mysql -uroot -p2313147023 ry-cloud < /tmp/ry-cloud.sql"
docker exec -it mysql bash -c "mysql -uroot -p2313147023 < /tmp/ry-config.sql"
```

### 3. URL参数转义问题

在LoongArch64环境中，URL参数中的特殊字符需要转义：

```bash
# 正确的URL格式示例（注意&符号需要转义为\&）
*******************************************************************************************************
```

### 4. 服务无法连接到Nacos或MySQL

确认网络配置正确：

```bash
# 检查网络
docker network inspect wenshu-network

# 检查容器IP
docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' nacos
docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' mysql
```

### 5. 处理已有容器冲突

如果系统中已经有运行的容器，可以先停止并删除它们：

```bash
# 停止并删除已有容器
docker stop wenshu-nacos wenshu-redis wenshu-mysql
docker rm wenshu-nacos wenshu-redis wenshu-mysql
```

### 6. 资源不足

龙芯设备可能资源有限，可以限制服务的资源使用：

```bash
docker update --memory=512m --memory-swap=1g nacos
```

## 八、系统架构

本系统包含以下主要服务：

1. **基础设施服务**：
   - MySQL: 数据存储
   - Nacos: 服务注册与配置中心

2. **核心服务**：
   - ruoyi-gateway: API网关
   - ruoyi-auth: 认证服务
   - ruoyi-system: 系统服务

3. **业务服务**：
   - wenshu-multimodal: 多模态处理服务
   - wenshu-chat: 聊天服务
   - wenshu-voice: 语音服务
   - wenshu-remind: 提醒服务
   - wenshu-editfile: 文件编辑服务 