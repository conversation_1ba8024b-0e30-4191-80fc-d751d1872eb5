package com.ruoyi.wenshuaudit.service;

import com.ruoyi.wenshuapi.pojo.audit.WenshuAuditLog;
import com.ruoyi.wenshuaudit.common.RestResult;

import java.util.List;

public interface WenshuAuditLogService {
    /**
     * 添加审计日志
     */
    RestResult<Void> addLog(WenshuAuditLog log);
    
    /**
     * 查询审计日志列表
     */
    RestResult<List<WenshuAuditLog>> getLogList(Integer auditId);
    
    /**
     * 删除审计日志
     */
    RestResult<Void> deleteLog(Integer logId);
}
