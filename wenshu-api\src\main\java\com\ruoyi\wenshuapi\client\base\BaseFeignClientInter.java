package com.ruoyi.wenshuapi.client.base;

import com.ruoyi.wenshuapi.common.RestResult;
import com.ruoyi.wenshuapi.pojo.base.BaseConversation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用于调用wenshu-base微服务
 */
@FeignClient(name = "wenshu-base", contextId = "baseFeignClientInter", path = "/api/conversation")
public interface BaseFeignClientInter {

    @PostMapping("/create")
    RestResult<BaseConversation> create(@RequestParam("userId") int userId);

    @PutMapping("/edit")
    public RestResult<Void> edit(@RequestBody BaseConversation conversation, @RequestParam("userId") int userId);

    @DeleteMapping("/del")
    RestResult<Void> del(@RequestParam(value = "conversationId") String conversationId);

    @GetMapping("/list")
    RestResult<List<BaseConversation>> list(@RequestParam("userId") int userId);

    @GetMapping("/get")
    RestResult<BaseConversation> get(@RequestParam(value = "conversationId") String conversationId, @RequestParam("userId") int userId);
} 