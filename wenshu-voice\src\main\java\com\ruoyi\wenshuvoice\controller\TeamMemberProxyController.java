//package com.ruoyi.wenshuvoice.controller;
//
//import com.ruoyi.wenshuapi.client.team.TeamMemberClient;
//import com.ruoyi.wenshuapi.pojo.team.TeamUserRelation;
//import com.ruoyi.wenshuapi.util.file.ApiResponse;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
///**
// * 团队-成员关系代理控制器
// * 通过 Feign 客户端代理访问团队服务
// * 顶层路径: /api/team/member
// */
//@RestController
//@RequestMapping("/api/team/member")
//public class TeamMemberProxyController {
//
//    private final TeamMemberClient teamMemberClient;
//
//    @Autowired
//    public TeamMemberProxyController(TeamMemberClient teamMemberClient) {
//        this.teamMemberClient = teamMemberClient;
//    }
//
//    /**
//     * 添加成员到团队 (代理)
//     *
//     * @param relation 团队-用户关系对象
//     * @return 操作结果
//     */
//    @PostMapping("/add")
//    public ApiResponse<Boolean> addMemberToTeam(@RequestBody TeamUserRelation relation) {
//        return teamMemberClient.addMemberToTeam(relation);
//    }
//
//    /**
//     * 从团队中移除成员 (代理)
//     *
//     * @param teamId 团队ID
//     * @param userId 用户ID
//     * @return 操作结果
//     */
//    @DeleteMapping("/remove")
//    public ApiResponse<Boolean> removeMemberFromTeam(
//            @RequestParam("teamId") int teamId,
//            @RequestParam("userId") int userId) {
//        return teamMemberClient.removeMemberFromTeam(teamId, userId);
//    }
//
//    /**
//     * 解散团队 (代理)
//     *
//     * @param teamId 团队ID
//     * @return 操作结果
//     */
//    @DeleteMapping("/disband/{teamId}")
//    public ApiResponse<Integer> disbandTeam(@PathVariable("teamId") int teamId) {
//        return teamMemberClient.disbandTeam(teamId);
//    }
//
//    /**
//     * 移除用户的所有团队关系 (代理)
//     *
//     * @param userId 用户ID
//     * @return 操作结果
//     */
//    @DeleteMapping("/removeuser/{userId}")
//    public ApiResponse<Integer> removeUserFromAllTeams(@PathVariable("userId") int userId) {
//        return teamMemberClient.removeUserFromAllTeams(userId);
//    }
//
//    /**
//     * 获取团队所有成员ID列表 (代理)
//     *
//     * @param teamId 团队ID
//     * @return 成员ID列表
//     */
//    @GetMapping("/members/{teamId}")
//    public ApiResponse<List<Integer>> getTeamMembers(@PathVariable("teamId") int teamId) {
//        return teamMemberClient.getTeamMembers(teamId);
//    }
//
//    /**
//     * 获取用户加入的所有团队ID列表 (代理)
//     *
//     * @param userId 用户ID
//     * @return 团队ID列表
//     */
//    @GetMapping("/teams/{userId}")
//    public ApiResponse<List<Integer>> getUserTeams(@PathVariable("userId") int userId) {
//        return teamMemberClient.getUserTeams(userId);
//    }
//
//    /**
//     * 检查用户是否在指定团队中 (代理)
//     *
//     * @param teamId 团队ID
//     * @param userId 用户ID
//     * @return 检查结果
//     */
//    @GetMapping("/check")
//    public ApiResponse<Boolean> isUserInTeam(
//            @RequestParam("teamId") int teamId,
//            @RequestParam("userId") int userId) {
//        return teamMemberClient.isUserInTeam(teamId, userId);
//    }
//
//    /**
//     * 将用户转移到新团队 (代理)
//     *
//     * @param userId    用户ID
//     * @param oldTeamId 原团队ID
//     * @param newTeamId 新团队ID
//     * @return 操作结果
//     */
//    @PutMapping("/transfer")
//    public ApiResponse<Boolean> transferUserToNewTeam(
//            @RequestParam("userId") int userId,
//            @RequestParam("oldTeamId") int oldTeamId,
//            @RequestParam("newTeamId") int newTeamId) {
//        return teamMemberClient.transferUserToNewTeam(userId, oldTeamId, newTeamId);
//    }
//
//    /**
//     * 获取团队成员数量 (代理)
//     *
//     * @param teamId 团队ID
//     * @return 成员数量
//     */
//    @GetMapping("/count/members/{teamId}")
//    public ApiResponse<Integer> countTeamMembers(@PathVariable("teamId") int teamId) {
//        return teamMemberClient.countTeamMembers(teamId);
//    }
//
//    /**
//     * 获取用户加入的团队数量 (代理)
//     *
//     * @param userId 用户ID
//     * @return 团队数量
//     */
//    @GetMapping("/count/teams/{userId}")
//    public ApiResponse<Integer> countUserTeams(@PathVariable("userId") int userId) {
//        return teamMemberClient.countUserTeams(userId);
//    }
//
//    /**
//     * 批量添加成员到团队 (代理)
//     *
//     * @param teamId  团队ID
//     * @param userIds 用户ID列表
//     * @return 成功添加的数量
//     */
//    @PostMapping("/batch/add")
//    public ApiResponse<Integer> batchAddMembersToTeam(
//            @RequestParam("teamId") int teamId,
//            @RequestBody List<Integer> userIds) {
//        return teamMemberClient.batchAddMembersToTeam(teamId, userIds);
//    }
//
//    /**
//     * 批量从团队中移除成员 (代理)
//     *
//     * @param teamId  团队ID
//     * @param userIds 用户ID列表
//     * @return 成功移除的数量
//     */
//    @DeleteMapping("/batch/remove")
//    public ApiResponse<Integer> batchRemoveMembersFromTeam(
//            @RequestParam("teamId") int teamId,
//            @RequestBody List<Integer> userIds) {
//        return teamMemberClient.batchRemoveMembersFromTeam(teamId, userIds);
//    }
//}