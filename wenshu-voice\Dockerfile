FROM lcr.loongnix.cn/library/openjdk:17

USER root
ENV TZ=Asia/Shanghaiimage.png
WORKDIR /tmp
RUN mkdir -p /tmp/temp
COPY wenshu-voice/target/*.jar /tmp/app.jar
ENV SPRING_PROFILES_ACTIVE=prod
ENV JAVA_OPTS="-Dnacos.remote.client.grpc.enable=false -Dcom.alibaba.nacos.client.config.impl.HttpAgent.isUseGrpcFeature=false"
EXPOSE 1014
VOLUME ["/app/temp"]
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar /app/app.jar"]
