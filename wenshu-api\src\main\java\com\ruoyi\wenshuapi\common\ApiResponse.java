package com.ruoyi.wenshuapi.common;

import lombok.Data;

/**
 * 统一API响应结果封装
 */
@Data
public class ApiResponse<T> {
    private Integer code;
    private String message;
    private T data;

    /**
     * 成功返回结果
     * @param data 获取的数据
     */
    public static <T> ApiResponse<T> success(T data) {
        return success(data, "操作成功");
    }

    /**
     * 成功返回结果
     * @param data 获取的数据
     * @param message 提示信息
     */
    public static <T> ApiResponse<T> success(T data, String message) {
        ApiResponse<T> apiResult = new ApiResponse<>();
        apiResult.setCode(0000);
        apiResult.setMessage(message);
        apiResult.setData(data);
        return apiResult;
    }

    /**
     * 失败返回结果
     * @param message 提示信息
     */
    public static <T> ApiResponse<T> failed(String message) {
        return failed(message, 500);
    }

    /**
     * 失败返回结果
     * @param message 提示信息
     * @param code 错误码
     */
    public static <T> ApiResponse<T> failed(String message, Integer code) {
        ApiResponse<T> apiResult = new ApiResponse<>();
        apiResult.setCode(code);
        apiResult.setMessage(message);
        return apiResult;
    }

    /**
     * 参数验证失败返回结果
     * @param message 提示信息
     */
    public static <T> ApiResponse<T> validateFailed(String message) {
        return failed(message, 400);
    }
} 