#!/bin/bash

# 更新Redis到v7.4.4版本
echo "=== 更新Redis到v7.4.4版本 ==="

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ 错误: Docker未运行，请先启动Docker"
    exit 1
fi

echo "✅ Docker运行正常"

# 1. 停止现有的Redis服务
echo ""
echo "1. 停止现有Redis服务..."
docker compose -f docker-compose.env.yml down redis 2>/dev/null || echo "   Redis服务未在运行"

# 2. 拉取新的Redis镜像
echo ""
echo "2. 拉取Redis v7.4.4镜像..."
docker pull lcr.loongnix.cn/library/redis:7.4.4

if [ $? -eq 0 ]; then
    echo "✅ Redis v7.4.4镜像拉取成功"
else
    echo "❌ Redis镜像拉取失败，请检查网络连接"
    exit 1
fi

# 3. 删除旧的Redis镜像（可选）
echo ""
echo "3. 清理旧的Redis镜像..."
docker rmi lcr.loongnix.cn/library/redis:7.0.0-alpine 2>/dev/null || echo "   旧镜像不存在或已被使用"

# 4. 备份Redis数据（可选）
read -p "是否备份Redis数据？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "   备份Redis数据..."
    timestamp=$(date +%Y%m%d_%H%M%S)
    docker run --rm -v wenshuintelligentcomputing-back_redis_data:/data -v $(pwd):/backup lcr.loongnix.cn/library/redis:7.4.4 tar czf /backup/redis_backup_${timestamp}.tar.gz -C /data .
    if [ $? -eq 0 ]; then
        echo "✅ Redis数据备份完成: redis_backup_${timestamp}.tar.gz"
    else
        echo "⚠️  Redis数据备份失败，但继续更新"
    fi
fi

# 5. 启动新版本的Redis
echo ""
echo "4. 启动Redis v7.4.4..."
docker compose -f docker-compose.env.yml up -d redis

# 6. 等待Redis启动完成
echo ""
echo "5. 等待Redis启动完成..."
for i in {1..30}; do
    if docker exec wenshu-redis redis-cli ping > /dev/null 2>&1; then
        echo "✅ Redis启动成功"
        break
    fi
    echo "   等待Redis启动... ($i/30)"
    sleep 2
done

# 检查Redis是否成功启动
if ! docker exec wenshu-redis redis-cli ping > /dev/null 2>&1; then
    echo "❌ Redis启动失败，请检查日志："
    echo "   docker compose -f docker-compose.env.yml logs redis"
    exit 1
fi

# 7. 验证Redis功能
echo ""
echo "6. 验证Redis功能..."

# 测试基本功能
echo "   测试基本读写功能..."
docker exec wenshu-redis redis-cli set test_key "Redis 7.4.4 works!" > /dev/null
test_value=$(docker exec wenshu-redis redis-cli get test_key)
if [ "$test_value" = "Redis 7.4.4 works!" ]; then
    echo "✅ Redis读写功能正常"
    docker exec wenshu-redis redis-cli del test_key > /dev/null
else
    echo "❌ Redis读写功能异常"
fi

# 检查Redis版本
echo "   检查Redis版本..."
redis_version=$(docker exec wenshu-redis redis-cli info server | grep redis_version | cut -d: -f2 | tr -d '\r')
echo "✅ 当前Redis版本: $redis_version"

# 检查内存使用
echo "   检查内存配置..."
max_memory=$(docker exec wenshu-redis redis-cli config get maxmemory | tail -1)
echo "✅ 最大内存限制: ${max_memory}字节 ($(($max_memory/1024/1024))MB)"

# 8. 重启依赖Redis的服务
echo ""
echo "7. 重启依赖Redis的服务..."
services_to_restart=("auth" "system" "gateway")

for service in "${services_to_restart[@]}"; do
    if docker ps --filter "name=wenshu-$service" --format "{{.Names}}" | grep -q "wenshu-$service"; then
        echo "   重启 $service 服务..."
        docker compose -f docker-compose.app.yml restart $service
        sleep 5
    fi
done

# 9. 最终检查
echo ""
echo "8. 最终状态检查..."
echo "=== Redis容器状态 ==="
docker ps --filter "name=wenshu-redis" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo "=== Redis健康检查 ==="
if docker exec wenshu-redis redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis服务正常"
else
    echo "❌ Redis服务异常"
fi

echo ""
echo "=== Redis配置信息 ==="
echo "端口: 6379"
echo "数据持久化: AOF + RDB"
echo "最大内存: 96MB"
echo "内存策略: allkeys-lru"
echo "最大客户端: 50"
echo "数据库数量: 8"

echo ""
echo "=== 更新完成 ==="
echo "🎉 Redis已成功更新到v7.4.4版本"
echo ""
echo "Redis v7.4.4新特性："
echo "  - 性能优化和bug修复"
echo "  - 更好的内存管理"
echo "  - 增强的安全性"
echo "  - 改进的集群支持"
echo ""
echo "访问Redis："
echo "  docker exec -it wenshu-redis redis-cli"
echo ""
echo "查看Redis日志："
echo "  docker compose -f docker-compose.env.yml logs redis"
