package com.ruoyi.wenshuaudit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.wenshuapi.pojo.audit.WenshuAuditLog;
import com.ruoyi.wenshuaudit.common.RestResult;
import com.ruoyi.wenshuaudit.dao.WenshuAuditLogDao;
import com.ruoyi.wenshuaudit.service.WenshuAuditLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class WenshuAuditLogServiceImpl implements WenshuAuditLogService {

    private final WenshuAuditLogDao wenshuAuditLogDao;

    @Override
    public RestResult<Void> addLog(WenshuAuditLog auditLog) {
        try {
            wenshuAuditLogDao.insert(auditLog);
            return RestResult.buildSuccessResult();
        } catch (Exception e) {
            log.error("添加审计日志失败", e);
            return RestResult.buildErrorResult("500", "添加审计日志失败");
        }
    }

    @Override
    public RestResult<List<WenshuAuditLog>> getLogList(Integer auditId) {
        try {
            List<WenshuAuditLog> logs = wenshuAuditLogDao.selectList(
                    new LambdaQueryWrapper<WenshuAuditLog>()
                            .eq(WenshuAuditLog::getAuditId, auditId)
            );
            return RestResult.buildSuccessResult(logs);
        } catch (Exception e) {
            log.error("查询审计日志列表失败", e);
            return RestResult.buildErrorResult("500", "查询审计日志列表失败");
        }
    }

    @Override
    public RestResult<Void> deleteLog(Integer logId) {
        try {
            wenshuAuditLogDao.deleteById(logId);
            return RestResult.buildSuccessResult();
        } catch (Exception e) {
            log.error("删除审计日志失败", e);
            return RestResult.buildErrorResult("500", "删除审计日志失败");
        }
    }
}
