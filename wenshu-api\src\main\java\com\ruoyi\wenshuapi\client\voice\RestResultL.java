package com.ruoyi.wenshuapi.client.voice;

public class RestResultL<T> {
    private int code;
    private String message;
    private T data;

    public static <T> RestResultL<T> success(String message, T data) {
        RestResultL<T> result = new RestResultL<>();
        result.setCode(200);
        result.setMessage(message);
        result.setData(data);
        return result;
    }

    public static <T> RestResultL<T> error(int code, String message) {
        RestResultL<T> result = new RestResultL<>();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
// getters/setters 省略
}