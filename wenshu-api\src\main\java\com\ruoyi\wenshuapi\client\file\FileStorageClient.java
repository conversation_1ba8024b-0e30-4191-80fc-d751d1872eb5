package com.ruoyi.wenshuapi.client.file;

import com.ruoyi.wenshuapi.fallback.file.FileStorageClientFallback;
import com.ruoyi.wenshuapi.util.file.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 文件存储服务Feign客户端接口
 * 专门用于文件上传、下载、删除等存储相关操作
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-28
 */
@FeignClient(
        name = "wenshu-file",
        path = "/wenshu/fileinfo",
        contextId = "fileStorageClient",
        fallbackFactory = FileStorageClientFallback.class
)
public interface FileStorageClient {

    /**
     * 文件上传接口（Feign远程调用）
     * 支持所有文件类型，单个文件不超过100MB
     * 上传成功后自动保存文件信息到数据库
     *
     * @param file 上传的文件
     * @param uploaderId 上传者ID（可选）
     * @param ownerType 所有者类型（个人/团队，默认为个人）
     * @param fileStatus 文件状态（可读写/协作/不可读写，默认为可读写）
     * @return 上传结果，包含文件信息和访问URL
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ApiResponse<Map<String, Object>> uploadFile(
            @RequestPart("file") MultipartFile file,
            @RequestParam(value = "uploaderId", required = false) Integer uploaderId,
            @RequestParam(value = "ownerType", defaultValue = "个人") String ownerType,
            @RequestParam(value = "fileStatus", defaultValue = "可读写") String fileStatus
    );

    /**
     * 批量文件上传接口（Feign远程调用）
     * 支持同时上传多个文件
     *
     * @param files 上传的文件数组
     * @param uploaderId 上传者ID（可选）
     * @param ownerType 所有者类型（个人/团队，默认为个人）
     * @param fileStatus 文件状态（可读写/协作/不可读写，默认为可读写）
     * @return 批量上传结果
     */
    @PostMapping(value = "/batchUpload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ApiResponse<Map<String, Object>> batchUploadFiles(
            @RequestPart("files") MultipartFile[] files,
            @RequestParam(value = "uploaderId", required = false) Integer uploaderId,
            @RequestParam(value = "ownerType", defaultValue = "个人") String ownerType,
            @RequestParam(value = "fileStatus", defaultValue = "可读写") String fileStatus
    );

    /**
     * 获取文件下载链接（Feign远程调用）
     *
     * @param fileId 文件ID
     * @return 文件下载链接
     */
    @GetMapping("/download/{fileId}")
    ApiResponse<String> getDownloadUrl(@PathVariable("fileId") int fileId);

    /**
     * 检查文件是否存在（Feign远程调用）
     *
     * @param fileId 文件ID
     * @return 文件是否存在
     */
    @GetMapping("/exists/{fileId}")
    ApiResponse<Boolean> fileExists(@PathVariable("fileId") int fileId);

    /**
     * 获取文件访问URL（Feign远程调用）
     *
     * @param fileId 文件ID
     * @return 文件访问URL
     */
    @GetMapping("/accessUrl/{fileId}")
    ApiResponse<String> getFileAccessUrl(@PathVariable("fileId") int fileId);

    /**
     * 验证文件完整性（Feign远程调用）
     *
     * @param fileId 文件ID
     * @return 验证结果
     */
    @GetMapping("/validate/{fileId}")
    ApiResponse<Map<String, Object>> validateFileIntegrity(@PathVariable("fileId") int fileId);

    /**
     * 获取文件存储统计信息（Feign远程调用）
     *
     * @param uploaderId 上传者ID（可选）
     * @return 存储统计信息
     */
    @GetMapping("/storage/stats")
    ApiResponse<Map<String, Object>> getStorageStats(
            @RequestParam(value = "uploaderId", required = false) Integer uploaderId
    );

    /**
     * 清理临时文件（Feign远程调用）
     *
     * @return 清理结果
     */
    @PostMapping("/cleanup/temp")
    ApiResponse<String> cleanupTempFiles();
}
