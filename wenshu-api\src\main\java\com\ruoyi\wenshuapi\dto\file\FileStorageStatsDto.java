package com.ruoyi.wenshuapi.dto.file;

import lombok.Data;

import java.util.Map;

/**
 * 文件存储统计DTO
 * 封装文件存储相关的统计信息
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-28
 */
@Data
public class FileStorageStatsDto {

    /**
     * 总文件数量
     */
    private Integer totalFiles;

    /**
     * 总文件大小（字节）
     */
    private Long totalSize;

    /**
     * 格式化的总大小
     */
    private String totalSizeFormatted;

    /**
     * 已使用存储空间
     */
    private String usedSpace;

    /**
     * 可用存储空间
     */
    private String availableSpace;

    /**
     * 存储空间使用率（百分比）
     */
    private Double usagePercentage;

    /**
     * 按文件类型分组的统计
     * Key: 文件类型（如：image, document, video等）
     * Value: 该类型的文件统计信息
     */
    private Map<String, FileTypeStats> fileTypeStats;

    /**
     * 按所有者类型分组的统计
     * Key: 所有者类型（个人/团队）
     * Value: 该类型的文件统计信息
     */
    private Map<String, OwnerTypeStats> ownerTypeStats;

    /**
     * 按文件状态分组的统计
     * Key: 文件状态（可读写/协作/不可读写）
     * Value: 该状态的文件统计信息
     */
    private Map<String, FileStatusStats> fileStatusStats;

    /**
     * 最近上传的文件数量（24小时内）
     */
    private Integer recentUploads;

    /**
     * 最大单个文件大小
     */
    private Long maxFileSize;

    /**
     * 最大单个文件大小（格式化）
     */
    private String maxFileSizeFormatted;

    /**
     * 平均文件大小
     */
    private Long averageFileSize;

    /**
     * 平均文件大小（格式化）
     */
    private String averageFileSizeFormatted;

    /**
     * 文件类型统计信息
     */
    @Data
    public static class FileTypeStats {
        /**
         * 文件数量
         */
        private Integer count;

        /**
         * 总大小（字节）
         */
        private Long totalSize;

        /**
         * 格式化的总大小
         */
        private String totalSizeFormatted;

        /**
         * 占比（百分比）
         */
        private Double percentage;
    }

    /**
     * 所有者类型统计信息
     */
    @Data
    public static class OwnerTypeStats {
        /**
         * 文件数量
         */
        private Integer count;

        /**
         * 总大小（字节）
         */
        private Long totalSize;

        /**
         * 格式化的总大小
         */
        private String totalSizeFormatted;

        /**
         * 占比（百分比）
         */
        private Double percentage;
    }

    /**
     * 文件状态统计信息
     */
    @Data
    public static class FileStatusStats {
        /**
         * 文件数量
         */
        private Integer count;

        /**
         * 总大小（字节）
         */
        private Long totalSize;

        /**
         * 格式化的总大小
         */
        private String totalSizeFormatted;

        /**
         * 占比（百分比）
         */
        private Double percentage;
    }
}
