<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="UTF-8">
    <title>视频通话</title>
    <link rel="stylesheet" type="text/css" th:href="@{/plugins/layui/css/layui.css}">
    <style>
        .local-video, .remote-video {
            width: 100%;
            height: 400px;
        }

        .user-list-li {
            height: 30px;
            line-height: 30px;
            background-color: #FFB800;
        }

        .pub {
            height: calc(100vh - 190px);
        }

        .opt {
            height: 40px;
        }
    </style>
</head>
<body>
<div class="layui-container">
    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 25px;">
        <legend style="margin-left: 40%;">直播</legend>
    </fieldset>
    <div class="layui-row layui-col-space5">
        <div class="layui-col-md9 pub">
            <blockquote class="layui-elem-quote">主播</blockquote>
            <div>
                <video autoplay id="localVideo" class="local-video" th:poster="@{/images/a.jpeg}"></video>
            </div>
        </div>
        <div class="layui-col-md3">
            <blockquote class="layui-elem-quote">观众列表</blockquote>
            <div>
                <ul class="user-list">
                    <li class="user-list-li">
                        <span>Halo</span>
                    </li>
                    <li class="user-list-li">
                        <span>Halo</span>
                    </li>
                    <li class="user-list-li">
                        <span>Halo</span>
                    </li>
                    <li class="user-list-li">
                        <span>Halo</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <div class="layui-row opt">
        <div class="layui-col-xs12">
            <fieldset class="layui-elem-field layui-field-title" style="margin-top: 25px;">
                <legend style="margin-left: 40%;">操作区域</legend>
            </fieldset>
            <div class="layui-btn-container">
                <button type="button" class="layui-btn layui-btn-normal" onclick="OPT.register()">开启直播</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="OPT.videoSwitch()">摄像头</button>
                <button type="button" class="layui-btn" onclick="OPT.audioSwitch()">麦克风</button>
                <button type="button" class="layui-btn layui-btn-normal" onclick="OPT.shareScreen()">屏幕共享</button>
            </div>
        </div>
    </div>
</div>
</body>

<!--注册弹窗-->
<div id="registerDialog" style="display: none;margin-top:10px;">
    <form class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label">用户名：</label>
            <div class="layui-input-inline">
                <input type="text" name="userId" placeholder="请输入用户名" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">身份：</label>
            <div class="layui-input-block">
                <input type="radio" name="isPub" value="1" title="主播" checked="">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">房间号：</label>
            <div class="layui-input-inline">
                <input type="text" name="roomId" placeholder="请输入房间号" class="layui-input">
            </div>
        </div>
    </form>
</div>

<!--jquery-->
<script type="application/javascript" th:src="@{/javascript/jquery-2.1.4.js}"></script>
<!--layui-->
<script type="application/javascript" th:src="@{/plugins/layui/layui.js}"></script>
<!--rtc-->
<script type="application/javascript" th:src="@{/javascript/live/pub/rtc.js}"></script>
<!--sip-->
<script type="application/javascript" th:src="@{/javascript/live/pub/sip.js}"></script>
<!--opt-->
<script type="application/javascript" th:src="@{/javascript/live/pub/opt.js}"></script>

<script>
    /**
     * PeerConnection
     */
    var PeerConnection = window.RTCPeerConnection ||
        window.mozRTCPeerConnection ||
        window.webkitRTCPeerConnection;

    /**
     * 存放观众的peerconnection
     */
    var RtcPcMaps = new Map();

    /**
     * 本地peerconnection 对象
     */
    var localStream = null;

    // 声明用户ID 、 房间号 ,身份
    var userId, roomId, isPub = null;
    // 存放房间人
    var roomList = [];

    var layer, form = null;

    $(function () {
        Layui.init();
    });

    /**
     * 初始化layui组件
     * @type {{init: Layui.init}}
     */
    var Layui = {
        /**
         * 初始化layui
         */
        init: () => {
            layui.use(['layer', 'form'], function () {
                layer = layui.layer;
                form = layui.form;
            });
        }
    }

    window.onbeforeunload = function () {
        SOCKET.disConnect();
    }
</script>
</html>
